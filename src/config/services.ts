let keys: {
  Locations: string
  Users: string
  Contacts: string
  Opportunities: string
  Conversations: string
}

export const override = false

if (override || process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'beta') {
  keys = {
      Locations: 'https://services.msgsndr.com/locations',
      Users: 'https://services.msgsndr.com/users',
      Contacts: 'https://services.msgsndr.com/contacts',
      Opportunities: 'https://services.msgsndr.com/opportunities',
      Conversations: 'https://services.msgsndr.com/conversations'
  }
} else if (process.env.NODE_ENV === 'staging' || process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
  keys = {
      Locations: 'https://staging.services.msgsndr.com/locations',
      Users: 'https://staging.services.msgsndr.com/users',
      Contacts: 'https://staging.services.msgsndr.com/contacts',
      Opportunities: 'https://staging.services.msgsndr.com/opportunities',
      Conversations: 'https://staging.services.msgsndr.com/conversations'
  }
  // if(process.env.NODE_ENV === 'development'){
  //   keys.Locations = 'http://localhost:5056/locations',
  //   keys.Contacts = 'http://localhost:5058/contacts'
  //   keys.Opportunities = 'http://localhost:5066/opportunities'
  //   keys.Conversations = 'http://localhost:5063/conversations'
  // }
}

export default keys
