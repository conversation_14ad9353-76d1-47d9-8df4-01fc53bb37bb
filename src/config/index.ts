interface FirebaseConfig {
  apiKey: string
  authDomain: string
  databaseURL: string
  projectId: string
  storageBucket: string
  messagingSenderId: string
  appId: string
}

interface TipaltiConfig {
  masterKey:string,
  baseURL:string
}

interface Config {
  mode: string
  baseUrl: string
  pythonBaseUrl: string
  dialogflowEngagementsBaseUrl: string
  paymentServiceBaseUrl: string
  saasServiceBaseUrl: string
  humanRollOverServiceBaseUrl: string
  funnelServiceBaseUrl: string
  firebase: FirebaseConfig
  cnameRedirectURL: string
  cnameWhitelabelURL?: string
  serverIPAddress: string
  whitelabelIPAddress: string
  fcmPublicKey: string
  fcmSenderId: string
  defaultReviewSMS?: string
  googleMapsAPIKey?: string
  stripeKey?: string
  appId?: string
  googleAnalyticId?: string
  membershipAppUrl?: string
  membershipUrl?: string
  cacheInvalidateUrl?: string
  attributionUrl?: string
  emailHomeAppUrl: string
  emailPreviewAppUrl: string
  mediaCenterAppUrl: string
  genericElizaId?: string
  builderPreviewUrl?: string
  companyIdForManagingTemplates: string
  smtpServiceUrl: string
  shortenUrlServiceUrl: string
  emailReportingUrl: string
  agentReportingUrl: string
  bulkReqURL: string
  frontEndCNameRedirectURL: string
  workflowAppURL: string
  workflowServiceURL: string
  newOnboardingFrontend: string
  emailBuilderServiceUrl: string
  hipaaComplianceServiceId: string
  gdprSignDocument: string
  termsOfServiceThrottle: number,
  publicApiUrl: string,
  restApiUrl: string,
  productsApiURL: string,
  requestLoggerApiURL: string,
  paymentsApiURL: string,
  funnelsApiURL: string,
  cloudFunctionsUrl: string,
  phoneCallReportingService: string
  opportunityReportingService: string
  depreciatedFeatureDate: string
  googleAuthId: string
  tipalti: TipaltiConfig
}

const apiUrls = {
  nik: 'https://nik-dot-highlevel-staging.appspot.com',
}

const pythonApiUrls = {
  staging: 'https://python-backend-dot-highlevel-staging.appspot.com',
}

const override = process.env.VUE_APP_DB_OVERRIDE === 'true'
const config: { [key: string]: Config } = {
  development: {
    mode: 'dev',
    baseUrl: 'http://localhost:5000',
    builderPreviewUrl: 'http://localhost:3344',
    pythonBaseUrl: override
      ? 'https://python-backend-dot-highlevel-backend.appspot.com'
      : 'http://localhost:5001',
    dialogflowEngagementsBaseUrl: override
      ? 'https://services.msgsndr.com/dialogflow-engagement'
      : 'http://localhost:5050',
    paymentServiceBaseUrl: 'https://staging.services.msgsndr.com/payment_service',
    saasServiceBaseUrl: override
      ? 'https://services.msgsndr.com/saas_service' // production saas URL
      // : 'https://staging.services.msgsndr.com/saas_service',
      :'http://localhost:5252/saas_service',
    humanRollOverServiceBaseUrl: override
      ? 'https://services.msgsndr.com/eliza-v1'
      : 'http://localhost:5020/eliza-v1',
    funnelServiceBaseUrl: override
      ? 'https://services.msgsndr.com/funnel_backend'
      : 'http://localhost:5030/funnel_backend',
    phoneCallReportingService: override
      ? 'https://services.msgsndr.com/phone_call_reporting'
      : 'http://localhost:5010/phone_call_reporting',
    opportunityReportingService: override
      ? 'https://services.msgsndr.com/opportunity_reporting'
      : 'http://localhost:6010/opportunity_reporting',
    frontEndCNameRedirectURL: 'staging.msgsndr.com',
    cnameRedirectURL: 'staging.msgsndr.com',
    serverIPAddress: '**************',
    cnameWhitelabelURL: 'link.msgsndr.com',
    whitelabelIPAddress: override ? '*************' : '*************',
    companyIdForManagingTemplates: '5DP4iH6HLkQsiKESj6rh',
    fcmSenderId: override ? '439472444885' : '439472444885',
    fcmPublicKey: override
      ? 'BAVB9sZxhteplpxkSQXvCF57NEXzFZbC3aO6sFxQGH5vujYLN12R6HxD52REe_xMotrXqAIeroKrQlAAgJH3hMc'
      : 'BGNTatB_WYHmXHCtRGGgF1TPUXoRjrhLrvOKA6sCPjg-v3C1o9TGTZj6cz_CejVb1zykPWRKn-jXTpUfOqTOIR4',
    tipalti: override
      ? {
        masterKey: '2CbN+Sct9H/cc41OzqfKX64aZL3SrQqijgzwhpTPv/PDyhJ6badnhD0XTodX6QEX',
        baseURL: 'https://ui2.tipalti.com/payeedashboard/'
      }
      : {
        masterKey: 'O+bfhrct8O02s/Qi4kjdtdPRhqgc5jIj5HEqe//r8hF2133w23FzRfcSGY9NPf9J',
        baseURL: 'https://ui2.sandbox.tipalti.com/payeedashboard/'
      },
    firebase: override
      ? {
          apiKey: 'AIzaSyB_w3vXmsI7WeQtrIOkjR6xTRVN5uOieiE',
          authDomain: 'highlevel-backend.firebaseapp.com',
          databaseURL: 'https://highlevel-backend.firebaseio.com',
          projectId: 'highlevel-backend',
          storageBucket: 'highlevel-backend.appspot.com',
          messagingSenderId: '439472444885',
          appId: '1:439472444885:android:c48022009a58ffc7',
        }
      : {
          apiKey: 'AIzaSyAbVVAyzRuw0Mx18DYczCehMsjWFCYX1Lo',
          authDomain: 'highlevel-staging.firebaseapp.com',
          databaseURL: 'https://highlevel-staging.firebaseio.com',
          projectId: 'highlevel-staging',
          storageBucket: 'highlevel-staging.appspot.com',
          messagingSenderId: '85350210461',
          appId: '1:85350210461:web:d3728d34bf8bd6b5',
        },
    googleMapsAPIKey: 'AIzaSyAEWVDkZgkfkJFWQKMPyJWgzXG-XongSRQ',
    defaultReviewSMS:
      'Hi there! Thank you for choosing {{{location.name}}}. Would you be willing to take 30 seconds and leave us a quick review? The link below makes it easy: {{{review_link}}}',
    stripeKey: override
      ? 'pk_live_MtxwO3obi7pfD7UZlGkfR2yj'
      : 'pk_test_WvH5LGch2F7bqyIJ6gVMu3iE',
    smtpServiceUrl: 'https://smpt-service-269314.appspot.com/',
    shortenUrlServiceUrl: override
      ? 'https://url-shorten-dot-highlevel-backend.appspot.com/'
      : 'https://url-shorten-dot-highlevel-staging.appspot.com/',
    membershipUrl: override
      ? 'https://msgsndr.com/membership'
      : 'http://localhost:7000',
    membershipAppUrl: 'http://localhost:4000',
    cacheInvalidateUrl: override
      ? 'https://services.msgsndr.com/cache/clear'
      : 'https://staging.services.msgsndr.com/cache/clear',
    attributionUrl: override
      ? 'https://services.msgsndr.com/attribution_service'
      : 'http://localhost:5020/attribution_service',
    emailReportingUrl: override
      ? 'https://services.msgsndr.com'
      : 'http://localhost:5010',
    agentReportingUrl: override
      ? 'https://services.msgsndr.com'
      : 'http://localhost:5020', //'http://localhost:5020'
    bulkReqURL: 'http://localhost:5080',
    emailHomeAppUrl: 'http://localhost:3003',
    emailPreviewAppUrl: 'http://localhost:3001',
    mediaCenterAppUrl: 'http://localhost:4040',
    genericElizaId: 'events-search-enhv',
    workflowAppURL: 'http://localhost:3002',
    workflowServiceURL: 'http://localhost:5020/workflow',
    emailBuilderServiceUrl: 'http://localhost:5021/email_builder',
    newOnboardingFrontend: 'http://localhost:8081',
    hipaaComplianceServiceId: 'tecJjL67Ol5kwLItrBwe',
    gdprSignDocument:
      'https://eform.pandadoc.com/?eform=5709f455-14d4-49ec-9b71-48c3ef52be50',
    termsOfServiceThrottle: 3,
    publicApiUrl: 'https://public-api-dot-highlevel-staging.uc.r.appspot.com',
    restApiUrl: 'https://staging.services.msgsndr.com',
    productsApiURL: 'https://staging.services.msgsndr.com',
    paymentsApiURL: 'https://staging.services.msgsndr.com',
    requestLoggerApiURL: 'https://staging.services.msgsndr.com',
    funnelsApiURL: 'https://staging.services.msgsndr.com',
    cloudFunctionsUrl:
      'https://us-central1-highlevel-staging.cloudfunctions.net',
    depreciatedFeatureDate: '2021-09-21',
    googleAuthId: "85350210461-eu3b772ek6gqmlpeim86m62ab9i9u5pc.apps.googleusercontent.com"
  },
  staging: {
    mode: 'staging',
    baseUrl:
      process.env.DEV_ENV && apiUrls[process.env.DEV_ENV]
        ? apiUrls[process.env.DEV_ENV]
        : 'https://highlevel-staging.appspot.com',
    builderPreviewUrl:
      'https://funnel-preview-dot-highlevel-staging.appspot.com',
    pythonBaseUrl:
      process.env.DEV_ENV && pythonApiUrls[process.env.DEV_ENV]
        ? pythonApiUrls[process.env.DEV_ENV]
        : 'https://python-backend-dot-highlevel-staging.appspot.com',
    dialogflowEngagementsBaseUrl:
      'https://staging.services.msgsndr.com/dialogflow-engagement',
    paymentServiceBaseUrl:
      'https://staging.services.msgsndr.com/payment_service',
    saasServiceBaseUrl: 'https://staging.services.msgsndr.com/saas_service', // Staging saas URL
    humanRollOverServiceBaseUrl:
      'https://staging.services.msgsndr.com/eliza-v1',
    funnelServiceBaseUrl: 'https://staging.services.msgsndr.com/funnel_backend',
    phoneCallReportingService: 'https://staging.services.msgsndr.com/phone_call_reporting',
    opportunityReportingService: 'https://staging.services.msgsndr.com/opportunity_reporting',
    frontEndCNameRedirectURL: 'staging.msgsndr.com',
    cnameRedirectURL: 'staging.msgsndr.com',
    serverIPAddress: '**************',
    cnameWhitelabelURL: 'link.msgsndr.com',
    whitelabelIPAddress: '*************',
    companyIdForManagingTemplates: '5DP4iH6HLkQsiKESj6rh',
    fcmSenderId: '85350210461',
    fcmPublicKey:
      'BGNTatB_WYHmXHCtRGGgF1TPUXoRjrhLrvOKA6sCPjg-v3C1o9TGTZj6cz_CejVb1zykPWRKn-jXTpUfOqTOIR4',
    tipalti:{
        masterKey: 'O+bfhrct8O02s/Qi4kjdtdPRhqgc5jIj5HEqe//r8hF2133w23FzRfcSGY9NPf9J',
        baseURL: 'https://ui2.sandbox.tipalti.com/payeedashboard/'
    },
    firebase: {
      apiKey: 'AIzaSyAbVVAyzRuw0Mx18DYczCehMsjWFCYX1Lo',
      authDomain: 'highlevel-staging.firebaseapp.com',
      databaseURL: 'https://highlevel-staging.firebaseio.com',
      projectId: 'highlevel-staging',
      storageBucket: 'highlevel-staging.appspot.com',
      messagingSenderId: '85350210461',
      appId: '1:85350210461:web:d3728d34bf8bd6b5',
    },
    googleMapsAPIKey: 'AIzaSyAEWVDkZgkfkJFWQKMPyJWgzXG-XongSRQ',
    defaultReviewSMS:
      'Hi there! Thank you for choosing {{{location.name}}}. Would you be willing to take 30 seconds and leave us a quick review? The link below makes it easy: {{{review_link}}}',
    stripeKey: 'pk_test_WvH5LGch2F7bqyIJ6gVMu3iE',
    smtpServiceUrl: 'https://smpt-service-269314.appspot.com/',
    shortenUrlServiceUrl:
      'https://url-shorten-dot-highlevel-staging.appspot.com/',
    membershipUrl:
      'https://membership-backend-dot-highlevel-staging.appspot.com',
    membershipAppUrl: 'https://staging.backend.memberships.msgsndr.com',
    cacheInvalidateUrl: 'https://staging.services.msgsndr.com/cache/clear',
    attributionUrl: 'https://staging.services.msgsndr.com/attribution_service',
    emailReportingUrl: 'https://staging.services.msgsndr.com',
    agentReportingUrl: 'https://staging.services.msgsndr.com', //'http://localhost:5020'
    bulkReqURL: 'https://bulk-request-dot-highlevel-staging.uc.r.appspot.com',
    emailHomeAppUrl: 'https://hl-email-home.web.app',
    emailPreviewAppUrl: 'https://hl-email-preview.web.app',
    mediaCenterAppUrl: 'https://hl-media-center.firebaseapp.com',
    genericElizaId: 'events-search-enhv',
    workflowAppURL: 'https://hl-workflow.web.app',
    workflowServiceURL: 'https://staging.services.msgsndr.com/workflow',
    emailBuilderServiceUrl:
      'https://staging.services.msgsndr.com/email_builder',
    newOnboardingFrontend: 'https://penitent-circle-mayur.surge.sh',
    hipaaComplianceServiceId: 'tecJjL67Ol5kwLItrBwe',
    gdprSignDocument:
      'https://eform.pandadoc.com/?eform=5709f455-14d4-49ec-9b71-48c3ef52be50',
    termsOfServiceThrottle: 3,
    publicApiUrl: 'https://public-api-dot-highlevel-staging.uc.r.appspot.com',
    restApiUrl: 'https://staging.services.msgsndr.com',
    funnelsApiURL: 'https://staging.services.msgsndr.com',
    requestLoggerApiURL: 'https://staging.services.msgsndr.com',
    productsApiURL: 'https://staging.services.msgsndr.com',
    paymentsApiURL: 'https://staging.services.msgsndr.com',
    cloudFunctionsUrl:
      'https://us-central1-highlevel-staging.cloudfunctions.net',
    depreciatedFeatureDate: '2021-09-21',
    googleAuthId: "85350210461-eu3b772ek6gqmlpeim86m62ab9i9u5pc.apps.googleusercontent.com"
  },
  beta: {
    mode: 'production',
    baseUrl: 'https://beta-dot-highlevel-backend.appspot.com',
    pythonBaseUrl: 'https://python-backend-dot-highlevel-backend.appspot.com',
    dialogflowEngagementsBaseUrl:
      'https://services.msgsndr.com/dialogflow-engagement',
    paymentServiceBaseUrl: 'https://services.msgsndr.com/payment_service',
    saasServiceBaseUrl: 'https://services.msgsndr.com/saas_service', // Production(Beta) saas URL
    humanRollOverServiceBaseUrl: 'https://services.msgsndr.com/eliza-v1',
    funnelServiceBaseUrl: 'https://services.msgsndr.com/funnel_backend',
    phoneCallReportingService: 'https://services.msgsndr.com/phone_call_reporting',
    opportunityReportingService: 'https://services.msgsndr.com/opportunity_reporting',
    frontEndCNameRedirectURL: 'app.msgsndr.com',
    cnameRedirectURL: 'flash.funnels.msgsndr.com',
    cnameWhitelabelURL: 'link.msgsndr.com',
    serverIPAddress: '***********',
    whitelabelIPAddress: '*************',
    companyIdForManagingTemplates: 'pfpm9iFi4k5yvjnstXWU',
    fcmSenderId: '439472444885',
    fcmPublicKey:
      'BAVB9sZxhteplpxkSQXvCF57NEXzFZbC3aO6sFxQGH5vujYLN12R6HxD52REe_xMotrXqAIeroKrQlAAgJH3hMc',
    tipalti:{
        masterKey: '2CbN+Sct9H/cc41OzqfKX64aZL3SrQqijgzwhpTPv/PDyhJ6badnhD0XTodX6QEX',
        baseURL: 'https://ui2.tipalti.com/payeedashboard/'
    },
    firebase: {
      apiKey: 'AIzaSyB_w3vXmsI7WeQtrIOkjR6xTRVN5uOieiE',
      authDomain: 'highlevel-backend.firebaseapp.com',
      databaseURL: 'https://highlevel-backend.firebaseio.com',
      projectId: 'highlevel-backend',
      storageBucket: 'highlevel-backend.appspot.com',
      messagingSenderId: '439472444885',
      appId: '1:439472444885:android:c48022009a58ffc7',
    },
    googleMapsAPIKey: 'AIzaSyAEWVDkZgkfkJFWQKMPyJWgzXG-XongSRQ',
    defaultReviewSMS:
      'Hi there! Thank you for choosing {{{location.name}}}. Would you be willing to take 30 seconds and leave us a quick review? The link below makes it easy: {{{review_link}}}',
    stripeKey: 'pk_live_MtxwO3obi7pfD7UZlGkfR2yj',
    smtpServiceUrl: 'https://smpt-service-269314.appspot.com/',
    shortenUrlServiceUrl:
      'https://url-shorten-dot-highlevel-backend.appspot.com/',
    membershipUrl:
      'https://msgsndr.com/membership',
    membershipAppUrl: 'https://backend.memberships.msgsndr.com',
    cacheInvalidateUrl: 'https://services.msgsndr.com/cache/clear',
    attributionUrl: 'https://services.msgsndr.com/attribution_service',
    emailReportingUrl: 'https://services.msgsndr.com',
    agentReportingUrl: 'https://services.msgsndr.com',
    bulkReqURL: 'https://bulk-request-dot-highlevel-backend.uc.r.appspot.com',
    genericElizaId: 'snehal-new-agent-kqqa',
    emailHomeAppUrl: 'https://email-home-prod.web.app',
    emailPreviewAppUrl: 'https://email-preview-prod.web.app',
    mediaCenterAppUrl: 'https://media-center-prod.firebaseapp.com',
    workflowAppURL: 'https://workflow.msgsndr.com',
    workflowServiceURL: 'https://services.msgsndr.com/workflow',
    newOnboardingFrontend: 'https://ghl-onboarding.web.app',
    emailBuilderServiceUrl: 'https://services.msgsndr.com/email_builder',
    hipaaComplianceServiceId: '1kMapL1iT8LNZhXsv6IC',
    gdprSignDocument:
      'https://eform.pandadoc.com/?eform=5709f455-14d4-49ec-9b71-48c3ef52be50',
    termsOfServiceThrottle: 100,
    publicApiUrl: 'https://rest.gohighlevel.com',
    restApiUrl: 'https://services.msgsndr.com',
    cloudFunctionsUrl: 'https://us-central1-highlevel-backend.cloudfunctions.net',
    depreciatedFeatureDate: '2021-10-21',
    googleAuthId: "439472444885-f4ompgiu3amine1b4vmjfh64obg2is2q.apps.googleusercontent.com"
  },
  production: {
    mode: 'production',
    baseUrl: 'https://msgsndr.com',
    pythonBaseUrl: 'https://python-backend-dot-highlevel-backend.appspot.com',
    dialogflowEngagementsBaseUrl:
      'https://services.msgsndr.com/dialogflow-engagement',
    paymentServiceBaseUrl: 'https://services.msgsndr.com/payment_service',
    saasServiceBaseUrl: 'https://services.msgsndr.com/saas_service', // Production saas URL
    humanRollOverServiceBaseUrl: 'https://services.msgsndr.com/eliza-v1',
    funnelServiceBaseUrl: 'https://services.msgsndr.com/funnel_backend',
    phoneCallReportingService: 'https://services.msgsndr.com/phone_call_reporting',
    opportunityReportingService: 'https://services.msgsndr.com/opportunity_reporting',
    frontEndCNameRedirectURL: 'app.msgsndr.com',
    cnameRedirectURL: 'flash.funnels.msgsndr.com',
    cnameWhitelabelURL: 'link.msgsndr.com',
    serverIPAddress: '***********',
    whitelabelIPAddress: '*************',
    companyIdForManagingTemplates: 'pfpm9iFi4k5yvjnstXWU',
    fcmSenderId: '439472444885',
    fcmPublicKey:
      'BAVB9sZxhteplpxkSQXvCF57NEXzFZbC3aO6sFxQGH5vujYLN12R6HxD52REe_xMotrXqAIeroKrQlAAgJH3hMc',
    tipalti:{
        masterKey: '2CbN+Sct9H/cc41OzqfKX64aZL3SrQqijgzwhpTPv/PDyhJ6badnhD0XTodX6QEX',
        baseURL: 'https://ui2.tipalti.com/payeedashboard/'
    },
    firebase: {
      apiKey: 'AIzaSyB_w3vXmsI7WeQtrIOkjR6xTRVN5uOieiE',
      authDomain: 'highlevel-backend.firebaseapp.com',
      databaseURL: 'https://highlevel-backend.firebaseio.com',
      projectId: 'highlevel-backend',
      storageBucket: 'highlevel-backend.appspot.com',
      messagingSenderId: '439472444885',
      appId: '1:439472444885:android:c48022009a58ffc7',
    },
    googleMapsAPIKey: 'AIzaSyAEWVDkZgkfkJFWQKMPyJWgzXG-XongSRQ',
    defaultReviewSMS:
      'Hi there! Thank you for choosing {{{location.name}}}. Would you be willing to take 30 seconds and leave us a quick review? The link below makes it easy: {{{review_link}}}',
    stripeKey: 'pk_live_MtxwO3obi7pfD7UZlGkfR2yj',
    googleAnalyticId: 'UA-115177999-2',
    smtpServiceUrl: 'https://smpt-service-269314.appspot.com/',
    shortenUrlServiceUrl:
      'https://url-shorten-dot-highlevel-backend.appspot.com/',
    membershipUrl: 'https://msgsndr.com/membership',
    membershipAppUrl: 'https://backend.memberships.msgsndr.com',
    cacheInvalidateUrl: 'https://services.msgsndr.com/cache/clear',
    attributionUrl: 'https://services.msgsndr.com/attribution_service',
    emailReportingUrl: 'https://services.msgsndr.com',
    agentReportingUrl: 'https://services.msgsndr.com',
    bulkReqURL: 'https://msgsndr.com',
    genericElizaId: 'snehal-new-agent-kqqa',
    emailHomeAppUrl: 'https://email-home-prod.web.app',
    emailPreviewAppUrl: 'https://email-preview-prod.web.app',
    mediaCenterAppUrl: 'https://media-center-prod.firebaseapp.com',
    workflowAppURL: 'https://workflow.msgsndr.com',
    workflowServiceURL: 'https://services.msgsndr.com/workflow',
    emailBuilderServiceUrl: 'https://services.msgsndr.com/email_builder',
    newOnboardingFrontend: 'https://onboarding.gohighlevel.com',
    hipaaComplianceServiceId: '1kMapL1iT8LNZhXsv6IC',
    gdprSignDocument:
      'https://eform.pandadoc.com/?eform=5709f455-14d4-49ec-9b71-48c3ef52be50',
    termsOfServiceThrottle: 100,
    publicApiUrl: 'https://rest.gohighlevel.com',
    restApiUrl: 'https://services.msgsndr.com',
    cloudFunctionsUrl: 'https://us-central1-highlevel-backend.cloudfunctions.net',
    depreciatedFeatureDate: '2021-10-21',
    googleAuthId: "439472444885-f4ompgiu3amine1b4vmjfh64obg2is2q.apps.googleusercontent.com"
  },
}

const configurations = config[process.env.NODE_ENV]

if(process.env.NODE_ENV === 'development' && !override){
  switch(process.env.VUE_APP_DEV_TEAM){
    case 'payments':
      configurations.membershipUrl = 'https://membership-backend-dot-highlevel-staging.appspot.com'
      configurations.productsApiURL = 'http://localhost:5053'
      configurations.paymentsApiURL = 'http://localhost:5055'
      configurations.requestLoggerApiURL = 'http://localhost:5067'
      configurations.funnelsApiURL = 'http://localhost:5054'
    break
  }

}

export default configurations
