<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 17.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="60px" height="60px" viewBox="0 0 60 60" enable-background="new 0 0 60 60" xml:space="preserve">
<g>
	<defs>
		<rect id="SVGID_1_" width="60" height="60"/>
	</defs>
	<clipPath id="SVGID_2_">
		<use xlink:href="#SVGID_1_"  overflow="visible"/>
	</clipPath>
	<path clip-path="url(#SVGID_2_)" fill="#FFFFFF" d="M52,21h-1c-3.866,0-7-3.134-7-7c0,3.866-3.134,7-7,7s-7-3.134-7-7
		c0,3.866-3.134,7-7,7s-7-3.134-7-7c0,3.866-3.134,7-7,7H8c-3.866,0-7-3.134-7-7v45h58V14C59,17.866,55.866,21,52,21"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M52,21h-1
		c-3.866,0-7-3.134-7-7c0,3.866-3.134,7-7,7s-7-3.134-7-7c0,3.866-3.134,7-7,7s-7-3.134-7-7c0,3.866-3.134,7-7,7H8
		c-3.866,0-7-3.134-7-7v45h58V14C59,17.866,55.866,21,52,21z"/>
	<path clip-path="url(#SVGID_2_)" fill="#FFFFFF" d="M58.903,11.659l-0.006-0.023C57.277,5.689,52.075,1.473,46,1.043V1h-1.023H42
		H18h-2.976H15v0.001C8.502,1.012,2.812,5.365,1.104,11.637C1.052,11.819,1.189,12,1.378,12H15h1h28h2h12.646
		C58.824,12,58.952,11.83,58.903,11.659"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M58.903,11.659
		l-0.006-0.023C57.277,5.689,52.075,1.473,46,1.043V1h-1.023H42H18h-2.976H15v0.001C8.502,1.012,2.812,5.365,1.104,11.637
		C1.052,11.819,1.189,12,1.378,12H15h1h28h2h12.646C58.824,12,58.952,11.83,58.903,11.659z"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M30,12H16
		c1.158-6.37,6.706-11,13.18-11H30V12z"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M44,12H30V1h0.82
		C37.294,1,42.842,5.63,44,12z"/>
	<path clip-path="url(#SVGID_2_)" fill="#E8FFE8" d="M9,21H8c-3.866,0-7-3.134-7-7v-1.84C1,12.072,1.072,12,1.16,12H16v2
		C16,17.866,12.866,21,9,21"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M9,21H8
		c-3.866,0-7-3.134-7-7v-1.84C1,12.072,1.072,12,1.16,12H16v2C16,17.866,12.866,21,9,21z"/>
	<path clip-path="url(#SVGID_2_)" fill="#E8FFE8" d="M23,21L23,21c-3.866,0-7-3.134-7-7v-2h14v2C30,17.866,26.866,21,23,21"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M23,21L23,21
		c-3.866,0-7-3.134-7-7v-2h14v2C30,17.866,26.866,21,23,21z"/>
	<path clip-path="url(#SVGID_2_)" fill="#E8FFE8" d="M37,21L37,21c-3.866,0-7-3.134-7-7v-2h14v2C44,17.866,40.866,21,37,21"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M37,21L37,21
		c-3.866,0-7-3.134-7-7v-2h14v2C44,17.866,40.866,21,37,21z"/>
	<path clip-path="url(#SVGID_2_)" fill="#E8FFE8" d="M52,21h-1c-3.866,0-7-3.134-7-7v-2h14.84c0.088,0,0.16,0.072,0.16,0.16V14
		C59,17.866,55.866,21,52,21"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M52,21h-1
		c-3.866,0-7-3.134-7-7v-2h14.84c0.088,0,0.16,0.072,0.16,0.16V14C59,17.866,55.866,21,52,21z"/>
	<path clip-path="url(#SVGID_2_)" fill="#FFFFFF" d="M55,59H40V33.5c0-4.142,3.358-7.5,7.5-7.5s7.5,3.358,7.5,7.5V59z"/>
	<path clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" d="M55,59H40V33.5
		c0-4.142,3.358-7.5,7.5-7.5s7.5,3.358,7.5,7.5V59z"/>
	<rect x="5" y="26" clip-path="url(#SVGID_2_)" fill="#E8FFE8" width="31" height="26"/>
	
		<rect x="5" y="26" clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-miterlimit="10" width="31" height="26"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="40" y1="45" x2="55" y2="45"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="15" y1="37" x2="19" y2="33"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="15" y1="42" x2="17" y2="40"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="19" y1="38" x2="19" y2="38"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="21" y1="36" x2="24" y2="33"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="15" y1="47" x2="24" y2="38"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="26" y1="36" x2="26" y2="36"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="7" y1="55" x2="8" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="3" y1="55" x2="4" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="10" y1="58" x2="11" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="2" y1="58" x2="3" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="6" y1="58" x2="7" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="15" y1="55" x2="16" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="11" y1="55" x2="12" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="14" y1="58" x2="15" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="23" y1="55" x2="24" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="19" y1="55" x2="20" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="26" y1="58" x2="27" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="18" y1="58" x2="19" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="22" y1="58" x2="23" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="31" y1="55" x2="32" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="27" y1="55" x2="28" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="30" y1="58" x2="31" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="35" y1="55" x2="36" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="34" y1="58" x2="35" y2="58"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="39" y1="55" x2="40" y2="55"/>
	
		<line clip-path="url(#SVGID_2_)" fill="none" stroke="#37CA37" stroke-width="2" stroke-linecap="round" stroke-miterlimit="10" x1="38" y1="58" x2="39" y2="58"/>
</g>
</svg>
