
tr.tr-loading {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  td {
    height: 50px;
    vertical-align: middle;
    padding: 8px;
    span {
      display: block;
    }
    &.td2-check {
      width: 20px;
      span {
        width: 20px;
        height: 20px;
        background-color: rgba(0, 0, 0, 0.15);
      }
    }
    &.td2-loading-short {
      max-width: 30px;
    }
    &.td2-loading-long {
      max-width: 75px;
    }
    &.td2-loading-long,
    &.td2-loading-short {
      span {
        min-width: 80px;
        height: 20px;
        background: red;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
}
