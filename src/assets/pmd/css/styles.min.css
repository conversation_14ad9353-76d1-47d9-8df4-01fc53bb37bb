/*!
  * ==========================================================================
  * **************************** HIGHLEVEL STYLES ****************************
  * ==========================================================================
  */

/*!
 * Bootstrap v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */

@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700");
:root {
  --blue: #188bf6;
  --indigo: #6610f2;
  --purple: #876cff;
  --pink: #ff3e7f;
  --red: #e93d3d;
  --orange: #ff7402;
  --yellow: #ffbc00;
  --green: #37ca37;
  --teal: #17cfbc;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #188bf6;
  --secondary: #ff7402;
  --success: #37ca37;
  --info: #17cfbc;
  --warning: #ffbc00;
  --danger: #e93d3d;
  --light: #afb8bc;
  --dark: #2a3135;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

*,
*::before,
*::after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

@-ms-viewport {
  width: device-width
}

article,
aside,
dialog,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #607179 !important;
  text-align: left;
  background-color: #f2f7fa
}

[tabindex="-1"]:focus {
  outline: 0 !important
}

hr {
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
  overflow: visible
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: .5rem
}

p {
  margin-top: 0;
  margin-bottom: 1rem
}

abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0
}

dt {
  font-weight: 700
}

dd {
  margin-bottom: .5rem;
  margin-left: 0
}

blockquote {
  margin: 0 0 1rem
}

dfn {
  font-style: italic
}

b,
strong {
  font-weight: bolder
}

small {
  font-size: 80%
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline
}

sub {
  bottom: -.25em
}

sup {
  top: -.5em
}

a {
  color: #188bf6;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects
}

a:hover {
  color: #0871d3;
  text-decoration: underline
}

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none
}

a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none
}

a:not([href]):not([tabindex]):focus {
  outline: 0
}

pre,
code,
kbd,
samp {
  font-family: monospace, monospace;
  font-size: 1em
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar
}

figure {
  margin: 0 0 1rem
}

img {
  vertical-align: middle;
  border-style: none
}

svg:not(:root) {
  overflow: hidden
}

table {
  border-collapse: collapse
}

caption {
  padding-top: .75rem;
  padding-bottom: .75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom
}

th {
  text-align: inherit
}

label {
  display: inline-block;
  margin-bottom: .5rem
}

button {
  border-radius: 0
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit
}

button,
input {
  overflow: visible
}

button,
select {
  text-transform: none
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none
}

input[type="radio"],
input[type="checkbox"] {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox
}

textarea {
  overflow: auto;
  resize: vertical
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal
}

progress {
  vertical-align: baseline
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto
}

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button
}

output {
  display: inline-block
}

summary {
  display: list-item;
  cursor: pointer
}

template {
  display: none
}

[hidden] {
  display: none !important
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  margin-bottom: .5rem;
  font-family: inherit;
  font-weight: 500;
  line-height: 1.2;
  color: inherit
}

h1,
.h1 {
  font-size: 2.5rem
}

h2,
.h2 {
  font-size: 2rem
}

h3,
.h3 {
  font-size: 1.75rem
}

h4,
.h4 {
  font-size: 1.5rem
}

h5,
.h5 {
  font-size: 1.25rem
}

h6,
.h6 {
  font-size: 1rem
}

.lead {
  font-size: 1.25rem;
  font-weight: 300
}

.display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2
}

.display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2
}

.display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2
}

.display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1)
}

small,
.small {
  font-size: 80%;
  font-weight: 400
}

mark,
.mark {
  padding: .2em;
  background-color: #fcf8e3
}

.list-unstyled {
  padding-left: 0;
  list-style: none
}

.list-inline {
  padding-left: 0;
  list-style: none
}

.list-inline-item {
  display: inline-block
}

.list-inline-item:not(:last-child) {
  margin-right: .5rem
}

.initialism {
  font-size: 90%;
  text-transform: uppercase
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem
}

.blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d
}

.blockquote-footer::before {
  content: "\2014 \00A0"
}

code,
kbd,
pre,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace
}

code {
  font-size: 87.5%;
  color: #ff3e7f;
  word-break: break-word
}

a>code {
  color: inherit
}

kbd {
  padding: .2rem .4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: .2rem
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal
}

.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll
}

.container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto
}

@media (min-width: 576px) {
  .container {
    max-width: 540px
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px
  }
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto
}

.row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px
}

.no-gutters {
  margin-right: 0;
  margin-left: 0
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
  padding-right: 0;
  padding-left: 0
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px
}

.col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%
}

.col-auto {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: none
}

.col-1 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 8.33333%;
  flex: 0 0 8.33333%;
  max-width: 8.33333%
}

.col-2 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 16.66667%;
  flex: 0 0 16.66667%;
  max-width: 16.66667%
}

.col-3 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%
}

.col-4 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 33.33333%;
  flex: 0 0 33.33333%;
  max-width: 33.33333%
}

.col-5 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 41.66667%;
  flex: 0 0 41.66667%;
  max-width: 41.66667%
}

.col-6 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%
}

.col-7 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 58.33333%;
  flex: 0 0 58.33333%;
  max-width: 58.33333%
}

.col-8 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 66.66667%;
  flex: 0 0 66.66667%;
  max-width: 66.66667%
}

.col-9 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%
}

.col-10 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 83.33333%;
  flex: 0 0 83.33333%;
  max-width: 83.33333%
}

.col-11 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 91.66667%;
  flex: 0 0 91.66667%;
  max-width: 91.66667%
}

.col-12 {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%
}

.order-first {
  -webkit-box-ordinal-group: 0;
  -ms-flex-order: -1;
  order: -1
}

.order-last {
  -webkit-box-ordinal-group: 14;
  -ms-flex-order: 13;
  order: 13
}

.order-0 {
  -webkit-box-ordinal-group: 1;
  -ms-flex-order: 0;
  order: 0
}

.order-1 {
  -webkit-box-ordinal-group: 2;
  -ms-flex-order: 1;
  order: 1
}

.order-2 {
  -webkit-box-ordinal-group: 3;
  -ms-flex-order: 2;
  order: 2
}

.order-3 {
  -webkit-box-ordinal-group: 4;
  -ms-flex-order: 3;
  order: 3
}

.order-4 {
  -webkit-box-ordinal-group: 5;
  -ms-flex-order: 4;
  order: 4
}

.order-5 {
  -webkit-box-ordinal-group: 6;
  -ms-flex-order: 5;
  order: 5
}

.order-6 {
  -webkit-box-ordinal-group: 7;
  -ms-flex-order: 6;
  order: 6
}

.order-7 {
  -webkit-box-ordinal-group: 8;
  -ms-flex-order: 7;
  order: 7
}

.order-8 {
  -webkit-box-ordinal-group: 9;
  -ms-flex-order: 8;
  order: 8
}

.order-9 {
  -webkit-box-ordinal-group: 10;
  -ms-flex-order: 9;
  order: 9
}

.order-10 {
  -webkit-box-ordinal-group: 11;
  -ms-flex-order: 10;
  order: 10
}

.order-11 {
  -webkit-box-ordinal-group: 12;
  -ms-flex-order: 11;
  order: 11
}

.order-12 {
  -webkit-box-ordinal-group: 13;
  -ms-flex-order: 12;
  order: 12
}

.offset-1 {
  margin-left: 8.33333%
}

.offset-2 {
  margin-left: 16.66667%
}

.offset-3 {
  margin-left: 25%
}

.offset-4 {
  margin-left: 33.33333%
}

.offset-5 {
  margin-left: 41.66667%
}

.offset-6 {
  margin-left: 50%
}

.offset-7 {
  margin-left: 58.33333%
}

.offset-8 {
  margin-left: 66.66667%
}

.offset-9 {
  margin-left: 75%
}

.offset-10 {
  margin-left: 83.33333%
}

.offset-11 {
  margin-left: 91.66667%
}

@media (min-width: 576px) {
  .col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
  }
  .col-sm-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }
  .col-sm-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%
  }
  .col-sm-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%
  }
  .col-sm-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
  }
  .col-sm-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%
  }
  .col-sm-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%
  }
  .col-sm-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
  }
  .col-sm-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%
  }
  .col-sm-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%
  }
  .col-sm-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
  }
  .col-sm-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%
  }
  .col-sm-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%
  }
  .col-sm-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
  }
  .order-sm-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
  }
  .order-sm-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
  }
  .order-sm-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }
  .order-sm-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }
  .order-sm-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }
  .order-sm-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }
  .order-sm-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }
  .order-sm-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }
  .order-sm-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }
  .order-sm-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }
  .order-sm-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }
  .order-sm-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
  }
  .order-sm-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
  }
  .order-sm-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
  }
  .order-sm-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
  }
  .offset-sm-0 {
    margin-left: 0
  }
  .offset-sm-1 {
    margin-left: 8.33333%
  }
  .offset-sm-2 {
    margin-left: 16.66667%
  }
  .offset-sm-3 {
    margin-left: 25%
  }
  .offset-sm-4 {
    margin-left: 33.33333%
  }
  .offset-sm-5 {
    margin-left: 41.66667%
  }
  .offset-sm-6 {
    margin-left: 50%
  }
  .offset-sm-7 {
    margin-left: 58.33333%
  }
  .offset-sm-8 {
    margin-left: 66.66667%
  }
  .offset-sm-9 {
    margin-left: 75%
  }
  .offset-sm-10 {
    margin-left: 83.33333%
  }
  .offset-sm-11 {
    margin-left: 91.66667%
  }
}

@media (min-width: 768px) {
  .col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
  }
  .col-md-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }
  .col-md-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%
  }
  .col-md-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%
  }
  .col-md-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
  }
  .col-md-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%
  }
  .col-md-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%
  }
  .col-md-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
  }
  .col-md-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%
  }
  .col-md-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%
  }
  .col-md-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
  }
  .col-md-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%
  }
  .col-md-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%
  }
  .col-md-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
  }
  .order-md-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
  }
  .order-md-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
  }
  .order-md-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }
  .order-md-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }
  .order-md-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }
  .order-md-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }
  .order-md-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }
  .order-md-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }
  .order-md-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }
  .order-md-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }
  .order-md-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }
  .order-md-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
  }
  .order-md-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
  }
  .order-md-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
  }
  .order-md-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
  }
  .offset-md-0 {
    margin-left: 0
  }
  .offset-md-1 {
    margin-left: 8.33333%
  }
  .offset-md-2 {
    margin-left: 16.66667%
  }
  .offset-md-3 {
    margin-left: 25%
  }
  .offset-md-4 {
    margin-left: 33.33333%
  }
  .offset-md-5 {
    margin-left: 41.66667%
  }
  .offset-md-6 {
    margin-left: 50%
  }
  .offset-md-7 {
    margin-left: 58.33333%
  }
  .offset-md-8 {
    margin-left: 66.66667%
  }
  .offset-md-9 {
    margin-left: 75%
  }
  .offset-md-10 {
    margin-left: 83.33333%
  }
  .offset-md-11 {
    margin-left: 91.66667%
  }
}

@media (min-width: 992px) {
  .col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
  }
  .col-lg-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }
  .col-lg-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%
  }
  .col-lg-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%
  }
  .col-lg-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
  }
  .col-lg-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%
  }
  .col-lg-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%
  }
  .col-lg-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
  }
  .col-lg-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%
  }
  .col-lg-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%
  }
  .col-lg-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
  }
  .col-lg-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%
  }
  .col-lg-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%
  }
  .col-lg-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
  }
  .order-lg-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
  }
  .order-lg-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
  }
  .order-lg-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }
  .order-lg-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }
  .order-lg-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }
  .order-lg-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }
  .order-lg-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }
  .order-lg-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }
  .order-lg-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }
  .order-lg-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }
  .order-lg-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }
  .order-lg-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
  }
  .order-lg-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
  }
  .order-lg-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
  }
  .order-lg-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
  }
  .offset-lg-0 {
    margin-left: 0
  }
  .offset-lg-1 {
    margin-left: 8.33333%
  }
  .offset-lg-2 {
    margin-left: 16.66667%
  }
  .offset-lg-3 {
    margin-left: 25%
  }
  .offset-lg-4 {
    margin-left: 33.33333%
  }
  .offset-lg-5 {
    margin-left: 41.66667%
  }
  .offset-lg-6 {
    margin-left: 50%
  }
  .offset-lg-7 {
    margin-left: 58.33333%
  }
  .offset-lg-8 {
    margin-left: 66.66667%
  }
  .offset-lg-9 {
    margin-left: 75%
  }
  .offset-lg-10 {
    margin-left: 83.33333%
  }
  .offset-lg-11 {
    margin-left: 91.66667%
  }
}

@media (min-width: 1200px) {
  .col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%
  }
  .col-xl-auto {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: none
  }
  .col-xl-1 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 8.33333%;
    flex: 0 0 8.33333%;
    max-width: 8.33333%
  }
  .col-xl-2 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 16.66667%;
    flex: 0 0 16.66667%;
    max-width: 16.66667%
  }
  .col-xl-3 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%
  }
  .col-xl-4 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 33.33333%;
    flex: 0 0 33.33333%;
    max-width: 33.33333%
  }
  .col-xl-5 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 41.66667%;
    flex: 0 0 41.66667%;
    max-width: 41.66667%
  }
  .col-xl-6 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%
  }
  .col-xl-7 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 58.33333%;
    flex: 0 0 58.33333%;
    max-width: 58.33333%
  }
  .col-xl-8 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 66.66667%;
    flex: 0 0 66.66667%;
    max-width: 66.66667%
  }
  .col-xl-9 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%
  }
  .col-xl-10 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 83.33333%;
    flex: 0 0 83.33333%;
    max-width: 83.33333%
  }
  .col-xl-11 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 91.66667%;
    flex: 0 0 91.66667%;
    max-width: 91.66667%
  }
  .col-xl-12 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%
  }
  .order-xl-first {
    -webkit-box-ordinal-group: 0;
    -ms-flex-order: -1;
    order: -1
  }
  .order-xl-last {
    -webkit-box-ordinal-group: 14;
    -ms-flex-order: 13;
    order: 13
  }
  .order-xl-0 {
    -webkit-box-ordinal-group: 1;
    -ms-flex-order: 0;
    order: 0
  }
  .order-xl-1 {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1
  }
  .order-xl-2 {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2
  }
  .order-xl-3 {
    -webkit-box-ordinal-group: 4;
    -ms-flex-order: 3;
    order: 3
  }
  .order-xl-4 {
    -webkit-box-ordinal-group: 5;
    -ms-flex-order: 4;
    order: 4
  }
  .order-xl-5 {
    -webkit-box-ordinal-group: 6;
    -ms-flex-order: 5;
    order: 5
  }
  .order-xl-6 {
    -webkit-box-ordinal-group: 7;
    -ms-flex-order: 6;
    order: 6
  }
  .order-xl-7 {
    -webkit-box-ordinal-group: 8;
    -ms-flex-order: 7;
    order: 7
  }
  .order-xl-8 {
    -webkit-box-ordinal-group: 9;
    -ms-flex-order: 8;
    order: 8
  }
  .order-xl-9 {
    -webkit-box-ordinal-group: 10;
    -ms-flex-order: 9;
    order: 9
  }
  .order-xl-10 {
    -webkit-box-ordinal-group: 11;
    -ms-flex-order: 10;
    order: 10
  }
  .order-xl-11 {
    -webkit-box-ordinal-group: 12;
    -ms-flex-order: 11;
    order: 11
  }
  .order-xl-12 {
    -webkit-box-ordinal-group: 13;
    -ms-flex-order: 12;
    order: 12
  }
  .offset-xl-0 {
    margin-left: 0
  }
  .offset-xl-1 {
    margin-left: 8.33333%
  }
  .offset-xl-2 {
    margin-left: 16.66667%
  }
  .offset-xl-3 {
    margin-left: 25%
  }
  .offset-xl-4 {
    margin-left: 33.33333%
  }
  .offset-xl-5 {
    margin-left: 41.66667%
  }
  .offset-xl-6 {
    margin-left: 50%
  }
  .offset-xl-7 {
    margin-left: 58.33333%
  }
  .offset-xl-8 {
    margin-left: 66.66667%
  }
  .offset-xl-9 {
    margin-left: 75%
  }
  .offset-xl-10 {
    margin-left: 83.33333%
  }
  .offset-xl-11 {
    margin-left: 91.66667%
  }
}

.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  background-color: rgba(0, 0, 0, 0)
}

.table th,
.table td {
  padding: .75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6
}

.table tbody+tbody {
  border-top: 2px solid #dee2e6
}

.table .table {
  background-color: #f2f7fa
}

.table-sm th,
.table-sm td {
  padding: .3rem
}

.table-bordered {
  border: 1px solid #dee2e6
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6
}

.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05)
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075)
}

.table-primary,
.table-primary>th,
.table-primary>td {
  background-color: #bedffc
}

.table-hover .table-primary:hover {
  background-color: #a6d3fb
}

.table-hover .table-primary:hover>td,
.table-hover .table-primary:hover>th {
  background-color: #a6d3fb
}

.table-secondary,
.table-secondary>th,
.table-secondary>td {
  background-color: #ffd8b8
}

.table-hover .table-secondary:hover {
  background-color: #ffca9f
}

.table-hover .table-secondary:hover>td,
.table-hover .table-secondary:hover>th {
  background-color: #ffca9f
}

.table-success,
.table-success>th,
.table-success>td {
  background-color: #c7f0c7
}

.table-hover .table-success:hover {
  background-color: #b3ebb3
}

.table-hover .table-success:hover>td,
.table-hover .table-success:hover>th {
  background-color: #b3ebb3
}

.table-info,
.table-info>th,
.table-info>td {
  background-color: #bef2ec
}

.table-hover .table-info:hover {
  background-color: #a9eee6
}

.table-hover .table-info:hover>td,
.table-hover .table-info:hover>th {
  background-color: #a9eee6
}

.table-warning,
.table-warning>th,
.table-warning>td {
  background-color: #ffecb8
}

.table-hover .table-warning:hover {
  background-color: #ffe59f
}

.table-hover .table-warning:hover>td,
.table-hover .table-warning:hover>th {
  background-color: #ffe59f
}

.table-danger,
.table-danger>th,
.table-danger>td {
  background-color: #f9c9c9
}

.table-hover .table-danger:hover {
  background-color: #f6b2b2
}

.table-hover .table-danger:hover>td,
.table-hover .table-danger:hover>th {
  background-color: #f6b2b2
}

.table-light,
.table-light>th,
.table-light>td {
  background-color: #e9ebec
}

.table-hover .table-light:hover {
  background-color: #dbdfe0
}

.table-hover .table-light:hover>td,
.table-hover .table-light:hover>th {
  background-color: #dbdfe0
}

.table-dark,
.table-dark>th,
.table-dark>td {
  background-color: #c3c5c6
}

.table-hover .table-dark:hover {
  background-color: #b6b8ba
}

.table-hover .table-dark:hover>td,
.table-hover .table-dark:hover>th {
  background-color: #b6b8ba
}

.table-active,
.table-active>th,
.table-active>td {
  background-color: rgba(0, 0, 0, 0.075)
}

.table-hover .table-active:hover {
  background-color: rgba(0, 0, 0, 0.075)
}

.table-hover .table-active:hover>td,
.table-hover .table-active:hover>th {
  background-color: rgba(0, 0, 0, 0.075)
}

.table .thead-dark th {
  color: #f2f7fa;
  background-color: #212529;
  border-color: #32383e
}

.table .thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #dee2e6
}

.table-dark {
  color: #f2f7fa;
  background-color: #212529
}

.table-dark th,
.table-dark td,
.table-dark thead th {
  border-color: #32383e
}

.table-dark.table-bordered {
  border: 0
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05)
}

.table-dark.table-hover tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.075)
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }
  .table-responsive-sm>.table-bordered {
    border: 0
  }
}

@media (max-width: 767.98px) {
  .table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }
  .table-responsive-md>.table-bordered {
    border: 0
  }
}

@media (max-width: 991.98px) {
  .table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }
  .table-responsive-lg>.table-bordered {
    border: 0
  }
}

@media (max-width: 1199.98px) {
  .table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar
  }
  .table-responsive-xl>.table-bordered {
    border: 0
  }
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar
}

.table-responsive>.table-bordered {
  border: 0
}

.form-control {
  display: block;
  width: 100%;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: .3125rem;
  -webkit-transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out
}

.form-control::-ms-expand {
  background-color: transparent;
  border: 0
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #93c9fb;
  outline: 0;
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25)
}

.form-control::-webkit-input-placeholder {
  color: #6c757d;
  opacity: 1
}

.form-control:-ms-input-placeholder {
  color: #6c757d;
  opacity: 1
}

.form-control::-ms-input-placeholder {
  color: #6c757d;
  opacity: 1
}

.form-control::placeholder {
  color: #6c757d;
  opacity: 1
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1
}

select.form-control:not([size]):not([multiple]) {
  height: calc(2.25rem + 2px)
}

select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff
}

.form-control-file,
.form-control-range {
  display: block;
  width: 100%
}

.col-form-label {
  padding-top: calc(.375rem + 1px);
  padding-bottom: calc(.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5
}

.col-form-label-lg {
  padding-top: calc(.5rem + 1px);
  padding-bottom: calc(.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5
}

.col-form-label-sm {
  padding-top: calc(.25rem + 1px);
  padding-bottom: calc(.25rem + 1px);
  font-size: .875rem;
  line-height: 1.5
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: .375rem;
  padding-bottom: .375rem;
  margin-bottom: 0;
  line-height: 1.5;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0
}

.form-control-plaintext.form-control-sm,
.input-group-sm>.form-control-plaintext.form-control,
.input-group-sm>.input-group-prepend>.form-control-plaintext.input-group-text,
.input-group-sm>.input-group-append>.form-control-plaintext.input-group-text,
.input-group-sm>.input-group-prepend>.form-control-plaintext.btn,
.input-group-sm>.input-group-append>.form-control-plaintext.btn,
.form-control-plaintext.form-control-lg,
.input-group-lg>.form-control-plaintext.form-control,
.input-group-lg>.input-group-prepend>.form-control-plaintext.input-group-text,
.input-group-lg>.input-group-append>.form-control-plaintext.input-group-text,
.input-group-lg>.input-group-prepend>.form-control-plaintext.btn,
.input-group-lg>.input-group-append>.form-control-plaintext.btn {
  padding-right: 0;
  padding-left: 0
}

.form-control-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-prepend>.input-group-text,
.input-group-sm>.input-group-append>.input-group-text,
.input-group-sm>.input-group-prepend>.btn,
.input-group-sm>.input-group-append>.btn {
  padding: .25rem .5rem;
  font-size: .875rem;
  line-height: 1.5;
  border-radius: .2rem
}

select.form-control-sm:not([size]):not([multiple]),
.input-group-sm>select.form-control:not([size]):not([multiple]),
.input-group-sm>.input-group-prepend>select.input-group-text:not([size]):not([multiple]),
.input-group-sm>.input-group-append>select.input-group-text:not([size]):not([multiple]),
.input-group-sm>.input-group-prepend>select.btn:not([size]):not([multiple]),
.input-group-sm>.input-group-append>select.btn:not([size]):not([multiple]) {
  height: calc(1.8125rem + 2px)
}

.form-control-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-prepend>.input-group-text,
.input-group-lg>.input-group-append>.input-group-text,
.input-group-lg>.input-group-prepend>.btn,
.input-group-lg>.input-group-append>.btn {
  padding: .5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: .3rem
}

select.form-control-lg:not([size]):not([multiple]),
.input-group-lg>select.form-control:not([size]):not([multiple]),
.input-group-lg>.input-group-prepend>select.input-group-text:not([size]):not([multiple]),
.input-group-lg>.input-group-append>select.input-group-text:not([size]):not([multiple]),
.input-group-lg>.input-group-prepend>select.btn:not([size]):not([multiple]),
.input-group-lg>.input-group-append>select.btn:not([size]):not([multiple]) {
  height: calc(2.875rem + 2px)
}

.form-group {
  margin-bottom: 1rem
}

.form-text {
  display: block;
  margin-top: .25rem
}

.form-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px
}

.form-row>.col,
.form-row>[class*="col-"] {
  padding-right: 5px;
  padding-left: 5px
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem
}

.form-check-input {
  position: absolute;
  margin-top: .3rem;
  margin-left: -1.25rem
}

.form-check-input:disabled~.form-check-label {
  color: #6c757d
}

.form-check-label {
  margin-bottom: 0
}

.form-check-inline {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: .75rem
}

.form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: .3125rem;
  margin-left: 0
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #37ca37
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(55, 202, 55, 0.8);
  border-radius: .2rem
}

.was-validated .form-control:valid,
.form-control.is-valid,
.was-validated .custom-select:valid,
.custom-select.is-valid {
  border-color: #37ca37
}

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus,
.was-validated .custom-select:valid:focus,
.custom-select.is-valid:focus {
  border-color: #37ca37;
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.25);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.25)
}

.was-validated .form-control:valid~.valid-feedback,
.was-validated .form-control:valid~.valid-tooltip,
.form-control.is-valid~.valid-feedback,
.form-control.is-valid~.valid-tooltip,
.was-validated .custom-select:valid~.valid-feedback,
.was-validated .custom-select:valid~.valid-tooltip,
.custom-select.is-valid~.valid-feedback,
.custom-select.is-valid~.valid-tooltip {
  display: block
}

.was-validated .form-check-input:valid~.form-check-label,
.form-check-input.is-valid~.form-check-label {
  color: #37ca37
}

.was-validated .form-check-input:valid~.valid-feedback,
.was-validated .form-check-input:valid~.valid-tooltip,
.form-check-input.is-valid~.valid-feedback,
.form-check-input.is-valid~.valid-tooltip {
  display: block
}

.was-validated .custom-control-input:valid~.custom-control-label,
.custom-control-input.is-valid~.custom-control-label {
  color: #37ca37
}

.was-validated .custom-control-input:valid~.custom-control-label::before,
.custom-control-input.is-valid~.custom-control-label::before {
  background-color: #9ce59c
}

.was-validated .custom-control-input:valid~.valid-feedback,
.was-validated .custom-control-input:valid~.valid-tooltip,
.custom-control-input.is-valid~.valid-feedback,
.custom-control-input.is-valid~.valid-tooltip {
  display: block
}

.was-validated .custom-control-input:valid:checked~.custom-control-label::before,
.custom-control-input.is-valid:checked~.custom-control-label::before {
  background-color: #5fd55f
}

.was-validated .custom-control-input:valid:focus~.custom-control-label::before,
.custom-control-input.is-valid:focus~.custom-control-label::before {
  -webkit-box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(55, 202, 55, 0.25);
  box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(55, 202, 55, 0.25)
}

.was-validated .custom-file-input:valid~.custom-file-label,
.custom-file-input.is-valid~.custom-file-label {
  border-color: #37ca37
}

.was-validated .custom-file-input:valid~.custom-file-label::before,
.custom-file-input.is-valid~.custom-file-label::before {
  border-color: inherit
}

.was-validated .custom-file-input:valid~.valid-feedback,
.was-validated .custom-file-input:valid~.valid-tooltip,
.custom-file-input.is-valid~.valid-feedback,
.custom-file-input.is-valid~.valid-tooltip {
  display: block
}

.was-validated .custom-file-input:valid:focus~.custom-file-label,
.custom-file-input.is-valid:focus~.custom-file-label {
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.25);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.25)
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: .25rem;
  font-size: 80%;
  color: #e93d3d
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(233, 61, 61, 0.8);
  border-radius: .2rem
}

.was-validated .form-control:invalid,
.form-control.is-invalid,
.was-validated .custom-select:invalid,
.custom-select.is-invalid {
  border-color: #e93d3d
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus,
.was-validated .custom-select:invalid:focus,
.custom-select.is-invalid:focus {
  border-color: #e93d3d;
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.25);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.25)
}

.was-validated .form-control:invalid~.invalid-feedback,
.was-validated .form-control:invalid~.invalid-tooltip,
.form-control.is-invalid~.invalid-feedback,
.form-control.is-invalid~.invalid-tooltip,
.was-validated .custom-select:invalid~.invalid-feedback,
.was-validated .custom-select:invalid~.invalid-tooltip,
.custom-select.is-invalid~.invalid-feedback,
.custom-select.is-invalid~.invalid-tooltip {
  display: block
}

.was-validated .form-check-input:invalid~.form-check-label,
.form-check-input.is-invalid~.form-check-label {
  color: #e93d3d
}

.was-validated .form-check-input:invalid~.invalid-feedback,
.was-validated .form-check-input:invalid~.invalid-tooltip,
.form-check-input.is-invalid~.invalid-feedback,
.form-check-input.is-invalid~.invalid-tooltip {
  display: block
}

.was-validated .custom-control-input:invalid~.custom-control-label,
.custom-control-input.is-invalid~.custom-control-label {
  color: #e93d3d
}

.was-validated .custom-control-input:invalid~.custom-control-label::before,
.custom-control-input.is-invalid~.custom-control-label::before {
  background-color: #f6b0b0
}

.was-validated .custom-control-input:invalid~.invalid-feedback,
.was-validated .custom-control-input:invalid~.invalid-tooltip,
.custom-control-input.is-invalid~.invalid-feedback,
.custom-control-input.is-invalid~.invalid-tooltip {
  display: block
}

.was-validated .custom-control-input:invalid:checked~.custom-control-label::before,
.custom-control-input.is-invalid:checked~.custom-control-label::before {
  background-color: #ee6b6b
}

.was-validated .custom-control-input:invalid:focus~.custom-control-label::before,
.custom-control-input.is-invalid:focus~.custom-control-label::before {
  -webkit-box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(233, 61, 61, 0.25);
  box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(233, 61, 61, 0.25)
}

.was-validated .custom-file-input:invalid~.custom-file-label,
.custom-file-input.is-invalid~.custom-file-label {
  border-color: #e93d3d
}

.was-validated .custom-file-input:invalid~.custom-file-label::before,
.custom-file-input.is-invalid~.custom-file-label::before {
  border-color: inherit
}

.was-validated .custom-file-input:invalid~.invalid-feedback,
.was-validated .custom-file-input:invalid~.invalid-tooltip,
.custom-file-input.is-invalid~.invalid-feedback,
.custom-file-input.is-invalid~.invalid-tooltip {
  display: block
}

.was-validated .custom-file-input:invalid:focus~.custom-file-label,
.custom-file-input.is-invalid:focus~.custom-file-label {
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.25);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.25)
}

.form-inline {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.form-inline .form-check {
  width: 100%
}

@media (min-width: 576px) {
  .form-inline label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 0
  }
  .form-inline .form-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle
  }
  .form-inline .form-control-plaintext {
    display: inline-block
  }
  .form-inline .input-group {
    width: auto
  }
  .form-inline .form-check {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: auto;
    padding-left: 0
  }
  .form-inline .form-check-input {
    position: relative;
    margin-top: 0;
    margin-right: .25rem;
    margin-left: 0
  }
  .form-inline .custom-control {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
  }
  .form-inline .custom-control-label {
    margin-bottom: 0
  }
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  padding: .375rem .75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: .3125rem;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;
  -o-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out
}

.btn:hover,
.btn:focus {
  text-decoration: none
}

.btn:focus,
.btn.focus {
  outline: 0;
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25)
}

.btn.disabled,
.btn:disabled {
  opacity: .65
}

.btn:not(:disabled):not(.disabled) {
  cursor: pointer
}

.btn:not(:disabled):not(.disabled):active,
.btn:not(:disabled):not(.disabled).active {
  background-image: none
}

a.btn.disabled,
fieldset:disabled a.btn {
  pointer-events: none
}

.btn-primary {
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.btn-primary:hover {
  color: #fff;
  background-color: #0978df;
  border-color: #0871d3
}

.btn-primary:focus,
.btn-primary.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5)
}

.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show>.btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0871d3;
  border-color: #086bc7
}

.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary:not(:disabled):not(.disabled).active:focus,
.show>.btn-primary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5)
}

.btn-secondary {
  color: #fff;
  background-color: #ff7402;
  border-color: #ff7402
}

.btn-secondary:hover {
  color: #fff;
  background-color: #db6300;
  border-color: #ce5d00
}

.btn-secondary:focus,
.btn-secondary.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5)
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #fff;
  background-color: #ff7402;
  border-color: #ff7402
}

.btn-secondary:not(:disabled):not(.disabled):active,
.btn-secondary:not(:disabled):not(.disabled).active,
.show>.btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #ce5d00;
  border-color: #c15700
}

.btn-secondary:not(:disabled):not(.disabled):active:focus,
.btn-secondary:not(:disabled):not(.disabled).active:focus,
.show>.btn-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5)
}

.btn-success {
  color: #fff;
  background-color: #37ca37;
  border-color: #37ca37
}

.btn-success:hover {
  color: #fff;
  background-color: #2ead2e;
  border-color: #2ba32b
}

.btn-success:focus,
.btn-success.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5)
}

.btn-success.disabled,
.btn-success:disabled {
  color: #fff;
  background-color: #37ca37;
  border-color: #37ca37
}

.btn-success:not(:disabled):not(.disabled):active,
.btn-success:not(:disabled):not(.disabled).active,
.show>.btn-success.dropdown-toggle {
  color: #fff;
  background-color: #2ba32b;
  border-color: #289928
}

.btn-success:not(:disabled):not(.disabled):active:focus,
.btn-success:not(:disabled):not(.disabled).active:focus,
.show>.btn-success.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5)
}

.btn-info {
  color: #fff;
  background-color: #17cfbc;
  border-color: #17cfbc
}

.btn-info:hover {
  color: #fff;
  background-color: #13ad9d;
  border-color: #12a192
}

.btn-info:focus,
.btn-info.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5)
}

.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #17cfbc;
  border-color: #17cfbc
}

.btn-info:not(:disabled):not(.disabled):active,
.btn-info:not(:disabled):not(.disabled).active,
.show>.btn-info.dropdown-toggle {
  color: #fff;
  background-color: #12a192;
  border-color: #119688
}

.btn-info:not(:disabled):not(.disabled):active:focus,
.btn-info:not(:disabled):not(.disabled).active:focus,
.show>.btn-info.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5)
}

.btn-warning {
  color: #212529;
  background-color: #ffbc00;
  border-color: #ffbc00
}

.btn-warning:hover {
  color: #212529;
  background-color: #d9a000;
  border-color: #cc9600
}

.btn-warning:focus,
.btn-warning.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5)
}

.btn-warning.disabled,
.btn-warning:disabled {
  color: #212529;
  background-color: #ffbc00;
  border-color: #ffbc00
}

.btn-warning:not(:disabled):not(.disabled):active,
.btn-warning:not(:disabled):not(.disabled).active,
.show>.btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #cc9600;
  border-color: #bf8d00
}

.btn-warning:not(:disabled):not(.disabled):active:focus,
.btn-warning:not(:disabled):not(.disabled).active:focus,
.show>.btn-warning.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5)
}

.btn-danger {
  color: #fff;
  background-color: #e93d3d;
  border-color: #e93d3d
}

.btn-danger:hover {
  color: #fff;
  background-color: #e51b1b;
  border-color: #da1919
}

.btn-danger:focus,
.btn-danger.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5)
}

.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #e93d3d;
  border-color: #e93d3d
}

.btn-danger:not(:disabled):not(.disabled):active,
.btn-danger:not(:disabled):not(.disabled).active,
.show>.btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #da1919;
  border-color: #cf1717
}

.btn-danger:not(:disabled):not(.disabled):active:focus,
.btn-danger:not(:disabled):not(.disabled).active:focus,
.show>.btn-danger.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5)
}

.btn-light {
  color: #212529;
  background-color: #afb8bc;
  border-color: #afb8bc
}

.btn-light:hover {
  color: #212529;
  background-color: #9aa6ab;
  border-color: #939fa5
}

.btn-light:focus,
.btn-light.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5)
}

.btn-light.disabled,
.btn-light:disabled {
  color: #212529;
  background-color: #afb8bc;
  border-color: #afb8bc
}

.btn-light:not(:disabled):not(.disabled):active,
.btn-light:not(:disabled):not(.disabled).active,
.show>.btn-light.dropdown-toggle {
  color: #212529;
  background-color: #939fa5;
  border-color: #8c999f
}

.btn-light:not(:disabled):not(.disabled):active:focus,
.btn-light:not(:disabled):not(.disabled).active:focus,
.show>.btn-light.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5)
}

.btn-dark {
  color: #fff;
  background-color: #2a3135;
  border-color: #2a3135
}

.btn-dark:hover {
  color: #fff;
  background-color: #191d20;
  border-color: #131719
}

.btn-dark:focus,
.btn-dark.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5);
  box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5)
}

.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #2a3135;
  border-color: #2a3135
}

.btn-dark:not(:disabled):not(.disabled):active,
.btn-dark:not(:disabled):not(.disabled).active,
.show>.btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #131719;
  border-color: #0e1011
}

.btn-dark:not(:disabled):not(.disabled):active:focus,
.btn-dark:not(:disabled):not(.disabled).active:focus,
.show>.btn-dark.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5);
  box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5)
}

.btn-outline-primary {
  color: #188bf6;
  background-color: transparent;
  background-image: none;
  border-color: #188bf6
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.btn-outline-primary:focus,
.btn-outline-primary.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5)
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: #188bf6;
  background-color: transparent
}

.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show>.btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-primary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.5)
}

.btn-outline-secondary {
  color: #ff7402;
  background-color: transparent;
  background-image: none;
  border-color: #ff7402
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #ff7402;
  border-color: #ff7402
}

.btn-outline-secondary:focus,
.btn-outline-secondary.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5)
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: #ff7402;
  background-color: transparent
}

.btn-outline-secondary:not(:disabled):not(.disabled):active,
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show>.btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #ff7402;
  border-color: #ff7402
}

.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-secondary.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 116, 2, 0.5)
}

.btn-outline-success {
  color: #37ca37;
  background-color: transparent;
  background-image: none;
  border-color: #37ca37
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #37ca37;
  border-color: #37ca37
}

.btn-outline-success:focus,
.btn-outline-success.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5)
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #37ca37;
  background-color: transparent
}

.btn-outline-success:not(:disabled):not(.disabled):active,
.btn-outline-success:not(:disabled):not(.disabled).active,
.show>.btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #37ca37;
  border-color: #37ca37
}

.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-success.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5);
  box-shadow: 0 0 0 .2rem rgba(55, 202, 55, 0.5)
}

.btn-outline-info {
  color: #17cfbc;
  background-color: transparent;
  background-image: none;
  border-color: #17cfbc
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17cfbc;
  border-color: #17cfbc
}

.btn-outline-info:focus,
.btn-outline-info.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5)
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #17cfbc;
  background-color: transparent
}

.btn-outline-info:not(:disabled):not(.disabled):active,
.btn-outline-info:not(:disabled):not(.disabled).active,
.show>.btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17cfbc;
  border-color: #17cfbc
}

.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-info.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(23, 207, 188, 0.5)
}

.btn-outline-warning {
  color: #ffbc00;
  background-color: transparent;
  background-image: none;
  border-color: #ffbc00
}

.btn-outline-warning:hover {
  color: #212529;
  background-color: #ffbc00;
  border-color: #ffbc00
}

.btn-outline-warning:focus,
.btn-outline-warning.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5)
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #ffbc00;
  background-color: transparent
}

.btn-outline-warning:not(:disabled):not(.disabled):active,
.btn-outline-warning:not(:disabled):not(.disabled).active,
.show>.btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffbc00;
  border-color: #ffbc00
}

.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-warning.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5);
  box-shadow: 0 0 0 .2rem rgba(255, 188, 0, 0.5)
}

.btn-outline-danger {
  color: #e93d3d;
  background-color: transparent;
  background-image: none;
  border-color: #e93d3d
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #e93d3d;
  border-color: #e93d3d
}

.btn-outline-danger:focus,
.btn-outline-danger.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5)
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #e93d3d;
  background-color: transparent
}

.btn-outline-danger:not(:disabled):not(.disabled):active,
.btn-outline-danger:not(:disabled):not(.disabled).active,
.show>.btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #e93d3d;
  border-color: #e93d3d
}

.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-danger.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5);
  box-shadow: 0 0 0 .2rem rgba(233, 61, 61, 0.5)
}

.btn-outline-light {
  color: #afb8bc;
  background-color: transparent;
  background-image: none;
  border-color: #afb8bc
}

.btn-outline-light:hover {
  color: #212529;
  background-color: #afb8bc;
  border-color: #afb8bc
}

.btn-outline-light:focus,
.btn-outline-light.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5)
}

.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #afb8bc;
  background-color: transparent
}

.btn-outline-light:not(:disabled):not(.disabled):active,
.btn-outline-light:not(:disabled):not(.disabled).active,
.show>.btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #afb8bc;
  border-color: #afb8bc
}

.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-light.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5);
  box-shadow: 0 0 0 .2rem rgba(175, 184, 188, 0.5)
}

.btn-outline-dark {
  color: #2a3135;
  background-color: transparent;
  background-image: none;
  border-color: #2a3135
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #2a3135;
  border-color: #2a3135
}

.btn-outline-dark:focus,
.btn-outline-dark.focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5);
  box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5)
}

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #2a3135;
  background-color: transparent
}

.btn-outline-dark:not(:disabled):not(.disabled):active,
.btn-outline-dark:not(:disabled):not(.disabled).active,
.show>.btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #2a3135;
  border-color: #2a3135
}

.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-dark.dropdown-toggle:focus {
  -webkit-box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5);
  box-shadow: 0 0 0 .2rem rgba(42, 49, 53, 0.5)
}

.btn-link {
  font-weight: 400;
  color: #188bf6;
  background-color: transparent
}

.btn-link:hover {
  color: #0871d3;
  text-decoration: underline;
  background-color: transparent;
  border-color: transparent
}

.btn-link:focus,
.btn-link.focus {
  text-decoration: underline;
  border-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none
}

.btn-link:disabled,
.btn-link.disabled {
  color: #6c757d
}

.btn-lg,
.btn-group-lg>.btn {
  padding: .5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: .3rem
}

.btn-sm,
.btn-group-sm>.btn {
  padding: .25rem .5rem;
  font-size: .875rem;
  line-height: 1.5;
  border-radius: .2rem
}

.btn-block {
  display: block;
  width: 100%
}

.btn-block+.btn-block {
  margin-top: .5rem
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%
}

.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear
}

.fade.show {
  opacity: 1
}

.collapse {
  display: none
}

.collapse.show {
  display: block
}

tr.collapse.show {
  display: table-row
}

tbody.collapse.show {
  display: table-row-group
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition: height 0.35s ease;
  -o-transition: height 0.35s ease;
  transition: height 0.35s ease
}

.dropup,
.dropdown {
  position: relative
}

.dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid;
  border-right: .3em solid transparent;
  border-bottom: 0;
  border-left: .3em solid transparent
}

.dropdown-toggle:empty::after {
  margin-left: 0
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: .5rem 0;
  margin: .125rem 0 0;
  font-size: 1rem;
  color: #607179 !important;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: .3125rem
}

.dropup .dropdown-menu {
  margin-top: 0;
  margin-bottom: .125rem
}

.dropup .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: 0;
  border-right: .3em solid transparent;
  border-bottom: .3em solid;
  border-left: .3em solid transparent
}

.dropup .dropdown-toggle:empty::after {
  margin-left: 0
}

.dropright .dropdown-menu {
  margin-top: 0;
  margin-left: .125rem
}

.dropright .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid transparent;
  border-bottom: .3em solid transparent;
  border-left: .3em solid
}

.dropright .dropdown-toggle:empty::after {
  margin-left: 0
}

.dropright .dropdown-toggle::after {
  vertical-align: 0
}

.dropleft .dropdown-menu {
  margin-top: 0;
  margin-right: .125rem
}

.dropleft .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: .255em;
  vertical-align: .255em;
  content: ""
}

.dropleft .dropdown-toggle::after {
  display: none
}

.dropleft .dropdown-toggle::before {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: .255em;
  vertical-align: .255em;
  content: "";
  border-top: .3em solid transparent;
  border-right: .3em solid;
  border-bottom: .3em solid transparent
}

.dropleft .dropdown-toggle:empty::after {
  margin-left: 0
}

.dropleft .dropdown-toggle::before {
  vertical-align: 0
}

.dropdown-divider {
  height: 0;
  margin: .5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: .25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0
}

.dropdown-item:hover,
.dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa
}

.dropdown-item.active,
.dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #188bf6
}

.dropdown-item.disabled,
.dropdown-item:disabled {
  color: #6c757d;
  background-color: transparent
}

.dropdown-menu.show {
  display: block
}

.dropdown-header {
  display: block;
  padding: .5rem 1.5rem;
  margin-bottom: 0;
  font-size: .875rem;
  color: #6c757d;
  white-space: nowrap
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle
}

.btn-group>.btn,
.btn-group-vertical>.btn {
  position: relative;
  -webkit-box-flex: 0;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto
}

.btn-group>.btn:hover,
.btn-group-vertical>.btn:hover {
  z-index: 1
}

.btn-group>.btn:focus,
.btn-group>.btn:active,
.btn-group>.btn.active,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn.active {
  z-index: 1
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group,
.btn-group-vertical .btn+.btn,
.btn-group-vertical .btn+.btn-group,
.btn-group-vertical .btn-group+.btn,
.btn-group-vertical .btn-group+.btn-group {
  margin-left: -1px
}

.btn-toolbar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start
}

.btn-toolbar .input-group {
  width: auto
}

.btn-group>.btn:first-child {
  margin-left: 0
}

.btn-group>.btn:not(:last-child):not(.dropdown-toggle),
.btn-group>.btn-group:not(:last-child)>.btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.btn-group>.btn:not(:first-child),
.btn-group>.btn-group:not(:first-child)>.btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.dropdown-toggle-split {
  padding-right: .5625rem;
  padding-left: .5625rem
}

.dropdown-toggle-split::after {
  margin-left: 0
}

.btn-sm+.dropdown-toggle-split,
.btn-group-sm>.btn+.dropdown-toggle-split {
  padding-right: .375rem;
  padding-left: .375rem
}

.btn-lg+.dropdown-toggle-split,
.btn-group-lg>.btn+.dropdown-toggle-split {
  padding-right: .75rem;
  padding-left: .75rem
}

.btn-group-vertical {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.btn-group-vertical .btn,
.btn-group-vertical .btn-group {
  width: 100%
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
  margin-top: -1px;
  margin-left: 0
}

.btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical>.btn-group:not(:last-child)>.btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:not(:first-child),
.btn-group-vertical>.btn-group:not(:first-child)>.btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.btn-group-toggle>.btn,
.btn-group-toggle>.btn-group>.btn {
  margin-bottom: 0
}

.btn-group-toggle>.btn input[type="radio"],
.btn-group-toggle>.btn input[type="checkbox"],
.btn-group-toggle>.btn-group>.btn input[type="radio"],
.btn-group-toggle>.btn-group>.btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none
}

.input-group {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%
}

.input-group>.form-control,
.input-group>.custom-select,
.input-group>.custom-file {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0
}

.input-group>.form-control:focus,
.input-group>.custom-select:focus,
.input-group>.custom-file:focus {
  z-index: 3
}

.input-group>.form-control+.form-control,
.input-group>.form-control+.custom-select,
.input-group>.form-control+.custom-file,
.input-group>.custom-select+.form-control,
.input-group>.custom-select+.custom-select,
.input-group>.custom-select+.custom-file,
.input-group>.custom-file+.form-control,
.input-group>.custom-file+.custom-select,
.input-group>.custom-file+.custom-file {
  margin-left: -1px
}

.input-group>.form-control:not(:last-child),
.input-group>.custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.form-control:not(:first-child),
.input-group>.custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.input-group>.custom-file {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.input-group>.custom-file:not(:last-child) .custom-file-label,
.input-group>.custom-file:not(:last-child) .custom-file-label::before {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.custom-file:not(:first-child) .custom-file-label,
.input-group>.custom-file:not(:first-child) .custom-file-label::before {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.input-group-prepend,
.input-group-append {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.input-group-prepend .btn,
.input-group-append .btn {
  position: relative;
  z-index: 2
}

.input-group-prepend .btn+.btn,
.input-group-prepend .btn+.input-group-text,
.input-group-prepend .input-group-text+.input-group-text,
.input-group-prepend .input-group-text+.btn,
.input-group-append .btn+.btn,
.input-group-append .btn+.input-group-text,
.input-group-append .input-group-text+.input-group-text,
.input-group-append .input-group-text+.btn {
  margin-left: -1px
}

.input-group-prepend {
  margin-right: -1px
}

.input-group-append {
  margin-left: -1px
}

.input-group-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: .375rem .75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: .3125rem
}

.input-group-text input[type="radio"],
.input-group-text input[type="checkbox"] {
  margin-top: 0
}

.input-group>.input-group-prepend>.btn,
.input-group>.input-group-prepend>.input-group-text,
.input-group>.input-group-append:not(:last-child)>.btn,
.input-group>.input-group-append:not(:last-child)>.input-group-text,
.input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),
.input-group>.input-group-append:last-child>.input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.input-group>.input-group-append>.btn,
.input-group>.input-group-append>.input-group-text,
.input-group>.input-group-prepend:not(:first-child)>.btn,
.input-group>.input-group-prepend:not(:first-child)>.input-group-text,
.input-group>.input-group-prepend:first-child>.btn:not(:first-child),
.input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem
}

.custom-control-inline {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem
}

.custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0
}

.custom-control-input:checked~.custom-control-label::before {
  color: #fff;
  background-color: #188bf6
}

.custom-control-input:focus~.custom-control-label::before {
  -webkit-box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(24, 139, 246, 0.25);
  box-shadow: 0 0 0 1px #f2f7fa, 0 0 0 .2rem rgba(24, 139, 246, 0.25)
}

.custom-control-input:active~.custom-control-label::before {
  color: #fff;
  background-color: #c4e1fd
}

.custom-control-input:disabled~.custom-control-label {
  color: #6c757d
}

.custom-control-input:disabled~.custom-control-label::before {
  background-color: #e9ecef
}

.custom-control-label {
  margin-bottom: 0
}

.custom-control-label::before {
  position: absolute;
  top: .25rem;
  left: 0;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: #dee2e6
}

.custom-control-label::after {
  position: absolute;
  top: .25rem;
  left: 0;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 50% 50%
}

.custom-checkbox .custom-control-label::before {
  border-radius: .3125rem
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::before {
  background-color: #188bf6
}

.custom-checkbox .custom-control-input:checked~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before {
  background-color: #188bf6
}

.custom-checkbox .custom-control-input:indeterminate~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E")
}

.custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
  background-color: rgba(24, 139, 246, 0.5)
}

.custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label::before {
  background-color: rgba(24, 139, 246, 0.5)
}

.custom-radio .custom-control-label::before {
  border-radius: 50%
}

.custom-radio .custom-control-input:checked~.custom-control-label::before {
  background-color: #188bf6
}

.custom-radio .custom-control-input:checked~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E")
}

.custom-radio .custom-control-input:disabled:checked~.custom-control-label::before {
  background-color: rgba(24, 139, 246, 0.5)
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: .375rem 1.75rem .375rem .75rem;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right .75rem center;
  background-size: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: .3125rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.custom-select:focus {
  border-color: #93c9fb;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 5px rgba(147, 201, 251, 0.5);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 5px rgba(147, 201, 251, 0.5)
}

.custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff
}

.custom-select[multiple],
.custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: .75rem;
  background-image: none
}

.custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef
}

.custom-select::-ms-expand {
  opacity: 0
}

.custom-select-sm {
  height: calc(1.8125rem + 2px);
  padding-top: .375rem;
  padding-bottom: .375rem;
  font-size: 75%
}

.custom-select-lg {
  height: calc(2.875rem + 2px);
  padding-top: .375rem;
  padding-bottom: .375rem;
  font-size: 125%
}

.custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin-bottom: 0
}

.custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin: 0;
  opacity: 0
}

.custom-file-input:focus~.custom-file-control {
  border-color: #93c9fb;
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25)
}

.custom-file-input:focus~.custom-file-control::before {
  border-color: #93c9fb
}

.custom-file-input:lang(en)~.custom-file-label::after {
  content: "Browse"
}

.custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(2.25rem + 2px);
  padding: .375rem .75rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: .3125rem
}

.custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(calc(2.25rem + 2px) - 1px * 2);
  padding: .375rem .75rem;
  line-height: 1.5;
  color: #495057;
  content: "Browse";
  background-color: #e9ecef;
  border-left: 1px solid #ced4da;
  border-radius: 0 .3125rem .3125rem 0
}

.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none
}

.nav-link {
  display: block;
  padding: .5rem 1rem
}

.nav-link:hover,
.nav-link:focus {
  text-decoration: none
}

.nav-link.disabled {
  color: #6c757d
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6
}

.nav-tabs .nav-item {
  margin-bottom: -1px
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: .3125rem;
  border-top-right-radius: .3125rem
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #f2f7fa;
  border-color: #dee2e6 #dee2e6 #f2f7fa
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0
}

.nav-pills .nav-link {
  border-radius: .3125rem
}

.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
  color: #fff;
  background-color: #188bf6
}

.nav-fill .nav-item {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center
}

.nav-justified .nav-item {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center
}

.tab-content>.tab-pane {
  display: none
}

.tab-content>.active {
  display: block
}

.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: .3125rem
}

.card>hr {
  margin-right: 0;
  margin-left: 0
}

.card>.list-group:first-child .list-group-item:first-child {
  border-top-left-radius: .3125rem;
  border-top-right-radius: .3125rem
}

.card>.list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: .3125rem;
  border-bottom-left-radius: .3125rem
}

.card-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem
}

.card-title {
  margin-bottom: .75rem
}

.card-subtitle {
  margin-top: -.375rem;
  margin-bottom: 0
}

.card-text:last-child {
  margin-bottom: 0
}

.card-link:hover {
  text-decoration: none
}

.card-link+.card-link {
  margin-left: 1.25rem
}

.card-header {
  padding: .75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125)
}

.card-header:first-child {
  border-radius: calc(.3125rem - 1px) calc(.3125rem - 1px) 0 0
}

.card-header+.list-group .list-group-item:first-child {
  border-top: 0
}

.card-footer {
  padding: .75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125)
}

.card-footer:last-child {
  border-radius: 0 0 calc(.3125rem - 1px) calc(.3125rem - 1px)
}

.card-header-tabs {
  margin-right: -.625rem;
  margin-bottom: -.75rem;
  margin-left: -.625rem;
  border-bottom: 0
}

.card-header-pills {
  margin-right: -.625rem;
  margin-left: -.625rem
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem
}

.card-img {
  width: 100%;
  border-radius: calc(.3125rem - 1px)
}

.card-img-top {
  width: 100%;
  border-top-left-radius: calc(.3125rem - 1px);
  border-top-right-radius: calc(.3125rem - 1px)
}

.card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(.3125rem - 1px);
  border-bottom-left-radius: calc(.3125rem - 1px)
}

.card-deck {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.card-deck .card {
  margin-bottom: 15px
}

@media (min-width: 576px) {
  .card-deck {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px
  }
  .card-deck .card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px
  }
}

.card-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.card-group>.card {
  margin-bottom: 15px
}

@media (min-width: 992px) {
  .card-group {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
  }
  .card-group>.card {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0
  }
  .card-group>.card+.card {
    margin-left: 0;
    border-left: 0
  }
  .card-group>.card:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0
  }
  .card-group>.card:first-child .card-img-top,
  .card-group>.card:first-child .card-header {
    border-top-right-radius: 0
  }
  .card-group>.card:first-child .card-img-bottom,
  .card-group>.card:first-child .card-footer {
    border-bottom-right-radius: 0
  }
  .card-group>.card:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0
  }
  .card-group>.card:last-child .card-img-top,
  .card-group>.card:last-child .card-header {
    border-top-left-radius: 0
  }
  .card-group>.card:last-child .card-img-bottom,
  .card-group>.card:last-child .card-footer {
    border-bottom-left-radius: 0
  }
  .card-group>.card:only-child {
    border-radius: .3125rem
  }
  .card-group>.card:only-child .card-img-top,
  .card-group>.card:only-child .card-header {
    border-top-left-radius: .3125rem;
    border-top-right-radius: .3125rem
  }
  .card-group>.card:only-child .card-img-bottom,
  .card-group>.card:only-child .card-footer {
    border-bottom-right-radius: .3125rem;
    border-bottom-left-radius: .3125rem
  }
  .card-group>.card:not(:first-child):not(:last-child):not(:only-child) {
    border-radius: 0
  }
  .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-top,
  .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom,
  .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-header,
  .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-footer {
    border-radius: 0
  }
}

.card-columns .card {
  margin-bottom: .75rem
}

@media (min-width: 576px) {
  .card-columns {
    -webkit-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    column-gap: 1.25rem
  }
  .card-columns .card {
    display: inline-block;
    width: 100%
  }
}

.pagination {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: .3125rem
}

.page-link {
  position: relative;
  display: block;
  padding: .5rem .75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #188bf6;
  background-color: #fff;
  border: 1px solid #dee2e6
}

.page-link:hover {
  color: #0871d3;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6
}

.page-link:focus {
  z-index: 2;
  outline: 0;
  -webkit-box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25);
  box-shadow: 0 0 0 .2rem rgba(24, 139, 246, 0.25)
}

.page-link:not(:disabled):not(.disabled) {
  cursor: pointer
}

.page-item:first-child .page-link {
  margin-left: 0;
  border-top-left-radius: .3125rem;
  border-bottom-left-radius: .3125rem
}

.page-item:last-child .page-link {
  border-top-right-radius: .3125rem;
  border-bottom-right-radius: .3125rem
}

.page-item.active .page-link {
  z-index: 1;
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6
}

.pagination-lg .page-link {
  padding: .75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5
}

.pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: .3rem;
  border-bottom-left-radius: .3rem
}

.pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: .3rem;
  border-bottom-right-radius: .3rem
}

.pagination-sm .page-link {
  padding: .25rem .5rem;
  font-size: .875rem;
  line-height: 1.5
}

.pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: .2rem;
  border-bottom-left-radius: .2rem
}

.pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: .2rem;
  border-bottom-right-radius: .2rem
}

.badge {
  display: inline-block;
  padding: .25em .4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .3125rem
}

.badge:empty {
  display: none
}

.btn .badge {
  position: relative;
  top: -1px
}

.badge-pill {
  padding-right: .6em;
  padding-left: .6em;
  border-radius: 10rem
}

.badge-primary {
  color: #fff;
  background-color: #188bf6
}

.badge-primary[href]:hover,
.badge-primary[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #0871d3
}

.badge-secondary {
  color: #fff;
  background-color: #ff7402
}

.badge-secondary[href]:hover,
.badge-secondary[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #ce5d00
}

.badge-success {
  color: #fff;
  background-color: #37ca37
}

.badge-success[href]:hover,
.badge-success[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #2ba32b
}

.badge-info {
  color: #fff;
  background-color: #17cfbc
}

.badge-info[href]:hover,
.badge-info[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #12a192
}

.badge-warning {
  color: #212529;
  background-color: #ffbc00
}

.badge-warning[href]:hover,
.badge-warning[href]:focus {
  color: #212529;
  text-decoration: none;
  background-color: #cc9600
}

.badge-danger {
  color: #fff;
  background-color: #e93d3d
}

.badge-danger[href]:hover,
.badge-danger[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #da1919
}

.badge-light {
  color: #212529;
  background-color: #afb8bc
}

.badge-light[href]:hover,
.badge-light[href]:focus {
  color: #212529;
  text-decoration: none;
  background-color: #939fa5
}

.badge-dark {
  color: #fff;
  background-color: #2a3135
}

.badge-dark[href]:hover,
.badge-dark[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #131719
}

.alert {
  position: relative;
  padding: .75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: .3125rem
}

.alert-heading {
  color: inherit
}

.alert-link {
  font-weight: 700
}

.alert-dismissible {
  padding-right: 4rem
}

.alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: .75rem 1.25rem;
  color: inherit
}

.alert-primary {
  color: #0c4880;
  background-color: #d1e8fd;
  border-color: #bedffc
}

.alert-primary hr {
  border-top-color: #a6d3fb
}

.alert-primary .alert-link {
  color: #082e51
}

.alert-secondary {
  color: #853c01;
  background-color: #ffe3cc;
  border-color: #ffd8b8
}

.alert-secondary hr {
  border-top-color: #ffca9f
}

.alert-secondary .alert-link {
  color: #522501
}

.alert-success {
  color: #1d691d;
  background-color: #d7f4d7;
  border-color: #c7f0c7
}

.alert-success hr {
  border-top-color: #b3ebb3
}

.alert-success .alert-link {
  color: #124112
}

.alert-info {
  color: #0c6c62;
  background-color: #d1f5f2;
  border-color: #bef2ec
}

.alert-info hr {
  border-top-color: #a9eee6
}

.alert-info .alert-link {
  color: #073e38
}

.alert-warning {
  color: #856200;
  background-color: #fff2cc;
  border-color: #ffecb8
}

.alert-warning hr {
  border-top-color: #ffe59f
}

.alert-warning .alert-link {
  color: #523c00
}

.alert-danger {
  color: #792020;
  background-color: #fbd8d8;
  border-color: #f9c9c9
}

.alert-danger hr {
  border-top-color: #f6b2b2
}

.alert-danger .alert-link {
  color: #511515
}

.alert-light {
  color: #5b6062;
  background-color: #eff1f2;
  border-color: #e9ebec
}

.alert-light hr {
  border-top-color: #dbdfe0
}

.alert-light .alert-link {
  color: #424648
}

.alert-dark {
  color: #16191c;
  background-color: #d4d6d7;
  border-color: #c3c5c6
}

.alert-dark hr {
  border-top-color: #b6b8ba
}

.alert-dark .alert-link {
  color: #000
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0
  }
  to {
    background-position: 0 0
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0
  }
  to {
    background-position: 0 0
  }
}

.progress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: .75rem;
  background-color: #e9ecef;
  border-radius: .3125rem
}

.progress-bar {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: #fff;
  text-align: center;
  background-color: #188bf6;
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease
}

.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem
}

.progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite
}

.close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .5
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  opacity: .75
}

.close:not(:disabled):not(.disabled) {
  cursor: pointer
}

button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none
}

.modal-open {
  overflow: hidden
}

.modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: .5rem;
  pointer-events: none
}

.modal.fade .modal-dialog {
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  -o-transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  transform: translate(0, -25%)
}

.modal.show .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0)
}

.modal-dialog-centered {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - (.5rem * 2))
}

.modal-content {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: .3rem;
  outline: 0
}

.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000
}

.modal-backdrop.fade {
  opacity: 0
}

.modal-backdrop.show {
  opacity: .5
}

.modal-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  border-top-left-radius: .3rem;
  border-top-right-radius: .3rem
}

.modal-header .close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5
}

.modal-body {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem
}

.modal-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #e9ecef
}

.modal-footer>:not(:first-child) {
  margin-left: .25rem
}

.modal-footer>:not(:last-child) {
  margin-right: .25rem
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto
  }
  .modal-dialog-centered {
    min-height: calc(100% - (1.75rem * 2))
  }
  .modal-sm {
    max-width: 300px
  }
}

@media (min-width: 992px) {
  .modal-lg {
    max-width: 800px
  }
}

.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: .875rem;
  word-wrap: break-word;
  opacity: 0
}

.tooltip.show {
  opacity: .9
}

.tooltip .arrow {
  position: absolute;
  display: block;
  width: .8rem;
  height: .4rem
}

.tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid
}

.bs-tooltip-top,
.bs-tooltip-auto[x-placement^="top"] {
  padding: .4rem 0
}

.bs-tooltip-top .arrow,
.bs-tooltip-auto[x-placement^="top"] .arrow {
  bottom: 0
}

.bs-tooltip-top .arrow::before,
.bs-tooltip-auto[x-placement^="top"] .arrow::before {
  top: 0;
  border-width: .4rem .4rem 0;
  border-top-color: #000
}

.bs-tooltip-right,
.bs-tooltip-auto[x-placement^="right"] {
  padding: 0 .4rem
}

.bs-tooltip-right .arrow,
.bs-tooltip-auto[x-placement^="right"] .arrow {
  left: 0;
  width: .4rem;
  height: .8rem
}

.bs-tooltip-right .arrow::before,
.bs-tooltip-auto[x-placement^="right"] .arrow::before {
  right: 0;
  border-width: .4rem .4rem .4rem 0;
  border-right-color: #000
}

.bs-tooltip-bottom,
.bs-tooltip-auto[x-placement^="bottom"] {
  padding: .4rem 0
}

.bs-tooltip-bottom .arrow,
.bs-tooltip-auto[x-placement^="bottom"] .arrow {
  top: 0
}

.bs-tooltip-bottom .arrow::before,
.bs-tooltip-auto[x-placement^="bottom"] .arrow::before {
  bottom: 0;
  border-width: 0 .4rem .4rem;
  border-bottom-color: #000
}

.bs-tooltip-left,
.bs-tooltip-auto[x-placement^="left"] {
  padding: 0 .4rem
}

.bs-tooltip-left .arrow,
.bs-tooltip-auto[x-placement^="left"] .arrow {
  right: 0;
  width: .4rem;
  height: .8rem
}

.bs-tooltip-left .arrow::before,
.bs-tooltip-auto[x-placement^="left"] .arrow::before {
  left: 0;
  border-width: .4rem 0 .4rem .4rem;
  border-left-color: #000
}

.tooltip-inner {
  max-width: 200px;
  padding: .25rem .5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: .3125rem
}

.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: .875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: .3rem
}

.popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: .5rem;
  margin: 0 .3rem
}

.popover .arrow::before,
.popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid
}

.bs-popover-top,
.bs-popover-auto[x-placement^="top"] {
  margin-bottom: .5rem
}

.bs-popover-top .arrow,
.bs-popover-auto[x-placement^="top"] .arrow {
  bottom: calc((.5rem + 1px) * -1)
}

.bs-popover-top .arrow::before,
.bs-popover-auto[x-placement^="top"] .arrow::before,
.bs-popover-top .arrow::after,
.bs-popover-auto[x-placement^="top"] .arrow::after {
  border-width: .5rem .5rem 0
}

.bs-popover-top .arrow::before,
.bs-popover-auto[x-placement^="top"] .arrow::before {
  bottom: 0;
  border-top-color: rgba(0, 0, 0, 0.25)
}

.bs-popover-top .arrow::after,
.bs-popover-auto[x-placement^="top"] .arrow::after {
  bottom: 1px;
  border-top-color: #fff
}

.bs-popover-right,
.bs-popover-auto[x-placement^="right"] {
  margin-left: .5rem
}

.bs-popover-right .arrow,
.bs-popover-auto[x-placement^="right"] .arrow {
  left: calc((.5rem + 1px) * -1);
  width: .5rem;
  height: 1rem;
  margin: .3rem 0
}

.bs-popover-right .arrow::before,
.bs-popover-auto[x-placement^="right"] .arrow::before,
.bs-popover-right .arrow::after,
.bs-popover-auto[x-placement^="right"] .arrow::after {
  border-width: .5rem .5rem .5rem 0
}

.bs-popover-right .arrow::before,
.bs-popover-auto[x-placement^="right"] .arrow::before {
  left: 0;
  border-right-color: rgba(0, 0, 0, 0.25)
}

.bs-popover-right .arrow::after,
.bs-popover-auto[x-placement^="right"] .arrow::after {
  left: 1px;
  border-right-color: #fff
}

.bs-popover-bottom,
.bs-popover-auto[x-placement^="bottom"] {
  margin-top: .5rem
}

.bs-popover-bottom .arrow,
.bs-popover-auto[x-placement^="bottom"] .arrow {
  top: calc((.5rem + 1px) * -1)
}

.bs-popover-bottom .arrow::before,
.bs-popover-auto[x-placement^="bottom"] .arrow::before,
.bs-popover-bottom .arrow::after,
.bs-popover-auto[x-placement^="bottom"] .arrow::after {
  border-width: 0 .5rem .5rem .5rem
}

.bs-popover-bottom .arrow::before,
.bs-popover-auto[x-placement^="bottom"] .arrow::before {
  top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.25)
}

.bs-popover-bottom .arrow::after,
.bs-popover-auto[x-placement^="bottom"] .arrow::after {
  top: 1px;
  border-bottom-color: #fff
}

.bs-popover-bottom .popover-header::before,
.bs-popover-auto[x-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7
}

.bs-popover-left,
.bs-popover-auto[x-placement^="left"] {
  margin-right: .5rem
}

.bs-popover-left .arrow,
.bs-popover-auto[x-placement^="left"] .arrow {
  right: calc((.5rem + 1px) * -1);
  width: .5rem;
  height: 1rem;
  margin: .3rem 0
}

.bs-popover-left .arrow::before,
.bs-popover-auto[x-placement^="left"] .arrow::before,
.bs-popover-left .arrow::after,
.bs-popover-auto[x-placement^="left"] .arrow::after {
  border-width: .5rem 0 .5rem .5rem
}

.bs-popover-left .arrow::before,
.bs-popover-auto[x-placement^="left"] .arrow::before {
  right: 0;
  border-left-color: rgba(0, 0, 0, 0.25)
}

.bs-popover-left .arrow::after,
.bs-popover-auto[x-placement^="left"] .arrow::after {
  right: 1px;
  border-left-color: #fff
}

.popover-header {
  padding: .5rem .75rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: inherit;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(.3rem - 1px);
  border-top-right-radius: calc(.3rem - 1px)
}

.popover-header:empty {
  display: none
}

.popover-body {
  padding: .5rem .75rem;
  color: #607179 !important
}

.align-baseline {
  vertical-align: baseline !important
}

.align-top {
  vertical-align: top !important
}

.align-middle {
  vertical-align: middle !important
}

.align-bottom {
  vertical-align: bottom !important
}

.align-text-bottom {
  vertical-align: text-bottom !important
}

.align-text-top {
  vertical-align: text-top !important
}

.bg-primary {
  background-color: #188bf6 !important
}

a.bg-primary:hover,
a.bg-primary:focus,
button.bg-primary:hover,
button.bg-primary:focus {
  background-color: #0871d3 !important
}

.bg-secondary {
  background-color: #ff7402 !important
}

a.bg-secondary:hover,
a.bg-secondary:focus,
button.bg-secondary:hover,
button.bg-secondary:focus {
  background-color: #ce5d00 !important
}

.bg-success {
  background-color: #37ca37 !important
}

a.bg-success:hover,
a.bg-success:focus,
button.bg-success:hover,
button.bg-success:focus {
  background-color: #2ba32b !important
}

.bg-info {
  background-color: #17cfbc !important
}

a.bg-info:hover,
a.bg-info:focus,
button.bg-info:hover,
button.bg-info:focus {
  background-color: #12a192 !important
}

.bg-warning {
  background-color: #ffbc00 !important
}

a.bg-warning:hover,
a.bg-warning:focus,
button.bg-warning:hover,
button.bg-warning:focus {
  background-color: #cc9600 !important
}

.bg-danger {
  background-color: #e93d3d !important
}

a.bg-danger:hover,
a.bg-danger:focus,
button.bg-danger:hover,
button.bg-danger:focus {
  background-color: #da1919 !important
}

.bg-light {
  background-color: #afb8bc !important
}

a.bg-light:hover,
a.bg-light:focus,
button.bg-light:hover,
button.bg-light:focus {
  background-color: #939fa5 !important
}

.bg-dark {
  background-color: #2a3135 !important
}

a.bg-dark:hover,
a.bg-dark:focus,
button.bg-dark:hover,
button.bg-dark:focus {
  background-color: #131719 !important
}

.bg-white {
  background-color: #fff !important
}

.bg-transparent {
  background-color: transparent !important
}



.border-top {
  border-top: 1px solid #dee2e6 !important
}

.border-right {
  border-right: 1px solid #dee2e6 !important
}

.border-bottom {
  border-bottom: 1px solid #dee2e6 !important
}

.border-left {
  border-left: 1px solid #dee2e6 !important
}

.border-0 {
  border: 0 !important
}

.border-top-0 {
  border-top: 0 !important
}

.border-right-0 {
  border-right: 0 !important
}

.border-bottom-0 {
  border-bottom: 0 !important
}

.border-left-0 {
  border-left: 0 !important
}

.border-primary {
  border-color: #188bf6 !important
}

.border-secondary {
  border-color: #ff7402 !important
}

.border-success {
  border-color: #37ca37 !important
}

.border-info {
  border-color: #17cfbc !important
}

.border-warning {
  border-color: #ffbc00 !important
}

.border-danger {
  border-color: #e93d3d !important
}

.border-light {
  border-color: #afb8bc !important
}

.border-dark {
  border-color: #2a3135 !important
}

.border-white {
  border-color: #fff !important
}

.rounded {
  border-radius: .3125rem !important
}

.rounded-top {
  border-top-left-radius: .3125rem !important;
  border-top-right-radius: .3125rem !important
}

.rounded-right {
  border-top-right-radius: .3125rem !important;
  border-bottom-right-radius: .3125rem !important
}

.rounded-bottom {
  border-bottom-right-radius: .3125rem !important;
  border-bottom-left-radius: .3125rem !important
}

.rounded-left {
  border-top-left-radius: .3125rem !important;
  border-bottom-left-radius: .3125rem !important
}

.rounded-circle {
  border-radius: 50% !important
}

.rounded-0 {
  border-radius: 0 !important
}

.clearfix::after {
  display: block;
  clear: both;
  content: ""
}

.d-none {
  display: none !important
}

.d-inline {
  display: inline !important
}

.d-inline-block {
  display: inline-block !important
}

.d-block {
  display: block !important
}

.d-table {
  display: table !important
}

.d-table-row {
  display: table-row !important
}

.d-table-cell {
  display: table-cell !important
}

.d-flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important
}

.d-inline-flex {
  display: -webkit-inline-box !important;
  display: -ms-inline-flexbox !important;
  display: inline-flex !important
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important
  }
  .d-sm-inline {
    display: inline !important
  }
  .d-sm-inline-block {
    display: inline-block !important
  }
  .d-sm-block {
    display: block !important
  }
  .d-sm-table {
    display: table !important
  }
  .d-sm-table-row {
    display: table-row !important
  }
  .d-sm-table-cell {
    display: table-cell !important
  }
  .d-sm-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
  }
  .d-sm-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important
  }
  .d-md-inline {
    display: inline !important
  }
  .d-md-inline-block {
    display: inline-block !important
  }
  .d-md-block {
    display: block !important
  }
  .d-md-table {
    display: table !important
  }
  .d-md-table-row {
    display: table-row !important
  }
  .d-md-table-cell {
    display: table-cell !important
  }
  .d-md-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
  }
  .d-md-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
  }
}

@media (min-width: 992px) {
  .d-lg-none {
    display: none !important
  }
  .d-lg-inline {
    display: inline !important
  }
  .d-lg-inline-block {
    display: inline-block !important
  }
  .d-lg-block {
    display: block !important
  }
  .d-lg-table {
    display: table !important
  }
  .d-lg-table-row {
    display: table-row !important
  }
  .d-lg-table-cell {
    display: table-cell !important
  }
  .d-lg-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
  }
  .d-lg-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
  }
}

@media (min-width: 1200px) {
  .d-xl-none {
    display: none !important
  }
  .d-xl-inline {
    display: inline !important
  }
  .d-xl-inline-block {
    display: inline-block !important
  }
  .d-xl-block {
    display: block !important
  }
  .d-xl-table {
    display: table !important
  }
  .d-xl-table-row {
    display: table-row !important
  }
  .d-xl-table-cell {
    display: table-cell !important
  }
  .d-xl-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
  }
  .d-xl-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
  }
}

@media print {
  .d-print-none {
    display: none !important
  }
  .d-print-inline {
    display: inline !important
  }
  .d-print-inline-block {
    display: inline-block !important
  }
  .d-print-block {
    display: block !important
  }
  .d-print-table {
    display: table !important
  }
  .d-print-table-row {
    display: table-row !important
  }
  .d-print-table-cell {
    display: table-cell !important
  }
  .d-print-flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important
  }
  .d-print-inline-flex {
    display: -webkit-inline-box !important;
    display: -ms-inline-flexbox !important;
    display: inline-flex !important
  }
}

.embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden
}

.embed-responsive::before {
  display: block;
  content: ""
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0
}

.embed-responsive-21by9::before {
  padding-top: 42.85714%
}

.embed-responsive-16by9::before {
  padding-top: 56.25%
}

.embed-responsive-4by3::before {
  padding-top: 75%
}

.embed-responsive-1by1::before {
  padding-top: 100%
}

.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
  -ms-flex-direction: row !important;
  flex-direction: row !important
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -ms-flex-direction: column !important;
  flex-direction: column !important
}

.flex-row-reverse {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: reverse !important;
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important
}

.flex-column-reverse {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: reverse !important;
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important
}

.justify-content-start {
  -webkit-box-pack: start !important;
  -ms-flex-pack: start !important;
  justify-content: flex-start !important
}

.justify-content-end {
  -webkit-box-pack: end !important;
  -ms-flex-pack: end !important;
  justify-content: flex-end !important
}

.justify-content-center {
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important
}

.justify-content-between {
  -webkit-box-pack: justify !important;
  -ms-flex-pack: justify !important;
  justify-content: space-between !important
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important
}

.align-items-start {
  -webkit-box-align: start !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important
}

.align-items-end {
  -webkit-box-align: end !important;
  -ms-flex-align: end !important;
  align-items: flex-end !important
}

.align-items-center {
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important
}

.align-items-baseline {
  -webkit-box-align: baseline !important;
  -ms-flex-align: baseline !important;
  align-items: baseline !important
}

.align-items-stretch {
  -webkit-box-align: stretch !important;
  -ms-flex-align: stretch !important;
  align-items: stretch !important
}

.align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important
}

.align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important
}

.align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important
}

.align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important
}

.align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important
}

.align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important
}

@media (min-width: 576px) {
  .flex-sm-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
  }
  .flex-sm-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
  }
  .flex-sm-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
  }
  .flex-sm-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
  }
  .flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
  }
  .flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
  }
  .flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
  }
  .justify-content-sm-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
  }
  .justify-content-sm-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
  }
  .justify-content-sm-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
  }
  .justify-content-sm-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
  }
  .justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
  }
  .align-items-sm-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
  }
  .align-items-sm-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
  }
  .align-items-sm-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
  }
  .align-items-sm-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
  }
  .align-items-sm-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
  }
  .align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
  }
  .align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
  }
  .align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
  }
  .align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
  }
  .align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
  }
  .align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
  }
  .align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
  }
  .align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
  }
  .align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
  }
  .align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
  }
  .align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
  }
  .align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
  }
}

@media (min-width: 768px) {
  .flex-md-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
  }
  .flex-md-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
  }
  .flex-md-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
  }
  .flex-md-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
  }
  .flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
  }
  .flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
  }
  .flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
  }
  .justify-content-md-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
  }
  .justify-content-md-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
  }
  .justify-content-md-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
  }
  .justify-content-md-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
  }
  .justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
  }
  .align-items-md-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
  }
  .align-items-md-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
  }
  .align-items-md-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
  }
  .align-items-md-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
  }
  .align-items-md-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
  }
  .align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
  }
  .align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
  }
  .align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
  }
  .align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
  }
  .align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
  }
  .align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
  }
  .align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
  }
  .align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
  }
  .align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
  }
  .align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
  }
  .align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
  }
  .align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
  }
}

@media (min-width: 992px) {
  .flex-lg-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
  }
  .flex-lg-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
  }
  .flex-lg-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
  }
  .flex-lg-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
  }
  .flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
  }
  .flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
  }
  .flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
  }
  .justify-content-lg-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
  }
  .justify-content-lg-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
  }
  .justify-content-lg-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
  }
  .justify-content-lg-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
  }
  .justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
  }
  .align-items-lg-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
  }
  .align-items-lg-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
  }
  .align-items-lg-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
  }
  .align-items-lg-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
  }
  .align-items-lg-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
  }
  .align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
  }
  .align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
  }
  .align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
  }
  .align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
  }
  .align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
  }
  .align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
  }
  .align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
  }
  .align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
  }
  .align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
  }
  .align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
  }
  .align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
  }
  .align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
  }
}

@media (min-width: 1200px) {
  .flex-xl-row {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: row !important;
    flex-direction: row !important
  }
  .flex-xl-column {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important
  }
  .flex-xl-row-reverse {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important
  }
  .flex-xl-column-reverse {
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: reverse !important;
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important
  }
  .flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important
  }
  .flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important
  }
  .flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important
  }
  .justify-content-xl-start {
    -webkit-box-pack: start !important;
    -ms-flex-pack: start !important;
    justify-content: flex-start !important
  }
  .justify-content-xl-end {
    -webkit-box-pack: end !important;
    -ms-flex-pack: end !important;
    justify-content: flex-end !important
  }
  .justify-content-xl-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important
  }
  .justify-content-xl-between {
    -webkit-box-pack: justify !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important
  }
  .justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important
  }
  .align-items-xl-start {
    -webkit-box-align: start !important;
    -ms-flex-align: start !important;
    align-items: flex-start !important
  }
  .align-items-xl-end {
    -webkit-box-align: end !important;
    -ms-flex-align: end !important;
    align-items: flex-end !important
  }
  .align-items-xl-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important
  }
  .align-items-xl-baseline {
    -webkit-box-align: baseline !important;
    -ms-flex-align: baseline !important;
    align-items: baseline !important
  }
  .align-items-xl-stretch {
    -webkit-box-align: stretch !important;
    -ms-flex-align: stretch !important;
    align-items: stretch !important
  }
  .align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important
  }
  .align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important
  }
  .align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important
  }
  .align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important
  }
  .align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important
  }
  .align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important
  }
  .align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important
  }
  .align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important
  }
  .align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important
  }
  .align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important
  }
  .align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important
  }
  .align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important
  }
}

.float-left {
  float: left !important
}

.float-right {
  float: right !important
}

.float-none {
  float: none !important
}

@media (min-width: 576px) {
  .float-sm-left {
    float: left !important
  }
  .float-sm-right {
    float: right !important
  }
  .float-sm-none {
    float: none !important
  }
}

@media (min-width: 768px) {
  .float-md-left {
    float: left !important
  }
  .float-md-right {
    float: right !important
  }
  .float-md-none {
    float: none !important
  }
}

@media (min-width: 992px) {
  .float-lg-left {
    float: left !important
  }
  .float-lg-right {
    float: right !important
  }
  .float-lg-none {
    float: none !important
  }
}

@media (min-width: 1200px) {
  .float-xl-left {
    float: left !important
  }
  .float-xl-right {
    float: right !important
  }
  .float-xl-none {
    float: none !important
  }
}

.position-static {
  position: static !important
}

.position-relative {
  position: relative !important
}

.position-absolute {
  position: absolute !important
}

.position-fixed {
  position: fixed !important
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  -webkit-clip-path: inset(50%);
  clip-path: inset(50%);
  border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
  -webkit-clip-path: none;
  clip-path: none
}

.w-25 {
  width: 25% !important
}

.w-50 {
  width: 50% !important
}

.w-75 {
  width: 75% !important
}

.w-100 {
  width: 100% !important
}

.h-25 {
  height: 25% !important
}

.h-50 {
  height: 50% !important
}

.h-75 {
  height: 75% !important
}

.h-100 {
  height: 100% !important
}

.mw-100 {
  max-width: 100% !important
}

.mh-100 {
  max-height: 100% !important
}

.m-0 {
  margin: 0 !important
}

.mt-0,
.my-0 {
  margin-top: 0 !important
}

.mr-0,
.mx-0 {
  margin-right: 0 !important
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important
}

.ml-0,
.mx-0 {
  margin-left: 0 !important
}

.m-1 {
  margin: .25rem !important
}

.mt-1,
.my-1 {
  margin-top: .25rem !important
}

.mr-1,
.mx-1 {
  margin-right: .25rem !important
}

.mb-1,
.my-1 {
  margin-bottom: .25rem !important
}

.ml-1,
.mx-1 {
  margin-left: .25rem !important
}

.m-2 {
  margin: .5rem !important
}

.mt-2,
.my-2 {
  margin-top: .5rem !important
}

.mr-2,
.mx-2 {
  margin-right: .5rem !important
}

.mb-2,
.my-2 {
  margin-bottom: .5rem !important
}

.ml-2,
.mx-2 {
  margin-left: .5rem !important
}

.m-3 {
  margin: 1rem !important
}

.mt-3,
.my-3 {
  margin-top: 1rem !important
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important
}

.m-4 {
  margin: 1.5rem !important
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important
}

.m-5 {
  margin: 3rem !important
}

.mt-5,
.my-5 {
  margin-top: 3rem !important
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important
}

.p-0 {
  padding: 0 !important
}

.pt-0,
.py-0 {
  padding-top: 0 !important
}

.pr-0,
.px-0 {
  padding-right: 0 !important
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important
}

.pl-0,
.px-0 {
  padding-left: 0 !important
}

.p-1 {
  padding: .25rem !important
}

.pt-1,
.py-1 {
  padding-top: .25rem !important
}

.pr-1,
.px-1 {
  padding-right: .25rem !important
}

.pb-1,
.py-1 {
  padding-bottom: .25rem !important
}

.pl-1,
.px-1 {
  padding-left: .25rem !important
}

.p-2 {
  padding: .5rem !important
}

.pt-2,
.py-2 {
  padding-top: .5rem !important
}

.pr-2,
.px-2 {
  padding-right: .5rem !important
}

.pb-2,
.py-2 {
  padding-bottom: .5rem !important
}

.pl-2,
.px-2 {
  padding-left: .5rem !important
}

.p-3 {
  padding: 1rem !important
}

.pt-3,
.py-3 {
  padding-top: 1rem !important
}

.pr-3,
.px-3 {
  padding-right: 1rem !important
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important
}

.pl-3,
.px-3 {
  padding-left: 1rem !important
}

.p-4 {
  padding: 1.5rem !important
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important
}

.p-5 {
  padding: 3rem !important
}

.pt-5,
.py-5 {
  padding-top: 3rem !important
}

.pr-5,
.px-5 {
  padding-right: 3rem !important
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important
}

.pl-5,
.px-5 {
  padding-left: 3rem !important
}

.m-auto {
  margin: auto !important
}

.mt-auto,
.my-auto {
  margin-top: auto !important
}

.mr-auto,
.mx-auto {
  margin-right: auto !important
}

.mb-auto,
.my-auto {
  margin-bottom: auto !important
}

.ml-auto,
.mx-auto {
  margin-left: auto !important
}

@media (min-width: 576px) {
  .m-sm-0 {
    margin: 0 !important
  }
  .mt-sm-0,
  .my-sm-0 {
    margin-top: 0 !important
  }
  .mr-sm-0,
  .mx-sm-0 {
    margin-right: 0 !important
  }
  .mb-sm-0,
  .my-sm-0 {
    margin-bottom: 0 !important
  }
  .ml-sm-0,
  .mx-sm-0 {
    margin-left: 0 !important
  }
  .m-sm-1 {
    margin: .25rem !important
  }
  .mt-sm-1,
  .my-sm-1 {
    margin-top: .25rem !important
  }
  .mr-sm-1,
  .mx-sm-1 {
    margin-right: .25rem !important
  }
  .mb-sm-1,
  .my-sm-1 {
    margin-bottom: .25rem !important
  }
  .ml-sm-1,
  .mx-sm-1 {
    margin-left: .25rem !important
  }
  .m-sm-2 {
    margin: .5rem !important
  }
  .mt-sm-2,
  .my-sm-2 {
    margin-top: .5rem !important
  }
  .mr-sm-2,
  .mx-sm-2 {
    margin-right: .5rem !important
  }
  .mb-sm-2,
  .my-sm-2 {
    margin-bottom: .5rem !important
  }
  .ml-sm-2,
  .mx-sm-2 {
    margin-left: .5rem !important
  }
  .m-sm-3 {
    margin: 1rem !important
  }
  .mt-sm-3,
  .my-sm-3 {
    margin-top: 1rem !important
  }
  .mr-sm-3,
  .mx-sm-3 {
    margin-right: 1rem !important
  }
  .mb-sm-3,
  .my-sm-3 {
    margin-bottom: 1rem !important
  }
  .ml-sm-3,
  .mx-sm-3 {
    margin-left: 1rem !important
  }
  .m-sm-4 {
    margin: 1.5rem !important
  }
  .mt-sm-4,
  .my-sm-4 {
    margin-top: 1.5rem !important
  }
  .mr-sm-4,
  .mx-sm-4 {
    margin-right: 1.5rem !important
  }
  .mb-sm-4,
  .my-sm-4 {
    margin-bottom: 1.5rem !important
  }
  .ml-sm-4,
  .mx-sm-4 {
    margin-left: 1.5rem !important
  }
  .m-sm-5 {
    margin: 3rem !important
  }
  .mt-sm-5,
  .my-sm-5 {
    margin-top: 3rem !important
  }
  .mr-sm-5,
  .mx-sm-5 {
    margin-right: 3rem !important
  }
  .mb-sm-5,
  .my-sm-5 {
    margin-bottom: 3rem !important
  }
  .ml-sm-5,
  .mx-sm-5 {
    margin-left: 3rem !important
  }
  .p-sm-0 {
    padding: 0 !important
  }
  .pt-sm-0,
  .py-sm-0 {
    padding-top: 0 !important
  }
  .pr-sm-0,
  .px-sm-0 {
    padding-right: 0 !important
  }
  .pb-sm-0,
  .py-sm-0 {
    padding-bottom: 0 !important
  }
  .pl-sm-0,
  .px-sm-0 {
    padding-left: 0 !important
  }
  .p-sm-1 {
    padding: .25rem !important
  }
  .pt-sm-1,
  .py-sm-1 {
    padding-top: .25rem !important
  }
  .pr-sm-1,
  .px-sm-1 {
    padding-right: .25rem !important
  }
  .pb-sm-1,
  .py-sm-1 {
    padding-bottom: .25rem !important
  }
  .pl-sm-1,
  .px-sm-1 {
    padding-left: .25rem !important
  }
  .p-sm-2 {
    padding: .5rem !important
  }
  .pt-sm-2,
  .py-sm-2 {
    padding-top: .5rem !important
  }
  .pr-sm-2,
  .px-sm-2 {
    padding-right: .5rem !important
  }
  .pb-sm-2,
  .py-sm-2 {
    padding-bottom: .5rem !important
  }
  .pl-sm-2,
  .px-sm-2 {
    padding-left: .5rem !important
  }
  .p-sm-3 {
    padding: 1rem !important
  }
  .pt-sm-3,
  .py-sm-3 {
    padding-top: 1rem !important
  }
  .pr-sm-3,
  .px-sm-3 {
    padding-right: 1rem !important
  }
  .pb-sm-3,
  .py-sm-3 {
    padding-bottom: 1rem !important
  }
  .pl-sm-3,
  .px-sm-3 {
    padding-left: 1rem !important
  }
  .p-sm-4 {
    padding: 1.5rem !important
  }
  .pt-sm-4,
  .py-sm-4 {
    padding-top: 1.5rem !important
  }
  .pr-sm-4,
  .px-sm-4 {
    padding-right: 1.5rem !important
  }
  .pb-sm-4,
  .py-sm-4 {
    padding-bottom: 1.5rem !important
  }
  .pl-sm-4,
  .px-sm-4 {
    padding-left: 1.5rem !important
  }
  .p-sm-5 {
    padding: 3rem !important
  }
  .pt-sm-5,
  .py-sm-5 {
    padding-top: 3rem !important
  }
  .pr-sm-5,
  .px-sm-5 {
    padding-right: 3rem !important
  }
  .pb-sm-5,
  .py-sm-5 {
    padding-bottom: 3rem !important
  }
  .pl-sm-5,
  .px-sm-5 {
    padding-left: 3rem !important
  }
  .m-sm-auto {
    margin: auto !important
  }
  .mt-sm-auto,
  .my-sm-auto {
    margin-top: auto !important
  }
  .mr-sm-auto,
  .mx-sm-auto {
    margin-right: auto !important
  }
  .mb-sm-auto,
  .my-sm-auto {
    margin-bottom: auto !important
  }
  .ml-sm-auto,
  .mx-sm-auto {
    margin-left: auto !important
  }
}

@media (min-width: 768px) {
  .m-md-0 {
    margin: 0 !important
  }
  .mt-md-0,
  .my-md-0 {
    margin-top: 0 !important
  }
  .mr-md-0,
  .mx-md-0 {
    margin-right: 0 !important
  }
  .mb-md-0,
  .my-md-0 {
    margin-bottom: 0 !important
  }
  .ml-md-0,
  .mx-md-0 {
    margin-left: 0 !important
  }
  .m-md-1 {
    margin: .25rem !important
  }
  .mt-md-1,
  .my-md-1 {
    margin-top: .25rem !important
  }
  .mr-md-1,
  .mx-md-1 {
    margin-right: .25rem !important
  }
  .mb-md-1,
  .my-md-1 {
    margin-bottom: .25rem !important
  }
  .ml-md-1,
  .mx-md-1 {
    margin-left: .25rem !important
  }
  .m-md-2 {
    margin: .5rem !important
  }
  .mt-md-2,
  .my-md-2 {
    margin-top: .5rem !important
  }
  .mr-md-2,
  .mx-md-2 {
    margin-right: .5rem !important
  }
  .mb-md-2,
  .my-md-2 {
    margin-bottom: .5rem !important
  }
  .ml-md-2,
  .mx-md-2 {
    margin-left: .5rem !important
  }
  .m-md-3 {
    margin: 1rem !important
  }
  .mt-md-3,
  .my-md-3 {
    margin-top: 1rem !important
  }
  .mr-md-3,
  .mx-md-3 {
    margin-right: 1rem !important
  }
  .mb-md-3,
  .my-md-3 {
    margin-bottom: 1rem !important
  }
  .ml-md-3,
  .mx-md-3 {
    margin-left: 1rem !important
  }
  .m-md-4 {
    margin: 1.5rem !important
  }
  .mt-md-4,
  .my-md-4 {
    margin-top: 1.5rem !important
  }
  .mr-md-4,
  .mx-md-4 {
    margin-right: 1.5rem !important
  }
  .mb-md-4,
  .my-md-4 {
    margin-bottom: 1.5rem !important
  }
  .ml-md-4,
  .mx-md-4 {
    margin-left: 1.5rem !important
  }
  .m-md-5 {
    margin: 3rem !important
  }
  .mt-md-5,
  .my-md-5 {
    margin-top: 3rem !important
  }
  .mr-md-5,
  .mx-md-5 {
    margin-right: 3rem !important
  }
  .mb-md-5,
  .my-md-5 {
    margin-bottom: 3rem !important
  }
  .ml-md-5,
  .mx-md-5 {
    margin-left: 3rem !important
  }
  .p-md-0 {
    padding: 0 !important
  }
  .pt-md-0,
  .py-md-0 {
    padding-top: 0 !important
  }
  .pr-md-0,
  .px-md-0 {
    padding-right: 0 !important
  }
  .pb-md-0,
  .py-md-0 {
    padding-bottom: 0 !important
  }
  .pl-md-0,
  .px-md-0 {
    padding-left: 0 !important
  }
  .p-md-1 {
    padding: .25rem !important
  }
  .pt-md-1,
  .py-md-1 {
    padding-top: .25rem !important
  }
  .pr-md-1,
  .px-md-1 {
    padding-right: .25rem !important
  }
  .pb-md-1,
  .py-md-1 {
    padding-bottom: .25rem !important
  }
  .pl-md-1,
  .px-md-1 {
    padding-left: .25rem !important
  }
  .p-md-2 {
    padding: .5rem !important
  }
  .pt-md-2,
  .py-md-2 {
    padding-top: .5rem !important
  }
  .pr-md-2,
  .px-md-2 {
    padding-right: .5rem !important
  }
  .pb-md-2,
  .py-md-2 {
    padding-bottom: .5rem !important
  }
  .pl-md-2,
  .px-md-2 {
    padding-left: .5rem !important
  }
  .p-md-3 {
    padding: 1rem !important
  }
  .pt-md-3,
  .py-md-3 {
    padding-top: 1rem !important
  }
  .pr-md-3,
  .px-md-3 {
    padding-right: 1rem !important
  }
  .pb-md-3,
  .py-md-3 {
    padding-bottom: 1rem !important
  }
  .pl-md-3,
  .px-md-3 {
    padding-left: 1rem !important
  }
  .p-md-4 {
    padding: 1.5rem !important
  }
  .pt-md-4,
  .py-md-4 {
    padding-top: 1.5rem !important
  }
  .pr-md-4,
  .px-md-4 {
    padding-right: 1.5rem !important
  }
  .pb-md-4,
  .py-md-4 {
    padding-bottom: 1.5rem !important
  }
  .pl-md-4,
  .px-md-4 {
    padding-left: 1.5rem !important
  }
  .p-md-5 {
    padding: 3rem !important
  }
  .pt-md-5,
  .py-md-5 {
    padding-top: 3rem !important
  }
  .pr-md-5,
  .px-md-5 {
    padding-right: 3rem !important
  }
  .pb-md-5,
  .py-md-5 {
    padding-bottom: 3rem !important
  }
  .pl-md-5,
  .px-md-5 {
    padding-left: 3rem !important
  }
  .m-md-auto {
    margin: auto !important
  }
  .mt-md-auto,
  .my-md-auto {
    margin-top: auto !important
  }
  .mr-md-auto,
  .mx-md-auto {
    margin-right: auto !important
  }
  .mb-md-auto,
  .my-md-auto {
    margin-bottom: auto !important
  }
  .ml-md-auto,
  .mx-md-auto {
    margin-left: auto !important
  }
}

@media (min-width: 992px) {
  .m-lg-0 {
    margin: 0 !important
  }
  .mt-lg-0,
  .my-lg-0 {
    margin-top: 0 !important
  }
  .mr-lg-0,
  .mx-lg-0 {
    margin-right: 0 !important
  }
  .mb-lg-0,
  .my-lg-0 {
    margin-bottom: 0 !important
  }
  .ml-lg-0,
  .mx-lg-0 {
    margin-left: 0 !important
  }
  .m-lg-1 {
    margin: .25rem !important
  }
  .mt-lg-1,
  .my-lg-1 {
    margin-top: .25rem !important
  }
  .mr-lg-1,
  .mx-lg-1 {
    margin-right: .25rem !important
  }
  .mb-lg-1,
  .my-lg-1 {
    margin-bottom: .25rem !important
  }
  .ml-lg-1,
  .mx-lg-1 {
    margin-left: .25rem !important
  }
  .m-lg-2 {
    margin: .5rem !important
  }
  .mt-lg-2,
  .my-lg-2 {
    margin-top: .5rem !important
  }
  .mr-lg-2,
  .mx-lg-2 {
    margin-right: .5rem !important
  }
  .mb-lg-2,
  .my-lg-2 {
    margin-bottom: .5rem !important
  }
  .ml-lg-2,
  .mx-lg-2 {
    margin-left: .5rem !important
  }
  .m-lg-3 {
    margin: 1rem !important
  }
  .mt-lg-3,
  .my-lg-3 {
    margin-top: 1rem !important
  }
  .mr-lg-3,
  .mx-lg-3 {
    margin-right: 1rem !important
  }
  .mb-lg-3,
  .my-lg-3 {
    margin-bottom: 1rem !important
  }
  .ml-lg-3,
  .mx-lg-3 {
    margin-left: 1rem !important
  }
  .m-lg-4 {
    margin: 1.5rem !important
  }
  .mt-lg-4,
  .my-lg-4 {
    margin-top: 1.5rem !important
  }
  .mr-lg-4,
  .mx-lg-4 {
    margin-right: 1.5rem !important
  }
  .mb-lg-4,
  .my-lg-4 {
    margin-bottom: 1.5rem !important
  }
  .ml-lg-4,
  .mx-lg-4 {
    margin-left: 1.5rem !important
  }
  .m-lg-5 {
    margin: 3rem !important
  }
  .mt-lg-5,
  .my-lg-5 {
    margin-top: 3rem !important
  }
  .mr-lg-5,
  .mx-lg-5 {
    margin-right: 3rem !important
  }
  .mb-lg-5,
  .my-lg-5 {
    margin-bottom: 3rem !important
  }
  .ml-lg-5,
  .mx-lg-5 {
    margin-left: 3rem !important
  }
  .p-lg-0 {
    padding: 0 !important
  }
  .pt-lg-0,
  .py-lg-0 {
    padding-top: 0 !important
  }
  .pr-lg-0,
  .px-lg-0 {
    padding-right: 0 !important
  }
  .pb-lg-0,
  .py-lg-0 {
    padding-bottom: 0 !important
  }
  .pl-lg-0,
  .px-lg-0 {
    padding-left: 0 !important
  }
  .p-lg-1 {
    padding: .25rem !important
  }
  .pt-lg-1,
  .py-lg-1 {
    padding-top: .25rem !important
  }
  .pr-lg-1,
  .px-lg-1 {
    padding-right: .25rem !important
  }
  .pb-lg-1,
  .py-lg-1 {
    padding-bottom: .25rem !important
  }
  .pl-lg-1,
  .px-lg-1 {
    padding-left: .25rem !important
  }
  .p-lg-2 {
    padding: .5rem !important
  }
  .pt-lg-2,
  .py-lg-2 {
    padding-top: .5rem !important
  }
  .pr-lg-2,
  .px-lg-2 {
    padding-right: .5rem !important
  }
  .pb-lg-2,
  .py-lg-2 {
    padding-bottom: .5rem !important
  }
  .pl-lg-2,
  .px-lg-2 {
    padding-left: .5rem !important
  }
  .p-lg-3 {
    padding: 1rem !important
  }
  .pt-lg-3,
  .py-lg-3 {
    padding-top: 1rem !important
  }
  .pr-lg-3,
  .px-lg-3 {
    padding-right: 1rem !important
  }
  .pb-lg-3,
  .py-lg-3 {
    padding-bottom: 1rem !important
  }
  .pl-lg-3,
  .px-lg-3 {
    padding-left: 1rem !important
  }
  .p-lg-4 {
    padding: 1.5rem !important
  }
  .pt-lg-4,
  .py-lg-4 {
    padding-top: 1.5rem !important
  }
  .pr-lg-4,
  .px-lg-4 {
    padding-right: 1.5rem !important
  }
  .pb-lg-4,
  .py-lg-4 {
    padding-bottom: 1.5rem !important
  }
  .pl-lg-4,
  .px-lg-4 {
    padding-left: 1.5rem !important
  }
  .p-lg-5 {
    padding: 3rem !important
  }
  .pt-lg-5,
  .py-lg-5 {
    padding-top: 3rem !important
  }
  .pr-lg-5,
  .px-lg-5 {
    padding-right: 3rem !important
  }
  .pb-lg-5,
  .py-lg-5 {
    padding-bottom: 3rem !important
  }
  .pl-lg-5,
  .px-lg-5 {
    padding-left: 3rem !important
  }
  .m-lg-auto {
    margin: auto !important
  }
  .mt-lg-auto,
  .my-lg-auto {
    margin-top: auto !important
  }
  .mr-lg-auto,
  .mx-lg-auto {
    margin-right: auto !important
  }
  .mb-lg-auto,
  .my-lg-auto {
    margin-bottom: auto !important
  }
  .ml-lg-auto,
  .mx-lg-auto {
    margin-left: auto !important
  }
}

@media (min-width: 1200px) {
  .m-xl-0 {
    margin: 0 !important
  }
  .mt-xl-0,
  .my-xl-0 {
    margin-top: 0 !important
  }
  .mr-xl-0,
  .mx-xl-0 {
    margin-right: 0 !important
  }
  .mb-xl-0,
  .my-xl-0 {
    margin-bottom: 0 !important
  }
  .ml-xl-0,
  .mx-xl-0 {
    margin-left: 0 !important
  }
  .m-xl-1 {
    margin: .25rem !important
  }
  .mt-xl-1,
  .my-xl-1 {
    margin-top: .25rem !important
  }
  .mr-xl-1,
  .mx-xl-1 {
    margin-right: .25rem !important
  }
  .mb-xl-1,
  .my-xl-1 {
    margin-bottom: .25rem !important
  }
  .ml-xl-1,
  .mx-xl-1 {
    margin-left: .25rem !important
  }
  .m-xl-2 {
    margin: .5rem !important
  }
  .mt-xl-2,
  .my-xl-2 {
    margin-top: .5rem !important
  }
  .mr-xl-2,
  .mx-xl-2 {
    margin-right: .5rem !important
  }
  .mb-xl-2,
  .my-xl-2 {
    margin-bottom: .5rem !important
  }
  .ml-xl-2,
  .mx-xl-2 {
    margin-left: .5rem !important
  }
  .m-xl-3 {
    margin: 1rem !important
  }
  .mt-xl-3,
  .my-xl-3 {
    margin-top: 1rem !important
  }
  .mr-xl-3,
  .mx-xl-3 {
    margin-right: 1rem !important
  }
  .mb-xl-3,
  .my-xl-3 {
    margin-bottom: 1rem !important
  }
  .ml-xl-3,
  .mx-xl-3 {
    margin-left: 1rem !important
  }
  .m-xl-4 {
    margin: 1.5rem !important
  }
  .mt-xl-4,
  .my-xl-4 {
    margin-top: 1.5rem !important
  }
  .mr-xl-4,
  .mx-xl-4 {
    margin-right: 1.5rem !important
  }
  .mb-xl-4,
  .my-xl-4 {
    margin-bottom: 1.5rem !important
  }
  .ml-xl-4,
  .mx-xl-4 {
    margin-left: 1.5rem !important
  }
  .m-xl-5 {
    margin: 3rem !important
  }
  .mt-xl-5,
  .my-xl-5 {
    margin-top: 3rem !important
  }
  .mr-xl-5,
  .mx-xl-5 {
    margin-right: 3rem !important
  }
  .mb-xl-5,
  .my-xl-5 {
    margin-bottom: 3rem !important
  }
  .ml-xl-5,
  .mx-xl-5 {
    margin-left: 3rem !important
  }
  .p-xl-0 {
    padding: 0 !important
  }
  .pt-xl-0,
  .py-xl-0 {
    padding-top: 0 !important
  }
  .pr-xl-0,
  .px-xl-0 {
    padding-right: 0 !important
  }
  .pb-xl-0,
  .py-xl-0 {
    padding-bottom: 0 !important
  }
  .pl-xl-0,
  .px-xl-0 {
    padding-left: 0 !important
  }
  .p-xl-1 {
    padding: .25rem !important
  }
  .pt-xl-1,
  .py-xl-1 {
    padding-top: .25rem !important
  }
  .pr-xl-1,
  .px-xl-1 {
    padding-right: .25rem !important
  }
  .pb-xl-1,
  .py-xl-1 {
    padding-bottom: .25rem !important
  }
  .pl-xl-1,
  .px-xl-1 {
    padding-left: .25rem !important
  }
  .p-xl-2 {
    padding: .5rem !important
  }
  .pt-xl-2,
  .py-xl-2 {
    padding-top: .5rem !important
  }
  .pr-xl-2,
  .px-xl-2 {
    padding-right: .5rem !important
  }
  .pb-xl-2,
  .py-xl-2 {
    padding-bottom: .5rem !important
  }
  .pl-xl-2,
  .px-xl-2 {
    padding-left: .5rem !important
  }
  .p-xl-3 {
    padding: 1rem !important
  }
  .pt-xl-3,
  .py-xl-3 {
    padding-top: 1rem !important
  }
  .pr-xl-3,
  .px-xl-3 {
    padding-right: 1rem !important
  }
  .pb-xl-3,
  .py-xl-3 {
    padding-bottom: 1rem !important
  }
  .pl-xl-3,
  .px-xl-3 {
    padding-left: 1rem !important
  }
  .p-xl-4 {
    padding: 1.5rem !important
  }
  .pt-xl-4,
  .py-xl-4 {
    padding-top: 1.5rem !important
  }
  .pr-xl-4,
  .px-xl-4 {
    padding-right: 1.5rem !important
  }
  .pb-xl-4,
  .py-xl-4 {
    padding-bottom: 1.5rem !important
  }
  .pl-xl-4,
  .px-xl-4 {
    padding-left: 1.5rem !important
  }
  .p-xl-5 {
    padding: 3rem !important
  }
  .pt-xl-5,
  .py-xl-5 {
    padding-top: 3rem !important
  }
  .pr-xl-5,
  .px-xl-5 {
    padding-right: 3rem !important
  }
  .pb-xl-5,
  .py-xl-5 {
    padding-bottom: 3rem !important
  }
  .pl-xl-5,
  .px-xl-5 {
    padding-left: 3rem !important
  }
  .m-xl-auto {
    margin: auto !important
  }
  .mt-xl-auto,
  .my-xl-auto {
    margin-top: auto !important
  }
  .mr-xl-auto,
  .mx-xl-auto {
    margin-right: auto !important
  }
  .mb-xl-auto,
  .my-xl-auto {
    margin-bottom: auto !important
  }
  .ml-xl-auto,
  .mx-xl-auto {
    margin-left: auto !important
  }
}

.text-justify {
  text-align: justify !important
}

.text-nowrap {
  white-space: nowrap !important
}

.text-truncate {
  overflow: hidden;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  white-space: nowrap
}

.text-left {
  text-align: left !important
}

.text-right {
  text-align: right !important
}

.text-center {
  text-align: center !important
}

@media (min-width: 576px) {
  .text-sm-left {
    text-align: left !important
  }
  .text-sm-right {
    text-align: right !important
  }
  .text-sm-center {
    text-align: center !important
  }
}

@media (min-width: 768px) {
  .text-md-left {
    text-align: left !important
  }
  .text-md-right {
    text-align: right !important
  }
  .text-md-center {
    text-align: center !important
  }
}

@media (min-width: 992px) {
  .text-lg-left {
    text-align: left !important
  }
  .text-lg-right {
    text-align: right !important
  }
  .text-lg-center {
    text-align: center !important
  }
}

@media (min-width: 1200px) {
  .text-xl-left {
    text-align: left !important
  }
  .text-xl-right {
    text-align: right !important
  }
  .text-xl-center {
    text-align: center !important
  }
}

.text-lowercase {
  text-transform: lowercase !important
}

.text-uppercase {
  text-transform: uppercase !important
}

.text-capitalize {
  text-transform: capitalize !important
}

.font-weight-light {
  font-weight: 300 !important
}

.font-weight-normal {
  font-weight: 400 !important
}

.font-weight-bold {
  font-weight: 700 !important
}

.font-italic {
  font-style: italic !important
}

.text-white {
  color: #fff
}

.text-primary {
  color: #188bf6 !important
}

a.text-primary:hover,
a.text-primary:focus {
  color: #0871d3 !important
}

.text-secondary {
  color: #ff7402 !important
}

a.text-secondary:hover,
a.text-secondary:focus {
  color: #ce5d00 !important
}

.text-success {
  color: #37ca37 !important
}

a.text-success:hover,
a.text-success:focus {
  color: #2ba32b !important
}

.text-info {
  color: #17cfbc !important
}

a.text-info:hover,
a.text-info:focus {
  color: #12a192 !important
}

.text-warning {
  color: #ffbc00 !important
}

a.text-warning:hover,
a.text-warning:focus {
  color: #cc9600 !important
}

.text-danger {
  color: #e93d3d !important
}

a.text-danger:hover,
a.text-danger:focus {
  color: #da1919 !important
}

.text-light {
  color: #afb8bc !important
}

a.text-light:hover,
a.text-light:focus {
  color: #939fa5 !important
}

.text-dark {
  color: #2a3135 !important
}

a.text-dark:hover,
a.text-dark:focus {
  color: #131719 !important
}

.text-muted {
  color: #6c757d !important
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0
}

.visible {
  visibility: visible !important
}

.invisible {
  visibility: hidden !important
}

@font-face {
  font-family: 'Magicons';
  src: url("../icons/Magicons.eot?g0nnmd");
  src: url("../icons/Magicons.eot?g0nnmd#iefix") format("embedded-opentype"), url("../icons/Magicons.ttf?g0nnmd") format("truetype"), url("../icons/Magicons.woff?g0nnmd") format("woff");
  font-weight: normal;
  font-style: normal
}

[class^="icon-"],
[class*=" icon-"] {
  font-family: 'Magicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.icon-menu:before {
  content: "\e900"
}

.icon-user:before {
  content: "\e999"
}

.icon-loupe:before {
  content: "\e953"
}

.icon-inbox:before {
  content: "\e939"
}

.icon-mail:before {
  content: "\e954"
}

.icon-image:before {
  content: "\e938"
}

.icon-home:before {
  content: "\e937"
}

.icon-cart:before {
  content: "\e91a"
}

.icon-link:before {
  content: "\e950"
}

.icon-pulse:before {
  content: "\e971"
}

.icon-music:before {
  content: "\e966"
}

.icon-calendar:before {
  content: "\e917"
}

.icon-notifications:before {
  content: "\e968"
}

.icon-notifications-filled:before {
  content: "\e967"
}

.icon-notifications-off:before {
  content: "\e969"
}

.icon-folder:before {
  content: "\e933"
}

.icon-folder-filled:before {
  content: "\e932"
}

.icon-folder-opened:before {
  content: "\e934"
}

.icon-bubble:before {
  content: "\e916"
}

.icon-bubble-filled:before {
  content: "\e915"
}

.icon-heart:before {
  content: "\e936"
}

.icon-heart-filled:before {
  content: "\e935"
}

.icon-camera:before {
  content: "\e919"
}

.icon-camera-filled:before {
  content: "\e918"
}

.icon-ribbon:before {
  content: "\e97b"
}

.icon-ribbon-filled:before {
  content: "\e97a"
}

.icon-ribbon-plus:before {
  content: "\e97c"
}

.icon-star:before {
  content: "\e985"
}

.icon-star-half:before {
  content: "\e984"
}

.icon-star-filled:before {
  content: "\e983"
}

.icon-menu-left-1:before {
  content: "\e958"
}

.icon-menu-center-1:before {
  content: "\e956"
}

.icon-menu-right-1:before {
  content: "\e95a"
}

.icon-menu-left-2:before {
  content: "\e959"
}

.icon-menu-center-2:before {
  content: "\e957"
}

.icon-menu-right-2:before {
  content: "\e95b"
}

.icon-enter-left:before {
  content: "\e92e"
}

.icon-enter-right:before {
  content: "\e92f"
}

.icon-exit-left:before {
  content: "\e930"
}

.icon-exit-right:before {
  content: "\e931"
}

.icon-pause:before {
  content: "\e946"
}

.icon-pause-filled:before {
  content: "\e945"
}

.icon-settings-1:before {
  content: "\e97d"
}

.icon-settings-2:before {
  content: "\e97e"
}

.icon-settings-3:before {
  content: "\e97f"
}

.icon-target-1:before {
  content: "\e98e"
}

.icon-target-2:before {
  content: "\e98f"
}

.icon-download:before {
  content: "\e92b"
}

.icon-share-1:before {
  content: "\e980"
}

.icon-share-2:before {
  content: "\e981"
}

.icon-play:before {
  content: "\e948"
}

.icon-play-filled:before {
  content: "\e947"
}

.icon-backward:before {
  content: "\e94a"
}

.icon-backward-filled:before {
  content: "\e949"
}

.icon-forward:before {
  content: "\e94c"
}

.icon-forward-filled:before {
  content: "\e94b"
}

.icon-clock:before {
  content: "\e91c"
}

.icon-clock-filled:before {
  content: "\e91b"
}

.icon-dashboard:before {
  content: "\e924"
}

.icon-dashboard-filled:before {
  content: "\e923"
}

.icon-plus-circle:before {
  content: "\e95f"
}

.icon-plus-circle-filled:before {
  content: "\e95e"
}

.icon-minus-circle:before {
  content: "\e962"
}

.icon-minus-circle-filled:before {
  content: "\e961"
}

.icon-ok-circle:before {
  content: "\e96b"
}

.icon-ok-circle-filled:before {
  content: "\e96a"
}

.icon-blocks:before {
  content: "\e914"
}

.icon-blocks-filled:before {
  content: "\e913"
}

.icon-tiles:before {
  content: "\e996"
}

.icon-tiles-filled:before {
  content: "\e995"
}

.icon-close-circle:before {
  content: "\e91e"
}

.icon-close-circle-filled:before {
  content: "\e91d"
}

.icon-list:before {
  content: "\e955"
}

.icon-text-align-justify:before {
  content: "\e991"
}

.icon-text-align-left:before {
  content: "\e992"
}

.icon-text-align-center:before {
  content: "\e990"
}

.icon-text-align-right:before {
  content: "\e993"
}

.icon-volume:before {
  content: "\e99a"
}

.icon-monitor:before {
  content: "\e94d"
}

.icon-laptop:before {
  content: "\e94e"
}

.icon-smartphone:before {
  content: "\e94f"
}

.icon-watch:before {
  content: "\e99b"
}

.icon-duplicate:before {
  content: "\e92c"
}

.icon-crop:before {
  content: "\e922"
}

.icon-resize-plus-1:before {
  content: "\e977"
}

.icon-resize-minus-1:before {
  content: "\e974"
}

.icon-resize-minus-2:before {
  content: "\e975"
}

.icon-resize-plus-2:before {
  content: "\e978"
}

.icon-resize-minus-3:before {
  content: "\e976"
}

.icon-resize-plus-3:before {
  content: "\e979"
}

.icon-battery-empty:before {
  content: "\e90f"
}

.icon-battery-low:before {
  content: "\e912"
}

.icon-battery-half:before {
  content: "\e911"
}

.icon-battery-full:before {
  content: "\e910"
}

.icon-signal:before {
  content: "\e982"
}

.icon-power:before {
  content: "\e943"
}

.icon-text:before {
  content: "\e994"
}

.icon-info:before {
  content: "\e93a"
}

.icon-document:before {
  content: "\e926"
}

.icon-document-filled:before {
  content: "\e925"
}

.icon-document-text:before {
  content: "\e928"
}

.icon-document-plus:before {
  content: "\e927"
}

.icon-pencil:before {
  content: "\e96e"
}

.icon-pencil-filled:before {
  content: "\e96d"
}

.icon-edit:before {
  content: "\e92d"
}

.icon-switch:before {
  content: "\e98a"
}

.icon-switch-filled:before {
  content: "\e989"
}

.icon-switches:before {
  content: "\e98b"
}

.icon-repeat:before {
  content: "\e944"
}

.icon-sort-az:before {
  content: "\e90e"
}

.icon-arrow-left-1:before {
  content: "\e902"
}

.icon-arrow-right-1:before {
  content: "\e903"
}

.icon-arrow-up-1:before {
  content: "\e905"
}

.icon-arrow-down-1:before {
  content: "\e904"
}

.icon-arrow-left-2:before {
  content: "\e906"
}

.icon-arrow-right-2:before {
  content: "\e907"
}

.icon-arrow-up-2:before {
  content: "\e909"
}

.icon-arrow-down-2:before {
  content: "\e908"
}

.icon-arrows-left-right:before {
  content: "\e90c"
}

.icon-arrows-up-down:before {
  content: "\e90d"
}

.icon-jump-left-up:before {
  content: "\e93e"
}

.icon-jump-right-up:before {
  content: "\e940"
}

.icon-jump-down-left:before {
  content: "\e93b"
}

.icon-jump-up-right:before {
  content: "\e942"
}

.icon-jump-left-down:before {
  content: "\e93d"
}

.icon-jump-right-down:before {
  content: "\e93f"
}

.icon-jump-up-left:before {
  content: "\e941"
}

.icon-jump-down-right:before {
  content: "\e93c"
}

.icon-arrow-left-right:before {
  content: "\e90a"
}

.icon-arrow-up-down:before {
  content: "\e90b"
}

.icon-reload-1:before {
  content: "\e972"
}

.icon-reload-2:before {
  content: "\e973"
}

.icon-plus:before {
  content: "\e960"
}

.icon-minus:before {
  content: "\e963"
}

.icon-ok:before {
  content: "\e96c"
}

.icon-close:before {
  content: "\e91f"
}

.icon-dots-hr:before {
  content: "\e929"
}

.icon-dots-vr:before {
  content: "\e92a"
}

.icon-mic:before {
  content: "\e95c"
}

.icon-mic-off:before {
  content: "\e95d"
}

.icon-zoom-in:before {
  content: "\e99d"
}

.icon-zoom-in-filled:before {
  content: "\e99c"
}

.icon-zoom-out:before {
  content: "\e99f"
}

.icon-zoom-out-filled:before {
  content: "\e99e"
}

.icon-stop:before {
  content: "\e986"
}

.icon-dots:before {
  content: "\e901"
}

.icon-pin:before {
  content: "\e970"
}

.icon-pin-filled:before {
  content: "\e96f"
}

.icon-trash:before {
  content: "\e998"
}

.icon-trash-filled:before {
  content: "\e997"
}

.icon-tag:before {
  content: "\e98d"
}

.icon-tag-filled:before {
  content: "\e98c"
}

.icon-cloud:before {
  content: "\e921"
}

.icon-cloud-filled:before {
  content: "\e920"
}

.icon-lock:before {
  content: "\e952"
}

.icon-lock-filled:before {
  content: "\e951"
}

.icon-sun:before {
  content: "\e988"
}

.icon-sun-filled:before {
  content: "\e987"
}

.icon-moon:before {
  content: "\e965"
}

.icon-moon-filled:before {
  content: "\e964"
}

body {
  font-family: Roboto, system, -apple-system, BlinkMacSystemFont, ".SFNSDisplay-Regular", "Helvetica Neue", Helvetica, Arial, sans-serif;
  color: #607179 !important;
  font-size: .875rem;
  font-weight: 400;
  line-height: 1.8;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-height: 100vh
}

@media (min-width: 768px) {
  body {
    padding-top: 82px
  }
}

body>.hl_wrapper {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  overflow: hidden
}

.hl_wrapper {
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s
}

@media (min-width: 768px) {
  .hl_wrapper {
    padding-left: 70px
  }
}

@media (min-width: 1200px) {
  .hl_wrapper {
    padding-left: 250px
  }
}

.hl_wrapper--inner {
  padding-top: 25px;
  padding-bottom: 25px
}

.container-fluid {
  max-width: 1200px
}

@media (min-width: 1200px) {
  .container-fluid {
    padding-left: 20px;
    padding-right: 20px
  }
}

a,
button {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer
}

a:hover,
a:active,
a:focus,
button:hover,
button:active,
button:focus {
  text-decoration: none;
  outline: none
}

a:active:focus,
button:active:focus {
  outline: none
}

a[x-apple-data-detectors] {
  color: inherit !important;
  text-decoration: none !important;
  font-size: inherit !important;
  font-family: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important
}

p {
  margin-bottom: 0
}

p+p {
  margin-top: .9375rem
}

p.lead {
  font-size: 1.125rem;
  line-height: 1.8;
  font-weight: normal
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #2a3135;
  font-weight: normal;
  margin-top: 0
}

h1.heading1,
h2.heading1,
h3.heading1,
h4.heading1,
h5.heading1,
h6.heading1 {
  font-size: 2.5rem
}

@media (max-width: 1200px) {
  h1.heading1,
  h2.heading1,
  h3.heading1,
  h4.heading1,
  h5.heading1,
  h6.heading1 {
    font-size: calc(1.2rem + 1.73333vw)
  }
}

h1.heading2,
h2.heading2,
h3.heading2,
h4.heading2,
h5.heading2,
h6.heading2 {
  font-size: 2rem
}

@media (max-width: 1200px) {
  h1.heading2,
  h2.heading2,
  h3.heading2,
  h4.heading2,
  h5.heading2,
  h6.heading2 {
    font-size: calc(1.1rem + 1.2vw)
  }
}

h1.heading3,
h2.heading3,
h3.heading3,
h4.heading3,
h5.heading3,
h6.heading3 {
  font-size: 1.75rem
}

@media (max-width: 1200px) {
  h1.heading3,
  h2.heading3,
  h3.heading3,
  h4.heading3,
  h5.heading3,
  h6.heading3 {
    font-size: calc(1.05rem + .93333vw)
  }
}

h1.heading4,
h2.heading4,
h3.heading4,
h4.heading4,
h5.heading4,
h6.heading4 {
  font-size: 1.5rem
}

@media (max-width: 1200px) {
  h1.heading4,
  h2.heading4,
  h3.heading4,
  h4.heading4,
  h5.heading4,
  h6.heading4 {
    font-size: calc(1rem + .66667vw)
  }
}

h1.heading5,
h2.heading5,
h3.heading5,
h4.heading5,
h5.heading5,
h6.heading5 {
  font-size: 1.25rem
}

@media (max-width: 1200px) {
  h1.heading5,
  h2.heading5,
  h3.heading5,
  h4.heading5,
  h5.heading5,
  h6.heading5 {
    font-size: calc(.95rem + .4vw)
  }
}

h1.heading6,
h2.heading6,
h3.heading6,
h4.heading6,
h5.heading6,
h6.heading6 {
  font-size: 16px
}

hr {
  border-top: 2px solid #f2f7fa
}

hr.tall {
  margin-top: 30px;
  margin-bottom: 30px
}

.hl_tooltip {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  color: #fff;
  font-weight: bold;
  background-color: rgba(96, 113, 121, 0.5)
}

.hl_tooltip:hover,
.hl_tooltip:focus,
.hl_tooltip:active {
  color: #fff;
  background-color: rgba(96, 113, 121, 0.7)
}

*.--blue {
  color: #188bf6
}

*.--yellow {
  color: #ffbc00
}

*.--green {
  color: #37ca37
}

*.--red {
  color: #e93d3d
}

*.--orange {
  color: #ff7402
}

*.--purple {
  color: #876cff
}

*.--teal {
  color: #17cfbc
}

*.--pink {
  color: #ff3e7f
}

*.--gray {
  color: #607179
}

*.--gray-drk {
  color: #2a3135
}

*.--gray-lt {
  color: #afb8bc
}

.alert {
  border: none;
  color: #fff
}

.alert .svg-inline--fa {
  display: inline-block;
  margin-right: 10px
}

.alert.--blue {
  background-color: #188bf6
}

.alert.--yellow {
  background-color: #ffbc00
}

.alert.--green {
  background-color: #37ca37
}

.alert.--red {
  background-color: #e93d3d
}

.alert.--orange {
  background-color: #ff7402
}

.alert.--purple {
  background-color: #876cff
}

.alert.--teal {
  background-color: #17cfbc
}

.alert.--pink {
  background-color: #ff3e7f
}

.alert.--gray {
  background-color: #607179
}

.alert.--gray-drk {
  background-color: #2a3135
}

.alert.--gray-lt {
  background-color: #afb8bc
}

.hl_navbar {
  background: #fff;
  width: 100%;
  padding: 25px 15px;
  -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  border-bottom: 2px solid #f2f7fa
}

@media (max-width: 767px) {
  .hl_navbar {
    padding: 15px
  }
}

@media (min-width: 768px) {
  .hl_navbar {
    position: fixed;
    overflow: auto;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    padding: 25px 0;
    width: 70px;
    bottom: 0;
    border-bottom: none
  }
}

@media (min-width: 1200px) {
  .hl_navbar {
    width: 250px
  }
}

.hl_navbar--logo {
  display: block
}

@media (min-width: 768px) {
  .hl_navbar--logo {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 35px
  }
  .hl_navbar--logo>svg {
    display: block;
    width: 28px;
    height: 26px;
    margin-left: auto;
    margin-right: auto
  }
  .hl_navbar--logo>img {
    display: block;
    width: auto;
    max-height: 32px;
    margin-left: auto;
    margin-right: auto
  }
}

@media (max-width: 767px) {
  .hl_navbar--collapse {
    border-top: 2px solid #f2f7fa;
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 20px;
    margin-left: -15px;
    margin-right: -15px;
    display: none
  }
}

@media (min-width: 768px) {
  .hl_navbar--collapse {
    display: block !important
  }
}

.hl_navbar--toggler {
  margin-top: 4px;
  margin-bottom: 4px;
  width: 26px;
  height: 26px;
  padding: 2px 0;
  border: none;
  position: absolute;
  top: 13px;
  right: 15px
}

@media (min-width: 768px) {
  .hl_navbar--toggler {
    display: none;
    visibility: hidden;
    opacity: 0
  }
}

.hl_navbar--toggler .navbar-toggler-bar {
  display: block;
  width: 26px;
  height: 2px;
  background: #afb8bc;
  -webkit-transition: all 0.3s cubic-bezier(0.555, 0.205, 0.295, 0.975) 0s;
  -o-transition: all 0.3s cubic-bezier(0.555, 0.205, 0.295, 0.975) 0s;
  transition: all 0.3s cubic-bezier(0.555, 0.205, 0.295, 0.975) 0s;
  border-radius: 4px;
  margin-bottom: 5px
}

.hl_navbar--toggler .navbar-toggler-bar:last-child {
  margin-bottom: 0
}

@media (min-width: 992px) {
  .hl_navbar--toggler .navbar-toggler-bar {
    display: none;
    visibility: hidden
  }
}

.hl_navbar--toggler:hover .navbar-toggler-bar,
.hl_navbar--toggler:active .navbar-toggler-bar {
  background: #607179
}

.hl_navbar--toggler.active .navbar-toggler-bar {
  background: #607179
}

.hl_navbar--toggler.active .navbar-toggler-bar:nth-child(1) {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  position: relative;
  top: 7px
}

.hl_navbar--toggler.active .navbar-toggler-bar:nth-child(2) {
  opacity: 0
}

.hl_navbar--toggler.active .navbar-toggler-bar:nth-child(3) {
  -webkit-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
  position: relative;
  bottom: 7px
}

.hl_navbar--button {
  font-size: .875rem !important;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 20px
}

.hl_navbar--button:before {
  display: none
}

@media (min-width: 768px) and (max-width: 1199px) {
  .hl_navbar--button {
    font-size: 0 !important;
    min-width: auto !important;
    height: 43px;
    width: 50px;
    padding-left: 0 !important;
    padding-right: 0 !important;
    position: relative
  }
  .hl_navbar--button:before {
    content: "\e960";
    display: block;
    font-family: 'Magicons' !important;
    speak: none;
    font-size: .8125rem;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 13px;
    position: absolute;
    top: 50%;
    right: 50%;
    -webkit-transform: translate(50%, -50%);
    -ms-transform: translate(50%, -50%);
    transform: translate(50%, -50%);
    color: #fff;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s
  }
}

@media (min-width: 1200px) {
  .hl_navbar--button {
    width: 170px;
    margin-bottom: 0
  }
}

.hl_navbar--links {
  margin-bottom: 0
}

@media (min-width: 768px) {
  .hl_navbar--links {
    margin-top: 45px
  }
}

.hl_navbar--links li a {
  display: block;
  padding: 15px 25px;
  color: #607179;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: none;
  -o-transition: none;
  transition: none
}

.hl_navbar--links li a:hover,
.hl_navbar--links li a:focus,
.hl_navbar--links li a:active {
  color: #188bf6
}

.hl_navbar--links li a:hover i,
.hl_navbar--links li a:focus i,
.hl_navbar--links li a:active i {
  opacity: 1
}

.hl_navbar--links li a i {
  display: block;
  font-size: 1rem;
  opacity: 0.5;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin-right: 20px;
  -webkit-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  transform: translateY(-2px)
}

.hl_navbar--links li a span {
  display: block
}

@media (min-width: 768px) and (max-width: 1199px) {
  .hl_navbar--links li a span {
    opacity: 0;
    -webkit-transition: all 0.2s ease-in-out 0s;
    -o-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    -webkit-transform: translateX(30px);
    -ms-transform: translateX(30px);
    transform: translateX(30px)
  }
}

@media (min-width: 1200px) {
  .hl_navbar--links li a span {
    opacity: 1;
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0)
  }
}

.hl_navbar--links li.active a {
  color: #188bf6;
  font-weight: 500;
  background: rgba(24, 139, 246, 0.1)
}

.hl_navbar--links li.active a i {
  opacity: 1
}

.reputation-nav-toggle {
  background: #fff;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out;
  position: relative
}

.reputation-nav-toggle:before {
  content: "";
  display: block;
  background: #fff;
  width: 4px;
  height: 100%;
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  bottom: 0;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out
}

.reputation-nav-toggle.open {
  background: #e7f3fe
}

.reputation-nav-toggle.open:before {
  background-color: #188bf6
}

.reputation-nav-toggle.open>a {
  color: #188bf6
}

.reputation-nav-toggle.open>a i {
  opacity: 1
}

.reputation-nav-toggle>a .icon {
  font-size: 8px;
  position: absolute;
  top: 50%;
  right: -37px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.reputation-nav {
  padding-left: 35px
}

.reputation-nav ul {
  padding-bottom: 20px
}

.reputation-nav ul li.active a {
  background: none
}

.reputation-nav ul li:not(:last-child) {
  margin-bottom: 10px
}

.reputation-nav ul li a {
  padding-top: 0;
  padding-bottom: 0
}

@media (min-width: 768px) {
  .modal-open .hl_header .container-fluid {
    padding-right: 45px
  }
}

.hl_header {
  background: #fff;
  -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s
}

@media (min-width: 768px) {
  .hl_header {
    -webkit-box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
    box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
    position: fixed;
    top: 0;
    left: 70px;
    right: 0;
    z-index: 9
  }
}

@media (min-width: 1200px) {
  .hl_header {
    top: 0;
    left: 250px;
    right: 0
  }
}

.hl_header.--no-shadow {
  -webkit-box-shadow: inset 2px 0 0 0 #f2f7fa;
  box-shadow: inset 2px 0 0 0 #f2f7fa
}

.hl_header .container-fluid {
  max-width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 82px;
  padding: 10px 30px
}

@media (max-width: 767px) {
  .hl_header .container-fluid {
    padding: 5px 15px;
    height: 54px
  }
}

.hl_header--picker {
  position: static
}

.hl_header--picker>.btn.dropdown-toggle {
  background: #fff !important;
  padding: 0 !important;
  border-radius: 0;
  height: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_header--picker>.btn.dropdown-toggle:before {
  display: none
}

.hl_header--picker>.btn.dropdown-toggle:after {
  right: -20px !important;
  color: #188bf6
}

.hl_header--picker>.btn.dropdown-toggle .filter-option {
  color: #2a3135 !important;
  font-weight: 400
}

.hl_header--picker>.btn.dropdown-toggle .filter-option img {
  display: none
}

.hl_header--picker>.dropdown-menu {
  width: 100% !important;
  top: 15px !important;
  left: 7px !important;
  right: 2px !important
}

.hl_header--picker>.dropdown-menu .popover-title {
  padding: 20px;
  font-size: 1.25rem;
  color: #2a3135
}

.hl_header--picker>.dropdown-menu .popover-title a {
  font-size: .875rem;
  margin-left: 10px
}

.hl_header--picker>.dropdown-menu .popover-title .close {
  font-weight: 300;
  font-size: 1.875rem;
  color: #afb8bc
}

.hl_header--picker>.dropdown-menu .inner .dropdown-menu li a {
  padding-top: 20px;
  padding-bottom: 20px;
  color: #2a3135 !important
}

.hl_header--picker>.dropdown-menu .inner .dropdown-menu li a .text img {
  border-radius: 50%;
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  margin-right: 20px
}

.hl_header--picker>.dropdown-menu .inner .dropdown-menu li.selected a {
  background: #f2f7fa !important
}

.hl_header--controls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_header--controls>*+* {
  margin-left: 10px
}

.hl_header--recent-activities {
  position: relative
}

.hl_header--recent-activities.-notification:before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffbc00;
  position: absolute;
  top: 2px;
  right: 2px
}

@media (max-width: 767px) {
  .hl_header .btn {
    width: 38px;
    height: 38px;
    line-height: 40px;
    font-size: .875rem
  }
}

.hl_header--avatar {
  display: block;
  max-width: 44px;
  height: 44px
}

@media (max-width: 767px) {
  .hl_header--avatar {
    max-width: 38px;
    height: 38px
  }
  .hl_header--avatar .avatar {
    height: 38px
  }
  .hl_header--avatar .avatar .avatar_img {
    min-width: 38px;
    width: 38px;
    height: 38px
  }
  .hl_header--avatar .avatar .avatar_img>img {
    max-width: 38px;
    max-height: 38px
  }
}

.hl_header--dropdown .dropdown-menu {
  margin-top: 10px
}

.hl_recent-activities {
  background-color: #fff
}

@media (max-width: 767px) {
  .hl_recent-activities {
    border-top: 1px solid #f2f7fa;
    height: 0;
    opacity: 0;
    visibility: hidden;
    -webkit-box-shadow: 0 10px 10px 0 rgba(0, 0, 0, 0.01);
    box-shadow: 0 10px 10px 0 rgba(0, 0, 0, 0.01)
  }
}

@media (min-width: 768px) {
  .hl_recent-activities {
    -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    width: 350px;
    height: 100vh;
    position: fixed;
    top: 82px;
    right: 0;
    z-index: 8;
    border-top: none;
    -webkit-transform: translateX(350px);
    -ms-transform: translateX(350px);
    transform: translateX(350px)
  }
}

@media (min-width: 768px) {
  .hl_recent-activities.--settings {
    top: 83px !important
  }
}

.hl_recent-activities--inner {
  padding: 0 30px;
  margin-top: 20px;
  margin-bottom: 30px;
  overflow-y: auto;
  height: auto
}

@media (min-width: 768px) {
  .hl_recent-activities--inner {
    height: calc(100vh - 60px);
    margin: 0;
    padding: 30px
  }
}

@media (max-width: 767px) {
  .hl_recent-activities.--open {
    height: auto;
    opacity: 1;
    visibility: visible;
    position: relative
  }
  .hl_recent-activities.--open:before {
    content: "";
    display: block;
    height: 20px;
    background: #fff;
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0
  }
  .hl_recent-activities.--open:after {
    content: "";
    display: block;
    height: 25px;
    background: #fff;
    position: absolute;
    margin: auto;
    bottom: 0;
    left: 0;
    right: 0
  }
}

@media (min-width: 768px) {
  .hl_recent-activities.--open {
    -webkit-box-shadow: -10px 0 60px 0 rgba(0, 0, 0, 0.1);
    box-shadow: -10px 0 60px 0 rgba(0, 0, 0, 0.1);
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0)
  }
}

.hl_recent-activities--item {
  position: relative;
  padding-left: 55px;
  margin-bottom: 0
}

.hl_recent-activities--item+.hl_recent-activities--item {
  margin-top: 25px
}

.hl_recent-activities--item .avatar {
  position: absolute;
  top: 0;
  left: 0;
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1)
}

.hl_recent-activities--item .avatar.--notification:before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffbc00;
  position: absolute;
  top: 1px;
  right: 1px;
  z-index: 3
}

.hl_recent-activities--item .recent-activities--icon {
  width: 35px;
  height: 35px;
  line-height: 36px;
  font-size: .875rem;
  text-align: center;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1)
}

.hl_recent-activities--item .recent-activities--icon.--rating {
  color: #37ca37;
  background-color: rgba(55, 202, 55, 0.2)
}

.hl_recent-activities--item .recent-activities--icon.--reviews {
  color: #188bf6;
  background-color: rgba(24, 139, 246, 0.2)
}

.hl_recent-activities--item .recent-activities--icon.--reviews .icon {
  width: 14px;
  height: 12px
}

.hl_recent-activities--item p {
  font-size: .875rem;
  line-height: 1.43
}

.hl_recent-activities--item p strong {
  font-weight: 400;
  color: #2a3135
}

.hl_recent-activities--item p .time-date {
  display: block;
  font-size: .75rem;
  font-weight: 500;
  line-height: 2;
  color: #afb8bc
}

.form-group {
  margin-bottom: 1.25rem
}

.form-group>label {
  display: block
}

.form-group .bootstrap-select>.btn.dropdown-toggle {
  padding: 12px 20px
}

.form-control {
  background: #f3f8fb;
  font-family: Roboto, system, -apple-system, BlinkMacSystemFont, ".SFNSDisplay-Regular", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: .875rem;
  color: #2a3135;
  border: none;
  border-radius: .3125rem;
  padding: 15px 20px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none
}

.form-control::-webkit-input-placeholder {
  color: #afb8bc
}

.form-control:-moz-placeholder {
  color: #afb8bc
}

.form-control::-moz-placeholder {
  color: #afb8bc
}

.form-control:-ms-input-placeholder {
  color: #afb8bc
}

.form-control:active,
.form-control:focus {
  background: #ecf3f8;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none
}

.form-control:active:focus {
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-color: #bbd5e5
}

.form-control.form-light {
  background: #fff
}

.form-control.form-light:active,
.form-control.form-light:focus {
  background: rgba(24, 139, 246, 0.05)
}

label {
  color: #607179;
  font-size: .75rem
}

label>.icon {
  font-size: 1rem;
  margin-right: 5px;
  color: #afb8bc;
  position: relative;
  top: 2px
}

.option-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.option-group>.option+.option {
  margin-left: 20px
}

.option {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative
}

.option label {
  line-height: 30px;
  display: block;
  padding-left: 30px;
  cursor: pointer;
  margin-bottom: 0;
  font-size: .875rem
}

.option label:before {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  border: 2px solid #e6edf2;
  background: #fff;
  position: absolute;
  top: 5px;
  left: 0
}

.option label:after {
  font-family: 'Magicons';
  color: #fff;
  content: "\e96c";
  font-size: .625rem;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid #00d26d;
  background-color: #00d26d;
  background-size: 10px 7px;
  position: absolute;
  top: 10px;
  left: 5px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0
}

.option input[type="checkbox"] {
  margin: 0;
  display: none;
  visibility: hidden
}

.option input[type="checkbox"]:checked+label {
  color: #2a3135
}

.option input[type="checkbox"]:checked+label:after {
  width: 20px;
  height: 20px;
  opacity: 1;
  top: 5px;
  left: 0;
  border-radius: 2px
}

.option input[type="checkbox"][type="radio"]+label:before,
.option input[type="checkbox"][type="radio"]+label:after {
  border-radius: 50%
}

.option input[type="checkbox"][type="radio"]:checked+label:after {
  width: 10px;
  height: 10px;
  top: 10px;
  left: 3px
}

.option.option-lg label {
  padding-left: 40px
}

.option.option-lg label:before {
  top: 0;
  width: 32px;
  height: 32px
}

.option.option-lg label:after {
  width: 20px;
  height: 20px;
  top: 6px;
  left: 6px;
  font-size: .75rem
}

.option.option-lg input[type="checkbox"]:checked+label:after {
  width: 32px;
  height: 32px;
  opacity: 1;
  top: 0;
  left: 0
}

.radio-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.radio-group>*:not(last-child) {
  margin-right: 20px
}

.radio-group .radio input {
  display: none;
  visibility: hidden;
  opacity: 0
}

.radio-group .radio input:checked+label:before {
  border-color: #37ca37
}

.radio-group .radio input:checked+label:after {
  opacity: 1
}

.radio-group .radio label {
  display: block;
  color: #2a3135;
  position: relative;
  padding-left: 21px;
  cursor: pointer
}

.radio-group .radio label:before {
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  background-color: #fff;
  border: solid 2px #cdd4dc;
  border-radius: 50%;
  position: absolute;
  top: 2px;
  left: 0;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out
}

.radio-group .radio label:after {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #11d15e;
  position: absolute;
  top: 6px;
  left: 4px;
  opacity: 0;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out
}

.bootstrap-select>.btn.dropdown-toggle {
  background: #f3f8fb;
  border: none;
  padding: 12px 20px;
  height: 50px;
  font-weight: normal
}

.bootstrap-select>.btn.dropdown-toggle:hover,
.bootstrap-select>.btn.dropdown-toggle:focus,
.bootstrap-select>.btn.dropdown-toggle:active {
  background: #ecf3f8;
  outline: none !important;
  -webkit-box-shadow: none;
  box-shadow: none
}

.bootstrap-select>.btn.dropdown-toggle:focus:active {
  background: #ecf3f8;
  outline: none;
  -webkit-box-shadow: none !important;
  box-shadow: none !important
}

.bootstrap-select>.btn.dropdown-toggle:active {
  -webkit-transform: none;
  -ms-transform: none;
  transform: none
}

.bootstrap-select>.btn.dropdown-toggle.bs-placeholder .filter-option {
  color: #afb8bc;
  font-weight: normal
}

.bootstrap-select>.btn.dropdown-toggle .filter-option {
  color: #607179;
  top: 5px
}

.bootstrap-select>.btn.dropdown-toggle:after {
  content: "\e904";
  display: block;
  margin: 0;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-family: 'Magicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 11px;
  height: 10px;
  font-size: 10px;
  border: none !important
}

.bootstrap-select>.dropdown-menu {
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  border: none
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a {
  color: #607179;
  font-size: .875rem
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a:hover,
.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a:focus,
.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a:active {
  background: #f2f7fa
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a:focus {
  background: #f2f7fa
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li a:focus:active {
  background: #f2f7fa
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li.selected a {
  color: #188bf6
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li.selected a.active {
  background: none
}

.bootstrap-select>.dropdown-menu .inner .dropdown-menu li.selected a.active:hover,
.bootstrap-select>.dropdown-menu .inner .dropdown-menu li.selected a.active:focus,
.bootstrap-select>.dropdown-menu .inner .dropdown-menu li.selected a.active:active {
  background: #f2f7fa
}

.bootstrap-select.show>.btn.dropdown-toggle {
  border-color: #bbd5e5
}

.bootstrap-select.fit-width>.btn.dropdown-toggle {
  padding-right: 50px
}

.bootstrap-select.fit-width>.btn.dropdown-toggle:before {
  display: none
}

.bootstrap-select.fit-width>.btn.dropdown-toggle .filter-option {
  padding: 0
}

.bootstrap-select.--blue>.btn.dropdown-toggle {
  background-color: rgba(24, 139, 246, 0.1);
  color: #188bf6
}

.bootstrap-select.--blue>.btn.dropdown-toggle .filter-option {
  color: #188bf6
}

.bootstrap-select.--blue>.btn.dropdown-toggle .filter-option {
  ccolor: #188bf6
}

.text-select>.btn.dropdown-toggle {
  background: none !important;
  padding: 0 !important;
  height: 24px;
  min-width: 0 !important;
  color: #188bf6 !important
}

.text-select>.btn.dropdown-toggle:after {
  display: none !important
}

.text-select>.btn.dropdown-toggle .filter-option {
  color: #188bf6 !important
}

.text-select .dropdown-menu li .option-avatar {
  width: 25px;
  height: 25px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 5px
}

.more-select {
  width: 20px !important
}

.more-select>.btn.dropdown-toggle {
  background: transparent;
  min-width: auto;
  border-radius: 4px !important;
  width: 20px;
  height: 30px;
  padding: 0;
  position: relative
}

.more-select>.btn.dropdown-toggle:hover {
  background: #f2f7fa
}

.more-select>.btn.dropdown-toggle:hover:before {
  color: #607179
}

.more-select>.btn.dropdown-toggle .filter-option {
  display: none !important
}

.more-select>.btn.dropdown-toggle:before {
  content: "\e92a";
  display: block;
  font-family: "Magicons" !important;
  speak: none;
  font-size: 1rem;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  position: absolute;
  top: 50%;
  right: 50%;
  -webkit-transform: translate(50%, -50%);
  -ms-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  color: #afb8bc;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.more-select>.btn.dropdown-toggle:after {
  display: none
}

.more-select>.dropdown-menu {
  min-width: 200px !important
}

@media (min-width: 576px) {
  .more-select>.dropdown-menu {
    left: auto !important;
    right: 0 !important
  }
}

.more-select.show>.btn.dropdown-toggle {
  background: #f2f7fa
}

.more-select.show>.btn.dropdown-toggle:before {
  color: #607179
}

.tgl {
  display: none
}

.tgl,
.tgl:after,
.tgl:before,
.tgl *,
.tgl *:after,
.tgl *:before,
.tgl+.tgl-btn {
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.tgl::-moz-selection,
.tgl:after::-moz-selection,
.tgl:before::-moz-selection,
.tgl *::-moz-selection,
.tgl *:after::-moz-selection,
.tgl *:before::-moz-selection,
.tgl+.tgl-btn::-moz-selection {
  background: none
}

.tgl::selection,
.tgl:after::selection,
.tgl:before::selection,
.tgl *::selection,
.tgl *:after::selection,
.tgl *:before::selection,
.tgl+.tgl-btn::selection {
  background: none
}

.tgl+.tgl-btn {
  outline: 0;
  display: block;
  width: 36px;
  height: 20px;
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  margin-bottom: 0
}

.tgl+.tgl-btn:after,
.tgl+.tgl-btn:before {
  position: relative;
  display: block;
  content: "";
  width: 50%;
  height: 100%
}

.tgl+.tgl-btn:after {
  left: 0
}

.tgl+.tgl-btn:before {
  display: none
}

.tgl:checked+.tgl-btn:after {
  left: 50%
}

.tgl-light+.tgl-btn {
  background: rgba(175, 184, 188, 0.2);
  border-radius: 2em;
  padding: 2px;
  -webkit-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease
}

.tgl-light+.tgl-btn:after {
  border-radius: 50%;
  background: #afb8bc;
  -webkit-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease
}

.tgl-light:checked+.tgl-btn {
  background: rgba(55, 202, 55, 0.2)
}

.tgl-light:checked+.tgl-btn:after {
  background: #37ca37
}

.progress {
  border-radius: 100px;
  background-color: #f2f7fa;
  height: 4px
}

.progress.-bar {
  background-color: #188bf6
}

.progress.--blue .progress-bar {
  background-color: #188bf6
}

.progress.--green .progress-bar {
  background-color: #37ca37
}

.progress.--red .progress-bar {
  background-color: #e93d3d
}

.progress.--yellow .progress-bar {
  background-color: #ffbc00
}

.semi-progress {
  position: relative
}

.semi-progress .bar-wrap {
  position: relative;
  overflow: hidden;
  width: 200px;
  height: 100px;
  margin-left: auto;
  margin-right: auto
}

.semi-progress .bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: 5px solid #f2f7fa;
  border-bottom-color: #188bf6;
  border-right-color: #188bf6
}

.form-date {
  position: relative
}

.form-date .form-control {
  padding-right: 35px
}

.form-date .icon {
  color: rgba(96, 113, 121, 0.5);
  position: absolute;
  bottom: 17px;
  right: 20px
}

.help-block {
  font-size: .6875rem;
  color: #afb8bc;
  margin-top: 5px
}

.help-block.--right {
  text-align: right
}

.form-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between
}

.form-footer .attach-files {
  color: #2a3135
}

.form-footer .attach-files .icon {
  margin-right: 10px;
  position: relative;
  top: 2px
}

.form-footer .btns {
  -ms-flex-item-align: end;
  align-self: flex-end
}

.form-footer .btns .btn:not(:last-child) {
  margin-right: 10px
}

.dropdown.--no-caret .dropdown-toggle:after {
  display: none
}

.dropdown .dropdown-menu {
  border: none;
  -webkit-box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1)
}

.dropdown .dropdown-menu li a {
  color: #607179;
  font-size: .875rem
}

.dropdown .dropdown-menu li a:hover,
.dropdown .dropdown-menu li a:focus,
.dropdown .dropdown-menu li a:active {
  background: #f2f7fa
}

.dropdown .dropdown-menu li a:focus {
  background: #f2f7fa
}

.dropdown .dropdown-menu li a:focus:active {
  background: #f2f7fa
}

.dropdown .dropdown-menu li .option-avatar {
  width: 25px;
  height: 25px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 10px
}

.dropdown .dropdown-menu .dropdown-item {
  color: #607179;
  font-size: .875rem
}

.dropdown .dropdown-menu .dropdown-item:hover,
.dropdown .dropdown-menu .dropdown-item:focus,
.dropdown .dropdown-menu .dropdown-item:active {
  background: #f2f7fa
}

.dropdown .dropdown-menu .dropdown-item:focus {
  background: #f2f7fa
}

.dropdown .dropdown-menu .dropdown-item:focus:active {
  background: #f2f7fa
}

.btn {
  font-family: Roboto, system, -apple-system, BlinkMacSystemFont, ".SFNSDisplay-Regular", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: .875rem;
  font-weight: 500;
  color: #fff;
  padding: 11px 20px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  min-width: 85px;
  border-radius: .3125rem;
  border: none
}

.btn:hover,
.btn:focus,
.btn:active {
  color: #fff;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none
}

.btn:active:focus {
  outline: none;
  -webkit-box-shadow: none !important;
  box-shadow: none !important
}

.btn:active {
  -webkit-transform: translateY(2px);
  -ms-transform: translateY(2px);
  transform: translateY(2px)
}

.btn.btn-sm,
.btn-group-sm>.btn {
  padding: 9.5px 15px;
  height: 40px;
  font-size: .875rem;
  font-weight: normal;
  min-width: 0
}

.btn-square {
  width: 45px;
  height: 45px;
  line-height: 47px;
  padding: 0;
  text-align: center;
  min-width: auto;
  font-size: .875rem
}

.btn-circle {
  width: 45px;
  height: 45px;
  line-height: 47px;
  padding: 0;
  text-align: center;
  border-radius: 50px;
  min-width: auto;
  font-size: 1rem
}

.btn.btn-link {
  color: #188bf6;
  padding-left: 0;
  padding-right: 0
}

.btn.btn-link:hover,
.btn.btn-link:focus,
.btn.btn-link:active {
  text-decoration: none
}

.btn.btn-link2 {
  color: #607179;
  padding-left: 0;
  padding-right: 0
}

.btn.btn-link2:hover,
.btn.btn-link2:focus,
.btn.btn-link2:active {
  color: #188bf6;
  text-decoration: none
}

.btn.btn-yellow {
  background-color: rgba(255, 188, 0, 0.1);
  border-color: rgba(255, 188, 0, 0.1);
  color: #ffbc00
}

.btn.btn-yellow:hover,
.btn.btn-yellow:focus,
.btn.btn-yellow:active {
  color: #ffbc00;
  background-color: rgba(255, 188, 0, 0.2);
  border-color: rgba(255, 188, 0, 0.2)
}

.btn.btn-primary {
  background-color: rgba(24, 139, 246, 0.1);
  border-color: rgba(24, 139, 246, 0.1);
  color: #188bf6
}

.btn.btn-primary:hover,
.btn.btn-primary:focus,
.btn.btn-primary:active {
  color: #188bf6 !important;
  background-color: rgba(24, 139, 246, 0.2) !important;
  border-color: rgba(24, 139, 246, 0.2) !important
}

.btn.btn-blue {
  background-color: #188bf6;
  border-color: #188bf6;
  color: #fff
}

.btn.btn-blue:hover,
.btn.btn-blue:focus,
.btn.btn-blue:active {
  color: #fff;
  background-color: #188bf6;
  border-color: #188bf6
}

.btn.btn-light {
  background: #fff;
  border-color: #f2f7fa;
  color: #607179
}

.btn.btn-light:hover,
.btn.btn-light:focus,
.btn.btn-light:active {
  background: rgba(24, 139, 246, 0.1);
  color: #188bf6
}

.btn.btn-light2 {
  background: #f7fafc;
  border-color: #f7fafc;
  color: #607179
}

.btn.btn-light2:hover,
.btn.btn-light2:focus,
.btn.btn-light2:active {
  background: #e4eef5;
  color: #607179
}

.btn.btn-light3 {
  background: #fff;
  border: 1px solid #e0ecf3;
  color: #607179
}

.btn.btn-light3:hover,
.btn.btn-light3:focus,
.btn.btn-light3:active {
  background: #f2f7fa;
  color: #607179
}

.btn.btn-green-lt {
  background-color: rgba(55, 202, 55, 0.1);
  border-color: rgba(55, 202, 55, 0.1);
  color: #37ca37
}

.btn.btn-green-lt:hover,
.btn.btn-green-lt:focus,
.btn.btn-green-lt:active {
  color: #37ca37;
  background-color: rgba(55, 202, 55, 0.2);
  border-color: rgba(55, 202, 55, 0.2)
}

.btn.btn-orange {
  background: #ff7402;
  border-color: #ff7402;
  color: #fff
}

.btn.btn-orange:hover,
.btn.btn-orange:focus,
.btn.btn-orange:active {
  background: #ce5d00;
  color: #fff
}

.card {
  background: #fff;
  border: none;
  margin-bottom: 20px
}

.card-header {
  background: #fff;
  border-bottom: 2px solid #f2f7fa;
  padding: 25px 50px 20px 30px;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  z-index: 2
}

.card-header.--center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.card-header.--no-right-padding {
  padding: 25px 30px
}

.card-header.--blue {
  background-color: #e7f3fe
}

.card-header.--blue .icon,
.card-header.--blue h2,
.card-header.--blue h3 {
  color: #188bf6 !important
}

.card-header h2,
.card-header h3 {
  font-size: 1rem;
  margin-bottom: 0;
  line-height: 1;
  margin-right: 20px;
  font-weight: normal;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.card-header h2 span,
.card-header h3 span {
  display: inline-block;
  color: #afb8bc;
  font-size: .875rem;
  margin-left: 10px
}

.card-header h2 img,
.card-header h3 img {
  margin-right: 10px
}

.card-header .more-select {
  position: absolute;
  top: 50%;
  left: auto !important;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

@media (min-width: 992px) {
  .card-header .more-select>.dropdown-menu {
    left: auto !important;
    right: 0 !important;
    -webkit-transform: translateX(0) translateY(40px) !important;
    -ms-transform: translateX(0) translateY(40px) !important;
    transform: translateX(0) translateY(40px) !important
  }
}

.card-header .card-control-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.card-header .card-control-right>*:not(:last-child) {
  margin-right: 15px
}

.card-header .list-inline {
  margin-bottom: 0;
  margin-right: 25px !important
}

.card-header .list-inline-item:not(:last-child) {
  margin-right: 15px;
  padding-right: 15px;
  position: relative
}

.card-header .list-inline-item:not(:last-child):before {
  content: "";
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #afb8bc;
  position: absolute;
  top: 50%;
  right: -2px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.card-header .list-inline-item.active {
  color: #188bf6
}

.card-header.--non-flex {
  display: block !important
}

.card-header-nav {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.card-header-nav h4 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 1;
  margin-left: 10px;
  margin-right: 10px
}

.card-header-nav h5 {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 1;
  margin-left: 10px;
  margin-right: 10px
}

.card-header-nav a {
  color: #767b80
}

.card-header-nav a:hover,
.card-header-nav a:focus,
.card-header-nav a:active {
  color: #2a3135
}

.card-header-nav>* {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 10px
}

.card-header-tab {
  margin-bottom: -10px
}

@media (min-width: 576px) {
  .card-header-tab {
    margin-bottom: -21px
  }
}

.card-header-tab ul {
  padding: 0;
  margin: 0;
  list-style: none
}

@media (min-width: 576px) {
  .card-header-tab ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
  }
  .card-header-tab ul>*:not(last-child) {
    margin-right: 30px
  }
}

@media (min-width: 576px) {
  .card-header-tab ul li {
    padding-bottom: 13px;
    position: relative
  }
  .card-header-tab ul li:before {
    content: "";
    display: block;
    width: 65px;
    height: 4px;
    background: transparent;
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    bottom: 0
  }
}

.card-header-tab ul li a {
  color: #607179;
  font-size: 12px;
  text-transform: uppercase
}

@media (min-width: 576px) {
  .card-header-tab ul li.active:before {
    background: #188bf6
  }
  .card-header-tab ul li.active a {
    font-weight: 500
  }
}

.card-header-tab ul li.active a {
  color: #188bf6;
  font-weight: 700
}

.card-body {
  padding: 20px 30px
}

.card-body.--no-padding {
  padding: 0 !important
}

.card-footer {
  background: none;
  border-top: 2px solid #f2f7fa
}

.card+.hl_controls {
  margin-top: 40px
}

@media (min-width: 992px) {
  .card-group {
    margin-bottom: 20px
  }
  .card-group>.card {
    margin-right: 10px
  }
  .card-group>.card:last-child {
    margin-right: 0
  }
  .card-group.--wide-gutter {
    margin-bottom: 20px
  }
  .card-group.--wide-gutter>.card {
    margin-right: 20px
  }
  .card-group.--wide-gutter>.card:last-child {
    margin-right: 0
  }
  .card-group.--wider-gutter {
    margin-bottom: 30px
  }
  .card-group.--wider-gutter>.card {
    margin-right: 30px
  }
  .card-group.--wider-gutter>.card:last-child {
    margin-right: 0
  }
}

.avatar {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 44px;
  border-radius: 50%
}

.avatar_text>h4 {
  font-size: 14px;
  font-weight: normal;
  white-space: nowrap;
  margin-bottom: 5px;
  line-height: 1
}

.avatar_text>h4 span {
  color: #607179
}

.avatar_text p {
  color: #607179;
  line-height: 1
}

.avatar_img {
  min-width: 44px;
  width: 44px;
  height: 44px;
  line-height: 44px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden
}

.avatar_img:first-child {
  position: relative;
  z-index: 2
}

.avatar_img>img {
  max-width: 44px;
  max-height: 44px;
  border-radius: 50%
}

.avatar_img+.avatar_img {
  margin-left: -20px
}

.avatar_img+h4 {
  margin-left: 10px
}

.avatar_img+.avatar_text {
  margin-left: 10px
}

.avatar_img.--blue {
  background-color: #188bf6
}

.avatar_img.--navy {
  background-color: #1976d2
}

.avatar_img.--red {
  background-color: #e93d3d
}

.avatar_img.--yellow {
  background-color: #ffbc00
}

.avatar_img.--green {
  background-color: #37ca37
}

.avatar_img.--purple {
  background-color: #876cff
}

.avatar_img.--teal {
  background-color: #17cfbc
}

.avatar_img.--pink {
  background-color: #ff3e7f
}

.avatar_img.--orange {
  background-color: #ff7402
}

.avatar_img.--lime {
  background-color: #7be43b
}

.avatar_img.--gray {
  background-color: #afb8bc
}

.avatar_img.--shadow {
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1)
}

.avatar>p+.avatar_img {
  margin-left: 10px
}

.avatar>h4 {
  font-size: 14px;
  font-weight: normal;
  white-space: nowrap;
  margin-bottom: 0;
  line-height: 1
}

.avatar>h4 span {
  color: #607179
}

.avatar:last-child {
  margin-right: 0
}

.avatar.--sm {
  height: 35px
}

.avatar.--sm .avatar_img {
  min-width: 35px;
  width: 35px;
  height: 35px;
  line-height: 35px;
  font-size: .75rem
}

.avatar.--sm .avatar_img>img {
  max-width: 35px;
  max-height: 35px
}

.avatar.--lg {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 150px;
  height: 150px
}

.avatar.--lg .avatar_img {
  min-width: 3150x;
  width: 150px;
  height: 150px;
  line-height: 150px;
  font-size: 1.5rem;
  font-weight: 500;
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  border: solid 6px #fff
}

.avatar.--lg .avatar_img>img {
  max-width: 150px;
  max-height: 150px
}

.icon.--blue {
  color: #188bf6
}

.icon.--yellow {
  color: #ffbc00
}

.icon.--green {
  color: #37ca37
}

.icon.--red {
  color: #e93d3d
}

.icon.--orange {
  color: #ff7402
}

.icon.--purple {
  color: #876cff
}

.icon.--teal {
  color: #17cfbc
}

.icon.--gray {
  color: #607179
}

.icon.--gray-drk {
  color: #2a3135
}

.icon.--gray-lt {
  color: #afb8bc
}

.icon-positive {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-positive.png") no-repeat center;
  background-size: 16px
}

.icon-negative {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-negative.png") no-repeat center;
  background-size: 16px
}

.icon-neutral {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-neutral.png") no-repeat center;
  background-size: 16px
}

.icon-send {
  display: inline-block;
  width: 16px;
  height: 14px;
  background: url("/pmd/img/icon-send.svg") no-repeat center;
  background-size: 16px 14px;
  -webkit-transform: translateY(4px);
  -ms-transform: translateY(4px);
  transform: translateY(4px)
}

.icon-send2 {
  display: inline-block;
  width: 16px;
  height: 14px;
  background: url("/pmd/img/icon-send2.svg") no-repeat center;
  background-size: 16px 14px
}

.icon-send3 {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-send3.svg") no-repeat center;
  background-size: 16px;
  position: relative;
  top: 2px
}

.icon-eye {
  display: inline-block;
  width: 16px;
  height: 11px;
  background: url("/pmd/img/icon-eye.svg") no-repeat center;
  background-size: 16px 11px;
  -webkit-transform: translateY(2px);
  -ms-transform: translateY(2px);
  transform: translateY(2px)
}

.icon-eye2 {
  display: inline-block;
  width: 16px;
  height: 11px;
  background: url("/pmd/img/icon-eye2.svg") no-repeat center;
  background-size: 16px 11px;
  -webkit-transform: translateY(2px);
  -ms-transform: translateY(2px);
  transform: translateY(2px)
}

.icon-warning:before {
  content: "!";
  font-size: 1.125rem;
  font-weight: 500
}

.icon-google {
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #fff url("/pmd/img/logo-google.png") no-repeat center;
  background-size: 36px;
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1)
}

.icon-facebook {
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: url("/pmd/img/logo-facebook.png") no-repeat center;
  background-size: 36px
}

.icon-twitter {
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: url("/pmd/img/logo-twitter.png") no-repeat center;
  background-size: 36px
}

.icon-twitter2 {
  display: inline-block;
  width: 12px;
  height: 10px;
  background: url("/pmd/img/icon-twitter2.svg") no-repeat center;
  background-size: 12px 10px
}

.icon-linkedin {
  display: inline-block;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: url("/pmd/img/logo-linkedin.png") no-repeat center;
  background-size: 36px
}

.icon-sms {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-sms.svg") no-repeat center;
  background-size: 16px
}

.icon-attach {
  display: inline-block;
  width: 10px;
  height: 16px;
  background: url("/pmd/img/icon-attach.svg") no-repeat center;
  background-size: 10px 16px
}

.icon-phone {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-phone2.svg") no-repeat center;
  background-size: 16px 16px
}

.icon-note {
  display: inline-block;
  width: 14px;
  height: 16px;
  background: url("/pmd/img/icon-note.svg") no-repeat center;
  background-size: 14px 16px
}

.icon-task {
  display: inline-block;
  width: 17px;
  height: 14px;
  background: url("/pmd/img/icon-task.svg") no-repeat center;
  background-size: 17px 14px
}

.icon-doc {
  display: inline-block;
  width: 13px;
  height: 16px;
  background: url("/pmd/img/icon-doc.svg") no-repeat center;
  background-size: 13px 16px
}

.icon-pdf {
  display: inline-block;
  width: 13px;
  height: 16px;
  background: url("/pmd/img/icon-pdf.svg") no-repeat center;
  background-size: 13px 16px
}

.icon-google-drive {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/logo-google-drive.png") no-repeat center;
  background-size: 16px
}

.icon-messenger {
  display: inline-block;
  width: 12px;
  height: 13px;
  background: url("/pmd/img/icon-messenger.svg") no-repeat center;
  background-size: 12px 13px
}

.icon-sms2 {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("/pmd/img/icon-sms2.svg") no-repeat center;
  background-size: 12px
}

.icon-google2 {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("/pmd/img/icon-google.svg") no-repeat center;
  background-size: 12px
}

.icon-file {
  display: inline-block;
  width: 11px;
  height: 16px;
  background: url("/pmd/img/icon-file.svg") no-repeat center;
  background-size: 11px 16px
}

.icon-emoji {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-emoji.svg") no-repeat center;
  background-size: 16px
}

.icon-facebooksm {
  display: inline-block;
  width: 10px;
  height: 15px;
  background: url("/pmd/img/icon-facebooksm.svg") no-repeat center;
  background-size: 10px 15px
}

.icon-twittersm {
  display: inline-block;
  width: 16px;
  height: 12px;
  background: url("/pmd/img/icon-twittersm.svg") no-repeat center;
  background-size: 16px 12px
}

.icon-googleplussm {
  display: inline-block;
  width: 15px;
  height: 15px;
  background: url("/pmd/img/icon-googleplussm.svg") no-repeat center;
  background-size: 15px
}

.icon-googlesm {
  display: inline-block;
  width: 15px;
  height: 15px;
  background: url("/pmd/img/logo-google2.png") no-repeat center;
  background-size: 15px
}

.rating {
  display: block
}

.rating .icon {
  color: #ffbc00;
  font-size: 1.125rem;
  display: inline-block
}

.rating .icon+.icon {
  margin-left: 4px
}

.rating.--sm .icon {
  font-size: .75rem
}

.rating.--sm .icon+.icon {
  margin-left: 2px
}

.rating.--sm .icon.icon-star {
  color: #dfe3e4
}

.table {
  margin-bottom: 0
}

.table thead tr th {
  border: none;
  color: #607179;
  font-weight: normal;
  padding: 12px 10px
}

.table thead tr th:first-child {
  padding-left: 30px
}

.table thead tr th:last-child {
  padding-right: 30px
}

.table thead tr th[data-sort='string'],
.table thead tr th[data-sort='float'],
.table thead tr th[data-sort='int'] {
  cursor: pointer
}

.table thead tr th.sorting-asc,
.table thead tr th.sorting-desc {
  color: #188bf6
}

.table thead tr th.sorting-asc .icon,
.table thead tr th.sorting-desc .icon {
  color: #188bf6
}

.table thead tr th .icon {
  font-size: .625rem;
  margin-left: 5px;
  color: rgba(96, 113, 121, 0.5)
}

.table thead tr th.sorting-desc .icon {
  display: inline-block;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg)
}

.table tbody tr td {
  color: #2a3135;
  border-top: 2px solid #f2f7fa;
  padding: 12px 10px;
  vertical-align: middle
}

.table tbody tr td:first-child {
  padding-left: 30px
}

.table tbody tr td:last-child {
  padding-right: 30px
}

@media (min-width: 992px) {
  .table tbody tr td .bootstrap-select.more-select>.dropdown-menu {
    left: auto !important;
    right: 0 !important;
    -webkit-transform: translateX(0) translateY(40px) !important;
    -ms-transform: translateX(0) translateY(40px) !important;
    transform: translateX(0) translateY(40px) !important
  }
}

.table tbody tr.table_warning {
  background-color: #fff8f7
}

.text_warning {
  color: #e93d3d
}

.table_spacer {
  display: inline-block;
  width: 80px
}

.table_status {
  position: relative;
  padding-left: 20px
}

.table_status:before {
  content: "";
  display: block;
  width: 11px;
  height: 4px;
  background: #afb8bc;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.table_status.--opened:before {
  background: #37ca37
}

.table_status.--clicked:before {
  background: #188bf6
}

.table_requests {
  font-size: .875rem;
  color: #90a4ae
}

.table_requests strong {
  font-weight: normal;
  color: #2a3135
}

.table_status--badge {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 50%;
  background: #eee;
  text-align: center
}

.table_status--badge.--good {
  background-color: rgba(55, 202, 55, 0.1);
  color: #37ca37
}

.table_status--badge.--warning {
  background-color: rgba(233, 61, 61, 0.1);
  color: #e93d3d
}

.table_no-value {
  font-size: .875rem;
  color: #afb8bc
}

.table_yes {
  color: #37ca37
}

.table_progress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.table_progress p {
  width: 100px;
  color: #90a4ae;
  margin-right: 15px;
  text-align: right
}

.table_progress p strong {
  font-size: 1rem;
  color: #2a3135;
  font-weight: normal
}

.table_progress p strong.--positive {
  color: #37ca37;
  font-weight: 500
}

.table_progress .progress {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  width: 150px;
  max-width: 150px
}

.table_social-icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.table_social-icon .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 1rem;
  min-width: 40px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #000;
  margin-right: 10px;
  color: #fff
}

.table_social-icon .icon.--facebook {
  background-color: #2877c1
}

.table_social-icon .icon.--google-plus {
  background-color: #ff5437
}

.table_social-icon .icon.--instagram {
  background-color: #f942b4
}

.table_social-icon .icon.--linkedin {
  background-color: #2877c1
}

.table_social-icon .icon.--twitter {
  background-color: #65aff5
}

.table_social-icon .icon.--blue {
  background-color: #188bf6
}

.table_social-icon .icon.--yellow {
  background-color: #ffbc00
}

.table_social-icon .icon.--green {
  background-color: #37ca37
}

.table_social-icon .icon.--red {
  background-color: #e93d3d
}

.table_social-icon .icon.--orange {
  background-color: #ff7402
}

.table_social-icon .icon.--purple {
  background-color: #876cff
}

.table_social-icon .icon.--teal {
  background-color: #17cfbc
}

.table_social-icon .icon.--gray {
  background-color: #607179
}

.table_social-icon .icon.--gray-drk {
  background-color: #2a3135
}

.table_social-icon .icon.--gray-lt {
  background-color: #afb8bc
}

.table_social-total span {
  color: #37ca37;
  font-style: italic;
  margin-left: 10px
}

@media (max-width: 991px) {
  .table-wrap table,
  .table-wrap thead,
  .table-wrap tbody,
  .table-wrap th,
  .table-wrap td,
  .table-wrap tr {
    display: block
  }
  .table-wrap thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px
  }
  .table-wrap td {
    border: none;
    border-bottom: 1px solid #f2f7fa;
    position: relative;
    padding-left: 50% !important;
    white-space: normal;
    text-align: left
  }
  .table-wrap td:before {
    color: #607179;
    position: absolute;
    top: 8px;
    left: 15px;
    width: 45%;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    font-weight: normal
  }
  .table-wrap td:first-child {
    padding-top: 17px
  }
  .table-wrap td:last-child {
    padding-bottom: 16px
  }
  .table-wrap td:first-child:before {
    top: 17px
  }
  .table-wrap td:before {
    content: attr(data-title)
  }
  .table tbody tr {
    border-top: 1px solid #f2f7fa
  }
  .table tbody tr:nth-child(odd) {
    background: rgba(242, 247, 250, 0.4)
  }
  .table tbody tr td {
    border: none
  }
}

.modal .modal-dialog {
  max-width: 680px;
  margin-left: .5rem;
  margin-right: .5rem
}

@media (min-width: 720px) {
  .modal .modal-dialog {
    margin-left: auto;
    margin-right: auto
  }
}

.modal .modal-dialog.--full {
  max-width: 1200px;
  margin-top: 10px;
  margin-left: .5rem;
  margin-right: .5rem
}

@media (min-width: 1230px) {
  .modal .modal-dialog.--full {
    margin-left: auto;
    margin-right: auto
  }
}

.modal .modal-dialog.--small {
  max-width: 540px
}

.modal .modal-dialog.--small .modal-body--inner {
  max-width: 480px;
  margin-left: auto;
  margin-right: auto
}

.modal .modal-dialog.--large {
  max-width: 1260px
}

.modal .modal-dialog.--large .modal-body--inner {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto
}

.modal .modal-dialog.--large .modal-footer--inner {
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto
}

.modal .modal-content {
  -webkit-box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 5px 8.7px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  border: none
}

.modal .close {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 1.875rem;
  font-weight: normal;
  z-index: 2;
  font-weight: 300;
  color: #afb8bc;
  text-shadow: none;
  opacity: 1
}

.modal .close:hover,
.modal .close:focus,
.modal .close:active {
  color: #607179
}

.modal .modal-header {
  border-bottom: 2px solid #f2f7fa;
  padding: 25px 15px;
  display: block
}

.modal .modal-header.--no-border {
  border-bottom: none
}

.modal .modal-header--inner {
  max-width: 600px;
  margin-left: auto;
  margin-right: auto
}

.modal .modal-title {
  font-size: 1rem
}

.modal .modal-title .icon {
  margin-right: 5px
}

.modal .modal-body {
  padding: 30px 15px
}

.modal .modal-body--inner {
  max-width: 600px;
  margin-left: auto;
  margin-right: auto
}

.modal .modal-buttons {
  padding-top: 15px;
  padding-bottom: 15px
}

.modal .modal-footer {
  border-top: 2px solid #f2f7fa;
  padding: 25px 15px;
  display: block
}

.modal .modal-footer--inner {
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.modal .modal-footer--inner>:not(:first-child) {
  margin-left: .25rem
}

.modal .modal-footer--inner>:not(:last-child) {
  margin-right: .25rem
}

.modal.review-link--modal .modal-dialog {
  max-width: 550px
}

.modal.review-link--modal .modal-content {
  background-color: #188bf6;
  color: #fff;
  text-align: center
}

.modal.review-link--modal .close {
  color: #fff
}

.modal.review-link--modal .modal-body {
  padding: 30px
}

.modal.review-link--modal img {
  max-width: 150px;
  height: auto;
  margin-bottom: 30px
}

.modal.review-link--modal h4 {
  color: #fff;
  font-size: .875rem;
  font-weight: 500;
  margin-bottom: 10px
}

.modal-backdrop {
  background-color: rgba(42, 49, 53, 0.8)
}

.modal-backdrop.show {
  opacity: 1
}

.hl_controls {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between
}

.hl_controls>*:not(:last-child) {
  margin-right: 20px
}

.hl_controls .hl_controls--left h1,
.hl_controls .hl_controls--left h2,
.hl_controls .hl_controls--left h3 {
  font-size: 1.25rem;
  margin: 0
}

.hl_controls .hl_controls--left h1 span,
.hl_controls .hl_controls--left h2 span,
.hl_controls .hl_controls--left h3 span {
  font-size: .875rem;
  color: #afb8bc;
  display: inline-block;
  margin-left: 10px
}

.hl_controls .hl_controls--left .bootstrap-select {
  margin-right: 20px
}

.hl_controls .hl_controls--left .bootstrap-select>.btn.dropdown-toggle {
  height: 40px;
  padding-left: 0;
  padding-right: 25px;
  background: none !important
}

.hl_controls .hl_controls--left .bootstrap-select>.btn.dropdown-toggle:after {
  right: 0;
  color: #188bf6
}

.hl_controls .hl_controls--left .bootstrap-select>.btn.dropdown-toggle .filter-option {
  font-size: 1.25rem;
  font-weight: 400;
  color: #2a3135;
  margin-top: -10px
}

.hl_controls .hl_controls--left .total {
  display: inline-block;
  color: #afb8bc
}

.hl_controls .hl_controls--right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_controls .hl_controls--right>* {
  margin-bottom: 10px
}

.hl_controls .hl_controls--right>*:not(:last-child) {
  margin-right: 10px
}

.hl_controls .hl_controls--right .bootstrap-select>.btn.dropdown-toggle {
  background-color: #fff;
  padding: 9.5px 50px 9.5px 20px;
  height: 40px !important
}

.hl_controls .hl_controls--right .bootstrap-select>.btn.dropdown-toggle:after {
  color: #188bf6
}

.hl_controls .hl_controls--right .bootstrap-select>.btn.dropdown-toggle .filter-option {
  font-weight: normal;
  color: #2a3135
}

.hl_controls .hl_controls--right .search-form {
  position: relative
}

@media (min-width: 992px) {
  .hl_controls .hl_controls--right .search-form {
    min-width: 300px
  }
}

.hl_controls .hl_controls--right .search-form .icon {
  position: absolute;
  top: 50%;
  left: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: rgba(96, 113, 121, 0.5)
}

.hl_controls .hl_controls--right .search-form input {
  padding: 10px 20px 10px 50px;
  height: 40px
}

.hl_controls .hl_controls--right .hl_reviews--filter-btn:focus {
  background: #fff;
  color: #607179
}

.hl_controls .hl_controls--right .hl_reviews--filter-btn.--open {
  color: #188bf6;
  background: rgba(24, 139, 246, 0.1)
}

.hl_controls .hl_controls--right .btn:not(.btn-sm) {
  font-weight: bold;
  padding: 9.5px 20px
}

.hl_controls .hl_controls--right .btn:not(.btn-sm) .icon {
  margin-right: 10px
}

.hl_controls .hl_controls--right .btn-sm,
.hl_controls .hl_controls--right .btn-group-sm>.btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_controls .hl_controls--right .btn-sm>span+.icon,
.hl_controls .hl_controls--right .btn-group-sm>.btn>span+.icon {
  margin-left: 10px
}

.hl_controls .hl_controls--right .btn-sm .icon,
.hl_controls .hl_controls--right .btn-group-sm>.btn .icon {
  display: block;
  width: 16px;
  color: #188bf6;
  font-size: 1rem
}

.hl_controls .hl_controls--right .form-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 10px;
  margin-right: 20px
}

.hl_controls .hl_controls--right .form-group label {
  margin-bottom: 0;
  margin-right: 10px;
  font-size: 14px
}

.hl_dashboard--reviews .card-body {
  padding: 30px
}

@media (min-width: 576px) {
  .hl_dashboard--reviews .card-body {
    padding: 40px 50px
  }
}

.hl_dashboard--reviews h3 {
  font-size: 3.125rem;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 20px
}

.hl_dashboard--reviews-stats>div {
  margin-bottom: 20px
}

.hl_dashboard--reviews-stats h4 {
  font-weight: 400;
  font-size: 1.375rem
}

.hl_dashboard--reviews-stats h4 .icon {
  font-size: 1rem;
  color: #37ca37
}

.hl_dashboard--reviews-stats h4 .icon-positive {
  margin-right: 10px
}

.hl_dashboard--reviews-stats p {
  max-width: 200px
}

.hl_dashboard--reviews-sources {
  padding-top: 20px
}

.hl_dashboard--reviews-sources h4 {
  font-weight: 400;
  font-size: .875rem;
  color: #607179;
  margin-bottom: 15px
}

.hl_dashboard--reviews-sources .sources_list li a {
  display: block;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: #eee;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 35px;
  text-indent: -999999px
}

.hl_dashboard--reviews-sources .sources_list li a.google {
  background-image: url("/pmd/img/icon-g+.svg")
}

.hl_dashboard--reviews-sources .sources_list li a.facebook {
  background-image: url("/pmd/img/icon-facebook.svg")
}

.hl_dashboard--reviews-sources .sources_list li a.twitter {
  background-image: url("/pmd/img/icon-twitter.svg")
}

.hl_dashboard--reviews-sources .sources_list li a.add {
  background-image: url("/pmd/img/icon-add.svg")
}

.hl_dashboard--avg-rating .card-body {
  padding: 30px
}

@media (min-width: 576px) {
  .hl_dashboard--avg-rating .card-body {
    padding: 40px 50px
  }
}

.hl_dashboard--avg-rating h3 {
  font-size: 3.125rem;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 20px;
  vertical-align: top
}

.hl_dashboard--avg-rating h3 .rating {
  display: inline-block;
  position: relative;
  top: 5px
}

.hl_dashboard--avg-rating h3 .rating .icon {
  vertical-align: top
}

.hl_dashboard--avg-rating .rating_percentage {
  margin-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_dashboard--avg-rating .rating_percentage h4 {
  font-size: 1.375rem;
  color: #afb8bc;
  font-weight: 400;
  margin-bottom: 0;
  margin-right: 20px;
  position: relative
}

.hl_dashboard--avg-rating .rating_percentage p {
  max-width: 200px
}

.hl_dashboard--avg-rating .rating_stats-item {
  margin-bottom: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.hl_dashboard--avg-rating .rating_stats-item h4 {
  font-size: .875rem;
  margin-bottom: 0;
  margin-right: 20px;
  width: 40px
}

.hl_dashboard--avg-rating .rating_stats-item h4 .icon {
  color: #afb8bc;
  font-size: .625rem
}

.hl_dashboard--avg-rating .rating_stats-item .progress {
  width: 100%;
  border-radius: 0;
  height: 15px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent
}

.hl_dashboard--avg-rating .rating_stats-item .progress .progress-bar {
  text-align: right;
  height: 4px;
  border-radius: 10px
}

.hl_dashboard--avg-rating .rating_stats-item .progress span {
  position: relative;
  right: -10px;
  color: #2a3135
}

.hl_dashboard--avg-rating .rating_stats-item.--star5 .progress .progress-bar {
  background-color: #37ca37
}

.hl_dashboard--avg-rating .rating_stats-item.--star4 .progress .progress-bar {
  background-color: #188bf6
}

.hl_dashboard--avg-rating .rating_stats-item.--star4 .progress .progress-bar {
  background-color: #ffbc00
}

.hl_dashboard--avg-rating .rating_stats-item.--star2 .progress .progress-bar {
  background-color: #ff7402
}

.hl_dashboard--avg-rating .rating_stats-item.--star1 .progress .progress-bar {
  background-color: #e93d3d
}

.hl_dashboard--sentiment .card-body {
  padding-top: 40px;
  padding-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center
}

.hl_dashboard--sentiment hr {
  max-width: 100px;
  margin-top: 30px;
  margin-bottom: 30px
}

.sentiment_review {
  text-align: center
}

.sentiment_review-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 200px;
  margin-left: auto;
  margin-right: auto
}

.sentiment_review h3 {
  font-size: 32px;
  font-weight: 300
}

.sentiment_review h4 {
  font-size: 22px
}

.sentiment_review-graph {
  width: 50px;
  height: 50px;
  position: relative;
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 20px
}

.sentiment_review-graph .percentage {
  display: block;
  width: 50px;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0
}

.sentiment_review.--positive h4 {
  color: #37ca37
}

.sentiment_review.--positive .sentiment_review-graph {
  background: url("/pmd/img/icon-positive.png") no-repeat center;
  background-size: 50px
}

.sentiment_review.--negative h4 {
  color: #e93d3d
}

.sentiment_review.--negative .sentiment_review-graph {
  background: url("/pmd/img/icon-negative.png") no-repeat center;
  background-size: 50px
}

.hl_dashboard--leaderboard-invites .card-body {
  padding-top: 30px;
  padding-bottom: 30px
}

.leaderboard_item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.leaderboard_item+.leaderboard_item {
  margin-top: 20px
}

.leaderboard_item .avatar {
  margin-right: 10px
}

.leaderboard_item>p {
  color: #90a4ae;
  font-size: .875rem;
  line-height: 1
}

.leaderboard_item>p strong {
  font-size: 1.125rem;
  font-weight: normal;
  color: #2a3135
}

.hl_dashboard--invites-goal .card-body {
  padding-top: 40px;
  padding-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.invites-goal_progress {
  width: 100%;
  max-width: 250px;
  text-align: center
}

.invites-goal_progress h3 {
  font-size: 3.438rem;
  font-weight: 300;
  position: relative
}

.invites-goal_progress h3:before {
  content: "";
  display: block;
  width: 16px;
  height: 14px;
  background: url("/pmd/img/icon-send.svg") no-repeat center;
  background-size: 16px 14px;
  position: absolute;
  margin: auto;
  top: -36px;
  left: 0;
  right: 0
}

.invites-goal_progress hr {
  max-width: 110px;
  margin-bottom: 30px
}

.invites-goal_progress h4 {
  font-size: 1rem;
  margin-bottom: 5px
}

@media (min-width: 992px) {
  .hl_dashboard--latest-review-requests .table tbody tr td:last-child {
    width: 150px
  }
}

.hl_dashboard--latest-review-requests .table tbody tr td:last-child .btn-blue {
  margin-right: 10px
}

.dashboard_emails-sent-chart .highcharts-credits {
  display: none !important
}

.dashboard_emails-sent-chart-stats {
  margin-top: 20px;
  margin-bottom: 20px;
  margin-left: 10px
}

.dashboard_emails-sent-chart-stats .list-inline-item {
  margin-bottom: 20px
}

.dashboard_emails-sent-chart-stats .list-inline-item:first-child p {
  padding-left: 0
}

.dashboard_emails-sent-chart-stats .list-inline-item:first-child p:before {
  display: none
}

.dashboard_emails-sent-chart-stats .list-inline-item:nth-child(2) p:before {
  background-color: #ffbc00
}

.dashboard_emails-sent-chart-stats .list-inline-item:nth-child(3) p:before {
  background-color: #37ca37
}

.dashboard_emails-sent-chart-stats .list-inline-item:nth-child(4) p:before {
  background-color: #188bf6
}

.dashboard_emails-sent-chart-stats .list-inline-item:nth-child(5) p:before {
  background-color: #e93d3d
}

.dashboard_emails-sent-chart-stats .list-inline-item:not(:last-child) {
  margin-right: 20px
}

@media (min-width: 576px) {
  .dashboard_emails-sent-chart-stats .list-inline-item:not(:last-child) {
    margin-right: 50px
  }
}

.dashboard_emails-sent-chart-stats h4 {
  margin-bottom: 5px;
  font-size: 2.125rem;
  font-weight: 300
}

@media (max-width: 1200px) {
  .dashboard_emails-sent-chart-stats h4 {
    font-size: calc(1.125rem + 1.33333vw)
  }
}

.dashboard_emails-sent-chart-stats p {
  position: relative;
  padding-left: 25px
}

.dashboard_emails-sent-chart-stats p:before {
  content: "";
  display: block;
  width: 15px;
  height: 4px;
  background-color: #eee;
  border-radius: 4px;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.hl_reviews--group {
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  min-height: 640px
}

@media (min-width: 768px) {
  .hl_reviews--group.--open {
    padding-right: 300px
  }
}

.hl_reviews--item {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.hl_reviews--item.active {
  background-color: #E7F3FE
}

.hl_reviews--item .card-header {
  padding: 15px 30px 0 30px;
  border-bottom: 0;
  background: transparent
}

.hl_reviews--item .card-header .card-header--left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_reviews--item .card-header .card-header--left>*+* {
  margin-left: 15px
}

.hl_reviews--item .card-header .date {
  font-size: .75rem
}

.hl_reviews--item .card-footer {
  padding: 0 30px 15px 30px;
  border-top: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end
}

.hl_reviews--item .card-footer .card-footer--left {
  margin-right: 20px
}

.hl_reviews--item .card-footer .card-footer--left .dropdown {
  display: inline
}

.hl_reviews--item .card-footer .card-footer--left .dropdown .dropdown-item {
  color: #2a3135
}

.hl_reviews--item .card-footer .card-footer--left .dropdown .dropdown-item .icon {
  width: 16px;
  margin-right: 10px
}

.hl_reviews--item .card-footer .card-footer--left .dropdown .dropdown-item.add-account {
  color: #188bf6
}

.hl_reviews--item .card-footer .card-footer--left a {
  color: #607179
}

.hl_reviews--item .card-footer .card-footer--left a:hover,
.hl_reviews--item .card-footer .card-footer--left a:active {
  color: #188bf6
}

.hl_reviews--item .card-footer .card-footer--left .respond-toggle span {
  display: none
}

.hl_reviews--item .card-footer .card-footer--left .respond-toggle:before {
  content: "Close Comments"
}

.hl_reviews--item .card-footer .card-footer--left .respond-toggle.collapsed:before {
  content: "Respond"
}

.hl_reviews--item .card-footer .card-footer--left .spacer {
  display: inline-block;
  margin: 0 10px;
  color: #afb8bc;
  font-size: 1.125rem;
  -webkit-transform: translateY(3px);
  -ms-transform: translateY(3px);
  transform: translateY(3px)
}

.hl_reviews--item .card-footer .card-footer--right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_reviews--item .card-footer .card-footer--right>*+* {
  margin-left: 10px
}

.hl_reviews--item .card-footer .card-footer--right .comment-count .icon {
  color: rgba(96, 113, 121, 0.5);
  display: inline-block;
  margin-right: 5px
}

.hl_reviews--item .card-footer .card-footer--right .comment-count span {
  font-size: .75rem
}

@media (min-width: 992px) {
  .hl_reviews--item .card-footer .card-footer--right .bootstrap-select.more-select>.dropdown-menu {
    left: auto !important;
    right: 0 !important;
    -webkit-transform: translateX(0) translateY(40px) !important;
    -ms-transform: translateX(0) translateY(40px) !important;
    transform: translateX(0) translateY(40px) !important
  }
}

.hl_reviews--item.comment-active {
  background: #e7f3fe
}

.hl_reviews--item.comment-active .card-footer--left a {
  color: #188bf6
}

.hl_reviews--item .comment {
  background: #fff
}

.hl_reviews--item .comment--inner {
  padding: 20px 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_reviews--item .comment--inner>*+* {
  margin-left: 20px
}

.hl_reviews--item .comment--inner .form-control {
  padding: 12px 20px
}

.hl_reviews--item .comment--inner .btn {
  padding: 11px 15px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 90px;
  flex: 1 0 90px
}

@media (max-width: 767px) {
  .hl_reviews--filter {
    border-top: 1px solid #f2f7fa;
    height: 0;
    visibility: hidden;
    margin-bottom: 20px
  }
}

@media (min-width: 768px) {
  .hl_reviews--filter {
    -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    opacity: 0;
    width: 300px;
    padding: 20px 0 0 30px;
    position: absolute;
    top: 0;
    right: 0;
    -webkit-transform: translateX(300px);
    -ms-transform: translateX(300px);
    transform: translateX(300px)
  }
}

@media (max-width: 767px) {
  .hl_reviews--filter.--open {
    height: auto;
    visibility: visible;
    position: relative
  }
}

@media (min-width: 768px) {
  .hl_reviews--filter.--open {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
    opacity: 1
  }
}

.hl_reviews--filter .form-group {
  margin-bottom: 20px
}

.hl_reviews--filter .bootstrap-select>.btn.dropdown-toggle {
  background-color: rgba(24, 139, 246, 0.1)
}

.hl_reviews--filter .bootstrap-select>.btn.dropdown-toggle .filter-option {
  color: #2a3135;
  font-weight: 400
}

.hl_reviews--filter .bootstrap-select>.btn.dropdown-toggle .filter-option .icon {
  font-size: .5rem;
  color: #afb8bc;
  margin-right: 5px
}

.hl_reviews--filter .bootstrap-select>.btn.dropdown-toggle.bs-placeholder {
  background: #fff
}

.hl_reviews--filter .bootstrap-select>.btn.dropdown-toggle.bs-placeholder .filter-option {
  color: #607179
}

.hl_reviews--filter .bootstrap-select .dropdown-menu .icon {
  font-size: .5rem;
  color: #afb8bc;
  margin-right: 5px
}

.customers-details {
  position: relative;
  max-width: 1600px;
  margin-left: auto;
  margin-right: auto
}

@media (min-width: 992px) {
  .customers-details {
    padding-right: 400px
  }
}

.hl_customers--table {
  margin-top: 20px
}

@media (min-width: 992px) {
  .hl_customers--table .table tbody tr td:last-child {
    width: 130px
  }
}

.hl_customers--table .table tbody tr td:last-child .btn-green-lt {
  margin-right: 10px
}

.hl_customers--reviews .card-body {
  padding-top: 40px
}

.hl_customers--reviews h3 {
  font-size: 3.125rem;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 30px;
  vertical-align: top
}

.hl_customers--reviews h3 .rating {
  display: inline-block;
  position: relative;
  top: 5px
}

.hl_customers--reviews h3 .rating .icon {
  vertical-align: top
}

.hl_customers--reviews .card-footer {
  padding: 0 30px 30px 30px;
  border-top: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end
}

.hl_customers--reviews .card-footer .card-footer--left {
  margin-right: 20px
}

.hl_customers--reviews .card-footer .card-footer--left a {
  color: #607179
}

.hl_customers--reviews .card-footer .card-footer--left a:hover,
.hl_customers--reviews .card-footer .card-footer--left a:active {
  color: #188bf6
}

.hl_customers--reviews .card-footer .card-footer--left .respond-toggle span {
  display: none
}

.hl_customers--reviews .card-footer .card-footer--left .respond-toggle:before {
  content: "Close Comments"
}

.hl_customers--reviews .card-footer .card-footer--left .respond-toggle.collapsed:before {
  content: "Respond"
}

.hl_customers--reviews .card-footer .card-footer--left .spacer {
  display: inline-block;
  margin: 0 10px;
  color: #afb8bc;
  font-size: 1.125rem;
  -webkit-transform: translateY(3px);
  -ms-transform: translateY(3px);
  transform: translateY(3px)
}

.hl_customers--reviews .card-footer .card-footer--right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_customers--reviews .card-footer .card-footer--right>*+* {
  margin-left: 10px
}

.hl_customers--reviews .card-footer .card-footer--right .comment-count .icon {
  color: rgba(96, 113, 121, 0.5);
  display: inline-block;
  margin-right: 5px
}

.hl_customers--reviews .card-footer .card-footer--right .comment-count span {
  font-size: .75rem
}

@media (min-width: 992px) {
  .hl_customers--reviews .card-footer .card-footer--right .bootstrap-select.more-select>.dropdown-menu {
    left: auto !important;
    right: 0 !important;
    -webkit-transform: translateX(0) translateY(40px) !important;
    -ms-transform: translateX(0) translateY(40px) !important;
    transform: translateX(0) translateY(40px) !important
  }
}

.hl_customers--reviews .review {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_customers--reviews .review>img {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  height: 50px;
  max-height: 50px;
  margin-right: 20px
}

.hl_customers--reviews .avatar {
  margin-top: 20px
}

.hl_customers--checkins .card-header {
  padding: 15px 30px
}

@media (min-width: 992px) {
  .hl_customers--checkins .table tbody tr td:last-child {
    width: 250px
  }
}

.hl_customers--review-attemps .card-header {
  padding: 15px 30px
}

.hl_customers--review-attemps .card-header .btn {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_customers--review-attemps .card-header .btn .icon {
  margin-right: 10px
}

@media (min-width: 992px) {
  .hl_customers--review-attemps .table tbody tr td:last-child {
    width: 100px
  }
}

.hl_customers--sidebar {
  background: #fff;
  padding: 40px 30px
}

@media (min-width: 992px) {
  .hl_customers--sidebar {
    width: 380px;
    position: absolute;
    margin: auto;
    top: 0;
    right: 0;
    bottom: 0
  }
}

@media (min-width: 992px) {
  .hl_customers--sidebar-inner {
    max-width: 290px;
    margin-left: auto;
    margin-right: auto
  }
}

.hl_customers--sidebar .sidebar_avatar-name {
  text-align: center;
  margin-bottom: 20px
}

.hl_customers--sidebar .sidebar_avatar-name .avatar {
  margin-bottom: 20px;
  margin-left: auto;
  margin-right: auto
}

.hl_customers--sidebar .sidebar_avatar-name h3 {
  font-size: 1.125rem;
  line-height: 1;
  margin-bottom: 0
}

.hl_customers--sidebar .btns {
  text-align: center
}

.hl_customers--sidebar .btns .btn {
  padding: 8px 20px;
  margin-left: 5px;
  margin-right: 5px
}

.hl_customers--sidebar .btns .btn .icon {
  margin-right: 5px
}

.hl_customers--sidebar .sidebar_info-item:not(last-child) {
  margin-bottom: 20px
}

.hl_customers--sidebar .sidebar_info-item h4 {
  font-size: .875rem;
  color: #607179;
  margin-bottom: 5px
}

.hl_customers--sidebar .sidebar_info-item p {
  color: #2a3135
}

.hl_customers--sidebar hr {
  margin-top: 30px;
  margin-bottom: 30px
}

.hl_customers--sidebar .sidebar_notes-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 20px
}

.hl_customers--sidebar .sidebar_notes-header h3 {
  font-size: .875rem;
  color: #607179;
  margin-bottom: 0
}

.hl_customers--sidebar .sidebar_notes-header a {
  font-size: .875rem
}

.hl_customers--sidebar .sidebar_notes-item:not(last-child) {
  margin-bottom: 20px
}

.hl_customers--sidebar .sidebar_notes-item p {
  color: #2a3135
}

.hl_customers--sidebar .sidebar_notes-item span {
  color: #607179
}

.hl_online-analysis .card-group {
  margin-bottom: 40px
}

.hl_online-analysis .card .card-header {
  padding: 40px 30px 0px 30px;
  border-color: #fff
}

@media (min-width: 576px) {
  .hl_online-analysis .card .card-header {
    padding: 40px 40px 0px 40px
  }
}

.hl_online-analysis .card .card-header h2 {
  color: #607179
}

@media (min-width: 576px) {
  .hl_online-analysis .card .card-body {
    padding: 25px 50px 40px 50px
  }
}

.hl_online-analysis .card .card-heading {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_online-analysis .card .card-heading h3 {
  font-size: 54px;
  font-weight: 300;
  line-height: 1;
  margin-bottom: 0;
  margin-right: 25px
}

.hl_online-analysis .card .card-heading h3 span {
  display: inline-block;
  vertical-align: top;
  color: #607179;
  font-size: 30px;
  position: relative;
  top: 4px
}

.hl_online-analysis .card .card-content {
  height: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between
}

.hl_online-analysis--badge {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 50%;
  background: #eee;
  text-align: center
}

.hl_online-analysis--badge.--good {
  background-color: rgba(55, 202, 55, 0.1);
  color: #37ca37
}

.hl_online-analysis--badge.--warning {
  background-color: rgba(233, 61, 61, 0.1);
  color: #e93d3d
}

.hl_online-analysis--badge.--star {
  background-color: rgba(255, 188, 0, 0.1);
  color: #ffbc00
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network h4 {
  font-weight: 400;
  font-size: .875rem;
  color: #607179;
  margin-bottom: 15px
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network .network_list {
  margin-bottom: 0
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network .network_list li a {
  display: block;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: #eee;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 35px;
  text-indent: -999999px
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network .network_list li a.google {
  background-image: url("/pmd/img/icon-g+.svg")
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network .network_list li a.facebook {
  background-image: url("/pmd/img/icon-facebook.svg")
}

.hl_online-analysis--online-presence-errors .hl_online-analysis--network .network_list li a.twitter {
  background-image: url("/pmd/img/icon-twitter.svg")
}

.hl_online-analysis--page-speed-score .link:before {
  content: "\e950";
  font-family: 'Magicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  margin-right: 10px;
  color: rgba(96, 113, 121, 0.5);
  font-size: 1rem;
  position: relative;
  top: 2px
}

.hl_online-analysis--page-speed-score .progress {
  position: relative;
  top: -10px
}

.hl_online-analysis--competition-leaderboard .search:before {
  content: "\e953";
  font-family: 'Magicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  margin-right: 10px;
  color: rgba(96, 113, 121, 0.5);
  font-size: 1rem;
  position: relative;
  top: 2px
}

@media (min-width: 992px) {
  .hl_online-analysis--table .table tbody tr td:last-child {
    width: 150px
  }
}

.hl_online-analysis--table .table tbody tr td:last-child .btn-blue {
  margin-right: 10px;
  padding: 10px 15px;
  min-width: 70px
}

.hl_team--table {
  margin-top: 20px
}

@media (min-width: 992px) {
  .hl_team--table .table tbody tr td:last-child {
    width: 50px
  }
}

.hl_widgets--header {
  border-bottom: 2px solid #e6edf2;
  margin-bottom: 35px
}

.hl_widgets--header-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 20px
}

.hl_widgets--header-inner h2 {
  font-size: 1.25rem
}

.hl_widgets--header-inner .btn .icon {
  color: #37ca37
}

.hl_widgets--nav {
  list-style: none;
  margin: 0;
  padding: 0
}

@media (max-width: 767px) {
  .hl_widgets--nav {
    padding-bottom: 10px
  }
}

@media (min-width: 768px) {
  .hl_widgets--nav {
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px)
  }
}

@media (min-width: 768px) {
  .hl_widgets--nav li {
    display: inline-block
  }
  .hl_widgets--nav li:not(last-child) {
    padding-right: 15px
  }
}

@media (min-width: 992px) {
  .hl_widgets--nav li:not(last-child) {
    padding-right: 30px
  }
}

.hl_widgets--nav li a {
  font-size: .875rem;
  color: #607179
}

@media (min-width: 768px) {
  .hl_widgets--nav li a {
    display: block;
    padding-bottom: 10px;
    border-bottom: 3px solid transparent
  }
}

.hl_widgets--nav li a:hover,
.hl_widgets--nav li a:focus,
.hl_widgets--nav li a:active {
  color: #188bf6
}

.hl_widgets--nav li.active a {
  color: #188bf6;
  font-weight: 500;
  border-color: #188bf6
}

.hl_widgets--tools {
  margin-bottom: 40px
}

.hl_widgets--tools .form-group label {
  font-size: .75rem;
  margin-bottom: 5px
}

.hl_widgets--tools .form-group .form-control {
  background: #fff;
  min-width: 360px
}

.hl_widgets--tools .hl_controls--right {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-transform: translateY(-5px);
  -ms-transform: translateY(-5px);
  transform: translateY(-5px)
}

.hl_widgets--tools .hl_controls--right .form-group {
  display: block !important
}

.hl_widgets--tools .hl_controls--right .form-group label {
  font-size: .75rem !important;
  margin-right: 0;
  margin-bottom: 5px
}

.hl_widgets--tools .color-select {
  background: #fff;
  padding: 10px 12px;
  height: 50px
}

.hl_widgets--tools .color-select p {
  margin-right: 10px;
  color: #2a3135
}

.hl_widgets--tools .btn-blue {
  height: 50px
}

.hl_widgets--tools .btn-blue .icon {
  font-size: .75rem
}

.hl_widgets--tools .btn-blue .icon:first-child {
  margin-right: 0 !important
}

.layout-select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.layout-select-item:not(:last-child) {
  margin-right: 10px
}

.layout-select-item input {
  display: none;
  opacity: 0;
  visibility: hidden
}

.layout-select-item input:checked+label {
  color: #188bf6;
  background-color: rgba(24, 139, 246, 0.1)
}

.layout-select-item label {
  width: 53px;
  height: 50px;
  margin-bottom: 0 !important;
  background-color: #fff;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: .875rem !important;
  font-weight: bold;
  color: #afb8bc;
  cursor: pointer;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.layout-select-item label:hover {
  color: #188bf6
}

.hl_widget--header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 10px
}

.hl_widget--tabs {
  margin-bottom: 40px
}

.hl_widget--tabs .tab-pane {
  padding-top: 0
}

.hl_widget--tabs iframe {
  display: block;
  margin: 0 auto;
  border: none;
  max-width: 1160px;
  -webkit-transition: all .3s ease-in-out;
  -o-transition: all .3s ease-in-out;
  transition: all .3s ease-in-out
}

.hl_view-select.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_view-select.nav>a {
  color: #afb8bc
}

.hl_view-select.nav>a:not(:last-child) {
  margin-right: 25px;
  position: relative
}

.hl_view-select.nav>a:not(:last-child):before {
  content: "";
  display: block;
  width: 3px;
  height: 3px;
  background-color: #afb8bc;
  border-radius: 50%;
  position: absolute;
  top: 40%;
  right: -14px
}

.hl_view-select.nav>a.active {
  color: #188bf6
}

.hl_screen-select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_screen-select-item:not(:last-child) {
  margin-right: 10px
}

.hl_screen-select-item input {
  display: none;
  opacity: 0;
  visibility: hidden
}

.hl_screen-select-item input:checked+label {
  color: #188bf6;
  background-color: rgba(24, 139, 246, 0.1)
}

.hl_screen-select-item input:checked+label svg {
  fill: #188bf6
}

.hl_screen-select-item label {
  width: 43px;
  height: 43px;
  line-height: 43px;
  text-align: center;
  margin-bottom: 0;
  background-color: #ffffff;
  border-radius: 4px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: #607179;
  cursor: pointer;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out
}

.hl_screen-select-item label:hover {
  color: #188bf6
}

.hl_screen-select-item label:hover svg {
  fill: #188bf6
}

.hl_screen-select-item label svg {
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out;
  fill: rgba(96, 113, 121, 0.5)
}

.hl_settings--profile,
.hl_settings--company,
.hl_settings--emails,
.hl_settings--sms,
.hl_settings--customize-communication,
.hl_settings--team-management,
.hl_settings--integrations {
  padding-top: 0
}

.hl_settings--profile .card .card-body,
.hl_settings--company .card .card-body,
.hl_settings--emails .card .card-body,
.hl_settings--sms .card .card-body,
.hl_settings--customize-communication .card .card-body,
.hl_settings--team-management .card .card-body,
.hl_settings--integrations .card .card-body {
  padding-top: 40px;
  padding-bottom: 40px;
  max-width: 640px;
  margin: 0 auto;
  width: 100%
}

.hl_settings--header {
  background-color: #fff;
  margin-bottom: 25px;
  padding-top: 15px;
  border-top: 1px solid #f2f7fa
}

@media (min-width: 768px) {
  .hl_settings--header {
    position: relative;
    -webkit-box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
    box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01)
  }
}

@media (max-width: 767px) {
  .hl_settings--header {
    padding-top: 20px
  }
}

.hl_settings--header h2 {
  font-size: 1.25rem;
  margin-bottom: 15px
}

.hl_settings--nav {
  list-style: none;
  margin: 0;
  padding: 0
}

@media (max-width: 767px) {
  .hl_settings--nav {
    padding-bottom: 10px
  }
}

@media (min-width: 768px) {
  .hl_settings--nav {
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px)
  }
}

@media (min-width: 768px) {
  .hl_settings--nav li {
    display: inline-block
  }
  .hl_settings--nav li:not(last-child) {
    padding-right: 15px
  }
}

@media (min-width: 992px) {
  .hl_settings--nav li:not(last-child) {
    padding-right: 30px
  }
}

.hl_settings--nav li a {
  font-size: .875rem;
  color: #607179
}

@media (min-width: 768px) {
  .hl_settings--nav li a {
    display: block;
    padding-bottom: 10px;
    border-bottom: 3px solid transparent
  }
}

.hl_settings--nav li a:hover,
.hl_settings--nav li a:focus,
.hl_settings--nav li a:active {
  color: #188bf6
}

.hl_settings--nav li.active a {
  color: #188bf6;
  font-weight: 500;
  border-color: #188bf6
}

.hl_settings--body .container-fluid {
  position: relative
}

.hl_settings--controls {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_settings--controls>* {
  margin-bottom: 20px
}

.hl_settings--controls-left {
  margin-right: 20px
}

.hl_settings--controls-left h2 {
  font-size: 1.125rem;
  margin-bottom: 0
}

.hl_settings--controls-left h2 span {
  font-size: .875rem;
  color: #afb8bc
}

.hl_settings--controls-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_settings--controls-right>*:not(:last-child) {
  margin-right: 10px
}

.hl_settings--controls-right .btn-sm,
.hl_settings--controls-right .btn-group-sm>.btn {
  color: #188bf6
}

.hl_settings--controls-right>.btn:not(.btn-sm) {
  padding: 9px 25px
}

.hl_settings--controls-right>.btn:not(.btn-sm) .icon {
  margin-right: 8px
}

.hl_settings--controls-right .more-select {
  width: 40px !important
}

.hl_settings--controls-right .more-select>.btn.dropdown-toggle {
  background: #fff;
  width: 40px;
  height: 40px
}

@media (min-width: 992px) {
  .hl_settings--controls-right .more-select>.dropdown-menu {
    left: auto !important;
    right: -20px !important;
    -webkit-transform: translateX(0) translateY(45px) !important;
    -ms-transform: translateX(0) translateY(45px) !important;
    transform: translateX(0) translateY(45px) !important
  }
}

.hl_settings--controls-right .toggle-control {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: #fff;
  height: 40px;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 4px
}

.hl_settings--controls-right .toggle-control .toggle {
  margin-left: 20px
}

.hl_settings--controls-right .toggle-control p.green {
  color: #37ca37
}

.hl_settings--controls .search-form {
  position: relative
}

@media (min-width: 992px) {
  .hl_settings--controls .search-form {
    min-width: 300px
  }
}

.hl_settings--controls .search-form .icon {
  position: absolute;
  top: 50%;
  left: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: rgba(96, 113, 121, 0.5)
}

.hl_settings--controls .search-form input {
  padding: 10px 20px 10px 50px;
  height: 40px
}

.personal-logo {
  margin-bottom: 40px
}

@media (min-width: 576px) {
  .personal-logo {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 10px
  }
}

.personal-logo>* {
  margin-bottom: 30px
}

.personal-logo .picture {
  width: 180px;
  height: 180px;
  background: #f7fafc url("/pmd/img/icon-cross.svg") no-repeat center;
  backgroud-size: 24px;
  border-radius: 4px
}

@media (min-width: 576px) {
  .personal-logo .picture {
    margin-right: 30px
  }
}

.personal-logo .picture>img {
  border-radius: 4px;
  width: 180px;
  height: 180px;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  font-family: 'object-fit: cover; object-position: center;'
}

.personal-logo .picture-text h4 {
  font-size: .875rem
}

.personal-logo .picture-text p+p {
  margin-top: 0
}

.personal-logo .picture-text .btns {
  margin-top: 10px
}

.personal-logo .picture-text .btns .btn {
  margin-right: 5px
}

.monitor-url-item {
  position: relative;
  margin-bottom: 5px;
  padding-right: 50px
}

.monitor-url-item .form-control {
  padding-left: 70px
}

.monitor-url-item .icon {
  color: #afb8bc;
  position: absolute;
  top: 50%;
  left: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 1rem
}

.monitor-url-item .icon.icon-link {
  left: 30px
}

.monitor-url-item .remove {
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #afb8bc;
  font-size: .75rem
}

.monitor-url-item .remove:hover,
.monitor-url-item .remove:focus,
.monitor-url-item .remove:active {
  color: #188bf6
}

.monitor-url .btn {
  margin-top: 20px
}

.monitor-url .btn .icon {
  margin-right: 10px
}

.social-network-item {
  position: relative;
  margin-bottom: 10px
}

.social-network-item .form-control {
  padding-left: 70px
}

.social-network-item .icon {
  color: #afb8bc;
  position: absolute;
  top: 50%;
  left: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 1rem
}

.review-link-item {
  position: relative;
  margin-bottom: 10px
}

.review-link-item .form-control {
  padding-left: 70px
}

.review-link-item .icon {
  color: #afb8bc;
  position: absolute;
  top: 50%;
  left: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  font-size: 1rem
}

.review-link-item .icon.icon-link {
  left: 30px
}

.hl_settings--sidebar .icon {
  margin-bottom: 30px
}

.hl_settings--sidebar .icon img {
  display: block;
  margin-left: auto;
  margin-right: auto
}

.hl_settings--sidebar h3 {
  font-size: 1.125rem
}

.hl_settings--sidebar .btn {
  margin-top: 20px
}

.hl_settings--sidebar .btn .icon {
  margin-right: 10px
}

@media (min-width: 992px) {
  .hl_settings--with-preview {
    padding-right: 400px
  }
}

.hl_settings--preview {
  width: 354px;
  height: 739px;
  background-color: #d2dce2;
  border-radius: 50px;
  padding: 87px 18px
}

@media (max-width: 991px) {
  .hl_settings--preview {
    margin-left: auto;
    margin-right: auto
  }
}

@media (min-width: 992px) {
  .hl_settings--preview {
    position: absolute;
    top: 60px;
    right: 20px
  }
}

.hl_settings--preview-inner {
  background-color: #fff;
  height: 565px
}

.hl_settings--preview-inner>img {
  width: 318px;
  height: 565px;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
  font-family: 'object-fit: cover; object-position: top;'
}

.box {
  background-color: #fff;
  border: solid 2px #f2f7fa;
  padding: 30px
}

.box p strong {
  display: block;
  font-weight: normal;
  color: #2a3135
}

.box p strong span {
  color: #188bf6
}

.box img {
  max-width: 100%
}

.box.box-select-background {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.box.box-select-background .option {
  -ms-flex-item-align: end;
  align-self: flex-end;
  margin-bottom: 20px
}

.box.box-select-background .btn {
  height: 50px
}

.hl_settings--color-select {
  padding-top: 10px
}

.color-select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  height: 52px;
  background-color: #f7fafc;
  border-radius: 4px;
  padding: 10px 20px
}

.color-select .color-selected {
  width: 26px;
  height: 26px;
  background-color: #fff;
  border: solid 2px #e6edf2;
  border-radius: 4px
}

.foot-note {
  font-size: .75rem;
  margin-top: 20px
}

.foot-note span {
  color: #188bf6
}

.add-site>.btn.dropdown-toggle {
  width: 200px
}

.add-site>.btn.dropdown-toggle .icon {
  margin-right: 10px
}

.hl_settings--email-color .box {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_settings--email-color .box .option {
  -ms-flex-item-align: end;
  align-self: flex-end;
  margin-bottom: 20px
}

.hl_settings--email-color .box .btn {
  height: 50px
}

.hl_settings--sms-color .box {
  height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #f2f7fa;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.hl_settings--sms-color .box .option {
  -ms-flex-item-align: end;
  align-self: flex-end;
  margin-bottom: 20px
}

.hl_settings--sms-color .box h4 {
  font-size: 1.5rem;
  max-width: 200px;
  margin-bottom: 10px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.hl_settings--sms-color .box p {
  text-align: left
}

.hl_settings--sms-color .box .btn {
  height: 50px
}

.hl_settings--sms-color .box.active {
  background-color: #188bf6;
  color: #fff
}

.hl_settings--sms-color .box.active h4 {
  color: #fff
}

.hl_settings--sms-color .box.active .btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff
}

.hl_settings--customize-communication .card-header h3 .icon {
  margin-right: 5px
}

.box-selection {
  margin-left: -5px;
  margin-right: -5px
}

.box-selection>div {
  padding-left: 5px;
  padding-right: 5px
}

.box-select input {
  display: none
}

.box-select input:checked+label {
  color: #188bf6;
  background-color: rgba(24, 139, 246, 0.1)
}

.box-select label {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 57px;
  background-color: #f7fafc;
  border-radius: 4px;
  color: #2a3135;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer
}

.hl_settings--team-management .card .card-body {
  max-width: 100%
}

@media (min-width: 992px) {
  .hl_settings--team-management .table tbody tr td:last-child {
    width: 150px
  }
}

.hl_settings--team-management .table tbody tr td:last-child .btn-blue {
  margin-right: 10px
}

.hl_settings--integrations .hl_settings--body .row>div {
  margin-bottom: 30px
}

.card-integration {
  background-color: #fff;
  border-radius: 4px;
  height: 100%
}

.card-integration--inner {
  padding: 30px;
  min-height: 300px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_sms-preview {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  height: 100%
}

.hl_sms-preview-header {
  height: 50px;
  width: 100%;
  border-bottom: 2px solid #f2f7fa;
  padding: 5px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_sms-preview-header .icon {
  margin-right: 15px;
  font-size: 1rem;
  color: #afb8bc
}

.hl_sms-preview-header h4 {
  margin-bottom: 0;
  font-size: 1rem
}

.hl_sms-preview-body {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  padding: 30px 10px
}

.hl_sms-preview-body .message-wrap {
  position: relative;
  padding-left: 40px
}

.hl_sms-preview-body .message-wrap+.message-wrap {
  margin-top: 20px
}

.hl_sms-preview-body .avatar {
  position: absolute;
  top: 0;
  left: 0
}

.hl_sms-preview-body .message-bubble {
  background-color: #2a3135;
  color: #fff;
  max-width: 80%;
  border-radius: 10px;
  overflow: hidden;
  line-height: 1.3;
  font-size: .9375rem
}

.hl_sms-preview-body .message-bubble>*:not(img) {
  padding: 10px 15px
}

.hl_sms-preview-body .message-bubble>img {
  width: 100%;
  max-width: 100%;
  height: auto;
  background: #fff
}

.hl_sms-preview-footer {
  height: 50px;
  width: 100%;
  border-top: 2px solid #f2f7fa;
  padding: 5px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  color: #afb8bc
}

.hl_sms-preview-footer .icon {
  margin-right: 15px;
  font-size: 1rem;
  color: #afb8bc
}

.hl_sms-preview-footer .btn {
  padding: 5px 15px;
  min-width: auto
}

@media (min-width: 768px) {
  .hl_login {
    margin-top: -82px
  }
}

.hl_login--header {
  background: #fff;
  border-bottom: 1px solid rgba(144, 164, 174, 0.2);
  padding: 15px 0;
  margin-bottom: 80px
}

.hl_login--header .container-fluid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_login--header svg {
  -webkit-transform: translateY(4px);
  -ms-transform: translateY(4px);
  transform: translateY(4px)
}

.hl_login--body .card {
  width: 100%;
  max-width: 550px;
  margin: 0 auto;
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05)
}

.hl_login--body .card .card-body {
  max-width: 430px;
  width: 100%;
  margin: 0 auto;
  padding-top: 50px;
  padding-bottom: 50px
}

.hl_login--body .login-card-heading {
  margin-bottom: 50px;
  text-align: center
}

.hl_login--body .login-card-heading p {
  line-height: 1.5
}

.hl_login--body .heading2 {
  margin-bottom: 10px
}

.hl_login--body .forgot-password {
  display: block;
  font-size: .75rem;
  text-align: right;
  margin-top: 5px
}

.hl_login--body .btn {
  margin-top: 40px;
  padding: 14.5px 20px
}

.hl_login--body .foot-note {
  font-size: .75rem;
  text-align: center;
  margin-top: 30px
}

.hl_calendar--wrap {
  overflow-y: auto
}

.hl_calendar--selection {
  min-width: 800px;
  margin-left: 0;
  margin-right: 0
}

@media (max-width: 950px) {
  .hl_calendar--selection {
    padding-right: 20px
  }
}

.hl_calendar--selection>.hl_calendar--set {
  padding-left: 10px;
  padding-right: 10px
}

@media (max-width: 950px) {
  .hl_calendar--selection>.hl_calendar--set {
    padding-left: 5px;
    padding-right: 5px
  }
}

.hl_calendar--set {
  text-align: center
}

.hl_calendar--set h3 {
  font-size: .75rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 15px
}

.hl_calendar--hours {
  list-style: none;
  padding: 0;
  margin: 0
}

.hl_calendar--hours li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 38px;
  line-height: 1;
  border-radius: 0.3125rem;
  border: solid 1px #c4d5e1;
  margin-bottom: 10px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  font-size: .8125rem;
  color: #2a3135;
  padding: 5px;
  cursor: pointer;
  transtion: all .2s ease-in-out
}

@media (max-width: 950px) {
  .hl_calendar--hours li {
    font-size: .75rem
  }
}

.hl_calendar--hours li.booked {
  background-color: #37ca37;
  border-color: #37ca37;
  color: #fff
}

.hl_calendar--hours li.unavailable {
  background-color: #e93d3d;
  border-color: #e93d3d;
  color: #fff
}

.hl_calendar--hours li.closed {
  background-color: #e6edf2;
  color: rgba(42, 49, 53, 0.4);
  border-color: #e6edf2;
  cursor: no-drop
}

.hl_conversations {
  padding: 0;
  overflow: hidden;
  overflow-x: scroll
}

.hl_conversations--wrap {
  min-width: 950px;
  background: #eee;
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: calc(100vh - 110px)
}

@media (min-width: 768px) {
  .hl_conversations--wrap {
    height: calc(100vh - 97px)
  }
}

.hl_conversations--wrap>* {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0
}

.hl_conversations--messages-list {
  background-color: #f9fafc;
  max-width: 280px
}

@media (min-width: 1400px) {
  .hl_conversations--messages-list {
    max-width: 320px
  }
}

.messages-list-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 20px;
  width: 100%;
  padding-bottom: 30px;
  background-color: #f9fafc;
  position: relative;
  z-index: 4
}

.messages-list-header h3 {
  font-size: 18px
}

.messages-list-header-actions {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.messages-list-header-actions>* {
  font-size: 1.125rem
}

.messages-list-header-actions>*:not(:last-child) {
  margin-right: 15px
}

.messages-list-header-actions .dropdown .dropdown-toggle:after {
  display: none
}

.messages-list-header-actions .dropdown .dropdown-menu {
  border: none;
  min-width: 360px;
  -webkit-box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.15)
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter {
  padding: 25px;
  font-size: .875rem
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 20px
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header h4 {
  margin-bottom: 0;
  font-size: 1.125rem
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header a {
  font-size: .75rem;
  color: #607179
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header a:hover,
.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header a:focus,
.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header a:active {
  color: #188bf6
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .dropdown-menu-filter-header a .icon {
  font-size: .625rem
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item:not(:last-child) {
  margin-bottom: 20px
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item h5 {
  font-size: .75rem;
  font-weight: normal;
  color: #607179
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item .the-option {
  display: inline-block;
  margin-right: 15px
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item .the-option input {
  display: none;
  opacity: 0
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item .the-option input:checked+label {
  color: #188bf6
}

.messages-list-header-actions .dropdown .dropdown-menu .dropdown-menu-filter .messages-filter-item .the-option label {
  font-size: .875rem;
  font-weight: 500;
  color: #2a3135;
  margin-bottom: 0;
  cursor: pointer
}

.message-file-uploaded {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.message-file-uploaded .file-uploaded-img {
  margin-bottom: 5px;
  margin-left: 2px;
  margin-right: 2px;
  min-width: 150px;
  width: 150px;
  height: 110px
}

.message-file-uploaded .file-uploaded-img>img {
  width: 150px;
  height: 110px;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: center;
  object-position: center;
  font-family: 'object-fit: cover; object-position: center;';
  border-radius: 4px
}

.messages-list-search {
  background-color: #f9fafc;
  position: relative;
  z-index: 3;
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 20px
}

.messages-list-search .icon {
  left: 35px !important;
  -webkit-transform: translateY(-110%) !important;
  -ms-transform: translateY(-110%) !important;
  transform: translateY(-110%) !important
}

.messages-list-search,
.files-search {
  margin-bottom: 20px;
  position: relative
}

.messages-list-search .form-control,
.files-search .form-control {
  background-color: #fff;
  border: solid 1px #d0d8e3;
  height: 36px;
  border-radius: 50px;
  padding-left: 40px
}

.messages-list-search .icon,
.files-search .icon {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.messages-list {
  margin-left: -20px;
  margin-right: -20px;
  padding-left: 20px;
  padding-right: 20px;
  overflow-y: auto;
  height: 100%;
  margin-top: -160px;
  padding-top: 140px
}

.messages-list--item {
  position: relative;
  display: block;
  color: #607179;
  padding: 20px 15px 20px 75px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer
}

.messages-list--item:hover {
  background-color: #eff1f7
}

.messages-list--item.active:before {
  content: "";
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff7402;
  position: absolute;
  top: 33px;
  left: 7px
}

.messages-list--item.active h4,
.messages-list--item.active p,
.messages-list--item.active .time-date {
  font-weight: 500;
  color: #2a3135
}

.messages-list--avatar {
  position: absolute;
  top: 15px;
  left: 20px
}

.messages-list--avatar .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #f6f8fb;
  position: absolute;
  top: 50%;
  right: -10px;
  z-index: 2;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.messages-list--text h4 {
  margin-bottom: 0;
  font-size: .875rem;
  font-weight: normal
}

.messages-list--text p {
  font-size: .875rem
}

.messages-list--text .time-date {
  font-size: .6875rem;
  color: #afb8bc;
  position: absolute;
  top: 20px;
  right: 20px
}

.hl_conversations--message {
  background-color: #fff
}

.hl_conversations--message-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 15px 30px;
  border-bottom: 1px solid #ebeef2
}

.hl_conversations--message-header h2 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0;
  line-height: 1
}

.hl_conversations--message-header .message-header-actions .icon {
  font-size: 1rem;
  position: relative;
  top: 1px;
  margin-right: 2px
}

.hl_conversations--message-header .message-header-actions .dropdown .dropdown-toggle {
  color: #188bf6
}

.hl_conversations--message-header .message-header-actions .dropdown .dropdown-menu {
  -webkit-box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.15);
  padding-top: 20px;
  padding-bottom: 20px
}

.hl_conversations--message-header .message-header-actions .dropdown .dropdown-menu li {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: .875rem;
  padding-left: 30px;
  padding-right: 30px;
  cursor: pointer;
  line-height: 1
}

.hl_conversations--message-header .message-header-actions .dropdown .dropdown-menu li:focus,
.hl_conversations--message-header .message-header-actions .dropdown .dropdown-menu li:active {
  color: #2a3135
}

.hl_conversations--message-header .message-header-btns>* {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  border-radius: 50%;
  border: solid 2px #188bf6;
  font-size: 1rem;
  color: #188bf6;
  position: relative
}

.hl_conversations--message-header .message-header-btns>*.call {
  border: solid 2px #00CF76;
  color: #37ca37;
  margin-left: 10px
}

.hl_conversations--message-header .message-header-btns>* .icon-video {
  display: block;
  width: 16px;
  height: 12px;
  background: url("/pmd/img/icon-video.svg") no-repeat center;
  background-size: 16px 12px;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0
}

.hl_conversations--message-header .message-header-btns>* .icon-call {
  display: block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-call.svg") no-repeat center;
  background-size: 16px;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0
}

.hl_conversations--message-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%
}

.hl_conversations--message-body>* {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  height: 100%
}

.hl_conversations--message-body .message-body--conversation {
  padding: 30px;
  padding-bottom: 170px;
  position: relative
}

.hl_conversations--message-body .message-box {
  position: absolute;
  margin: auto;
  left: 1px;
  right: 1px;
  padding: 20px;
  bottom: 60px;
  -webkit-box-shadow: 0 -10px 40px 0 rgba(0, 0, 0, 0.02);
  box-shadow: 0 -10px 40px 0 rgba(0, 0, 0, 0.02);
  background: #fff;
  z-index: 2
}

.hl_conversations--message-body .message-box .form-control {
  resize: none
}

.hl_conversations--message-body .message-box .form-control.drag-active {
  background: #e4eef5
}

.hl_conversations--message-body .message-box .btn {
  width: 48px;
  height: 48px;
  min-width: auto;
  padding: 0;
  border-radius: 50%;
  position: absolute;
  top: 10px;
  right: 10px
}

.hl_conversations--message-body .message-box .btn .icon-send {
  display: block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-send-white.svg") no-repeat center;
  background-size: 16px;
  position: absolute;
  margin: auto;
  top: -8px;
  left: 0;
  right: 0;
  bottom: 0
}

.hl_conversations--message-body .message-box .file-open>input {
  display: none
}

.hl_conversations--message-body .message-box .file-open label {
  cursor: pointer;
  position: absolute;
  top: 32px;
  right: 100px;
  opacity: 0.6;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.hl_conversations--message-body .message-box .file-open label:hover,
.hl_conversations--message-body .message-box .file-open label:active,
.hl_conversations--message-body .message-box .file-open label:focus {
  opacity: 1
}

.hl_conversations--message-body .message-box .emoji-open {
  position: absolute;
  top: 32px;
  right: 75px;
  opacity: 0.6
}

.hl_conversations--message-body .message-box .emoji-open:hover,
.hl_conversations--message-body .message-box .emoji-open:active,
.hl_conversations--message-body .message-box .emoji-open:focus {
  opacity: 1
}

.message-body--aside {
  max-width: 280px;
  padding-bottom: 130px;
  background-color: #fff;
  -webkit-box-shadow: -1px 0 0 0 #ebeef2;
  box-shadow: -1px 0 0 0 #ebeef2;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

@media (min-width: 1400px) {
  .message-body--aside {
    max-width: 300px
  }
}

.message-body--aside .btn-sched-wrap {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 50px;
  padding: 10px 30px 30px 30px;
  z-index: 2;
  background: #fff
}

.message-body--aside .btn-sched {
  display: block
}

.aside-texts {
  text-align: center;
  overflow-y: auto;
  padding: 50px 30px 0 30px;
  width: 100%
}

.aside-texts .avatar {
  height: 72px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 15px
}

.aside-texts .avatar_img {
  min-width: 72px;
  width: 72px;
  height: 72px;
  line-height: 72px;
  font-size: 1.25rem
}

.aside-texts .avatar_img>img {
  max-width: 72px;
  max-height: 72px
}

.aside-texts-name {
  margin-bottom: 40px
}

.aside-texts-name input {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 10px;
  max-width: 100%;
  width: auto;
  display: inline-block;
  padding: 0;
  padding-bottom: 5px;
  line-height: 1;
  text-align: center;
  border: none;
  border-bottom: 1px solid #dadee2
}

.aside-texts-name input:focus,
.aside-texts-name input:active {
  outline: none
}

.aside-texts-name h3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0
}

.aside-texts-name p {
  font-size: .875rem
}

.aside-texts-appointment {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 40px
}

.aside-texts-appointment .appointment-item {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  width: 50%
}

.aside-texts-appointment .appointment-item:last-child {
  border-left: 1px solid #f2f7fa
}

.aside-texts-appointment h4 {
  font-size: .75rem;
  font-weight: normal;
  color: #607179;
  margin-bottom: 0
}

.aside-texts-appointment p {
  font-size: .875rem;
  color: #2a3135
}

.aside-texts-appointment a {
  font-size: .875rem
}

.aside-texts-infos {
  text-align: left;
  margin-bottom: 40px
}

.aside-texts-infos p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: .875rem;
  color: #2a3135
}

.aside-texts-infos p i {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  min-width: 16px;
  max-width: 16px;
  margin-right: 15px;
  position: relative;
  top: 4px
}

.aside-texts-infos p+p {
  margin-top: 10px
}

.aside-texts-infos input {
  width: 100%;
  padding: 0;
  padding-bottom: 5px;
  border: none;
  border-bottom: 1px solid #dadee2;
  height: 25px;
  position: relative;
  top: 5px
}

.aside-texts-infos input:focus,
.aside-texts-infos input:active {
  outline: none
}

.aside-texts-infos .icon-gender {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-gender.svg") no-repeat center;
  background-size: 16px
}

.aside-texts-infos .icon-phone {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-phone.svg") no-repeat center;
  background-size: 16px
}

.aside-texts-infos .icon-email {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-email.svg") no-repeat center;
  background-size: 16px
}

.aside-texts-files {
  text-align: left
}

.aside-texts-files .files-heading {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 15px
}

.aside-texts-files .files-heading h4 {
  margin-bottom: 0;
  font-size: .875rem;
  font-weight: 500
}

.aside-texts-files .files-heading a {
  font-size: .75rem
}

.aside-texts-files .files-heading-action>*:not(:last-child) {
  margin-right: 10px
}

.aside-texts-files .files-heading-action a {
  color: #afb8bc;
  font-size: .875rem
}

.aside-texts-files .files-heading-action a:hover,
.aside-texts-files .files-heading-action a:focus,
.aside-texts-files .files-heading-action a:active {
  color: #607179
}

.aside-texts-files .files-heading-action a.active {
  color: #188bf6
}

.aside-texts-files .no-files {
  text-align: center;
  margin-bottom: 20px
}

.aside-texts-files .no-files .icon {
  font-size: 24px;
  color: #afb8bc
}

.aside-texts-files .no-files p {
  font-size: .75rem
}

.aside-texts-files .files-group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-left: -5px;
  margin-right: -5px
}

.aside-texts-files .files-group.--list {
  display: block
}

.aside-texts-files .files-group.--list .file-item {
  width: 100%;
  height: auto;
  background: none !important;
  color: #2a3135;
  text-align: left !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
  font-size: .875rem;
  border-radius: 0 !important
}

.aside-texts-files .files-group.--list .file-item .file-name {
  display: block
}

.aside-texts-files .files-group.--list .file-item .icon:not(.icon-close) {
  margin-right: 5px
}

.aside-texts-files .files-group.--list .file-item .file-remove {
  top: 5px !important;
  right: 0 !important
}

.aside-texts-files .files-group.--list .file-item.--doc .icon:not(.icon-close) {
  color: #188bf6
}

.aside-texts-files .files-group.--list .file-item.--photo .icon:not(.icon-close) {
  color: #ffbc00
}

.aside-texts-files .files-group.--list .file-item.--video .icon:not(.icon-close) {
  color: #00cf76
}

.aside-texts-files .files-group .file-item {
  width: 52px;
  height: 52px;
  border-radius: 8px;
  background-color: #afb8bc;
  margin-left: 5px;
  margin-right: 5px;
  margin-bottom: 10px;
  color: #fff;
  font-size: 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: relative
}

.aside-texts-files .files-group .file-item:hover .file-remove {
  opacity: 1
}

.aside-texts-files .files-group .file-item.--doc {
  background-color: #188bf6
}

.aside-texts-files .files-group .file-item.--photo {
  background-color: #ffbc00
}

.aside-texts-files .files-group .file-item.--video {
  background-color: #00cf76
}

.aside-texts-files .files-group .file-item .file-name {
  display: none
}

.aside-texts-files .files-group .file-item .file-remove {
  color: #e93d3d;
  font-size: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #fff;
  -webkit-box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.2);
  position: absolute;
  top: -8px;
  right: -8px;
  opacity: 0
}

.aside-texts-files .files-group .file-item .file-remove:hover {
  opacity: 1
}

.messages-group {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  margin: -30px;
  padding: 30px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.messages-group.drag-active {
  background: #e4eef5
}

.messages-group-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column
}

.messages-group-heading {
  text-align: center;
  margin-bottom: 20px;
  border-top: 1px solid #e4e7ea
}

.messages-group-heading img {
  background: #fff;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: -25px
}

.messages-group-heading h3 {
  font-size: .875rem;
  font-weight: 500;
  margin-bottom: 0
}

.messages-group-heading p {
  font-size: .75rem;
  color: #8e9eb3
}

.messages-single {
  position: relative;
  padding-left: 47px;
  margin-bottom: 25px
}

@media (min-width: 1600px) {
  .messages-single {
    max-width: 80%
  }
}

.messages-single .avatar {
  width: 32px;
  height: 32px;
  position: absolute;
  top: 0;
  left: 0
}

.messages-single .avatar .avatar_img {
  min-width: 32px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: .625rem;
  font-weight: 500
}

.messages-single .avatar .avatar_img>img {
  max-width: 32px;
  max-height: 32px
}

.messages-single .message-bubble {
  border-radius: 0 16px 16px 16px;
  background-color: #188bf6;
  color: #fff;
  padding: 20px 25px;
  margin-bottom: 5px;
  position: relative
}

.messages-single .message-bubble:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 20px 30px 0;
  border-color: transparent #188bf6 transparent transparent;
  position: absolute;
  top: 0;
  left: -10px
}

.messages-single .time-date {
  color: #afb8bc;
  font-size: .75rem
}

.messages-single.--own-message {
  padding-left: 0;
  padding-right: 47px;
  -ms-flex-item-align: end;
  align-self: flex-end
}

.messages-single.--own-message .avatar {
  left: auto;
  right: 0
}

.messages-single.--own-message .message-bubble {
  background-color: #eff2f9;
  border-radius: 16px 0 16px 16px;
  color: #607179
}

.messages-single.--own-message .message-bubble:before {
  border-width: 30px 20px 0 0;
  border-color: #eff2f9 transparent transparent transparent;
  left: auto;
  right: -10px
}

.messages-single.--own-message .time-date {
  float: right
}

.schedule-appointment-modal .hl_calendar--selection {
  min-width: 790px
}

.schedule-appointment-modal .hl_calendar--hours li {
  font-size: .75rem;
  height: 35px
}

.new-chat-modal .dropdown .dropdown-menu {
  width: 100%;
  border: none;
  -webkit-box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1)
}

.new-chat-modal .dropdown .dropdown-menu .dropdown-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.new-chat-modal .dropdown .dropdown-menu .dropdown-item .icon {
  margin-right: 10px
}

.new-chat-modal .dropdown .dropdown-menu .dropdown-item .dropdown-avatar {
  width: 32px;
  height: 32px;
  margin-top: 5px;
  margin-bottom: 5px;
  margin-right: 10px
}

.new-chat-modal .dropdown .dropdown-menu .dropdown-item .dropdown-user h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
  line-height: 1
}

.new-chat-modal .dropdown .dropdown-menu .dropdown-item .dropdown-user p {
  font-size: 12px;
  color: #607179;
  line-height: 1
}

.hl_social .hl_controls {
  margin-bottom: 20px
}

.hl_social--post-item .card-header {
  padding: 20px 30px
}

.hl_social--post-item .card-header .card-header-btns {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_social--post-item .card-header .social-post-label {
  color: #fff;
  display: block;
  padding: 9px 20px;
  margin-right: 10px;
  border-radius: 4px;
  min-width: 100px;
  text-align: center
}

.hl_social--post-item .card-header .social-post-label.--draft {
  background-color: #ff7402
}

.hl_social--post-item .card-header .social-post-label.--published {
  background-color: #37ca37
}

@media (min-width: 576px) {
  .hl_social--post-item .card-header .avatar {
    height: 60px
  }
  .hl_social--post-item .card-header .avatar .avatar_img {
    min-width: 60px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 1.125rem;
    position: relative;
    margin-right: 10px
  }
  .hl_social--post-item .card-header .avatar .avatar_img>img {
    max-width: 60px;
    max-height: 60px
  }
}

.hl_social--post-item .card-header .avatar .avatar_img {
  overflow: visible
}

.hl_social--post-item .card-header .avatar .avatar_text h4 {
  margin-bottom: 5px
}

.hl_social--post-item .card-header .avatar .avatar_text h4 strong {
  font-weight: 500
}

@media (min-width: 576px) {
  .hl_social--post-item .card-header .avatar .avatar_text h4 strong {
    font-size: 1rem
  }
}

@media (min-width: 576px) {
  .hl_social--post-item .card-header .avatar .avatar_text h4 span {
    font-size: 1rem
  }
}

.hl_social--post-item .card-header .avatar .avatar_text>* span {
  color: #188bf6
}

.hl_social--post-item .card-header .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: .75rem;
  min-width: 25px;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: #000;
  margin-right: 10px;
  color: #fff;
  position: absolute;
  bottom: -5px;
  right: -10px
}

.hl_social--post-item .card-header .icon.--facebook {
  background-color: #2877c1
}

.hl_social--post-item .card-header .icon.--google-plus {
  background-color: #ff5437
}

.hl_social--post-item .card-header .icon.--instagram {
  background-color: #f942b4
}

.hl_social--post-item .card-header .icon.--linkedin {
  background-color: #2877c1
}

.hl_social--post-item .card-header .icon.--twitter {
  background-color: #65aff5
}

.hl_social--post-item .post-image {
  width: 100%;
  max-width: 100%;
  height: auto
}

.hl_social--post-item .post-comment {
  padding: 0;
  letter-spacing: 0.5px;
  color: #607179;
  border: none;
  background: none
}

.hl_social--post-item .post-comment:focus,
.hl_social--post-item .post-comment:active {
  outline: none
}

.hl_social--post-item .social-tags .tag {
  display: inline-block;
  margin-right: 10px
}

.hl_social--post-item .card-footer {
  padding-left: 30px;
  padding-right: 30px
}

@media (min-width: 992px) {
  .hl_social--post-item .card-footer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    min-height: 80px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
  }
  .hl_social--post-item .card-footer>* {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0px;
    flex: 1 0 0;
    margin-bottom: 0
  }
}

.hl_social--post-item-details {
  margin-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_social--post-footer-item {
  margin-bottom: 10px
}

.hl_social--post-footer-item h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px
}

.hl_social--post-footer-item p {
  color: #188bf6;
  font-size: 1.563rem;
  line-height: 1.2
}

.hl_social--edit-post-modal .modal-content {
  background-color: #f2f7fa
}

.hl_social--edit-post-modal .modal-body {
  padding-top: 40px
}

.hl_social--edit-post-modal .modal-body h3 {
  font-size: 1rem;
  margin-bottom: 20px
}

@media (max-width: 767px) {
  .hl_social--edit-post-modal .modal-body .row>div {
    margin-bottom: 40px
  }
}

.hl_social--edit-post-modal .modal-body .card {
  background: #fff
}

.hl_edit-post-card .card-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_edit-post-card .card-footer .card-footer-btn .btn:not(:last-child) {
  margin-right: 5px
}

.social-post-form {
  position: relative;
  padding-left: 60px;
  margin-bottom: 30px
}

@media (min-width: 576px) {
  .social-post-form {
    padding-left: 80px
  }
}

.social-post-form .avatar {
  position: absolute;
  top: 0;
  left: 0px
}

@media (min-width: 576px) {
  .social-post-form .avatar {
    height: 60px
  }
  .social-post-form .avatar .avatar_img {
    min-width: 60px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 1.125rem;
    position: relative;
    overflow: visible;
    margin-right: 10px
  }
  .social-post-form .avatar .avatar_img>img {
    max-width: 60px;
    max-height: 60px
  }
}

.social-post-form .dots {
  position: absolute;
  bottom: 0;
  right: 35px;
  color: #afb8bc;
  font-size: 1rem
}

.social-post-form .dots:hover,
.social-post-form .dots:focus,
.social-post-form .dots:active {
  color: #607179
}

.social-post-form .photo {
  position: absolute;
  bottom: 0;
  right: 10px;
  color: #afb8bc;
  font-size: 1rem
}

.social-post-form .photo:hover,
.social-post-form .photo:focus,
.social-post-form .photo:active {
  color: #607179
}

.social-post-media {
  background-color: #fcfcfc;
  border-top: solid 2px #f2f7fa;
  border-bottom: solid 2px #f2f7fa;
  padding: 30px;
  margin-left: -30px;
  margin-right: -30px
}

.social-post-media .progress {
  height: 40px;
  border-radius: 4px;
  position: relative;
  margin-top: 20px
}

.social-post-media .progress .progress-bar {
  font-size: .875rem
}

.media-preview {
  background: #fff;
  padding: 15px;
  border: 1px solid #f2f7fa;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  position: relative
}

.media-preview+.media-preview {
  margin-top: 20px
}

.media-preview>img {
  min-width: 150px;
  width: 150px;
  max-width: 150px;
  height: auto;
  margin-right: 15px;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0
}

.media-preview .media-preview-text h4 {
  font-size: 1rem;
  margin-bottom: 5px
}

.media-preview .media-preview-text p {
  color: #afb8bc
}

.media-preview .media-preview-text textarea {
  width: 100%;
  color: #607179;
  line-height: 1.5;
  border: none;
  padding: 0;
  resize: none
}

.media-preview .media-preview-text textarea:focus,
.media-preview .media-preview-text textarea:active {
  outline: none
}

.media-preview .media-preview-text textarea:first-child {
  font-weight: 500;
  color: #2a3135
}

.media-preview .media-preview-text .link {
  font-size: .75rem;
  color: #afb8bc;
  line-height: 1.2
}

.media-preview .remove {
  font-size: 10px;
  position: absolute;
  top: 15px;
  right: 15px;
  color: #afb8bc
}

.media-preview .remove:focus,
.media-preview .remove:active,
.media-preview .remove:hover {
  color: #607179
}

.drag-drop {
  color: #afb8bc;
  padding: 15px;
  border-radius: 4px;
  border: dashed 1px #c6dce9;
  text-align: center;
  margin-top: 20px
}

.social-post-network {
  padding-top: 30px
}

.social-post-network h4 {
  font-size: 1rem;
  margin-bottom: 15px
}

.social-post-network .bootstrap-select {
  margin-bottom: 30px
}

.selected-social-network {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start
}

.selected-social-network>* {
  margin-right: 10px;
  margin-bottom: 10px
}

.selected-social-network .avatar {
  position: relative
}

@media (min-width: 576px) {
  .selected-social-network .avatar {
    height: 60px
  }
  .selected-social-network .avatar .avatar_img {
    min-width: 60px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 1.125rem;
    position: relative;
    margin-right: 10px
  }
  .selected-social-network .avatar .avatar_img>img {
    max-width: 60px;
    max-height: 60px
  }
  .selected-social-network .avatar .avatar_img .icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: .75rem;
    min-width: 25px;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: #000;
    margin-right: 10px;
    color: #fff;
    position: absolute;
    bottom: -5px;
    right: -10px
  }
  .selected-social-network .avatar .avatar_img .icon.--facebook {
    background-color: #2877c1
  }
  .selected-social-network .avatar .avatar_img .icon.--google-plus {
    background-color: #ff5437
  }
  .selected-social-network .avatar .avatar_img .icon.--instagram {
    background-color: #f942b4
  }
  .selected-social-network .avatar .avatar_img .icon.--linkedin {
    background-color: #2877c1
  }
  .selected-social-network .avatar .avatar_img .icon.--twitter {
    background-color: #65aff5
  }
}

.selected-social-network .avatar .avatar_img {
  overflow: visible
}

.selected-social-network .avatar .remove {
  position: absolute;
  top: 0;
  left: -4px;
  z-index: 2;
  font-size: 8px
}

.selected-social-network .avatar .remove .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 8px;
  min-width: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #188bf6;
  margin-right: 10px;
  color: #fff
}

.hl_edit-post-calendar h4 {
  font-size: 1rem;
  margin-bottom: 20px
}

.hl_edit-post-calendar .social-post-calendar {
  margin-bottom: 20px
}

.hl_edit-post-calendar .social-post-calendar .fc-toolbar .fc-left h2 {
  font-size: 1rem
}

.hl_edit-post-calendar .social-post-calendar .fc-day-header {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
  font-size: .625rem
}

.hl_edit-post-calendar .social-post-calendar .fc-basic-view .fc-day-number,
.hl_edit-post-calendar .social-post-calendar .fc-basic-view .fc-week-number {
  font-size: .625rem !important;
  font-weight: 500 !important;
  padding: 10px !important;
  color: #607179 !important
}

.hl_edit-post-calendar .social-post-calendar .fc-day-grid-event {
  color: #fff !important;
  border: none !important;
  padding: 5px 10px !important;
  font-size: .625rem !important;
  margin-left: 5px !important;
  margin-right: 5px !important
}

.hl_edit-post-calendar .hl_edit-post-calendar-select {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-bottom: 10px
}

.hl_edit-post-calendar .hl_edit-post-calendar-select>*:not(:last-child) {
  margin-right: 20px
}

.hl_edit-post-calendar .card-footer {
  padding-top: 30px;
  padding-bottom: 30px
}

.hl_edit-post-calendar .card-footer .btn {
  float: right
}

.hl_edit-post-calendar .scheduled-for span {
  color: #188bf6
}

.hl_social--posts-details .hl_controls {
  min-height: 50px
}

.hl_social--posts-details-inner .card {
  margin-bottom: 0;
  border-bottom: solid 2px #f2f7fa
}

.hl_social--posts-details-inner .card:last-child {
  border: none
}

.hl_social--posts-details-inner .card .card-body {
  padding-top: 25px;
  padding-bottom: 25px
}

.hl_social--posts-details-inner>.hl_social--post-item {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0
}

.hl_social--posts-details-inner .hl_social--post-item-comment-item {
  border-radius: 0
}

.hl_social--posts-details-inner .hl_social--post-item-add-comment {
  border-radius: 0
}

.hl_social--post-item-comment-item {
  background-color: #fcfcfc
}

.hl_social--post-item-comment-item .card-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.hl_social--post-item-comment-item .avatar {
  position: relative;
  min-width: 44px;
  margin-right: 20px
}

@media (min-width: 576px) {
  .hl_social--post-item-comment-item .avatar {
    min-width: 60px;
    height: 60px;
    margin-right: 25px
  }
  .hl_social--post-item-comment-item .avatar .avatar_img {
    min-width: 60px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 1.125rem;
    position: relative;
    margin-right: 10px
  }
  .hl_social--post-item-comment-item .avatar .avatar_img>img {
    max-width: 60px;
    max-height: 60px
  }
}

.hl_social--post-item-comment-item .social-comment-text h4 {
  font-size: 1rem;
  margin-bottom: 5px
}

.hl_social--post-item-comment-item .social-comment-text .comments-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: #afb8bc;
  margin-top: 10px
}

.hl_social--post-item-comment-item .social-comment-text .comments-info p+p {
  margin-top: 0
}

.hl_social--post-item-comment-item .social-comment-text .comments-info p:not(:last-child) {
  margin-right: 20px
}

.view-previous-comments {
  text-align: center;
  background-color: #fcfcfc
}

.view-previous-comments a {
  display: block;
  padding-top: 20px;
  padding-bottom: 20px;
  font-style: italic;
  font-size: .9375rem;
  color: #607179
}

.view-previous-comments a:hover,
.view-previous-comments a:focus,
.view-previous-comments a:active {
  color: #188bf6
}

.social-previous-comments {
  background-color: #fcfcfc
}

.social-previous-comments .card-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.social-previous-comments .previous-comments-wrap {
  width: 100%;
  max-width: 450px
}

.social-previous-comments .previous-comments-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 30px;
  border-left: 2px solid #f3f3f3
}

.social-previous-comments .previous-comments-item h4 {
  font-size: 1rem;
  margin-bottom: 5px
}

.social-previous-comments .previous-comments-item .avatar {
  margin-right: 20px
}

.social-previous-comments .previous-comments-item .comments-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  color: #afb8bc;
  margin-top: 10px
}

.social-previous-comments .previous-comments-item .comments-info p+p {
  margin-top: 0
}

.social-previous-comments .previous-comments-item .comments-info p:not(:last-child) {
  margin-right: 20px
}

.social-previous-comments .previous-add-comments {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding-top: 20px;
  padding-bottom: 20px;
  padding-left: 30px
}

.social-previous-comments .previous-add-comments .previous-add-comment-form {
  width: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0
}

.social-previous-comments .previous-add-comments .avatar {
  margin-right: 20px
}

.social-previous-comments .previous-add-comments .previous-add-comment-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 20px
}

.social-previous-comments .previous-add-comments .previous-add-comment-btn>*:not(:last-child) {
  margin-right: 10px
}

.view-more-comments {
  text-align: center;
  background-color: #fcfcfc
}

.view-more-comments a {
  display: block;
  padding-top: 20px;
  padding-bottom: 20px;
  font-style: italic;
  font-size: .9375rem
}

.hl_social--post-item-add-comment .card-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.hl_social--post-item-add-comment .avatar {
  position: relative;
  min-width: 44px;
  margin-right: 20px
}

@media (min-width: 576px) {
  .hl_social--post-item-add-comment .avatar {
    min-width: 60px;
    height: 60px;
    margin-right: 25px
  }
  .hl_social--post-item-add-comment .avatar .avatar_img {
    min-width: 60px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    font-size: 1.125rem;
    position: relative;
    margin-right: 10px
  }
  .hl_social--post-item-add-comment .avatar .avatar_img>img {
    max-width: 60px;
    max-height: 60px
  }
}

.hl_social--post-item-add-comment .social-add-comment-form {
  width: 100%;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0
}

.hl_social--post-item-add-comment .social-add-comment-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 20px
}

.hl_social--post-item-add-comment .social-add-comment-btn>*:not(:last-child) {
  margin-right: 10px
}

.hl_social--posts-stats .card {
  margin-bottom: 0;
  border-bottom: solid 2px #f2f7fa;
  border-radius: 0
}

.hl_social--posts-stats .card:first-child {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px
}

.hl_social--posts-stats .card:last-child {
  border: none
}

.hl_social--posts-stats .card .card-body {
  padding-top: 25px;
  padding-bottom: 25px
}

.hl_social--posts-stats .card .card-body>h4 {
  font-size: 1rem;
  margin-bottom: 20px
}

.hl_social--posts-stats .post-stats-item .post-score-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_social--posts-stats .post-stats-item .post-score-item h4 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 5px;
  color: #607179
}

.hl_social--posts-stats .post-stats-item .post-score-item p {
  color: #188bf6;
  font-size: 1.563rem;
  line-height: 1.2
}

.hl_social--posts-stats .post-stats-chart-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_social--posts-stats .post-stats-chart {
  width: 100%;
  min-width: 200px;
  max-width: 200px;
  height: 200px;
  margin-right: 40px
}

.hl_social--posts-stats .post-stats-chart .highcharts-credits {
  display: none !important;
  opacity: 0 !important
}

.chart-scores .chart-score-item {
  margin-bottom: 5px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.chart-scores .chart-score-item:before {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 2px;
  background-color: #607179;
  margin-right: 10px;
  position: relative;
  top: -1px
}

.chart-scores .chart-score-item strong {
  font-size: 1rem;
  display: inline-block;
  margin-right: 5px
}

.chart-scores.--people-reached-chart .chart-score-item:nth-child(1):before {
  background-color: #ffbc00
}

.chart-scores.--people-reached-chart .chart-score-item:nth-child(2):before {
  background-color: #e6edf2
}

.chart-scores.--story-teller-gender-chart .chart-score-item:nth-child(1):before {
  background-color: #188bf6
}

.chart-scores.--story-teller-gender-chart .chart-score-item:nth-child(2):before {
  background-color: #ff72f4
}

.chart-scores.--sentiment-chart .chart-score-item:nth-child(1):before {
  background-color: #37ca37
}

.chart-scores.--sentiment-chart .chart-score-item:nth-child(2):before {
  background-color: #e93d3d
}

.no-content {
  padding-top: 50px;
  padding-bottom: 50px;
  text-align: center;
  font-style: italic;
  color: #afb8bc
}

.no-content p {
  max-width: 350px;
  margin: 0 auto
}

.fc-toolbar .fc-left h2 {
  font-size: 1.25rem
}

.fc-toolbar .fc-right button {
  background: #fff;
  -webkit-box-shadow: none;
  box-shadow: none;
  border: 1px solid #e0ecf3;
  color: #2a3135
}

.fc-toolbar .fc-right button .fc-icon:after {
  font-size: 1.25rem;
  color: #2a3135
}

.fc td,
.fc th {
  background-color: #fff !important
}

.fc-unthemed td.fc-today {
  background: #ebf5fe !important
}

.fc-day-header {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
  font-size: 1rem;
  font-weight: 500
}

.fc-basic-view .fc-day-number,
.fc-basic-view .fc-week-number {
  font-size: 1rem !important;
  font-weight: 500 !important;
  padding: 10px !important;
  color: #607179 !important
}

.fc-day-grid-event {
  color: #fff !important;
  border: none !important;
  padding: 7px 15px !important;
  font-size: .875rem !important;
  margin-left: 10px !important;
  margin-right: 10px !important
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
  border-color: #e0ecf3 !important
}

.calendar-popover {
  border: none;
  -webkit-box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1);
  box-shadow: 0 8px 16px 5px rgba(0, 0, 0, 0.1);
  width: 360px;
  max-width: 360px;
  border-radius: 4px;
  background: #fff
}

.calendar-popover .popover-body {
  padding: 30px
}

.calendar-popover .calendar-popover-header {
  padding-left: 30px;
  padding-right: 30px;
  padding-bottom: 20px;
  margin-left: -30px;
  margin-right: -30px;
  margin-bottom: 20px;
  border-bottom: 2px solid #f2f7fa;
  position: relative
}

.calendar-popover .calendar-popover-header h4 {
  font-size: 1rem;
  margin-bottom: 5px
}

.calendar-popover .calendar-popover-header .date-time .time {
  color: #188bf6
}

.calendar-popover .calendar-popover-header .icon-close {
  position: absolute;
  top: -10px;
  right: 20px;
  cursor: pointer
}

.calendar-popover .calendar-label {
  border-radius: 4px;
  color: #fff;
  padding: 7px 15px;
  background: #afb8bc;
  margin-bottom: 15px
}

.calendar-popover .calendar-label.--blue {
  background-color: #188bf6
}

.calendar-popover .calendar-label.--yellow {
  background-color: #ffbc00
}

.calendar-popover .calendar-label.--green {
  background-color: #37ca37
}

.calendar-popover .calendar-label.--red {
  background-color: #e93d3d
}

.calendar-popover .calendar-label.--orange {
  background-color: #ff7402
}

.calendar-popover .calendar-label.--purple {
  background-color: #876cff
}

.calendar-popover .calendar-label.--teal {
  background-color: #17cfbc
}

.calendar-popover .calendar-label.--pink {
  background-color: #ff3e7f
}

.calendar-popover .calendar-label.--gray {
  background-color: #607179
}

.calendar-popover .calendar-label.--gray-drk {
  background-color: #2a3135
}

.calendar-popover .calendar-label.--gray-lt {
  background-color: #afb8bc
}

.calendar-popover .calendar-popover-body {
  margin-bottom: 20px
}

.calendar-popover .calendar-popover-body>img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-bottom: 10px
}

.calendar-popover .calendar-popover-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.calendar-popover .calendar-popover-footer .btn {
  padding: 8px 15px
}

.calendar-popover .calendar-popover-footer .btn:not(:last-child) {
  margin-right: 10px
}

.hl_agency .hl_controls {
  margin-top: 10px;
  margin-bottom: 30px
}

.hl_agency .hl_controls h3 {
  font-size: 1.5rem
}

.hl_header.--agency {
  border-bottom: 3px solid #37ca37
}

.hl_agency-dashboard--cards {
  -webkit-box-align: stretch;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%
}

.hl_agency-dashboard--cards>.card {
  border-radius: 4px !important
}

.hl_agency-dashboard--cards>.card:nth-child(1) {
  background-image: -webkit-gradient(linear, left top, right top, from(#49a4f8), to(#188bf6));
  background-image: -webkit-linear-gradient(left, #49a4f8, #188bf6);
  background-image: -o-linear-gradient(left, #49a4f8, #188bf6);
  background-image: linear-gradient(to right, #49a4f8, #188bf6)
}

.hl_agency-dashboard--cards>.card:nth-child(2) {
  background-image: -webkit-gradient(linear, left top, right top, from(#ffc933), to(#ffbc00));
  background-image: -webkit-linear-gradient(left, #ffc933, #ffbc00);
  background-image: -o-linear-gradient(left, #ffc933, #ffbc00);
  background-image: linear-gradient(to right, #ffc933, #ffbc00)
}

.hl_agency-dashboard--cards>.card:nth-child(3) {
  background-image: -webkit-gradient(linear, left top, right top, from(#5fd55f), to(#37ca37));
  background-image: -webkit-linear-gradient(left, #5fd55f, #37ca37);
  background-image: -o-linear-gradient(left, #5fd55f, #37ca37);
  background-image: linear-gradient(to right, #5fd55f, #37ca37)
}

.hl_agency-dashboard--cards .card {
  height: 100%;
  width: 100%;
  color: #fff;
  position: relative;
  overflow: hidden
}

.hl_agency-dashboard--cards .card-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end
}

.hl_agency-dashboard--cards .card .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  margin-bottom: 15px
}

.hl_agency-dashboard--cards .card h4 {
  color: #fff;
  margin-bottom: 5px;
  font-size: .875rem
}

.hl_agency-dashboard--cards .card p {
  font-size: 2.25rem;
  line-height: 1
}

.hl_agency-dashboard--cards .card .card-bg {
  position: absolute;
  right: -10px;
  bottom: -20px
}

@media (min-width: 992px) {
  .hl_agency-dashboard--table .table tbody tr td:nth-child(2) {
    width: 250px
  }
}

@media (min-width: 992px) {
  .hl_agency-dashboard--table .table tbody tr td:last-child {
    width: 80px
  }
}

.hl_agency-dashboard--table .table .table_a-progress {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_agency-dashboard--table .table .table_a-progress p {
  color: #2a3135;
  margin-right: 15px;
  text-align: right
}

.hl_agency-dashboard--table .table .table_a-progress .progress {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0;
  width: 200px;
  max-width: 200px
}

@media (min-width: 1200px) {
  .hl_agency-dashboard--table .table .table_a-progress .progress {
    width: 250px;
    max-width: 250px
  }
}

.hl_agency-dashboard--table .table .table_a-product {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_agency-dashboard--table .table .table_a-product .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 15px;
  background-color: #607179
}

.hl_agency-dashboard--table .table .table_a-product .icon.--green {
  background-color: #37ca37
}

.hl_agency-dashboard--table .table .table_a-product .icon.--yellow {
  background-color: #ffbc00
}

.hl_agency-dashboard--table .table .table_a-product .icon.--blue {
  background-color: #188bf6
}

.hl_agency-dashboard--table .table .table_a-product .icon.--pink {
  background-color: #ff3e7f
}

.hl_agency-dashboard--table .table .table_a-product p {
  line-height: 1
}

.hl_location--group {
  position: relative;
  overflow: hidden;
  -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
  min-height: 640px
}

@media (min-width: 768px) {
  .hl_location--group.--open {
    padding-right: 300px
  }
}

@media (max-width: 767px) {
  .hl_location--filter {
    border-top: 1px solid #f2f7fa;
    height: 0;
    visibility: hidden;
    margin-bottom: 20px
  }
}

@media (min-width: 768px) {
  .hl_location--filter {
    -webkit-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    -o-transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    transition: all 0.4s cubic-bezier(0.72, 0.16, 0.345, 0.875) 0s;
    opacity: 0;
    width: 300px;
    padding: 20px 0 0 30px;
    position: absolute;
    top: 0;
    right: 0;
    -webkit-transform: translateX(300px);
    -ms-transform: translateX(300px);
    transform: translateX(300px)
  }
}

@media (max-width: 767px) {
  .hl_location--filter.--open {
    height: auto;
    visibility: visible;
    position: relative
  }
}

@media (min-width: 768px) {
  .hl_location--filter.--open {
    -webkit-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
    opacity: 1
  }
}

.hl_location--filter .form-group {
  margin-bottom: 20px
}

.hl_location--filter .bootstrap-select>.btn.dropdown-toggle {
  background-color: rgba(24, 139, 246, 0.1)
}

.hl_location--filter .bootstrap-select>.btn.dropdown-toggle .filter-option {
  color: #2a3135;
  font-weight: 400
}

.hl_location--filter .bootstrap-select>.btn.dropdown-toggle .filter-option .icon {
  font-size: .5rem;
  color: #afb8bc;
  margin-right: 5px
}

.hl_location--filter .bootstrap-select>.btn.dropdown-toggle.bs-placeholder {
  background: #fff
}

.hl_location--filter .bootstrap-select>.btn.dropdown-toggle.bs-placeholder .filter-option {
  color: #607179
}

.hl_location--filter .bootstrap-select .dropdown-menu .icon {
  font-size: .5rem;
  color: #afb8bc;
  margin-right: 5px
}

.hl_location--item-inner {
  padding-top: 30px;
  padding-left: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start
}

.hl_location--item .card-body {
  padding: 0 30px 30px 0;
  -webkit-box-flex: 1;
  -ms-flex: 1 0 0px;
  flex: 1 0 0
}

.hl_location--item .hl_icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #607179
}

.hl_location--item .hl_icon.--green {
  background-color: #37ca37
}

.hl_location--item .hl_icon.--yellow {
  background-color: #ffbc00
}

.hl_location--item .hl_icon.--blue {
  background-color: #188bf6
}

.hl_location--item .hl_icon.--pink {
  background-color: #ff3e7f
}

.hl_location--logo {
  width: 60px;
  margin-right: 20px
}

.hl_location--logo>img {
  max-width: 100%;
  height: auto
}

.hl_location--header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-bottom: 10px
}

.hl_location--header>* {
  margin-bottom: 10px
}

.hl_location--header h3 {
  font-size: 1rem;
  font-weight: 500;
  color: #188bf6;
  margin-bottom: 5px
}

.hl_location--header h3 .label {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: .6875rem;
  font-weight: 500;
  margin-left: 5px
}

.hl_location--header h3 .label.--green {
  background-color: #d8f5d8;
  color: #37ca37
}

.hl_location--header h3 .label.--blue {
  background-color: #dceefe;
  color: #188bf6
}

.hl_location--header .hl_location--header-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex
}

.hl_location--header .hl_location--header-right .hl_icon:not(:last-child) {
  margin-right: 15px
}

.hl_location--info-list {
  margin-bottom: 0
}

.hl_location--info-list li:not(:last-child) {
  margin-bottom: 5px
}

.hl_location--info-list li .icon {
  margin-right: 5px
}

.hl_location--footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 15px 30px 5px 30px
}

.hl_location--footer>* {
  margin-bottom: 10px
}

.hl_location--footer a .icon {
  font-size: .625rem
}

.hl_location--footer .list-inline {
  margin-bottom: 0
}

.hl_location--footer .list-inline li:not(:last-child) {
  margin-right: 20px
}

.hl_location--footer .list-inline li .icon {
  margin-right: 5px;
  position: relative;
  top: 2px
}

.hl_location--footer .list-inline li .icon.icon-a-refresh {
  margin-left: 5px
}

.icon-a-pin {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/pmd/img/icon-a-pin.svg") no-repeat center;
  background-size: 12.25px 14px
}

.icon-a-phone {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/pmd/img/icon-a-phone.svg") no-repeat center;
  background-size: 14px 14px
}

.icon-a-report {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-a-report.svg") no-repeat center;
  background-size: 16px 16px
}

.icon-a-mail {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/icon-a-mail.svg") no-repeat center;
  background-size: 16px 12px
}

.icon-a-refresh {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url("/pmd/img/icon-a-refresh.svg") no-repeat center;
  background-size: 12px 12px
}

.hl_agency-location--map {
  padding: 0;
  position: relative
}

.the-location-map {
  width: 100%;
  min-height: 500px;
  height: calc(100vh - 82px)
}

.hl_location--search {
  padding-left: 15px;
  padding-right: 15px;
  position: absolute;
  margin: auto;
  top: 30px;
  left: 0;
  right: 0;
  z-index: 3;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto
}

.hl_location--search .icon {
  position: absolute;
  top: 22px;
  left: 35px;
  color: #b0b8bc;
  font-size: 1rem
}

.hl_location--search .form-control {
  padding-left: 50px;
  padding-right: 150px;
  background: #fff;
  border-radius: 8px;
  background-color: #fff;
  -webkit-box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  height: 60px;
  font-size: 1.125rem;
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
  color: #2a3135
}

.hl_location--search .form-control.active {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0
}

.hl_location--search .dropdown .dropdown-menu {
  list-style: none;
  width: 100%;
  margin: auto !important;
  top: 0px;
  border: none;
  border-top: 1px solid #e4e7ea;
  right: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  -webkit-box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1)
}

.hl_location--search .dropdown .dropdown-menu .dropdown-item {
  font-size: .875rem
}

.hl_location--search .add-manually {
  font-size: .75rem;
  position: absolute;
  top: 22px;
  right: 35px
}

.hl_location--search-results {
  position: absolute;
  margin: auto;
  top: 130px;
  right: 15px;
  z-index: 2;
  width: 100%;
  max-width: 445px;
  padding: 30px;
  border-radius: 4px;
  background-color: #fff;
  -webkit-box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1)
}

.search-results-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 30px
}

.search-results-header h4 {
  font-size: 1.125rem;
  margin-bottom: 0
}

.search-results-header .hide {
  font-size: .75rem
}

.search-result-item {
  display: block;
  position: relative;
  padding-left: 50px;
  margin-bottom: 20px
}

.search-result-item:hover .icon-arrow-right-1 {
  -webkit-transform: translateY(-50%) translateX(5px);
  -ms-transform: translateY(-50%) translateX(5px);
  transform: translateY(-50%) translateX(5px)
}

.search-result-item .icon-pin-filled {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #2a3135;
  color: #fff;
  position: absolute;
  top: -2px;
  left: 0
}

.search-result-item .icon-pin-filled.--blue {
  background-color: #188bf6
}

.search-result-item .icon-pin-filled.--yellow {
  background-color: #ffbc00
}

.search-result-item .icon-pin-filled.--green {
  background-color: #37ca37
}

.search-result-item .icon-pin-filled.--red {
  background-color: #e93d3d
}

.search-result-item .icon-pin-filled.--orange {
  background-color: #ff7402
}

.search-result-item .icon-pin-filled.--purple {
  background-color: #876cff
}

.search-result-item .icon-pin-filled.--teal {
  background-color: #17cfbc
}

.search-result-item .icon-pin-filled.--pink {
  background-color: #ff3e7f
}

.search-result-item h5 {
  font-size: .75rem;
  font-weight: 500;
  margin-bottom: 5px
}

.search-result-item p {
  font-size: .75rem;
  color: #607179
}

.search-result-item .icon-arrow-right-1 {
  font-size: .5rem;
  position: absolute;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #188bf6;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s
}

.hl_agency-location--add .hl_controls--left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_agency-location--add .hl_controls--left .back {
  font-size: 1.25rem;
  margin-right: 15px
}

.hl_agency-location--add .card .card-header,
.hl_agency-location--add .card .card-body {
  padding-left: 40px;
  padding-right: 40px
}

.hl_agency-location--add .form-group .business-category {
  position: relative
}

.hl_agency-location--add .form-group .business-category>a {
  color: #b0b8bc;
  font-size: .75rem;
  position: absolute;
  top: 50%;
  right: -18px;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%)
}

.hl_agency-location--add .form-group .business-category>a:hover {
  color: #2a3135
}

.hl_agency-location--add .form-group .business-category+.business-category {
  margin-top: 10px
}

.hours-item {
  margin-bottom: 20px
}

@media (min-width: 576px) {
  .hours-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
  }
}

.hours-item .option {
  width: 120px;
  min-width: 120px
}

.hours-item .dash {
  display: block;
  width: 8px;
  min-width: 8px;
  height: 1px;
  background-color: #728091
}

@media (min-width: 576px) {
  .hours-item .dash {
    margin-left: 10px;
    margin-right: 10px
  }
}

.hl_agency-location--add-sidebar .card-body {
  padding: 20px !important
}

.hl_agency-location--add-sidebar .card-body h4 {
  font-size: .875rem;
  font-weight: 500
}

.hl_agency-location--add-sidebar .card-body p {
  font-size: 12px
}

.hl_agency-location--add-sidebar .card-body p+p {
  margin-top: 0
}

.hl_agency-location--add-sidebar .card-body ul {
  margin-top: 10px
}

.hl_agency-location--add-sidebar .card-body ul li {
  margin-right: 15px
}

.hl_agency-location--add-sidebar .card-body ul li a img {
  max-width: 13px;
  height: auto
}

.side-map {
  width: 100%;
  height: 240px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px
}

.hl_agency-location--details .hl_controls--left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center
}

.hl_agency-location--details .hl_controls--left .back {
  font-size: 1.25rem;
  margin-right: 15px
}

.hl_agency-location--details .product-revenue {
  padding-left: 10px;
  border-left: 3px solid #37ca37
}

.hl_agency-location--details .product-revenue h4 {
  font-size: .75rem;
  font-weight: normal;
  color: #607179;
  margin-bottom: 5px
}

.hl_agency-location--details .product-revenue p {
  font-size: 1.125rem;
  color: #607179;
  line-height: 1;
  color: #2a3135
}

.nav.nav-tabs {
  border-color: #d5dde1
}

@media (max-width: 767px) {
  .nav.nav-tabs {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
  }
}

.nav.nav-tabs .nav-item:not(:last-child) {
  margin-right: 40px
}

.nav.nav-tabs .nav-link {
  border: none;
  background: none;
  border-radius: 0;
  padding: 0;
  padding-bottom: 5px;
  border-bottom: 2px solid transparent;
  color: #607179
}

@media (max-width: 767px) {
  .nav.nav-tabs .nav-link {
    border: none
  }
}

.nav.nav-tabs .nav-link:hover,
.nav.nav-tabs .nav-link:focus,
.nav.nav-tabs .nav-link:active {
  color: #188bf6;
  background: none
}

.nav.nav-tabs .nav-link.active {
  color: #188bf6;
  border-color: #188bf6
}

@media (max-width: 767px) {
  .nav.nav-tabs .nav-link.active {
    font-weight: 500
  }
}

.nav.nav-tabs.location-details-tab {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between
}

.tab-pane {
  padding-top: 30px
}

.hl_activity-history {
  margin-top: 40px
}

.hl_activity-history h3 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 25px
}

.hl_activity-history--item {
  margin-bottom: 35px;
  position: relative;
  padding-left: 50px
}

.hl_activity-history--item .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #2a3135;
  color: #fff;
  position: absolute;
  top: -2px;
  left: 0
}

.hl_activity-history--item .icon.--blue {
  background-color: #188bf6
}

.hl_activity-history--item .icon.--yellow {
  background-color: #ffbc00
}

.hl_activity-history--item .icon.--green {
  background-color: #37ca37
}

.hl_activity-history--item .icon.--red {
  background-color: #e93d3d
}

.hl_activity-history--item .icon.--orange {
  background-color: #ff7402
}

.hl_activity-history--item .icon.--purple {
  background-color: #876cff
}

.hl_activity-history--item .icon.--teal {
  background-color: #17cfbc
}

.hl_activity-history--item .icon.--pink {
  background-color: #ff3e7f
}

.hl_activity-history--item-header {
  margin-bottom: 10px
}

.hl_activity-history--item-header h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 3px
}

.hl_activity-history--item-header h4 span {
  color: #607179;
  font-weight: normal
}

.hl_activity-history--item-header p strong {
  color: #2a3135;
  font-weight: normal
}

.hl_activity-history--item-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 5px
}

.hl_activity-history--item-footer p strong {
  color: #2a3135
}

.hl_activity-history--item-footer p+p {
  margin-top: 0
}

.hl_activity-history--item-footer p:not(:last-child) {
  margin-right: 15px
}

.hl_activity-history--item p {
  font-size: .75rem;
  line-height: 1.5
}

.hl_tasks--item {
  position: relative;
  padding-left: 35px;
  font-size: .75rem;
  margin-bottom: 25px
}

.hl_tasks--item.--late .hl_tasks--item-header p {
  color: #e93d3d
}

.hl_tasks--item.--late .option label:before {
  border-color: #e93d3d
}

.hl_tasks--item.--due .hl_tasks--item-header p {
  color: #ffbc00
}

.hl_tasks--item.--due .option label:before {
  border-color: #ffbc00
}

.hl_tasks--item.--done {
  color: #afb8bc
}

.hl_tasks--item.--done .hl_tasks--item-header h4 {
  color: #afb8bc;
  text-decoration: line-through
}

.hl_tasks--item.--done .hl_tasks--item-footer p strong {
  color: #afb8bc
}

.hl_tasks--item .option {
  position: absolute;
  top: 4px;
  left: 0
}

.hl_tasks--item-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
  margin-bottom: 5px
}

.hl_tasks--item-header h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 0;
  margin-right: 20px;
  line-height: 1
}

.hl_tasks--item-header p {
  font-weight: 500;
  line-height: 1
}

.hl_tasks--item-footer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 0
}

.hl_tasks--item-footer p {
  margin-right: 20px
}

.hl_tasks--item-footer p+p {
  margin-top: 0
}

.hl_tasks--item-footer p strong {
  font-weight: 500;
  color: #2a3135
}

.hl_products .icon {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 15px;
  background-color: #607179
}

.hl_products .icon.--green {
  background-color: #37ca37
}

.hl_products .icon.--yellow {
  background-color: #ffbc00
}

.hl_products .icon.--blue {
  background-color: #188bf6
}

.hl_products .icon.--pink {
  background-color: #ff3e7f
}

.hl_products--item {
  position: relative;
  padding-left: 50px;
  font-size: .75rem;
  margin-bottom: 25px
}

.hl_products--item .icon {
  position: absolute;
  top: 0;
  left: 0
}

.hl_products--item-body {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap
}

.hl_products--item-text {
  margin-right: 20px
}

.hl_products--item-text h4 {
  margin-bottom: 4px;
  font-size: .875rem;
  font-weight: 500
}

.hl_products--item-text .list-inline-item {
  line-height: 1.4
}

.hl_products--item-text .list-inline-item:not(:last-child) {
  padding-right: 10px;
  border-right: 1px solid #e4e7ea
}

.hl_products--item-text .list-inline-item strong {
  color: #37ca37
}

.hl_agency-billing-btns .btn {
  margin-left: 10px
}

.hl_agency-billing--table .table tbody tr td:first-child {
  font-weight: 500
}

@media (min-width: 992px) {
  .hl_agency-billing--table .table tbody tr td:last-child {
    width: 80px
  }
}

.hl_agency-sales-resources--item .card-body {
  padding-top: 30px;
  padding-bottom: 30px
}

@media (min-width: 576px) {
  .hl_agency-sales-resources--item .card-body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
  }
}

.hl_agency-sales-resources--item .card-body>img {
  width: 100px;
  height: auto;
  margin-bottom: 15px
}

@media (min-width: 576px) {
  .hl_agency-sales-resources--item .card-body>img {
    margin-bottom: 0;
    margin-right: 30px
  }
}

.hl_agency-sales-resources--item .card-body .card-text {
  margin-bottom: 15px
}

@media (min-width: 576px) {
  .hl_agency-sales-resources--item .card-body .card-text {
    margin-bottom: 0;
    margin-right: 30px
  }
}

.hl_agency-sales-resources--item .card-body .card-text h3 {
  font-size: 18px;
  font-weight: 500;
  color: #188bf6
}

.hl_agency-sales-resources--item .card-body .card-btns {
  max-width: 150px;
  margin: 0 auto
}

.hl_agency-sales-resources--item .card-body .card-btns .btn {
  margin-bottom: 15px
}

#outdated {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 170px;
  text-align: center;
  text-transform: uppercase;
  z-index: 1500;
  background-color: #f25648;
  color: #ffffff
}

#outdated h6 {
  font-size: 25px;
  line-height: 25px;
  margin: 30px 0 10px
}

#outdated p {
  font-size: 12px;
  line-height: 12px;
  margin: 0
}

#outdated #btnUpdateBrowser {
  display: block;
  position: relative;
  padding: 10px 20px;
  margin: 30px auto 0;
  width: 230px;
  color: #ffffff;
  text-decoration: none;
  border: 2px solid #ffffff;
  cursor: pointer
}

#outdated #btnUpdateBrowser:hover {
  color: #f25648;
  background-color: #ffffff
}

#outdated .last {
  position: absolute;
  top: 10px;
  right: 25px;
  width: 20px;
  height: 20px
}

#outdated .last[dir='rtl'] {
  right: auto !important;
  left: 25px !important
}

#outdated #btnCloseUpdateBrowser {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: #ffffff;
  font-size: 36px;
  line-height: 36px
}

* html #outdated {
  position: absolute
}

select.bs-select-hidden,
select.selectpicker {
  display: none !important
}

.bootstrap-select {
  width: 220px \0
}

.bootstrap-select>.dropdown-toggle {
  position: relative;
  width: 100%;
  padding-right: 25px;
  z-index: 1
}

.bootstrap-select>.dropdown-toggle.bs-placeholder,
.bootstrap-select>.dropdown-toggle.bs-placeholder:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder:active {
  color: #607179
}

.bootstrap-select>select {
  position: absolute !important;
  bottom: 0;
  left: 50%;
  display: block !important;
  width: 0.5px !important;
  height: 100% !important;
  padding: 0 !important;
  opacity: 0 !important;
  border: none
}

.bootstrap-select>select.mobile-device {
  top: 0;
  left: 0;
  display: block !important;
  width: 100% !important;
  z-index: 2
}

.has-error .bootstrap-select .dropdown-toggle,
.error .bootstrap-select .dropdown-toggle,
.bootstrap-select.is-invalid .dropdown-toggle,
.was-validated .bootstrap-select .selectpicker:invalid+.dropdown-toggle {
  border-color: #e93d3d
}

.bootstrap-select.is-valid .dropdown-toggle,
.was-validated .bootstrap-select .selectpicker:valid+.dropdown-toggle {
  border-color: #37ca37
}

.bootstrap-select.fit-width {
  width: auto !important
}

.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
  width: 100%
}

.bootstrap-select .dropdown-toggle:focus {
  outline: thin dotted #333333 !important;
  outline: 5px auto -webkit-focus-ring-color !important;
  outline-offset: -2px
}

.bootstrap-select.form-control {
  margin-bottom: 0;
  padding: 0;
  border: none
}

.bootstrap-select.form-control:not([class*="col-"]) {
  width: 100%
}

.bootstrap-select.form-control.input-group-btn {
  z-index: auto
}

.bootstrap-select.form-control.input-group-btn:not(:first-child):not(:last-child)>.btn {
  border-radius: 0
}

.bootstrap-select:not(.input-group-btn),
.bootstrap-select[class*="col-"] {
  float: none;
  display: inline-block;
  margin-left: 0
}

.bootstrap-select.dropdown-menu-right,
.bootstrap-select[class*="col-"].dropdown-menu-right,
.row .bootstrap-select[class*="col-"].dropdown-menu-right {
  float: right
}

.form-inline .bootstrap-select,
.form-horizontal .bootstrap-select,
.form-group .bootstrap-select {
  margin-bottom: 0
}

.form-group-lg .bootstrap-select.form-control,
.form-group-sm .bootstrap-select.form-control {
  padding: 0
}

.form-group-lg .bootstrap-select.form-control .dropdown-toggle,
.form-group-sm .bootstrap-select.form-control .dropdown-toggle {
  height: 100%;
  font-size: inherit;
  line-height: inherit;
  border-radius: inherit
}

.form-inline .bootstrap-select .form-control {
  width: 100%
}

.bootstrap-select.disabled,
.bootstrap-select>.disabled {
  cursor: not-allowed
}

.bootstrap-select.disabled:focus,
.bootstrap-select>.disabled:focus {
  outline: none !important
}

.bootstrap-select.bs-container {
  position: absolute;
  top: 0;
  left: 0;
  height: 0 !important;
  padding: 0 !important
}

.bootstrap-select.bs-container .dropdown-menu {
  z-index: 1060
}

.bootstrap-select .dropdown-toggle:before {
  content: '';
  display: inline-block;
  width: 100%
}

.bootstrap-select .dropdown-toggle .filter-option {
  position: absolute;
  top: 0;
  left: 0;
  padding-top: inherit;
  padding-right: inherit;
  padding-left: inherit;
  overflow: hidden;
  width: 100%;
  text-align: left
}

.bootstrap-select .dropdown-toggle .filter-option-inner {
  overflow: hidden
}

.bootstrap-select .dropdown-toggle .caret {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -2px;
  vertical-align: middle
}

.bootstrap-select[class*="col-"] .dropdown-toggle {
  width: 100%
}

.bootstrap-select .dropdown-menu {
  min-width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.bootstrap-select .dropdown-menu>.inner:focus {
  outline: none !important
}

.bootstrap-select .dropdown-menu.inner {
  position: static;
  float: none;
  border: 0;
  padding: 0;
  margin: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none
}

.bootstrap-select .dropdown-menu li {
  position: relative
}

.bootstrap-select .dropdown-menu li.active small {
  color: #fff
}

.bootstrap-select .dropdown-menu li.disabled a {
  cursor: not-allowed
}

.bootstrap-select .dropdown-menu li a {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none
}

.bootstrap-select .dropdown-menu li a.opt {
  position: relative;
  padding-left: 2.25em
}

.bootstrap-select .dropdown-menu li a span.check-mark {
  display: none
}

.bootstrap-select .dropdown-menu li a span.text {
  display: inline-block
}

.bootstrap-select .dropdown-menu li small {
  padding-left: 0.5em
}

.bootstrap-select .dropdown-menu .notify {
  position: absolute;
  bottom: 5px;
  width: 96%;
  margin: 0 2%;
  min-height: 26px;
  padding: 3px 5px;
  background: #f5f5f5;
  border: 1px solid #e3e3e3;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  pointer-events: none;
  opacity: 0.9;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.bootstrap-select .no-results {
  padding: 3px;
  background: #f5f5f5;
  margin: 0 5px;
  white-space: nowrap
}

.bootstrap-select.fit-width .dropdown-toggle .filter-option {
  position: static
}

.bootstrap-select.fit-width .dropdown-toggle .caret {
  position: static;
  top: auto;
  margin-top: -1px
}

.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
  position: absolute;
  display: inline-block;
  right: 15px;
  top: 5px
}

.bootstrap-select.show-tick .dropdown-menu li a span.text {
  margin-right: 34px
}

.bootstrap-select .bs-ok-default:after {
  content: '';
  display: block;
  width: 0.5em;
  height: 1em;
  border-style: solid;
  border-width: 0 0.26em 0.26em 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg)
}

.bootstrap-select.show-menu-arrow.open>.dropdown-toggle {
  z-index: 1061
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
  content: '';
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid #f2f7fa;
  position: absolute;
  bottom: -4px;
  left: 9px;
  display: none
}

.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
  content: '';
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid white;
  position: absolute;
  bottom: -4px;
  left: 10px;
  display: none
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
  bottom: auto;
  top: -3px;
  border-top: 7px solid #f2f7fa;
  border-bottom: 0
}

.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
  bottom: auto;
  top: -3px;
  border-top: 6px solid white;
  border-bottom: 0
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:before {
  right: 12px;
  left: auto
}

.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:after {
  right: 13px;
  left: auto
}

.bootstrap-select.show-menu-arrow.open>.dropdown-toggle:before,
.bootstrap-select.show-menu-arrow.open>.dropdown-toggle:after {
  display: block
}

.bs-searchbox,
.bs-actionsbox,
.bs-donebutton {
  padding: 4px 8px
}

.bs-actionsbox {
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.bs-actionsbox .btn-group button {
  width: 50%
}

.bs-donebutton {
  float: left;
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.bs-donebutton .btn-group button {
  width: 100%
}

.bs-searchbox+.bs-actionsbox {
  padding: 0 8px 4px
}

.bs-searchbox .form-control {
  margin-bottom: 0;
  width: 100%;
  float: none
}



/*# sourceMappingURL=styles.min.css.map */
