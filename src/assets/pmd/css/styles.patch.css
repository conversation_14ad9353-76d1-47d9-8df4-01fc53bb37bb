body {
  overflow-y: auto !important;
}

.avatar_img > img {
  max-width: 100%;
  max-height: inherit;
}

.hl_navbar--logo {
  cursor: default;
}

.hl_navbar--logo img {
  max-width: 80%;
  max-height: 80px;
}

.top-buffer {
  margin-top: 50px;
}

.hl_login--body .btn {
  margin-top: 0px;
}

.button-holder {
  margin-top: 20px;
}

.show {
  display: block;
}

/* .invisible {
    visibility: hidden;
} */

.invisible {
  opacity: 0;
}

.bottom-buffer {
  margin-bottom: 50px;
}

.pointer {
  cursor: pointer;
}

.message-bubble p {
  white-space: pre-wrap;
  margin: 0;
}

.messages-single .time-date {
  color: #afb8bc;
  font-size: 0.75rem;
  text-align: right;
  margin-right: 5px;
}

.messages-single.\--own-message .time-date {
  float: none;
  margin-left: 5px;
  text-align: left;
}

.hl_recent-activities--item p.time-date {
  color: #afb8bc !important;
}

.--own-message .message-file-uploaded {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.message-file-uploaded {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.message-bubble a {
  color: white;
  text-decoration: underline;
}

.--own-message .message-bubble a {
  color: #607179;
  text-decoration: underline;
}

.aside-texts-infos p {
  height: 38px;
}

.file-uploaded-img > img,
.message-bubble > img {
  cursor: pointer;
  max-width: 100%;
  max-height: 100%;
  margin-bottom: 10px;
}

.messages-single {
  max-width: 400px;
}

.--own-message.messages-single {
  float: right;
}

@media (min-width: 768px) {
  .hl_marketing--header {
    margin-top: 20px;
  }
}

.hl_edit-event-modal-main
  .card
  .compose-controls
  .compose-controls-left
  a.active {
  color: #188bf6;
}

.hl_marketing--header-inner > .name-holder {
  display: grid;
  width: 100%;
}

.hl_marketing--header-inner > .name-holder > input {
  border: none;
  background: unset;
  margin-bottom: 0.5rem;
  line-height: 1.2;
  font-size: 1.5rem;
  font-weight: normal;
  margin-top: 0;
  color: #2a3135;
  outline: none;
}

.marketing-config-user {
  margin-top: 20px;
  height: 35px;
}

.marketing-config-user > div {
  display: inline-block;
  position: relative;
}

.marketing-config-user .icon-close {
  font-size: 0.7em;
  position: absolute;
  top: 15px;
  right: -25px;
  cursor: pointer;
}

.hl_settings--preview-inner {
  overflow: scroll;
  padding: 16px 10px 16px 10px;
}

.calendar-day-inner > p {
  margin-bottom: 8px;
}

.calendar-day-inner > h5 {
  margin-bottom: 0px;
}

.delay-value {
  outline: none;
  border: none;
  width: 50px;
  margin-right: 5px;
  padding: 0px 10px;
}

.attachment > div {
  position: relative;
  display: inline-block;
}

.attachment > div > span {
  margin-left: 5px;
}

.attachment .icon-close {
  font-size: 0.7em;
  position: absolute;
  top: 10px;
  right: -25px;
  cursor: pointer;
}

.message-bubble.attachment {
  background-color: white !important;
}

/* If this is not there, in the create campaign screen
the second email/sms modal that opens up after the action
type picker modal does not scroll*/
.modal {
  overflow-y: auto;
}

.messages-list--text p {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.messages-list--item.selected {
  background-color: rgba(24, 139, 246, 0.1);
}

.hl_conversations--message {
  height: 100%;
}

.unavailable {
  font-style: italic;
  font-weight: 300;
}

.aside-texts-infos p {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.messages-list {
  margin: 0px;
  padding: 0px;
  height: calc(100vh - 265px);
}

.event-card--title .fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}

.hl_add-event-modal .fa-phone {
  display: block;
  margin: 0px auto 10px auto;
  font-size: 24px;
  color: #37ca37;
}

.hl_add-event-modal .modal-body .row > div a .fas {
  display: block;
  margin-bottom: 10px;
  font-size: 24px;
  color: #37ca37;
}

.hl_campaign-configuration.edit-call .form-group input {
  width: 160px;
}

.hl_campaign-configuration.edit-call h6 {
  font-size: 14px;
  margin-bottom: 5px;
}

.hl_campaign-configuration hr {
  margin-bottom: 20px;
}

.hl_campaign-configuration .scheduling {
  margin-left: 50px;
}

.hl_campaign-configuration .scheduling .condition .explanation {
  width: 306px;
  display: block;
  margin-top: 10px;
}

.hl_campaign-configuration .scheduling .condition .bootstrap-select {
  width: 150px;
}

.slide-enter-active {
  transition: all 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateY(-50px);
  opacity: 0;
}

.audio-upload {
  width: 372.25px;
  height: 44px;
  text-align: center;
  border: 1px dashed #404040;
  cursor: pointer;
  padding: 8px;
  border-radius: 5px;
}

.audio-upload .message {
  font-weight: 500;
  color: #404040;
}

.dollar-right {
  top: 13px;
  position: absolute;
  z-index: 4;
  right: 15px;
  font-weight: 500;
  cursor: default;
}

.hl_wrapper--inner.opportunities {
  padding-bottom: 0px;
}

.hl_opportunities--set ul {
  padding-right: 5px;
  max-height: unset;
  margin-bottom: 0px;
  width:260px;
}

.hl_opportunities--drag-actions-inner {
  padding:0px;
}

.hl_opportunities-item .card-info .avatar {
  margin-right: 5px;
}

.hl_opportunities-item .tag-group {
  max-height: 22px;
  overflow: hidden;
  margin-bottom: 5px;
}

.hl_opportunities-item .tag-group .tag {
  padding: 5px 8px;
}

.hl_opportunities-item .card-body {
  padding: 7px 7px 5px 7px;
  font-size: 13px;
}

.hl_opportunities-item .card-info:not(:last-child) {
  margin-bottom: 5px;
}

.smooth-dnd-container.vertical {
  height: 100%;
}

.tag-options {
  max-height: 350px;
  overflow-y: scroll;
  width: 100%;
  position: absolute;
  top: 81px;
}

.tag-options .add-new a {
  color: #188bf6 !important;
}

.tag-options .add-new a i {
  margin-right: 10px;
  font-size: 0.7rem;
}

.add-new-contacts h4 {
  font-weight: 400;
}

.add-new-contacts .form-group.dropdown {
  max-width: 350px;
}

.add-new-contacts .form-group.dropdown label {
  display: none;
}

.add-new-contacts .form-group.dropdown .tag-options {
  top: 52px;
}

.aside-texts-infos .icon-tag {
  color: #afb8bc !important;
}

.report-type>.form-group .bootstrap-select>.btn.dropdown-toggle {
  background: #ffffff
}

.conversation-tags {
  overflow: visible !important;
}

.conversation-tags .dropdown {
  width: 100%;
}

.conversation-tags .dropdown .tag-options {
  top: 35px;
}

.conversation-tags .tag-options .add-new a i {
  top: 0px;
}

.conversation-dnd svg {
  color: #afb8bc !important;
  margin-right: 15px;
}

.campaign-tags .dropdown .tag-options {
  top: 50px;
}

.stage-holder {
  margin-top: 10px;
}

.stage-holder .fa-chevron-circle-up,
.stage-holder .fa-chevron-circle-down {
  margin-top: 4px;
  display: block;
  cursor: pointer;
}

.stage-holder .arrow-holder {
  display: inline-block;
  margin-right: 10px;
  vertical-align: middle;
}

.stage-holder .name-holder {
  width: 90%;
  display: inline-grid;
}

.stage-holder .fa-trash-alt {
  margin: 15px 10px;
  cursor: pointer;
}
.stage-holder .fa-filter {
  margin: 15px 10px;
  cursor: pointer;
}

.hl_opportunities .row {
  overflow-x: overlay;
  overflow-y: hidden;
}

.opportunities-filter-date-picker {
  width: 94px;
  background-color: transparent !important;
  padding: 0;
  cursor: pointer;
  color: #188bf6;
}

.opportunities-filter-date-picker:hover {
  text-decoration: underline;
}

.opportunities-filter-date-picker::placeholder {
  color: #188bf6 !important;
  opacity: 1;
}

.opportunities-filter-date-picker::-webkit-input-placeholder {
  color: #188bf6;
  opacity: 1;
}

.opportunities-filter-date-picker.start-date {
  text-align: right;
}

.opportunities-date-separator {
  margin: 0 10px;
}

.hl_dashboard--sales-funnel .sales-funnel-chart {
  max-width: 420px;
}

.sales-funnel-chart svg.highcharts-root {
  width: 480px !important;
}

.hl_conversations--wrap {
  height: calc(100vh - 83px);
}

.hl_opportunities--drag-actions {
  z-index: 10;
}

.hl_opportunities--drag-actions .drag-action.\--lost:hover,
.hl_opportunities--drag-actions .drag-action.\--lost.active {
  background-color: #ec5454;
}

.hl_opportunities--drag-actions .drag-action.\--abandoned:hover,
.hl_opportunities--drag-actions .drag-action.\--abandoned.active {
  background-color: #fff;
}

.hl_opportunities--drag-actions .drag-action.\--won:hover,
.hl_opportunities--drag-actions .drag-action.\--won.active {
  background-color: #4bcf4b;
}

.lost-hover {
  background-color: #da1919 !important;
}

.won-hover {
  background-color: #2ba32b !important;
}

.abandoned-hover {
  background-color: #cde0ec !important;
}

.no-top-padding {
  padding-top: 0px;
}

.messages-single {
  display: inline-block;
}

.\--own-message.messages-single {
  text-align: right;
}

.messages-single.\--own-message .message-bubble p {
  text-align: left;
  margin: 0;
}

.hl_navbar--links > li a svg {
  display: block;
  font-size: 1rem;
  opacity: 0.5;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  margin-right: 20px;
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
}

.hl_navbar--links > li.active > a svg {
  opacity: 1;
}

.hl_navbar--links > li a:hover svg,
.hl_navbar--links > li a:focus svg,
.hl_navbar--links > li a:active svg {
  opacity: 1;
}

.date-picker {
  background: #f3f8fb !important;
}

.vdp-datepicker__calendar {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 1rem !important;
  margin-bottom: 3rem !important;
}

.hint-icon {
  color: #607179;
  opacity: 0.6;
}

.hint-icon-disabled {
  color: #607179;
  opacity: 0.1;
}

.icon-with-loader {
  margin-right: 5px;
  display: inline-block;
  height: 25px;
  width: 25px;
  position: relative;
}

.center-inside {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.event-card--bottom select {
  background-color: white;
  border: none;
  height: 24px;
  border-radius: 0;
}

.event-card--bottom .when {
  margin-right: 5px;
}

.event-card--bottom .type {
  width: 100px;
}

.v-select {
  width: 100%;
  background: #f3f8fb;
  border-radius: 4px !important;
  height: auto;
}

.custom-element.v-select .dropdown-toggle::after {
  display: none;
}

.v-select .dropdown-toggle {
  border: 0px !important;
  padding: 12px !important;
  height: auto;
  font-size: 14px;
  color: #607179;
}

.v-select .dropdown-toggle .selected-tag .close {
  display: none;
}

.v-select .dropdown-toggle .selected-tag {
  padding: 5px;
}

.hl_navbar--links {
  margin-top: 15px;
  overflow-y: auto;
  height: calc(100vh - 198px);
  overflow-x: hidden;
}

*.--light {
  color: #afb8bc;
}

.table_progress p {
  width: unset;
}

.messages-list-search {
  margin-bottom: 0px;
}

.clearfix::after {
  content: ' ';
  display: block;
  height: 0;
  clear: both;
}

.text-template-option:hover {
  color: #2a3135;
}

.dragoverd {
  background-color: #afb8bc;
  z-index: 2;
  opacity: 0.45;
}

.triggers_actions .btn-group.show, .form-builder-list .btn-group.show {
  display: inline-flex;
}

.dropdown.v-select.searchable {
  width: 100%;
}

.trigger-name {
  background: none;
  border: none;
  border-bottom: 1px solid rgb(224, 224, 224);
  width: 80%;
}

.trigger-name:focus {
  outline: none;
}

.trigger-state,
.trigger-state:hover,
.trigger-state:active,
.trigger-state:focus {
  height: auto !important;
  font-size: 0.6875rem;
  font-weight: normal;
  padding: 9.5px 20px;
  color: #2a3135;
}

.form-group.dropdown {
  width: 100%;
}

.trigger-type p {
  font-size: 0.8rem;
  line-height: 1.3;
  margin-top: 5px;
}

.trigger-type h5 {
  font-weight: 500;
  font-size: 0.8rem;
}

.hl_rules--wrap .card .form-input-group.filters > * {
  max-width: unset;
}

.message-box .use-template {
  display: inline-block;
  font-weight: 500;
  color: #607179;
  opacity: 0.6;
}

.message-box .use-template:hover,
.message-box .use-template:active,
.message-box .use-template:focus {
  opacity: 1;
}

.message-box .use-template .icon {
  font-size: 16px;
}

.hl_conversations {
  overflow-x: auto !important;
}

.hl_conversations--message-body .message-box .message-box-bottom {
  flex-direction: row;
  -ms-flex-direction: row;
  -webkit-box-direction: normal;
}

.hl_conversations--message-body > * {
  height: 98.5%;
}

.hl_wrapper {
  overflow: visible;
}

.between {
  -ms-flex-pack: space-between !important;
  -webkit-box-pack: justify !important;
  -webkit-justify-content: space-between !important;
  justify-content: space-between !important;
}

.messages-group-heading > .img {
  background: #fff;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: -25px;
}

.message-input-wrap {
  z-index: 8 !important;
}

.overflowdisabled {
  overflow: unset !important; /* Bug reported by Evan Shapiro Case Story 988*/
}

.hl_recent-activities {
  z-index: 100;
}

div.scroll-area::-webkit-scrollbar {
  -webkit-appearance: none;
  height: 7px;
}

div.scroll-area::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

div.vertical-scroll-area::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 7px;
}

div.vertical-scroll-area::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.phone-modal .vertical-scroll-area {
  max-height: 200px;
  overflow: scroll;
}

.phone-modal .radio > label {
  font-size: 0.875rem;
  font-weight: 500;
}

.v-select .dropdown-menu > .highlight > a {
  background: #5897fb !important;
  color: #fff !important;
}

.ctk-date-time-picker input:focus {
  border:0;
  outline:none;
  box-shadow:none;
}
.field.has-value:not(.no-label) .field-input {
  padding-top: 0px;
}
/*
.ctk-date-time-picker .field .field-input,
.date-time-picker .field .field-input {
  background: #f3f8fb !important;
  font-family: Roboto, system, -apple-system, BlinkMacSystemFont,
    '.SFNSDisplay-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  font-size: 0.875rem !important;
  color: #2a3135 !important;
  border: none !important;
  border-radius: 0.3125rem !important;
  padding: 15px 20px !important;
  -webkit-transition: all 0.2s ease-in-out 0s !important;
  transition: all 0.2s ease-in-out 0s !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  display: block !important;
  width: 100% !important;
  line-height: 1.5 !important;
  overflow: visible !important;
  font-weight: 400 !important;
  height: 51px !important;
} */

/* .ctk-date-time-picker .field .field-label {
	font-weight: 400 !important;
	color: #6c757d !important;
	font-size: 1rem !important;
	opacity: 1 !important;
} */

.date-time-picker .field.has-value .field-label,
.ctk-date-time-picker .field.has-value .field-label {
  color: #6c757d !important;
  opacity: 0 !important;
}

.messages-single .message-bubble {
  background-color: #eff2f9 !important;
  color: #000000cc;
}

.messages-single.\--own-message .message-bubble {
  background-color: #2284ff !important;
  color: #fff;
}

.messages-single .message-bubble:before {
  border-color: transparent #eff2f9 transparent transparent;
}

.messages-single.\--own-message .message-bubble:before {
  border-color: #2284ff transparent transparent transparent;
}

.\--own-message .message-bubble a {
  color: #eff2f9;
}

.message-bubble a {
  color: #607179;
}

.calendar-day-on-drag:before,
.smooth-dnd-ghost:before {
  content: none !important;
}

.hl_create-campaign .new-event-day-item .event-card {
  cursor: grab;
}

.hl_create-campaign .new-event-day-item .event-card:active {
  cursor: grabbing;
}

.hl_create-campaign .new-event-day-item + .new-event-day-item {
  margin-top: 30px;
}

.hl_create-campaign
  .smooth-dnd-draggable-wrapper
  + .smooth-dnd-draggable-wrapper {
  margin-top: 30px;
}

@media (min-width: 768px) {
  .hl_create-campaign .new-event-day-item .calendar-day:before {
    content: none !important;
  }
}

@media (min-width: 768px) {
  .hl_create-campaign
    .smooth-dnd-container.vertical
    > .smooth-dnd-draggable-wrapper:before {
    content: '';
    display: block;
    width: 1px;
    background-color: #e0ecf3;
    position: absolute;
    margin: auto;
    top: 0;
    left: 32px;
    bottom: -30px;
    z-index: 1;
  }
}

.edit-wait .scheduling .form-group {
  display: block;
}

.edit-when .bootstrap-select {
  max-width: 125px !important;
  min-width: 125px !important;
}

.wait-start-time .bootstrap-select {
  max-width: 100px !important;
  min-width: 100px !important;
}

.wait-start-time .bootstrap-select.ampm {
  margin-left: 10px;
}

/* .times-holder > div {
	display: flex;
    align-items: center;
}

.times-holder .dropdown.bootstrap-select {
	width: 130px !important;
} */

.hl_add-event-modal .modal-body .row > div a svg {
  display: block;
  margin-bottom: 10px;
  font-size: 24px;
  color: #37ca37;
  margin: 0px auto 10px auto;
}

.hl_create-campaign
  .new-event-day-item
  .event-card--top
  .event-card--title
  .action-icon {
  font-size: 20px;
  margin-right: 10px;
  color: #188bf6;
}

.hl_header--phone .caller-id .dropdown-menu {
  margin-top: 0px;
}

.opportunity-actions .fa-phone,
.contact-detail-actions .fa-phone {
  font-size: 0.875rem;
  color: #37ca37;
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.field-type-group {
  margin: 0px;
}

.field-type-group > div {
  padding: 15px;
  text-align: center;
  color: #000000;
  background-color: #f9f9f9;
  border: 2px solid #ffffff;
  cursor: pointer;
  border-radius: 5px;
}

.field-type-group > div:hover {
  background-color: #cee6ea !important;
}

.field-type-group > div.selected {
  background-color: #cee6ea !important;
}

.field-type-group > div > p {
  text-transform: capitalize;
}

/* #dashboard .hl_dashboard--sales-funnel {
  z-index: 0;
} */
#dashboard.hl_dashboard .card-header {
  z-index: auto;
}
#dashboard.hl_dashboard .card-header .dropdown.bootstrap-select {
  z-index: 2;
}

#dashboard .hl_dashboard--sales-funnel .card-header {
  padding-right: 20px;
}

#dashboard .hl_dashboard--matters-added .card-header,
#dashboard .hl_dashboard--pipeline-value .card-header,
#dashboard .hl_dashboard--conversion-rate .card-header {
  padding-right: 20px;
}

#dashboard .hl_dashboard--conversion-rate .vdp-datepicker__calendar,
#dashboard .hl_dashboard--sales-funnel .vdp-datepicker__calendar {
  right: 0px;
}

.email-item--body {
  word-break: break-all;
}

/*--------FORM BUILDER------------*/
#form-builder {
  width: 100%;
}

.hl_form-builder--sidebar .dragdrop-items li {
  padding: 0;
}

.hl_form-builder--sidebar .dragdrop-items li .drag {
  padding: 10px 15px;
  width: 100%;
}

.hl_form-builder--sidebar .dragdrop-items li .drag i {
  position: relative;
  float: right;
  top: 8px;
}

.hl_form-builder--main .form-builder--wrap .form-builder--item,
.menu-field-wrap {
  position: relative;
}

#the-form-builder .menu-field-wrap .close-icon {
  position: absolute;
  right: 10px;
  top: 5px;
  opacity: 1;
  transition: all 0.5s ease-in;
  font-size: 10px;
  background: #f5f5f5;
  padding: 5px 7px;
  border-radius: 50%;
  height: 25px;
  cursor: pointer;
}

/* #the-form-builder .menu-field-wrap:hover .close-icon {
  opacity: 1;
  transition: all 0.5s ease-in;
}

.animated .close-icon {
  opacity: 0;
} */

.hl_form-builder--main .form-builder--wrap .form-builder--item:hover {
  border: none;
}

#_builder-form .form-builder--item {
  border: none;
}

.hl_form-builder--main
  .form-builder--wrap
  .form-builder--item
  .captcha-vlue
  input {
  border: 0;
  width: 20px;
  font-size: 29px;
  font-weight: bold;
  color: #999;
}

.hl_form-builder--main .form-builder--wrap .form-builder--item .captcha-vlue {
  float: left;
  width: auto;
  font-size: 29px;
  font-weight: bold;
  line-height: 42px;
  color: #999;
}

.hl_form-builder--main .form-builder--wrap .form-builder--item #captchaInput {
  width: 75px;
  margin-left: 0.5em;
  border: 0px;
  background: #f2f7fa;
  height: 45px;
  padding: 0 10px;
  outline: none;
  font-size: 32px;
  font-weight: bold;
  line-height: 45px;
  color: #999;
}

.hl_form-builder--main
  .form-builder--wrap
  .form-builder--item
  .captcha-vlue
  span {
  font-weight: normal;
  font-size: 36px;
  color: #ccc;
}

div#the-form-builder.drag-enter {
  background-color: #f9f3de !important;
}

.form-builder--wrap {
  position: relative;
  padding: 0;
}

div#_branding-wrap {
  position: relative;
  padding: 0px 16px 10px;
}

.form-builder-drop {
  min-height: 400px;
  padding: 30px 15px;
}

.builder-padding-remove {
  padding: 30px 0;
}

.branding-url {
  display: block;
  padding: 0;
  text-align: left;
}

.branding-url > img {
  width: 100px;
}

.branding-url .cmp-name {
  color: #607179;
  font-size: 12px;
  /* margin-left: 9px; */
  display: block;
}

.add-dropzone-builder {
  position: absolute;
  background: transparent;
  width: 100%;
  height: 86px;
  top: 0;
  left: 0;
}

.drop-zone-wrap.hover {
  padding: 0 15px;
}

.drop-zone-wrap.hover .add-dropzone-builder {
  border-top: 2px solid #00bd24;
}

.drop-zone-wrap.hover .triangle {
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
  border-style: solid;
  width: 0px;
  height: 0px;
  line-height: 0px;
  border-width: 0px 8px 8px 8px;
  border-color: transparent transparent #00bd24 transparent;
  position: absolute;
  top: 2px;
  margin: 0 auto;
  left: 0;
  right: 0;
  transform: rotate(180deg);
}

.blank-dropzone {
  background: transparent;
  width: 100%;
  height: 80px;
  position: relative;
  border: 1px dotted #e1e1e1;
}

.builder-form-name:hover {
  text-decoration: none;
}

.captcha-wrap-img {
  width: 335px;
  display: block;
}

.hl_form-builder--sidebar .tab-content {
  padding: 50px 0;
}

.hl_form-builder--sidebar .dragdrop-wrap {
  top: 53px;
}

.form-builder--item.form-builder--image img {
  width: 100%;
}

.form-builder--item {
  margin-bottom: 15px;
}

.fields-settings-overlay {
  height: 100%;
  width: 300px;
  position: fixed;
  top: 0;
  z-index: 9999;
  background-color: #e7f3fe;
  margin: auto;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  padding-top: 100px;
  padding-bottom: 0;
  padding-left: 20px;
  padding-right: 20px;
  overflow-y: auto;
}

.builder-inline
  .smooth-dnd-draggable-wrapper.col-md-6
  .f-even
  .form-builder--item {
  padding-left: 4px;
}

.builder-inline
  .smooth-dnd-draggable-wrapper.col-md-6
  .f-odd
  .form-builder--item {
  padding-right: 4px;
}

.builder-inline
  .smooth-dnd-draggable-wrapper.col-12
  .form-builder--item {
    padding-left: 4px;
    padding-right: 4px;
  }
#the-form-builder .menu-field-wrap .close-icon {
  right: 20px;
}

#_builder-form .active {
  border: 1px dashed #ff9900;
}

.fields-settings-commom .form-group {
  position: relative;
}

.fields-settings-commom .required-fields {
  position: relative;
  top: 3px;
  left: 2px;
}

a.field-seeting-align {
  padding: 0 20px 0 0;
  font-size: 20px;
}

.btn-group.font-family-dropdown button {
  color: #2a3135;
  font-weight: 400;
  text-align: left;
  font-size: 0.875rem;
}

.btn-group.font-family-dropdown {
  width: 100%;
}

.custom-field-wrap-btn {
  margin-top: 20px;
}
.hl_form-builder--sidebar .nav-tabs.top-nav-wrapper-customfield {
  background: #e7f3fe;
  z-index: 9;
}

.custom-field-name-heading {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.form-builder--item .dropdown.bootstrap-select button.dropdown-toggle {
  pointer-events: none;
}
.form-builder--item.form-builder--image {
  margin-top: 15px;
  padding-left: 0;
  padding-right: 0;
}
/*----------END OF FORM BUILDER--------*/

/*---SURVEY START--*/
.survey-wrap-container .survey-form-container {
  margin: 0 auto;
  padding-top: 10px;
  padding-bottom: 10px;
  position: relative;
}
.survey-form-container a.close-icon-survey {
  position: absolute;
  right: 10px;
  top: 10px;
}

hr.logic-br {
  border: 0.5px dashed #aaa;
}

.logic-container .justify-content-between {
  margin-bottom: 20px;
}
.logic-container .justify-content-between select {
  height: 28px;
  background: #fff;
  padding: 0px;
  /* border: none; */
  top: 0;
  position: relative;
  border-radius: 3px;
  color: #607179;
  width: 70px;
}
.form-child-wrapper {
  margin: 0 auto;
}
.survey-form-container .form-builder--item.form-builder--image {
  margin-top: 15px;
  padding-left: 0;
  padding-right: 0;
}
/*---SURVEY END---*/

/*Radio Button*/
.option-radio {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
}

.option-radio label {
  line-height: 30px;
  display: block;
  padding-left: 30px;
  cursor: pointer;
  margin-bottom: 0;
  font-size: 0.875rem;
}

.option-radio label:before {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  border-radius: 10px;
  border: 2px solid #e6edf2;
  background: #fff;
  position: absolute;
  top: 5px;
  left: 0;
}

.option-radio label:after {
  font-family: 'Magicons';
  color: #fff;
  content: '\e96c';
  font-size: 0.625rem;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid #00d26d;
  background-color: #00d26d;
  background-size: 10px 7px;
  position: absolute;
  top: 10px;
  left: 5px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  opacity: 0;
}

.option-radio input[type='radio'] {
  margin: 0;
  display: none;
  visibility: hidden;
}

.option-radio input[type='radio']:checked + label {
  color: #2a3135;
}

.option-radio input[type='radio']:checked + label:after {
  width: 20px;
  height: 20px;
  opacity: 1;
  top: 5px;
  left: 0;
  border-radius: 10px;
}

.option-radio input[type='radio'] + label:before,
.option-radio input[type='radio'] + label:after {
  border-radius: 50%;
}

.option-radio label {
  width: 100%;
}
.option-radio label input.add-custom-opt {
  background: #fff;
  border: none;
  width: 50%;
  border-bottom: 1px solid #e1e1e1;
  border-radius: 3px;
}
.ghl-date-input .vdp-datepicker {
  width: 100%;
}

textarea.footer-modal-html {
  background: url(http://i.imgur.com/2cOaJ.png);
  background-attachment: local;
  background-repeat: no-repeat;
  padding-left: 35px;
  padding-top: 10px;
  border-color: #ccc;
  width: 100%;
}

a.ghl-link {
  color: blue !important;
  text-decoration: underline !important;
}

/*End of radio button*/

/*--integrate Page--*/
.form-builder-integrate-wrapper {
  /* position: fixed; */
  /* top: 0;
  left: 0; */
  width: 100%;
  min-height: 100vh;
  background: #fff;
  z-index: 9999;
}

.back-btn {
  background: #188bf6;
}

.form-builder-integrate-wrapper .top-header.dark-bg button.save-exit {
  position: absolute;
  right: 10px;
}

.form-builder-integrate-wrapper .top-header.dark-bg button.back-btn {
  position: absolute;
  left: 10px;
}

.form-builder-integrate-wrapper .top-header.dark-bg {
  background: #fff;
  padding: 20px;
  min-height: 80px;
  box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
  -webkit-box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
}

.form-builder-integrate-wrapper .tab-wrapper {
  margin: 50px auto 0;
  max-width: 750px;
  text-align: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 500px;
  padding: 50px 40px;
  background: #fff;
}

.form-builder-integrate-wrapper .tab-wrapper .sub-heading {
  font-size: 20px;
}

.form-builder-integrate-wrapper .tab-wrapper #tabs {
  margin-top: 20px;
}

.form-builder-integrate-wrapper .tab-wrapper label {
  font-size: 18px;
  text-align: left;
}

.nothing-found p {
  text-align: center;
}

.box img {
  max-height: 70%;
}

.v-spinner .v-moon1 {
  margin: auto;
}

.handle {
  cursor: grab;
}

.sortable-chosen .handle {
  cursor: grabbing;
}

.input-group-text {
  border-left: unset !important;
  border-bottom: unset !important;
  border-top: unset !important;
  border-right: 1px solid #dee5e8;
  background-color: #f3f8fb !important;
}

.input-group-prepend {
  margin-right: 0px !important;
}

.sortable-chosen {
  background-color: #e2e6e8 !important;
}

.hl_contact-details-left {
  z-index: 8 !important;
}

.hl_contact-details-new--wrap
  .hl_contact-details-right
  .hl_contact-details-right-tabs-bottom.expand {
  background: #fff;
}

.hl_contact-details-new--wrap .hl_contact-details-right {
  z-index: 0 !important;
}

.item-center {
  position: relative;
  float: left;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.hl_contact-details-new--wrap .hl_contact-details-left {
  max-width: 430px !important;
}

.hl_contact-details-new--wrap .hl_contact-details-right {
  max-width: 401px !important;
}

.hl_contact-details-new--wrap .hl_contact-details-left {
  flex: 4 1 0;
  max-width: unset !important;
}

.hl_contact-details-center {
  flex: 6 1 0;
  max-width: unset !important;
}

.hl_contact-details-new--wrap .hl_contact-details-right {
  flex: 4 1 0;
  max-width: unset !important;
}

/* .hl_contact-details-new--wrap .hl_contact-details-left .hl_activity-history {
  top: 61% !important;
} */

/* .hl_contact-details-new--wrap
  .hl_contact-details-left
  .hl_contact-details-left-tabs {
  bottom: 40% !important;
} */

.vdp-datepicker__calendar .cell.selected,
.vdp-datepicker__calendar .cell.selected.highlighted,
.vdp-datepicker__calendar .cell.selected:hover {
  background: #188bf6 !important;
  color: #fff;
}

.hl_add-event-modal .modal-body .row > div a .fab {
  display: block;
  margin-bottom: 10px;
  font-size: 24px;
  color: #37ca37;
}

.hl_add-event-modal .modal-body .row > div a .fab {
  display: block;
  margin-bottom: 10px;
  font-size: 24px;
  color: #37ca37;
}

.message-bubble {
  word-break: break-word;
}

.error {
  color: #e93d3d;
}

#dashboard .hl_dashboard--need-action .card-header {
  padding-right: 20px;
}
#dashboard .date-time-picker .field .field-input,
#opportunities .date-time-picker .field .field-input,
#stats-tab .date-time-picker .field .field-input,
#customers .date-time-picker .field .field-input {
  height: auto !important;
  padding: 0 !important;
  color: #188bf6 !important;
  text-align: center;
  border: none !important;
  background: transparent !important;
}

#dashboard .date-time-picker .field .field-input:hover,
#opportunities .date-time-picker .field .field-input:hover,
#customers .date-time-picker .field .field-input:hover {
  text-decoration: underline;
  cursor: pointer;
}

.hl_selectpicker2_controls .bootstrap-select > .btn.dropdown-toggle {
  background-color: #fff;
  padding: 9.5px 50px 9.5px 20px;
  height: 40px !important;
}

.hl_selectpicker2_controls
  .bootstrap-select
  > .btn.dropdown-toggle
  .filter-option {
  font-weight: normal;
  color: #2a3135;
}

.hl_dashboard--need-action .hl_tasks-list .dropdown.show {
  display: inline-block !important;
}

img.radio-image-option-set {
  width: 100%;
  margin-bottom: 10px;
  cursor: pointer;
}

#bsd-iframe-widget {
  min-height: 300px;
  max-height: 720px !important;
  max-width: 372px;
  /*border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 18px;*/
  position: fixed;
  right: 0;
  bottom: 0;
  height: calc(100% - 120px) !important;
  width: 380px;
  border: none;
  z-index: 9999998;
}

@media screen and (max-width: 800px) {
  #bsd-iframe-widget {
    border-radius: 0;

    max-width: 100% !important;
    max-height: 100% !important;
    bottom: 0;
    right: 0;

    height: 100% !important;
    width: 100% !important;

    z-index: 1000000000;
  }
}

#bsd-iframe-button {
  right: -0;
}

.hl_agency-location--details .hl_tasks-list .dropdown.show {
  display: inline-block !important;
}

.container-fluid {
  max-width: unset !important;
}

@media (min-width: 992px) {
  .calendar-settings .table tbody tr td:last-child {
    width: 170px;
  }
}

.action-type-tag {
  display: inline-block;
  margin-left: 10px;
  font-size: 12px;
  background-color: #188bf6;
  color: white;
  padding: 8px 10px;
  border-radius: 100px;
  line-height: 1;
}

.hl_create-campaign .new-event-day-item .event-card--top .event-card--title h5 {
  display: inline-block;
}

.card
  .card-body
  .form-group
  .form-input-group
  .form-input-group-item:first-child {
  border-left: none;
}
.card .card-body .form-group .form-input-group > input {
  border-left: 1px solid #e0ecf3;
}
.hl_rules--wrap .card .form-input-group-item:nth-last-child(2) .form-control {
  border-radius: 0 0.3125rem 0.3125rem 0;
}
.hl_rules--wrap .card .form-input-group-item:first-child .form-control {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-right: 1px solid #e0ecf3 !important;
}
.hl_rules--wrap .card .form-input-group-item:nth-of-type(3) .form-control {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-left: 1px solid #e0ecf3 !important;
}
.hl_rules--wrap .card .form-input-group-item:last-child .form-control {
  border-right: none;
}

.dropdown-menu .section-heading {
  padding: 2px 10px;
  font-weight: 500;
  font-size: 13px;
  background-color: #afb8bc;
  color: white;
}

.hl_rules--wrap .card .form-input-group-item .dropdown-menu .dropdown-item {
  padding-top: 5px;
  padding-bottom: 5px;
}

.mce-notification {
  display: none !important;
}

.py-15 {
  /*Follow bootstrap utilities naming convention*/
  padding-top: 15px !important;
  padding-bottom: 15px !important;
}

.tab-pane {
  padding-top: 15px !important;
}

.bootstrap-select .dropdown-menu {
  min-width: 275px !important; /*Case 1341 Bug in style.min.css and/or style.min.map.css. min-width:100% causing issue see case */
  max-width: 95% !important;
}

.capitalize {
  text-transform: capitalize;
}

@media (min-width: 768px) {
  .hl_campaign-configuration .form-group>label {
    min-width: 165px;
  }
}

.margin-top-25 {
  margin-top: 25px;
}

.merge-fields-call-modal {
  position: absolute;
  right: 5px;
}

.call-whisper-custom-value{
  z-index: 1;
}

.zoomable:hover {
  transform: scale(1.4); /* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
}

.fa-mods{
  color:#86949a;
  font-size: 0.8rem;
}


@-webkit-keyframes moving-gradient {
  0% {
    background-position: -700px 0;
  }
  100% {
    background-position: 700px 0;
  }
}

div.workinprogress {
  display: block;
  height: 20px;
  width:100%;
  background: linear-gradient(to right, #E8F3FE 20%, #7db6ea 50%, #67aef0 80%);
  background-size: 700px 100px;
  animation-name: moving-gradient;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
}


.workinprogress2 {
  display: block;
  background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
  background-size: 100% 100%;
  animation-name: moving-gradient;
  animation-duration: 20s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
}

span.workinprogress {
  height: 20px;
  display: block;
  background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
  background-size: 700px 100px;
  animation-name: moving-gradient;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
  animation-fill-mode: forwards;
}

.alert-box-contact {
  background: #cfd8dc;
  padding: 6px;
  border-radius: 7px;
  text-align: center;
}

.space-wrap{
 white-space: pre-wrap;
}

.space-wrap-preline{
  white-space: pre-line;
 }

.center {
  text-align: center;
}

.form-group.form-check {
  display: flex;
  justify-content: flex-start;
}
.form-hide {
  padding-left: 10px;
}
.fields-settings-commom .hide-fields {
  position: relative;
  top: 2px;
  right: -2px;
}
/*------Date picker chnage for form & survey only----*/
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  width: 250px!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
  border: none!important;
  padding: 1rem!important;
  margin-bottom: 3rem!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar header {
  line-height: normal!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  -webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 1rem !important;
  margin-bottom: 3rem !important;
}
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar .cell {
  height: 27px!important;
  line-height: 27px!important;
}
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar span {
  color: #000;
}

.icon-tag-add {
  display: inline-block;
  width: 18px;
  height: 18px;
  font-weight: bolder;
  background: url("/pmd/img/tag-add.svg") no-repeat center;
  background-size: 18px 18px
}

.icon-tag-delete {
  display: inline-block;
  width: 18px;
  height: 18px;
  font-weight: bolder;
  background: url("/pmd/img/tag-delete.svg") no-repeat center;
  background-size: 18px 18px
}

.icon-phone-svg {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/pmd/img/blue-phone.svg") no-repeat center;
  background-size: 14px 14px
}

.icon-email-svg {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/pmd/img/blue-email.svg") no-repeat center;
  background-size: 14px 14px
}

.icon-email-svg-gray {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url("/pmd/img/gray-email.svg") no-repeat center;
  background-size: 14px 14px
}


.space-wrap{
 white-space: pre-wrap;
}

.center {
  text-align: center;
}

.form-group.form-check {
  display: flex;
  justify-content: flex-start;
}
.form-hide {
  padding-left: 10px;
}
.fields-settings-commom .hide-fields {
  position: relative;
  top: 2px;
  right: -2px;
}
/*------Date picker chnage for form & survey only----*/
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  width: 250px!important;
}
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
  border: none!important;
  padding: 1rem!important;
  margin-bottom: 3rem!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar header {
  line-height: normal!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar {
  -webkit-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border: none !important;
  padding: 1rem !important;
  margin-bottom: 3rem !important;
}
.hl_wrapper--inner.form-builder .vdp-datepicker__calendar .cell {
  height: 27px!important;
  line-height: 27px!important;
}

.hl_wrapper--inner.form-builder .vdp-datepicker__calendar span {
  color: #000;
}

.--blue{
  color: #188bf6 !important;
}


.table_tag {
  padding: 8px 8px !important;
  border-radius: 12px !important;
  line-height: 1 !important;
  max-width: 300px;
  font-weight: 500;
  white-space: normal;
  word-wrap: break-word;
}

.table_tag-more, .tag-more {
  display: inline-block;
  margin-right: 5px;
  margin-bottom: 5px;
  font-size: 12px;
  background: #188bf6;
  color: #fff;
  padding: 8px 8px !important;
  border-radius: 16px !important;
  line-height: 1 !important;
}

@media (min-width: 992px) {
  #opportunities .hl_controls .hl_controls--right .search-form {
    min-width: 250px
  }
}

#opportunities .hl_controls .hl_controls--left h3 {
  height: auto;
  font-size: 1.1rem;
}

div.card-like{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  overflow: hidden;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  position: static;
}

.popover{ /* important don't remove it is useing with b-popover*/
  z-index: 1020 !important;
}

.error-icon{
  position: absolute;
  left: -20px;
  top: 5px;
}
@media (min-width: 768px) {
  .hl_opportunities--drag-actions .drag-action h4 {
    font-size: 16px;
    padding: 17px;
  }
  .hl_opportunities--drag-actions .drag-action {
    padding: 5px;
  }
}

.hl_conversations--messages-list {
  z-index: unset !important;
}

.vs__dropdown-toggle {
  border: 0px;
}
input.vs__search {
    background: rgba(0, 0, 0, 0);
    color: #2a3135;
    border: none;
    border-radius: 0.3125rem;
    padding: 15px 20px;
    -webkit-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
.vs__search, .vs__search:focus {
  border: 0px !important;
}

input.vs__search::-webkit-input-placeholder, input.vs__search::placeholder, input.vs__search::-moz-placeholder {
    color: #afb8bc !important;
}
.v-select.vs--single.vs--searchable {
  border-radius: 0.3125rem;
}

.icon-phone:before {
  content: "" !important;
}

.icon-emoji {
  display: inline-block !important;
}

.icon-file:before {
  content: "" !important;
}

.hl_conversations--message-body .message-input-wrap {
  max-height: unset !important;
}

.hl_conversations--message-body .message-input-wrap .message-box {
  max-height: 350px;
}

.vs__search, .vs__search:focus{
  padding: 12px 18px !important;
}

.vs--open .vs__selected{
  display: none;
}

.custom-dropdown-menu {
  position: relative;
}

.custom-dropdown-menu .dropdown {
  position: absolute;
  top: 26px;
  z-index: 999;
}

.more-select.show > .btn.dropdown-toggle {
  background: unset;
}

.more-select.show > .btn.dropdown-toggle:before {
  color: #afb8bc !important;
}

.hl_triggers--item {
  padding: 6px 20px;
}

.hl_conversations--message-body .message-input-wrap textarea.form-control {
  height: 90px;
  padding-right: 90px;
}

.hl_settings--phone-numbers .table thead tr th:last-child,
.hl_settings--phone-numbers .table tbody tr td:last-child {
  max-width: 160px;
}

.hl_settings--phone-numbers table {
  table-layout: fixed;
  border-collapse: collapse;
}

#dashboard .hl_tasks-list button.dropdown-toggle {
    white-space: nowrap;
    max-width: 350px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.campaign_new_header .hl_datepicker .date-time-picker .field #undefined-input {
  background: rgba(24, 139, 246, 0.0) !important;
}

/* this changes are for the b-modal bootstrap modal */
#alertModal___BV_modal_outer_ {
  z-index: 9999 !important;
  background: coral;
}
#alertModal___BV_modal_content_ {
  z-index: 9999 !important;
}
#alertModal___BV_modal_backdrop_ {
  z-index: 1000 !important;
}

.uxmodal div.modal-dialog.modal-sm {
   margin-top: 200px;
}

.hl_dashboard--sales-funnel .btn.btn-primary{
  background-color: #ffffff !important;
  color: #607179 !important;
}

.table-hl tbody tr:nth-of-type(even) {
  background-color: #f9fafb;
}

.table-hl thead tr th {
  background-color: #f9fafb;
  border-color: #e5e7eb;
  border-bottom-width: 1px;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
  font-size: 0.75rem;
}

.table-hl thead tr th[aria-sort] {
  cursor: pointer;
  background-image: none;
  background-repeat: no-repeat;
  background-size: 0.65em 1em;
  background-position: right calc(0.75rem / 2) center;
  padding-right: calc(0.75rem + 0.65em);
}
.table-hl thead tr th[aria-sort="none"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='101' height='101' view-box='0 0 101 101' preserveAspectRatio='none'%3e%3cpath fill='black' opacity='.3' d='M51 1l25 23 24 22H1l25-22zM51 101l25-23 24-22H1l25 22z'/%3e%3c/svg%3e");
}
.table-hl thead tr th[aria-sort="ascending"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='101' height='101' view-box='0 0 101 101' preserveAspectRatio='none'%3e%3cpath fill='black' d='M51 1l25 23 24 22H1l25-22z'/%3e%3cpath fill='black' opacity='.3' d='M51 101l25-23 24-22H1l25 22z'/%3e%3c/svg%3e");
}

.table-hl thead tr th[aria-sort="descending"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='101' height='101' view-box='0 0 101 101' preserveAspectRatio='none'%3e%3cpath fill='black' opacity='.3' d='M51 1l25 23 24 22H1l25-22z'/%3e%3cpath fill='black' d='M51 101l25-23 24-22H1l25 22z'/%3e%3c/svg%3e");
}

.table-hl tbody tr td {
  padding: 8px 15px !important;
}

.table-hl tbody tr td.no-record {
  padding: 25px !important;
}

.more-select-left{
  width: 20px !important;
}
.more-select-left> .btn.dropdown-toggle {
  background: transparent;
  min-width: auto;
  border-radius: 4px !important;
  width: 20px !important;
  height: 30px;
  padding: 0 !important;
  position: relative;
}
.more-select-left> .btn.dropdown-toggle:hover {
  background: #f2f7fa;
}
.more-select-left> .btn.dropdown-toggle:hover:before {
  color: #607179;
}
.more-select-left> .btn.dropdown-toggle .filter-option {
  display: none !important;
}
.more-select-left> .btn.dropdown-toggle:before {
  content: '\e92a';
  display: block;
  font-family: 'Magicons' !important;
  speak: none;
  font-size: 1rem;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  position: absolute;
  top: 50%;
  right: 50%;
  -webkit-transform: translate(50%, -50%);
  -ms-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  color: #afb8bc;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.more-select-left> .btn.dropdown-toggle:after {
  display: none;
}
.more-select-left> .dropdown-menu {
  min-width: 200px !important;
}

.more-select-2.show > .btn.dropdown-toggle {
  background: #f2f7fa;
}
.more-select-2.show > .btn.dropdown-toggle:before {
  color: #607179;
}

.dont-scroll-100{
  height:100% !important;
  overflow-y: hidden !important
}

.dont-scroll-95{
  height:95% !important;
  overflow-y: hidden !important
}

.scroll-me {
  height: 100%;
  overflow-y: auto;
}
.hl_call--settings p {
  font-size: 12px;
  line-height: 18px;
  color: #607179;
  margin-top: 36px;
}
.hl_call--settings .call-text h4 {
  font-size: 12px;
  color: #000;
  margin-top: 36px;
  font-weight: normal;
  line-height: 14px;
}

.hl_call--settings .audio-upload {
  background: #f0f8fe;
  border-radius: 5px;
  border: none
}

.hl_call--settings .text-inner-upload {
  font-size: 13px;
  line-height: 18px;
  color: #607179;
  opacity: 0.5;
}

.hl_call--settings .file-name {
  color: #2a3135;
  font-size: 14px;
  margin-right: 10px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 180px;
  display: block;

}
.hl_call--settings .icon-trash {
  color: #f2724a;
  margin-left: 10px;
  font-size: 14px;
}
#collapse-user-call-settings .card-body .hl_call--settings .form-group label {
  color: #607179;
  opacity: 0.5;
}
#collapse-user-call-settings .card-body .hl_call--settings .form-group .col-5 {
  color: #a6a6a6;
  font-size: 13px;
  line-height: 18px;
}
#collapse-user-call-settings .card-body .call-text h4 {
  font-size: 12px;
  color: #607179;
  margin-top: 36px;
  font-weight: 300;
  line-height: 14px;
}
.hl_team-management--modal .custom-card {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.11);
  border: 1px solid #e5e5e5;
}
.hl_team-management--modal .custom-card .card-header {
  padding: 10px;
  height: 66px;
}
.hl_team-management--modal .text-title-wrap
{
  width: 100%;
  display: flex;
  margin-right: 0 !important;
  justify-content: space-between;
  align-items: center;
}
.hl_team-management--modal .text-title-wrap button {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.hl_team-management--modal .info-edit {
  font-size: 14px;
  color: #f2994a !important;
  font-weight: normal;
}

.hl_team-management--modal .info-edit .fa-pencil-alt {
  color: #f2994a;
  margin-right: 10px;
  text-decoration: underline;
}

.hl_team-management--modal .header-wrap {
  font-size: 14px !important;
  color: #178af6 !important;
  font-weight: 500;
  line-height: 20px;
}
.hl_team-management--modal .icon-arrow-down-1 {
  font-size: 11px !important;
  font-weight: normal;
}
.text-title-wrap .collapsed .header-wrap .icon-arrow-down-1:before {
  transition: 0.5s;
  transform: rotate(-90deg) !important;
}
.text-title-wrap .header-wrap .icon-arrow-down-1:before {
  transition: 0.5s;
}
.btn.btn-green-dark {
  background-color: rgba(39, 174, 96, 1);
  border-color: rgba(39, 174, 96, 1);
  color: var(--button-icon-color);
}
.btn.btn-green-light {
  color: rgba(39, 174, 96, 1);
  border-color: rgb(25, 29, 26);
  background-color: var(--button-icon-color);
  box-shadow: 0px 1px 11px rgba(0, 0, 0, 0.17);
}

.btn.btn-green-dark:hover,
.btn.btn-green-dark:focus,
.btn.btn-green-dark:active {
  color: var(--button-icon-color);
  background-color: rgba(39, 174, 96, 1);
  border-color: rgba(39, 174, 96, 1);
}

.info-blue{
  background: #EDF5FE;
  color: #188bf6 ;
  font-weight: 500;
}

.tooltip {
  opacity: .9;
}

body .call-dropDown .bootstrap-select > .btn.dropdown-toggle{
  background: #FFFFFF;
  border: 1px solid #EBEFF2;
  box-sizing: border-box;
  border-radius: 5px;
  height: 37px;
  padding: 3px 10px;
}
body .call-dropDown .bootstrap-select > .dropdown-menu .inner .dropdown-menu li a ,body .call-dropDown .bootstrap-select > .btn.dropdown-toggle .filter-option{
  color: #353535;
  font-size: 0.875rem;
}
.power-dialer-main {
  padding: 16px 0;
  height: 396px;
  width: 300px;
  display: flex;
  flex-direction: column;
}

.power-dialer-header ,.power-dialer-footer{
  padding:0 16px;
}
.power-dialer-body {
  flex: 1;
  overflow: auto;
  padding:0 10px;
  margin:0 6px;
}

.power-dialer-body::-webkit-scrollbar-track
{
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
	border-radius: 10px;
  background-color: #F2F2F2;
  border-radius: 19px;
}

.power-dialer-body::-webkit-scrollbar
{
	width: 4px;
  background: #ADADAD;
  border-radius: 19px;

}

.power-dialer-body::-webkit-scrollbar-thumb
{
	border-radius: 19px;
	-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
	background-color: #ADADAD;
}


.power-dialer-footer > .footer-buttons {
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.power-dialer-footer > .footer-buttons > .disabledBtn {
   opacity: 0.5;
   pointer-events: none;
}

.power-dialer-footer > .footer-buttons >.back-btn {
  background: #FFFFFF;
  border: 1px solid rgba(185, 185, 185, 0.35);
  box-sizing: border-box;
  border-radius: 34px;
  width: 85px;
  height: 37px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.power-dialer-footer > .footer-buttons > .transferend {
  background: #EB5757;
  border: 1px solid #EB5757;
  box-sizing: border-box;
  border-radius: 34px;
  width: 173px;
  height: 37px;
  display: flex;
  color: #fff;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
}
.power-dialer-footer > .footer-buttons > .transferend > i {
  margin-right:5px;
  margin-top:5px;
}

.power-dialer-footer > .footer-buttons > .callstop3 {
  background: #eb5757;
  border-radius: 34px;
  border: none;
  width: 173px;
  height: 37px;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: #FFFFFF;
}

.power-dialer-footer > .footer-buttons > .callandhold {
  background: #FF9416;
  border: 1px solid #FF9416;
  box-sizing: border-box;
  border-radius: 34px;
  width: 173px;
  height: 37px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}
.power-dialer-footer > .footer-buttons > .callandhold > i {
  margin-right: 15px;
  margin-top: 6px;
}
.label-header {
  padding: 10px 0;
  background: #FFFFFF;
  position: absolute;
  width: 260px;
  z-index: 10;
}

.label-header > p {
  font-size: 12px;
  line-height: 14px;
  color: #607179;
}

.power-dialer-body > .contact-list > .contactList {
  padding-top: 30px;
}

.detail-box {
  background: #FFFFFF;
  border-radius: 7px;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  height: 48px;
  cursor: pointer;
}
.detail-box:hover {
  background: #f9f9f9;
}
.detail-box .img-circle {
  display: flex;
  align-items: center;
}
.detail-box .img-circle .avatar .avatar_img {
  min-width: 32px;
  width: 32px;
  height: 32px;
}
.detail-box .img-circle .avatar .avatar_img img {
  max-width: 100%;
}
.detail-box .user-info {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}
.detail-box .user-info p {
  font-size: 14px;
  line-height: 16px;
  color: #555555;
  margin: 0;
  opacity: 1;
  text-align: left;
}
.detail-box .user-info span {
  font-size: 12px;
  line-height: 14px;
  margin-top: 3px;
  color: #A4A4A4;
}
.power-dialer-body > .selectedList {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.custom-contact {
  display: flex;
  flex-direction: column;
  flex-grow: 1;

}
.custom-contact .label-header {
  position: sticky;
  top: 0;
}
.custom-contact .contact-list {
  flex-grow: 1;
  overflow: auto;
}
.dialling-text{
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin-top: 30px;
  line-height: 18px;
}
p.dialling-text img {
  width: 20px;
}

.search-form input {
  padding: 10px 20px 10px 50px;
  height: 38px;
}

.hl_header--phone .dropdown-menu {
  margin-top: 25px;
  padding: 0;
  min-width: 300px;
}

.hl_header--phone .callFromSection .dropdown-menu {
  margin-top: 2px;
}

.img-circle .avatar .avatar_img {
  min-width: 32px;
  width: 32px;
  height: 32px;
  font-size: 14px;
  line-height: 32px;
}

.icon-eye-slash {
  display: inline-block;
  width: 16px;
  height: 11px;
  background: url('/pmd/img/icon-eye-slash.svg') no-repeat center;
  background-size: 16px 11px;
  -webkit-transform: translateY(2px);
  -ms-transform: translateY(2px);
  transform: translateY(2px);
}

.icon-calendar-settings {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url(/pmd/img/icon-calendar-settings.svg) no-repeat center;
}

.icon-calendar-search {
  display: inline-block;
  width: 24px;
  height: 24px;
  background: url(/pmd/img/icon-calendar-search.svg) no-repeat center;
}

.icon-team-member {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url(/pmd/img/icon-team-member.svg) no-repeat center;
}

.icon-calendar-small {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url(/pmd/img/icon-calendar-small.svg) no-repeat center;
}

.icon-ellipsis {
  display: inline-block;
  width: 20px;
  height: 12px;
  background: url(/pmd/img/icon-ellipsis.svg) no-repeat center;
}

.icon-calendar-code {
  display: inline-block;
  width: 18px;
  height: 18px;
  background: url(/pmd/img/icon-calendar-code.svg) no-repeat center;
  /* TODO: (MP) Put actual icon here once calendar code icon is ready by designer */
}

.icon-copy-to-clipboard {
  display: inline-block;
  width: 14px;
  height: 14px;
  background: url(/pmd/img/icon-copy-to-clipboard.svg) no-repeat center;
  /* TODO: (MP) Put actual icon here once calendar code icon is ready by designer */
}

.icon-sms-gray {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url(/pmd/img/icon-sms-gray.svg) no-repeat center;
  background-size: 16px;
}

div#modal-bulkstatusreport-44 .modal-dialog{
  min-width: 1000px !important;
}

div#modal-bulkstatuscontact-11 .modal-dialog {
  min-width: 800px !important;
}

select.bs-select-hidden.selectpicker { /* For select element having both bs-select-hidden and selectpicker classes as its style*/
  display: none !important
}

 /* Patch unset padding top on core style sheet */
body {
  padding-top: 0px !important;
}
/* padding top is moved to hl_wrapper  */
@media (min-width: 768px){
  .hl_wrapper {
    padding-top: 82px ;
  }
}

.hl_request-payment--modal .add-remove-items
{
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  font-size: large;
}

.hl_request-payment--modal .display-inline {
  display: flex;
}

.info-icon {
  margin: 10px;
}

.icon-request {
  display: inline-block !important;
  width: 16px;
  height: 16px;
  background: url("/pmd/img/request_icon.svg") no-repeat center;
  background-size: 16px 16px;
}

input[type=number] {
  -moz-appearance:textfield;
}

.calendar-view {
  bottom: 0;
}

.review-conversation {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: auto;
}

.messages-list--text .rating .icon {
  font-size: 0.875rem;
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping-wrapper {
  display: flex;
  width: .75rem;
  height: .75rem;
  margin-top: -.25rem;
  margin-right: -.25rem;
  right: 0;
  top: 0;
  position: absolute;
}
.animate-ping {
  -webkit-animation: ping 1s cubic-bezier(0,0,.2,1) infinite;
  animation: ping 1s cubic-bezier(0,0,.2,1) infinite;
  opacity: .75;
  border-radius: 9999px;
  width: 100%;
  height: 100%;
  display: inline-flex;
  position: absolute;
}
.animate-ping-dot {
  border-radius: 9999px;
  width: .75rem;
  height: .75rem;
  display: inline-flex;
  position: relative;
}

/* Tailwind patch */

.hl_dashboard .date-time-picker input {
  --tw-ring-color: none;
  --tw-ring-shadow: none;
}

.tag-input input:focus {
  --tw-ring-color: none;
 --tw-ring-shadow: none;
}

.conversation-phone-number input:focus{
  --tw-ring-color: none !important;
 --tw-ring-shadow: none !important;
}

.conversation-phone-number input{
  border-radius: 0px !important;
}

.dialerView .dialer-input input{
  border: none;
  --tw-ring-color: none !important;
  --tw-ring-shadow: none !important;
  font-size: 18px;
  font-weight: 400;
}

.dialerView .dialer-input div {
  --tw-shadow: none !important;
}

.form-group .bootstrap-select .dropdown-toggle.btn-light {
  background: #fff;
  border: 1px solid rgba(209, 213, 219, 1);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);

}

.form-group .bootstrap-select .dropdown-toggle {
  height: 38px;
}
.form-group .bootstrap-select > .btn.dropdown-toggle .filter-option {
  top: 0;
}
.form-group  .bootstrap-select .dropdown-toggle .filter-option  {
padding-top: 8px;
padding-left: 12px;

}
.v-select  {
  background: #fff;
  border: 1px solid rgba(209, 213, 219, 1);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.v-select input {
  border: none !important;
  outline: none;

}
.v-select input:focus {
  --tw-ring-color: none;
 --tw-ring-shadow: none;
 border: none !important;
 outline: none;
 margin-top:0px;
}
.input.vs__search {
  padding: 0px;
}
.vs__search, .vs__search:focus {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
  padding-left: 12px !important;
}
.v-select .vs__dropdown-toggle {
  min-height: 38px;
  padding: 0px;
}
.v-select .vs__selected {
  padding-left: 7px;
  color: rgba(55, 65, 81, 1);
}
.v-select .vs__selected,.v-select  input {
  margin:2px
}
.v-select input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
  color: rgba(55, 65, 81, .7) !important;
}
.v-select input::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
  color: rgba(55, 65, 81, .7) !important;

}
.hl_controls--left .trigger-name  {
  --tw-ring-color: none !important;
  --tw-ring-shadow: none !important;
}
.campaign_new_header .hl_datepicker {
  background: #fff !important;
}
.campaign_new_header .hl_datepicker .date-time-picker input {
  padding-top: 0px !important;
  border: none !important;
}
.event-card--bottom select {
  height: unset !important;
  color: #000
}

.form-input-group-item i {
  margin-top: -2px;
}

.date-time-picker .field .field-input:focus {
  --tw-ring-shadow: none !important;
}

.option-card select {
  border-radius: 8px;
  border: 1px solid rgba(209, 213, 219, 1);
}

.option-card select:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(56, 160, 219, 1);
}

.modal-content--service .service-step.active:focus {
  outline: none !important;
}

.date-time-picker .field input {
  padding-top: 0px !important;
}

.date-time-picker input {
  border: none !important;
}

.hl_marketing--header .campaign_new_header .hl_controls--left input {
  background-color: transparent;
}

.hl_wrapper .hl_wrapper--inner .hl_controls .trigger-name {
  background-color: transparent;
}

.hl_wrapper--inner .triggers .dropdown .dropdown-item {
  width: 0 !important;
}
.hl_navbar--links > li a svg path {
  stroke: #607179;
}
.hl_navbar--links > li a:hover svg path,
.hl_navbar--links > li a:focus svg path,
.hl_navbar--links > li a:active svg path
{
  stroke: #188bf6;
}

.hl_error_text a{
  color: #fff !important;
}


.active-users-group {
  display: flex;
}
.active-users-group > div {
  margin-left: -8px;
}

.skeleton-color {
  background-color: #e2e8f0;
}

.form-input-group-item input::placeholder,
.form-input-dropdown input::placeholder {
  color: rgb(175, 184, 188);
  opacity: 1;
}
