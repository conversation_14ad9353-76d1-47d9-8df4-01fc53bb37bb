@mixin for-tablet-portrait-up {
    @media (min-width: 768px) { @content; }
}  
body[data-theme='default-dark-v1'] {  
  .sidebar-v2-location {   
      @apply bg-black;   
      .hl_header {
        @apply bg-gray-800;
        box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.11), inset 0px -1px 0px rgba(255, 255, 255, 0.11);

        .topmenu-nav .topmenu-navtitle {
            @apply text-left mx-2 px-2 pb-2 md:pb-3 text-white text-lg font-medium;
        }
    
        .topmenu-nav .topmenu-navitem {
            @apply text-gray-400;
    
            &.active {
              @apply border-b-2 text-curious-blue-500 border-curious-blue-500;
              
              &.divider {
                @apply text-gray-600 border-b-0 border-l-2 border-gray-500 cursor-text opacity-30;
              }
    
              a.active {
                @apply text-curious-blue-500;
              }
            }
    
            &.divider {
              @apply text-gray-600 border-b-0 border-l-2 border-gray-500 cursor-text opacity-30 mb-2 w-px	h-7;
            }
        }
      }
    
      #sidebar-v2 {
        box-shadow: inset -1px 0px 0px rgba(255, 255, 255, 0.11);
        
        nav {
          a {
            &:hover, &.active {
              @apply opacity-100 bg-gray-700;
  
              &.divider {
                  @apply opacity-70;
                  background: transparent;
              }
            }
          }
        }
  
        .default-bg-color, &.default-bg-color {
            @apply bg-gray-800;
        }
  
  
        #location-switcher-sidbar-v2 {
          @apply md:mx-0 lg:mx-2 xl:mx-2 mb-4 bg-gray-900 md:bg-transparent lg:bg-gray-900 xl:bg-gray-900;
          
          .switcher-caret-holder {
            &.default-bg-color {
                @apply bg-gray-800;
            }
          }
  
          .hl_v2-location_switcher {
            background: #374151;
  
            .hl_v2_tip-arrow {
              border-right: 10px solid #374151;
            }
  
            input {
              @apply text-white;
              background: #374151;
              border: 1px solid #4B5563;
            }
  
            #switcher-agency-switch div{
              border-bottom: 1px solid #4B5563;
            }
  
            .hl_account {
              border: 1.5px solid #4B5563;
              &.active {
                border: 1.5px solid #A5B4FC;
              }
            }
          }
        }
      }

        #launchpad {
            .launchpad-title {
                @apply text-white;
            }

            .webchat-center-containter {
                @apply text-white;

                .webchat-title {
                    @apply text-white;
                }
            }
        }
        
        .hl_controls--left h3, 
        .hl_settings--controls-left h2,
        .hl_controls--left h2,
        .hl_controls--left h1,
        .hl_marketing--heading-text p,
        .nothing-found p,
        #smartlists .header-ul a,
        .hl_contact-restore,
        .hl_settings--controls-left label,
        .hl_opportunities--set-header h4,
        .hl_marketing--header-inner h2 {
            @apply text-white;
        }

        .calender-wrapper {
            @apply bg-white;
        }
  }

  .sidebar-v2-agency {
    .hl_header {
      @apply bg-gray-800;
      box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.11), inset 0px -1px 0px rgba(255, 255, 255, 0.11);

      .topmenu-nav .topmenu-navtitle {
        @apply text-left mx-2 px-2 pb-2 md:pb-3 text-white text-lg font-medium;
      }

      .topmenu-nav .topmenu-navitem {
        @apply text-gray-400;

        &.active {
          @apply border-b-2 text-curious-blue-500 border-curious-blue-500;
          
          &.divider {
            @apply text-gray-600 border-b-0 border-l-2 border-gray-500 cursor-text opacity-30;
          }

          a.active {
            @apply text-curious-blue-500;
          }
        }

        &.divider {
          @apply text-gray-600 border-b-0 border-l-2 border-gray-500 cursor-text opacity-30 mb-2 w-px	h-7;
        }
      }
    }

    #sidebar-v2 {
      box-shadow: inset -1px 0px 0px rgba(255, 255, 255, 0.11);
      
      nav {
        a {
          &:hover, &.active {
            @apply opacity-100 bg-gray-700;

            &.divider {
                @apply opacity-70;
                background: transparent;
            }
          }
        }
      }

      .default-bg-color, &.default-bg-color {
          @apply bg-gray-800;
      }


      #location-switcher-sidbar-v2 {
        @apply md:mx-0 lg:mx-2 xl:mx-2 mb-4 bg-gray-900 md:bg-transparent lg:bg-gray-900 xl:bg-gray-900;
        
        .switcher-caret-holder {
          &.default-bg-color {
              @apply bg-gray-800;
          }
        }

        .hl_v2-location_switcher {
          background: #374151;

          .hl_v2_tip-arrow {
            border-right: 10px solid #374151;
          }

          input {
            @apply text-white;
            background: #374151;
            border: 1px solid #4B5563;
          }

          #switcher-agency-switch div{
            border-bottom: 1px solid #4B5563;
          }

          .hl_account {
            border: 1.5px solid #4B5563;
            &.active {
              border: 1.5px solid #A5B4FC;
            }
          }
        }
      }
    }

    .hl_wrapper {
        @apply bg-black;

        .hl_controls {
            h1, h2 {
                @apply text-white;
            }
        }

        .billing-header h2, .hl_marketing--header-inner h2, .hl_settings--controls h2 {
            @apply text-white;
        }

        .saas-plan-builder__page-heading {
            @apply text-white;
        }
    }
  }
}