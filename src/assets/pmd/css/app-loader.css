body{
    background-color: #f2f7fa;
  }
  #app.loading+.app-loader{
    position: fixed;
    display: flex;
    height: 100%;
    width: 100%;
    z-index: 99999;
    top: 0;
    left: 0;
    background: azure;
    opacity: 0.8;
  }
  #app+.app-loader {
    display: none;
  }
  .app-loader {
    height: 100vh;
    padding-top: 0;
    justify-content: center;
    align-items: center;
    align-content: center;
    text-align: center;
  }
  .overflow-hidden {
    height: calc(100vh - 92px); 
    overflow: hidden;
  }
  #app.loading+.lds-grid {
    display: inline-block;
  }
  .lds-grid {
    position: relative;
    width: 80px;
    height: 80px;
  }
  .lds-grid div {
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #37ca37;
    animation: lds-grid 1.2s linear infinite;
  }
  .lds-grid div:nth-child(1) {
    top: 8px;
    left: 8px;
    animation-delay: 0s;
  }
  .lds-grid div:nth-child(2) {
    top: 8px;
    left: 32px;
    animation-delay: -0.4s;
  }
  .lds-grid div:nth-child(3) {
    top: 8px;
    left: 56px;
    animation-delay: -0.8s;
  }
  .lds-grid div:nth-child(4) {
    top: 32px;
    left: 8px;
    animation-delay: -0.4s;
  }
  .lds-grid div:nth-child(5) {
    top: 32px;
    left: 32px;
    animation-delay: -0.8s;
  }
  .lds-grid div:nth-child(6) {
    top: 32px;
    left: 56px;
    animation-delay: -1.2s;
  }
  .lds-grid div:nth-child(7) {
    top: 56px;
    left: 8px;
    animation-delay: -0.8s;
  }
  .lds-grid div:nth-child(8) {
    top: 56px;
    left: 32px;
    animation-delay: -1.2s;
  }
  .lds-grid div:nth-child(9) {
    top: 56px;
    left: 56px;
    animation-delay: -1.6s;
  }
  @keyframes lds-grid {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }