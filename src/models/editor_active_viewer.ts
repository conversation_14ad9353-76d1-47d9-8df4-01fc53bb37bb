import firebase from 'firebase/app'
import moment from 'moment'
import { pickBy } from 'lodash'

export interface ActiveUser {
  id: string
  first_name: string
  last_name: string
  email: string
  profile_photo: string
  phone: string
  date_update: any
}


export interface Meta {
  funnelId: string
  pageId: string
}


export default class EditorActiveViewer {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('editor_active_viewers')
  }


  public static queryByLocation(
    locationId: string
  ) {
    return EditorActiveViewer.collectionRef()
      .where('location_id', '==', locationId)
  }

  private _id: string

  private _data: { [field: string]: any }

  private _ref: firebase.firestore.DocumentReference



  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = EditorActiveViewer.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this.deleted = false
      this.dateAdded = moment().utc()
    }
  }


  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }


  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get altId(): Array<string> {
    return this._data.alt_id
  }

  set altId(altIds: Array<string>) {
    this._data.alt_id = altIds
  }

  get meta(): Meta {
    return this._data.meta
  }

  set meta(meta: Meta) {
    this._data.meta = meta
  }

  get activeUsers(): Array<ActiveUser> {
    return this._data.active_users
  }

  set activeUsers(user: Array<ActiveUser>) {
    this._data.active_users = user
  }
}
