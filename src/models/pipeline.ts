import firebase from 'firebase/app'
import moment from 'moment-timezone'

export interface Stage {
  id: string
  name: string
  win_probability?: number
  position: number
  showInFunnel?: boolean
  showInPieChart?: boolean
}

export default class Pipeline {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('pipelines')
  }

  public static getById(id): Promise<Pipeline> {
    return new Promise((resolve, reject) => {
      Pipeline.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new Pipeline(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByLocationIdQuery(
    locationId: string
  ): firebase.firestore.Query {
    return Pipeline.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('name', 'asc')
  }

  public static async getByLocationId(locationId: string): Promise<Pipeline[]> {
    const snapshot = await this.getByLocationIdQuery(locationId).get()
    return snapshot.docs.map(d => new Pipeline(d))
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Pipeline | undefined> {
    const snapshot = await Pipeline.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Pipeline(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    snapshot?:
      | firebase.firestore.DocumentSnapshot
      | firebase.firestore.QueryDocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else if (snapshot) {
      this._id = snapshot.id
      this._ref = Pipeline.collectionRef().doc(snapshot.id)
      this._data = snapshot
      // if (snapshot.date_added instanceof moment === false) {
      //   this.dateAdded = moment(snapshot.date_added)
      // }
      // if (snapshot.date_updated instanceof moment === false) {
      //   this.dateUpdated = moment(snapshot.date_updated)
      // }
    } else {
      this._ref = Pipeline.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment()
      this.deleted = false
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get dateAdded() {
    return moment(this._data.date_added.toDate())
  }

  set dateAdded(dateAdded) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get showInFunnel(): boolean {
    return this._data.showInFunnel
  }

  set showInFunnel(showInFunnel: boolean) {
    this._data.showInFunnel = showInFunnel
  }

  get showInPieChart(): boolean {
    return this._data.showInPieChart
  }

  set showInPieChart(showInPieChart: boolean) {
    this._data.showInPieChart = showInPieChart
  }

  get stages(): Stage[] {
    return this._data.stages
  }

  set stages(stages: Stage[]) {
    this._data.stages = stages
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  public async save() {
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }
}
