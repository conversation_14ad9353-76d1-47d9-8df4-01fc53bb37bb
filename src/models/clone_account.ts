import firebase from 'firebase/app'
import moment from 'moment-timezone'
import CustomField from './custom_field'
import CustomValue from './custom_value'
import Location from './location'
import { v4 as uuid } from 'uuid'
import {
  Template,
  Pipeline,
  Calendar,
  Formbuilder,
  Campaign,
  Link,
  Trigger,
  EmailBuilder,
  Formsurvey,
  Folder
} from '.'

export default class CloneAccount {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('clone_account')
  }

  public static async getById(id: string): Promise<CloneAccount | undefined> {
    const snapshot = await CloneAccount.collectionRef()
      .doc(id)
      .get()
    if (!snapshot.exists || snapshot.data().deleted) return undefined
    return new CloneAccount(snapshot)
  }

  public static getByCompanyIdRealtime(
    companyId: string
  ): firebase.firestore.Query {
    return CloneAccount.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', companyId)
  }

  public static getByCompanyIdAndTypeRealtime(
    companyId: string,
    type: 'own' | 'imported'
  ): firebase.firestore.Query {
    return CloneAccount.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', companyId)
      .where('type', '==', type)
  }

  public static async getByCompanyId(
    companyId: string
  ): Promise<CloneAccount[]> {
    const snapshot = await CloneAccount.getByCompanyIdRealtime(companyId).get()
    return snapshot.docs.map(d => new CloneAccount(d))
  }

  protected _id: string
  protected _data: firebase.firestore.DocumentData
  protected _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
    } else {
      this._ref = CloneAccount.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
      this.dateUpdated = moment()
    }
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get type(): 'own' | 'imported' {
    return this._data.type
  }

  set type(type: 'own' | 'imported') {
    this._data.type = type
  }

  get accountData(): { [key: string]: any } {
    return this._data.account_data
  }

  set accountData(accountData: { [key: string]: any }) {
    this._data.account_data = accountData
  }

  get accountDataUrl(): string {
    return this._data.account_data_url
  }

  set accountDataUrl(accountDataUrl: string) {
    this._data.account_data_url = accountDataUrl
  }

  public async save(): Promise<CloneAccount> {
    await this._ref.set(this.data)
    return this
  }

  public async runDehydrate() {
    const accountData = await CloneAccount.dehydrateAccount(this.locationId)
    this.accountData = accountData
  }

  public static async dehydrate(
    companyId: string,
    locationId: string,
    name: string
  ) {
    const cloneAccount = new CloneAccount()
    cloneAccount.companyId = companyId
    cloneAccount.locationId = locationId
    cloneAccount.name = name
    cloneAccount.type = 'own'
    await cloneAccount.runDehydrate()
    await cloneAccount.save()
  }

  public static async dehydrateAccount(locationId: string) {
    const accountData: { [key: string]: any } = {
      custom_fields: [],
      custom_values: [],
      review_settings: {},
      text_templates: [],
      pipelines: [],
      calendars: [],
      forms: [],
      campaigns: [],
      links: [],
      triggers: [],
      email_templates: [],
      folders: []
    }

    try {
      const customFields = await CustomField.getByLocationId(locationId).get()
      accountData.custom_fields = customFields.docs.map(customField => {
        const field = new CustomField(customField)
        const data = {
          id: field.id,
          key: field.fieldKey,
          model: field.model,
          name: field.name,
          picklist_options: field.picklistOptions,
          placeholder: field.placeholder,
          position: field.position,
          available_on: field.availableOn,
          data_type: field.dataType
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
    }

    accountData.custom_values = await CloneAccount.getCustomValues(locationId)
    accountData.review_settings = await CloneAccount.getReviewSettings(
      locationId
    )
    accountData.text_templates = await CloneAccount.getTextTemplates(locationId)
    accountData.email_templates = await CloneAccount.getEmailTemplates(
      locationId
    )
    accountData.pipelines = await CloneAccount.getPipelines(locationId)
    accountData.calendars = await CloneAccount.getCalendars(locationId)
    accountData.forms = await CloneAccount.getForms(locationId)
    accountData.surveys = await CloneAccount.getSurveys(locationId)
    accountData.links = await CloneAccount.getLinks(locationId)
    accountData.campaigns = await CloneAccount.getCampaigns(locationId)
    accountData.triggers = await CloneAccount.getTriggers(locationId)
    accountData.folders = await CloneAccount.getFolders(locationId)

    return accountData
  }

  public static async hydrate(locationId: string, snapshotId: string) {
    const snapshot = await CloneAccount.getById(snapshotId)
    await CloneAccount.hydrateWithData(locationId, snapshot.accountData)
    await Location.collectionRef()
      .doc(locationId)
      .update({
        snapshots_loaded: firebase.firestore.FieldValue.arrayUnion(snapshotId)
      })
  }

  public static async hydrateWithData(
    locationId: string,
    accountData: { [key: string]: any }
  ) {
    const originalData = lodash.cloneDeep(accountData)

    const folderMapping = await CloneAccount.loadFolders(
      locationId,
      accountData.folders || []
    )
    accountData = CloneAccount.updateMappings(accountData, folderMapping)

    const fieldsResponse: {
      [key: string]: any
    }[] = await CloneAccount.loadCustomFields(
      locationId,
      accountData.custom_fields || []
    )
    accountData = CloneAccount.updateMappings(accountData, fieldsResponse)

    const customValuesMappings = await CloneAccount.loadCustomValues(
      locationId,
      accountData.custom_values || []
    )
    accountData = CloneAccount.updateMappings(accountData, customValuesMappings)

    const textTemplatesMapping = await CloneAccount.loadTextTemplates(
      locationId,
      accountData.text_templates || []
    )
    accountData = CloneAccount.updateMappings(accountData, textTemplatesMapping)

    const emailTemplatesMapping = await CloneAccount.loadEmailTemplates(
      locationId,
      accountData.email_templates || []
    )
    accountData = CloneAccount.updateMappings(
      accountData,
      emailTemplatesMapping
    )

    const pipelineStagesMapping = await CloneAccount.loadPipelineStages(
      locationId,
      accountData.pipelines || []
    )
    accountData = CloneAccount.updateMappings(
      accountData,
      pipelineStagesMapping
    )

    const pipelinesMapping = await CloneAccount.loadPipelines(
      locationId,
      accountData.pipelines || []
    )
    accountData = CloneAccount.updateMappings(accountData, pipelinesMapping)

    const formsMapping = await CloneAccount.loadForms(
      locationId,
      accountData.forms || []
    )
    accountData = CloneAccount.updateMappings(accountData, formsMapping)

    const surveysMapping = await CloneAccount.loadSurveys(
      locationId,
      accountData.surveys || []
    )
    accountData = CloneAccount.updateMappings(accountData, surveysMapping)

    const calendarsMapping = await CloneAccount.loadCalendars(
      locationId,
      accountData.calendars || []
    )
    accountData = CloneAccount.updateMappings(accountData, calendarsMapping)

    const linksMapping = await CloneAccount.loadLinks(
      locationId,
      accountData.links || []
    )
    accountData = CloneAccount.updateMappings(accountData, linksMapping)

    const campaignsMapping = await CloneAccount.loadCampaigns(
      locationId,
      accountData.campaigns || []
    )
    accountData = CloneAccount.updateMappings(accountData, campaignsMapping)

    const triggersMapping = await CloneAccount.loadTriggers(
      locationId,
      accountData.triggers || []
    )
    accountData = CloneAccount.updateMappings(accountData, triggersMapping)

    //Doing this once more so that the follow up campaing id's are updated with the new id's.
    let campaigns = lodash.cloneDeep(accountData.campaigns)
    campaigns = campaigns.map((campaign, index) => {
      campaign.id = originalData.campaigns[index].id
      if (campaign.campaign_data) {
        campaign.campaign_data = CloneAccount.updateMappings(
          campaign.campaign_data,
          campaignsMapping
        )
        campaign.campaign_data = CloneAccount.updateMappings(
          campaign.campaign_data,
          triggersMapping
        )
      }
      return campaign
    })
    await CloneAccount.loadCampaigns(locationId, campaigns)

    await CloneAccount.loadReviewSettings(
      locationId,
      accountData.review_settings
    )
  }

  public static updateMappings(
    accountData: { [key: string]: any },
    updatedMappings: { [key: string]: any }[]
  ) {
    let stringData = JSON.stringify(accountData)
    for (let i = 0; i < updatedMappings.length; i++) {
      const mapping = updatedMappings[i]
      stringData = stringData.replace(
        new RegExp(mapping.id, 'g'),
        mapping.new_id
      )
    }
    return JSON.parse(stringData)
  }

  public static loadCustomFields(
    locationId: string,
    fields: { [key: string]: any }[]
  ) {
    return Promise.all(
      fields.map(async field => {
        let customField: CustomField = await CustomField.getByOriginId(
          locationId,
          field.id
        )
        if (!customField) {
          customField = new CustomField()
          customField.locationId = locationId
          customField.originId = field.id
        }
        customField.fieldKey = field.key
        customField.model = field.model
        customField.name = field.name
        customField.picklistOptions = field.picklist_options
        customField.placeholder = field.placeholder
        customField.position = field.position
        customField.availableOn = field.available_on
        customField.dataType = field.data_type
        await customField.save()
        return {
          id: field.id,
          new_id: customField.id
        }
      })
    )
  }

  public static async getCustomValues(locationId) {
    try {
      const customValues = await CustomValue.getByLocationId(locationId)
      return customValues.map(customValue => {
        const data = {
          id: customValue.id,
          field_key: customValue.fieldKey,
          name: customValue.name
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadCustomValues(
    locationId: string,
    customValues: { [key: string]: any }[]
  ) {
    return await Promise.all(
      customValues.map(async customValue => {
        let doc: CustomValue = await CustomValue.getByOriginId(
          locationId,
          customValue.id
        )
        if (!doc) {
          doc = new CustomValue()
          doc.locationId = locationId
          doc.originId = customValue.id
        }
        doc.fieldKey = customValue.field_key
        doc.name = customValue.name
        await doc.save()
        return {
          id: customValue.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getReviewSettings(locationId) {
    const location = await Location.getById(locationId)
    const data = {
      sms: location.settings.sms,
      email: location.settings.email
    }
    return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
  }

  public static async loadReviewSettings(
    locationId: string,
    reviewSettings: { [key: string]: any }
  ) {
    const location = await Location.getById(locationId)
    let updated = false
    if (reviewSettings.sms) {
      location.settings.sms = reviewSettings.sms
      updated = true
    }
    if (reviewSettings.email) {
      location.settings.email = reviewSettings.email
      updated = true
    }
    if (updated) await location.save()
  }

  public static async getTextTemplates(locationId) {
    try {
      const templates = await Template.getByLocation(locationId)
      return templates.map(template => {
        const data = {
          id: template.id,
          name: template.name,
          template: template.template,
          type: template.type
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadTextTemplates(
    locationId: string,
    templates: { [key: string]: any }[]
  ) {
    return await Promise.all(
      templates.map(async template => {
        let doc: Template = await Template.getByOriginId(
          locationId,
          template.id
        )
        if (!doc) {
          doc = new Template()
          doc.locationId = locationId
          doc.originId = template.id
        }
        doc.name = template.name
        doc.template = template.template
        doc.type = template.type
        await doc.save()
        return {
          id: template.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getEmailTemplates(locationId) {
    try {
      const templates = await EmailBuilder.getByLocationId(locationId)
      return templates.map(template => {
        const data = {
          id: template.id,
          name: template.name,
          email_builder_data: template.emailBuilderData
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadEmailTemplates(
    locationId: string,
    templates: { [key: string]: any }[]
  ) {
    return await Promise.all(
      templates.map(async template => {
        let doc: EmailBuilder = await EmailBuilder.getByOriginId(
          locationId,
          template.id
        )
        if (!doc) {
          doc = new EmailBuilder()
          doc.locationId = locationId
          doc.originId = template.id
        }
        doc.name = template.name
        doc.emailBuilderData = template.email_builder_data
        await doc.save()
        return {
          id: template.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getPipelines(locationId) {
    try {
      const pipelines = await Pipeline.getByLocationId(locationId)
      return pipelines.map(pipeline => {
        const data = {
          id: pipeline.id,
          name: pipeline.name,
          stages: pipeline.stages
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadPipelineStages(
    locationId: string,
    pipelines: { [key: string]: any }[]
  ) {
    const mappings: { [key: string]: any }[] = []
    await Promise.all(
      pipelines.map(async pipeline => {
        const existingPipeline: Pipeline = await Pipeline.getByOriginId(
          locationId,
          pipeline.id
        )
        const existingStages = existingPipeline ? existingPipeline.stages : []
        pipeline.stages.forEach(stage => {
          let originalId = stage.id
          let exists = lodash.find(existingStages, {
            origin_id: btoa(originalId)
          })
          if (!exists) {
            exists = lodash.find(existingStages, { name: stage.name })
          }
          stage.origin_id = btoa(originalId)
          stage.id = exists ? exists.id : uuid()
          mappings.push({
            id: originalId,
            new_id: stage.id
          })
        })
      })
    )
    return mappings
  }

  public static async loadPipelines(
    locationId: string,
    pipelines: { [key: string]: any }[]
  ) {
    return await Promise.all(
      pipelines.map(async pipeline => {
        let doc: Pipeline = await Pipeline.getByOriginId(
          locationId,
          pipeline.id
        )
        if (!doc) {
          doc = new Pipeline()
          doc.locationId = locationId
          doc.originId = pipeline.id
        }
        doc.name = pipeline.name
        doc.stages = pipeline.stages
        await doc.save()
        return {
          id: pipeline.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getCalendars(locationId) {
    try {
      const calendars = await Calendar.getAllByLocation(locationId)
      return calendars.map(calendar => {
        const data = {
          id: calendar.id,
          name: calendar.name,
          slot_duration: calendar.slotDuration,
          slot_interval: calendar.slotInterval,
          slot_buffer: calendar.slotBuffer,
          open_hours: calendar.openHours,
          alert_email: calendar.alertEmail,
          thankyou_url: calendar.thankyouURL,
          one_way_sync_appointment: calendar.onewaySyncAppointment,
          auto_confirm: calendar.autoConfirm,
          appointments_per_day: calendar.appoinmentPerDay,
          allow_booking_for: calendar.allowBookingFor,
          allow_booking_for_unit: calendar.allowBookingForUnit,
          allow_booking_after: calendar.allowBookingAfter,
          allow_booking_after_unit: calendar.allowBookingAfterUnit,
          appointments_per_slot: calendar.appoinmentPerSlot,
          pixel_id: calendar.pixelID,
          allow_reschedule: calendar.allowReschedule,
          allow_cancellation: calendar.allowCancellation,
          meeting_location: calendar.meetingLocation,
          sync_option: calendar.syncOption,
          stripe: calendar.stripe,
          sticky_contact: calendar.stickyContact,
          form_id: calendar.formId,
          code_block: calendar.codeBlock,
          thanks_message: calendar.thanksMessage,
          notes: calendar.notes,
          event_title: calendar.eventTitle,
          event_color: calendar.eventColor
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadCalendars(
    locationId: string,
    calendars: { [key: string]: any }[]
  ) {
    return await Promise.all(
      calendars.map(async calendar => {
        let doc: Calendar = await Calendar.getByOriginId(
          locationId,
          calendar.id
        )
        if (!doc) {
          doc = new Calendar()
          doc.locationId = locationId
          doc.originId = calendar.id
        }
        doc.name = calendar.name
        doc.slotDuration = calendar.slot_duration
        doc.slotInterval = calendar.slot_interval
        doc.slotBuffer = calendar.slot_buffer
        doc.openHours = calendar.open_hours
        doc.alertEmail = calendar.alert_email
        doc.thankyouURL = calendar.thankyou_url
        doc.onewaySyncAppointment = calendar.one_way_sync_appointment
        doc.autoConfirm = calendar.auto_confirm
        doc.appoinmentPerDay = calendar.appointments_per_day
        doc.allowBookingFor = calendar.allow_booking_for
        doc.allowBookingForUnit = calendar.allow_booking_for_unit
        doc.allowBookingAfter = calendar.allow_booking_after
        doc.allowBookingAfterUnit = calendar.allow_booking_after_unit
        doc.appoinmentPerSlot = calendar.appointments_per_slot
        doc.pixelID = calendar.pixel_id
        doc.allowReschedule = calendar.allow_reschedule
        doc.allowCancellation = calendar.allow_cancellation
        doc.meetingLocation = calendar.meeting_location
        doc.syncOption = calendar.sync_option
        doc.stripe = calendar.stripe
        doc.stickyContact = calendar.sticky_contact
        doc.formId = calendar.form_id
        doc.codeBlock = calendar.code_block
        doc.thanksMessage = calendar.thanks_message
        doc.notes = calendar.notes
        doc.eventTitle = calendar.event_title
        doc.eventColor = calendar.event_color
        await doc.save()
        return {
          id: calendar.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getForms(locationId) {
    try {
      const forms = await Formbuilder.getByLocationId(locationId)
      return forms.map(form => {
        const data = {
          id: form.id,
          name: form.name,
          form_data: form.formData
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadForms(
    locationId: string,
    forms: { [key: string]: any }[]
  ) {
    return await Promise.all(
      forms.map(async form => {
        let doc: Formbuilder = await Formbuilder.getByOriginId(
          locationId,
          form.id
        )
        if (!doc) {
          doc = new Formbuilder()
          doc.locationId = locationId
          doc.originId = form.id
        }
        doc.name = form.name
        doc.formData = form.form_data
        await doc.save()
        return {
          id: form.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getSurveys(locationId) {
    try {
      const surveys = await Formsurvey.getByLocationId(locationId)
      return surveys.map(survey => {
        const data = {
          id: survey.id,
          name: survey.name,
          form_data: survey.formData
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadSurveys(
    locationId: string,
    forms: { [key: string]: any }[]
  ) {
    return await Promise.all(
      forms.map(async form => {
        let doc: Formsurvey = await Formsurvey.getByOriginId(
          locationId,
          form.id
        )
        if (!doc) {
          doc = new Formsurvey()
          doc.locationId = locationId
          doc.originId = form.id
        }
        doc.name = form.name
        doc.formData = form.form_data
        await doc.save()
        return {
          id: form.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getCampaigns(locationId) {
    try {
      const campaigns = await Campaign.getByLocationId(locationId)
      return campaigns.map(campaign => {
        const campaignData = lodash.cloneDeep(campaign.campaignData)
        delete campaignData.users
        const data = {
          id: campaign.id,
          name: campaign.name,
          status: campaign.status,
          campaign_data: campaignData,
          tag_contacts: campaign.tagContacts,
          allow_multiple: campaign.allowMultiple,
          stop_on_reply: campaign.stopOnReply,
          folder_id: campaign.folderId
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadCampaigns(
    locationId: string,
    campaigns: { [key: string]: any }[]
  ) {
    return await Promise.all(
      campaigns.map(async campaign => {
        let doc: Campaign = await Campaign.getByOriginId(
          locationId,
          campaign.id
        )
        if (!doc) {
          doc = new Campaign()
          doc.locationId = locationId
          doc.originId = campaign.id
        }
        doc.name = campaign.name
        doc.status = campaign.status
        if (doc.campaignData.users)
          campaign.campaign_data.users = doc.campaignData.users
        doc.campaignData = campaign.campaign_data
        doc.tagContacts = campaign.tag_contacts
        doc.allowMultiple = campaign.allow_multiple
        doc.stopOnReply = campaign.stop_on_reply
        doc.folderId = campaign.folder_id
        await doc.save()
        return {
          id: campaign.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getLinks(locationId) {
    try {
      const links = await Link.getByLocationId(locationId)
      return links.map(link => {
        const data = {
          id: link.id,
          name: link.name,
          redirect_to: link.redirectTo
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadLinks(
    locationId: string,
    links: { [key: string]: any }[]
  ) {
    return await Promise.all(
      links.map(async link => {
        let doc: Link = await Link.getByOriginId(locationId, link.id)
        if (!doc) {
          doc = new Link()
          doc.locationId = locationId
          doc.originId = link.id
        }
        doc.name = link.name
        doc.redirectTo = link.redirect_to
        await doc.save()
        return {
          id: link.id,
          new_id: doc.id
        }
      })
    )
  }

  public static async getFolders(locationId) {
    try {
      const folders = await Folder.getByLocationId(locationId)
      return folders.map(folder => {
        const data = {
          id: folder.id,
          name: folder.name,
          type: folder.type,
          conditions: folder.conditions
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadFolders(
    locationId: string,
    folders: { [key: string]: any }[]
  ) {
    let response = [] as { [key: string]: any }[]
    for (let i = 0; i < folders.length; i++) {
      const folder = folders[i]
      let doc: Folder = await Folder.getByOriginId(locationId, folder.id)
      if (!doc) {
        doc = new Folder()
        doc.locationId = locationId
        doc.originId = folder.id
      }
      doc.conditions = folder.conditions
      doc.name = folder.name
      doc.type = folder.type
      await doc.save()
      response.push({
        id: folder.id,
        new_id: doc.id
      })
    }
    return response
  }

  public static async getTriggers(locationId) {
    try {
      const triggers = await Trigger.getByLocationId(locationId)
      return triggers.map(trigger => {
        const data = {
          id: trigger.id,
          title: trigger.title,
          folderId: trigger.folderId,
          belongs_to: trigger.belongsTo,
          workflow_id: trigger.workflowId,
          active: trigger.active,
          description: trigger.description,
          type: trigger.type,
          conditions: trigger.conditions,
          actions: trigger.actions,
          folder_id: trigger.folderId
        }
        return lodash.pickBy(data, (v: any) => v !== null && v !== undefined)
      })
    } catch (err) {
      console.error(err)
      return []
    }
  }

  public static async loadTriggers(
    locationId: string,
    triggers: { [key: string]: any }[]
  ) {
    return await Promise.all(
      triggers.map(async trigger => {
        let doc: Trigger = await Trigger.getByOriginId(locationId, trigger.id)
        if (!doc) {
          doc = new Trigger()
          doc.locationId = locationId
          doc.originId = trigger.id
          doc.active = false
        } else {
          doc.active = trigger.active
        }
        doc.title = trigger.title
        doc.folderId = trigger.folderId
        doc.belongsTo = trigger.belongs_to
        doc.workflowId = trigger.workflow_id
        doc.description = trigger.description
        doc.type = trigger.type
        doc.conditions = trigger.conditions
        doc.actions = trigger.actions
        doc.folderId = trigger.folder_id
        await doc.save()
        return {
          id: trigger.id,
          new_id: doc.id
        }
      })
    )
  }
}
