import firebase from 'firebase/app'
import moment from 'moment-timezone'
import Model from './model'
import { ModelType } from './model'
import axios from 'axios'

export enum Priority {
  None = 0,
  Low = 1,
  Medium = 2,
  High = 3
}

export enum Status {
  Open = 'open',
  Won = 'won',
  Lost = 'lost',
  Abandoned = 'abandoned'
}

export default class Opportunity extends Model {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('opportunities')
  }

  public static async getById(id: string): Promise<Opportunity> {
    return new Promise<Opportunity>((resolve, reject) => {
      Opportunity.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (!snapshot.exists) return resolve()
          if (snapshot.data().deleted) return resolve()
          resolve(new Opportunity(snapshot))
        })
        .catch((err) => {
          reject(err);
        });
    })
  }

  public static fetchAllOpportunities(
    locationId: string
  ): firebase.firestore.Query {
    return Opportunity.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('priority', 'desc')
      .orderBy('date_added', 'asc')
  }

  public static getByStageRealtime(locationId: string, stageId: string) {
    return Opportunity.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .where('pipeline_stage_id', '==', stageId)
      .orderBy('date_updated', 'desc')
  }

  public static fetchByContact(contactId: string, locationId: string) {
    return Opportunity.collectionRef()
      .where('contact_id', '==', contactId)
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static getByStage(
    locationId: string,
    stageId: string
  ): Promise<Opportunity[]> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('pipeline_stage_id', '==', stageId)
        .orderBy('priority', 'desc')
        .orderBy('date_added', 'asc')
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Opportunity(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByContactId(contactId: string, locationId: string): Promise<Opportunity[]> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('contact_id', '==', contactId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Opportunity(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByContactIdStatus(
    contactId: string,
    status: string,
    locationId: string
  ): Promise<Opportunity[]> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('contact_id', '==', contactId)
        .where('location_id', '==', locationId)
        .where('status', '==', status)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Opportunity(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByPipeline(
    locationId: string,
    pipelineId: string
  ): Promise<Opportunity[]> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('pipeline_id', '==', pipelineId)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Opportunity(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByPipelineAndContactId(
    locationId: string,
    pipelineId: string,
    contactId: string
  ): Promise<Opportunity[]> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('pipeline_id', '==', pipelineId)
        .where('contact_id', '==', contactId)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Opportunity(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static pipelineHasData(
    locationId: string,
    pipelineId: string
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('pipeline_id', '==', pipelineId)
        .limit(1)
        .get()
        .then(snapshot => {
          if (snapshot.empty) return resolve(false)
          return resolve(true)
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static async migrateStageId(
    locationId: string,
    deletedStageId: string,
    newStageId: string
  ) {
    var limit = 25
    let snapshot
    let lastObject: Opportunity

    do {
      let query = Opportunity.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('pipeline_stage_id', '==', deletedStageId)
        .orderBy('date_updated', 'desc')
      if (lastObject) {
        query = query.startAfter(lastObject.snapshot)
      }
      snapshot = await query.limit(limit).get()
      if (snapshot.size > 0) {
        lastObject = new Opportunity(snapshot.docs[snapshot.size - 1])
        await Promise.all(
          snapshot.docs
            .map(d => new Opportunity(d))
            .map(async (opportunity: Opportunity) => {
              opportunity.pipelineStageId = newStageId
              opportunity.dateUpdated = moment()
              await opportunity.save()
            })
        )
      }
    } while (snapshot.size === limit)
  }

  public static getByPipelineRealtime(locationId: string, pipelineId: string) {
    return Opportunity.collectionRef()
      .where('location_id', '==', locationId)
      // .where('deleted', '==', false)
      .where('pipeline_id', '==', pipelineId)
      .orderBy('date_updated', 'desc')
  }

  public static createNewWithVuex(vuexData) {
    return vuexData ? new Opportunity(lodash.cloneDeep(vuexData)) : undefined
  }

  public static getStatusKeyVals() {
    let keys = Object.keys(Status)
    let items = keys.map(a => ({ key: Status[a], value: a }));
    return items
  }

  constructor(
    snapshot?:
      | firebase.firestore.DocumentSnapshot
      | firebase.firestore.QueryDocumentSnapshot
      | { [key: string]: any }
  ) {
    super(snapshot)
    if (snapshot) {
      if (this.modelType !== ModelType.firestore && !this.keepESFields) {
        // Remove records added by ES
        if (snapshot.company_name) {
          delete this._data.company_name;
          delete this._oldData.company_name;
        }
        if (snapshot.contact_name) {
          delete this._data.contact_name;
          delete this._oldData.contact_name;
        }
        if (snapshot.email) {
          delete this._data.email;
          delete this._oldData.email;
        }
        if (snapshot.tags) {
          delete this._data.tags;
          delete this._oldData.tags;
        }
        if (snapshot.phone) {
          delete this._data.phone;
          delete this._oldData.phone;
        }
      }
    } else {
      // New record
      this.priority = Priority.None
      // Discuss with Varun
      this._data.last_action_date = firebase.firestore.FieldValue.serverTimestamp()
      this._data.date_updated = firebase.firestore.FieldValue.serverTimestamp()
      this.deleted = false
    }
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get assignedTo(): string {
    return this._data.assigned_to
  }

  set assignedTo(assignedTo: string) {
    this._data.assigned_to = assignedTo
  }

  get closeDate(): moment.Moment {
    return moment(this._data.close_date.toMillis())
  }

  set closeDate(closeDate: moment.Moment) {
    this._data.close_date = firebase.firestore.Timestamp.fromMillis(
      closeDate.valueOf()
    )
  }

  get source(): string {
    return this._data.source
  }

  set source(source: string) {
    this._data.source = source
  }

  get details(): string {
    return this._data.details
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  set details(details: string) {
    this._data.details = details
  }

  get pipelineId(): string {
    return this._data.pipeline_id
  }

  set pipelineId(pipelineId: string) {
    this._data.pipeline_id = pipelineId
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get priority(): Priority {
    return this._data.priority
  }

  set priority(priority: Priority) {
    this._data.priority = priority
  }

  get pipelineStageId(): string {
    return this._data.pipeline_stage_id
  }

  set pipelineStageId(pipelineStageId: string) {
    this._data.pipeline_stage_id = pipelineStageId
  }

  get status(): Status {
    return this._data.status
  }

  set status(status: Status) {
    this._data.status = status
  }

  get winProbability(): number {
    return this._data.win_probability
  }

  set winProbability(winProbability: number) {
    this._data.win_probability = winProbability
  }

  get monetaryValue(): number {
    return typeof this._data.monetary_value === 'object' ? undefined : this._data.monetary_value;
  }

  set monetaryValue(monetaryValue: number) {
    if((monetaryValue===null||monetaryValue===undefined) && this._data.monetary_value)
      this._data.monetary_value=firebase.firestore.FieldValue.delete()
    else if (typeof monetaryValue === 'number')
      this._data.monetary_value = monetaryValue
  }

  get monetaryParsedValue(): number {
    if (!this.monetaryValue) return 0;
    const parsedMonetaryValue = parseFloat(this.monetaryValue.toString().replace(/[^0-9\.]+/g, ''));
    return isNaN(parsedMonetaryValue) ? 0 : parsedMonetaryValue;
  }

  get lastActionDate(): moment.Moment {
    return moment(this._data.last_action_date.toMillis())
  }

  set lastActionDate(lastActionDate: moment.Moment) {
    this._data.last_action_date = firebase.firestore.Timestamp.fromMillis(
      lastActionDate.valueOf()
    )
  }

  get decayTasks(): Array<decayTask> {
    return this._data.decayTasks
  }

  set decayTasks(decayTasks: Array<decayTask>) {
    this._data.decayTasks = decayTasks
  }

  get indexVersion(): number {
    return this._data.index_version;
  }

  set indexVersion(indexVersion: number) {
    this._data.index_version = indexVersion;
  }

  get internalSource(): { [key: string]: any } {
    return this._data.internal_source;
  }

  set internalSource(internalSource: { [key: string]: any }) {
    this._data.internal_source = internalSource;
  }

  get lastStatusChangeDate(): moment.Moment {
    return this._data['last_status_change_date']
      ? typeof this._data['last_status_change_date'] === 'number'
        ? moment(this._data['last_status_change_date'])
        : moment(this._data['last_status_change_date'].toMillis())
      : undefined;
  }

  set lastStatusChangeDate(lastStatusChangeDate: moment.Moment) {
    this._data.last_status_change_date = firebase.firestore.Timestamp.fromMillis(
      lastStatusChangeDate.valueOf()
    );
  }

  public clone() {
    const opportunity = new Opportunity()
    opportunity._data = this._data
    opportunity._id = this._id
    opportunity._ref = this._ref
    opportunity._snapshot = this._snapshot
    return opportunity
  }

  public async save() {
    if (this._oldData.status != this.status) {
      this._data.last_status_change_date = firebase.firestore.FieldValue.serverTimestamp()
    }
    return super.save()
      .then(async snapshot => {
        // await Opportunity.updateIndex(this.id)
      })
  }

  public static async updateIndex(
    opportunity,
    oldStatus?: string,
    onlyIndex?: boolean
  ) {
    return axios.get('/v2/elasticsearch/opportunity', {
      params: {
        id: opportunity.id,
        status: opportunity.status,
        pipeline_stage_id: opportunity.pipeline_stage_id || opportunity.pipelineStageId,
        onlyIndex: onlyIndex,
        isStatusChange: oldStatus !== opportunity.status
      }
    })
  }

  //   public static async updateStats(payload:{
  //     opportunity: any,
  //     pipeline_stage_id_old?: string,
  //     event_type?: string,
  //     user
  //   }){
  //     // console.log(payload);
  //     try {
  //       let statsPayload = {
  //         opportunity_id: payload.opportunity.id,
  //         opportunity_name: payload.opportunity.name,

  //         pipeline_id: payload.opportunity.pipeline_id,
  //         pipeline_stage_id_old: payload.pipeline_stage_id_old,
  //         pipeline_stage_id_new: payload.opportunity.pipeline_stage_id,

  //         location_id: payload.opportunity.location_id,
  //         monetary_value: payload.opportunity.monetary_value,
  //         status: payload.opportunity.status,
  //         source: payload.opportunity.source,
  //         type: payload.opportunity.type,
  //         tags: payload.opportunity.tags,
  //         attributed: payload.opportunity.attributed,
  //         index_version: payload.opportunity.index_version,
  //         date_added: new Date(payload.opportunity.date_added.seconds * 1000),

  //         contact_id: payload.opportunity.contact_id,
  //         contact_name: payload.opportunity.contact_name,
  //         contact_email: payload.opportunity.email,
  //         contact_phone: payload.opportunity.phone,
  //         assigned_to: payload.opportunity.assigned_to,
  //         user_id: payload.user.id,
  //         // agent_id: this.user.id,
  //         // agent_email: this.user.email,
  //         // agent_name: this.user.name,
  //         event_type: payload.event_type
  //       }
  //       await axios.post(`/analytics/opportunity/new_event`, statsPayload);
  //     } catch (err) {
  //       console.error(err);
  //     }
  //   }
}
interface decayTask {
  taskRequestId: string
  triggerId: string
}
