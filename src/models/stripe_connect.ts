import restAgent from '../restAgent'
import { PaymentServices } from '../services';

export class LocationStripeConnect {

  private locationId: string;

  private constructor(locationId: string){
    this.locationId = locationId;
  }

  public static async forLocation(locationId: string){
    let dto = null;
    try {
      console.log('fetching stripe connect')
      const { data } = await PaymentServices.findStripeIntegration(locationId);
      return data?.live?.accountId || data?.test?.accountId || data?.accountId
    } catch (err) {
      console.log(`Error fetching stripe connect information for location - ${locationId}`);
      console.log(err);
    }
    return;
  }
}
