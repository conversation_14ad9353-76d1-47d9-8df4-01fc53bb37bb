import firebase from 'firebase/app'
import * as lodash from 'lodash'
import moment from 'moment-timezone'
import axios from 'axios'
import { Calendar } from '.'

export default class Team {
  public static collectionRef() {
    return firebase.firestore().collection('teams')
  }

  public static getById(id: string): Promise<Team> {
    return new Promise((resolve, reject) => {
      Team.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Team(snapshot))
          resolve()
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static fetchAllByLocation(
    locationId: string
  ): firebase.firestore.Query {
    return Team.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static getAllByLocation(locationId: string): Promise<Team[]> {
    return new Promise<Team[]>(async (resolve, reject) => {
      Team.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve([])
          return resolve(snapshot.docs.map(provider => new Team(provider)))
        })
    })
  }

  /**
   * Get provider by passing slug name
   * Provider slug should be globally unique across all the locations
   */
  public static async getBySlugName(slug: string): Promise<Team> {
    return new Promise<Team>(async (resolve, reject) => {
      const snapshot = await Team.collectionRef()
        .where('slug', '==', slug)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          return resolve(new Team(snapshot.docs[0]))
        })
    })
  }

  private _data: firebase.firestore.DocumentData
  private _id: string
  private _ref: firebase.firestore.DocumentReference

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot
      this._ref = Team.collectionRef().doc(snapshot.id)
      if (
        snapshot.date_added &&
        snapshot.date_added instanceof firebase.firestore.Timestamp === false
      ) {
        snapshot.date_added = new firebase.firestore.Timestamp(
          snapshot.date_added.seconds,
          snapshot.date_added.nanoseconds
        )
        if (snapshot.date_updated.seconds) {
          snapshot.date_updated = new firebase.firestore.Timestamp(
            snapshot.date_updated.seconds,
            snapshot.date_updated.nanoseconds
          )
        }
      }
    } else {
      this._ref = Team.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment().utc()
    }
    this.name = this.name
    this.calendarProviderName = this.calendarProviderName
    this.calendarProviderDescription = this.calendarProviderDescription
    if (this.isActive === undefined) {
      this.isActive = true
    }
    this.deleted = this.deleted || false
    this._data.slug = this._data.slug
    this._data.location_id = this._data.location_id
    if (this._data.user_ids && Array.isArray(this._data.user_ids))
      this._data.user_ids = this._data.user_ids
    else this._data.user_ids = []
    // // Default behaviour
    // if (this.shouldApplyMemberChangeOnServices === undefined) {
    //   this.shouldApplyMemberChangeOnServices = true
    // }
    if (this.shouldApplyMemberSelectionChangeOnServices === undefined) {
      this.shouldApplyMemberSelectionChangeOnServices = true
    }
    this.shouldApplyMemberSelectionChangeOnServices = this.shouldApplyMemberSelectionChangeOnServices
    this.shouldAssignContactToTeamMember = this.shouldAssignContactToTeamMember
    this.shouldSkipAssigningContactForExisting = this.shouldSkipAssigningContactForExisting
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toDate())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromDate(
      dateAdded.toDate()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toDate())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromDate(
      dateUpdated.toDate()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(value: string) {
    this._data.name = value
  }

  get calendarProviderName(): string {
    return this._data.calendar_provider_name
  }

  set calendarProviderName(value: string) {
    this._data.calendar_provider_name = value
  }

  get calendarProviderDescription(): string {
    return this._data.calendar_provider_description
  }

  set calendarProviderDescription(value: string) {
    this._data.calendar_provider_description = value
  }

  get isActive(): boolean {
    return this._data.is_active
  }

  set isActive(value: boolean) {
    this._data.is_active = value
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get slug(): string {
    return this._data.slug
  }

  set slug(value: string) {
    this._data.slug = value
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(value: string) {
    this._data.location_id = value
  }

  // #Cal-002 - here tag 'Cal-002' actually meant for props (below two) that are also being used in another class 'User'
  /**
   * This is the calendar where you would like to add new events as they’re scheduled.
   * Provider can have only one calendar as primary.
   * This only applicable to location specific Providers. And it's going to be undefined for user specific (user team) Providers
   */
  get primaryCalendarId(): string | undefined {
    if (this.isTiedToUsers) return undefined
    return this._data.primary_calendar_id
  }

  set primaryCalendarId(value: string | undefined) {
    this._data.primary_calendar_id = value
  }

  /**
   * These calendar(s) are used to check for conflicts to prevent double bookings.
   * Provider can have multiple calendars to check for conflicts.
   * This only applicable to location specific Providers. And it's going to be undefined for user specific (user team) Providers
   */
  get checkForConflictsCalendarIds(): string[] | undefined {
    if (this.isTiedToUsers) return undefined
    return this._data.check_for_conflicts_calendar_ids || []
  }

  set checkForConflictsCalendarIds(value: string[] | undefined) {
    this._data.check_for_conflicts_calendar_ids = value
  }

  // #Cal-003
  /**
   * This is going to be useful (not mandatory) in creating a contact as part of synching calendar events from integrations like google
   * By linking service id to appointment booking, which eventually helps to fetch slot buffer time for that particular booked appointment
   * This only applicable to location specific Providers. And it's going to be undefined for user specific (user team) Providers
   */
  get defaultCalendarServiceId(): string | undefined {
    if (this.isTiedToUsers) return undefined
    return this._data.default_calendar_service_id
  }
  set defaultCalendarServiceId(value: string | undefined) {
    this._data.default_calendar_service_id = value
  }

  get timezone(): string {
    return this._data.timezone
  }

  set timezone(value: string) {
    this._data.timezone = value
  }

  // Whether this provider is tied with user(s) or not. This flag is required since Location specific (v2/v3) calendars are not attached to any user(s)
  get isTiedToUsers(): boolean {
    return this._data.is_tied_to_users
  }

  set isTiedToUsers(isTiedToUsers: boolean) {
    this._data.is_tied_to_users = isTiedToUsers
  }

  set userIds(value: string[]) {
    this._data.user_ids = value
  }

  get userIds(): string[] {
    return this._data.user_ids
  }

  /**
   * It tells whether to apply the team member selection change on all existing services of this provider or not
   */
  get shouldApplyMemberSelectionChangeOnServices(): boolean {
    return this._data.should_apply_member_selection_change_on_services
  }

  set shouldApplyMemberSelectionChangeOnServices(value: boolean) {
    this._data.should_apply_member_selection_change_on_services = value
  }

  /**
   * It tells whether to assign the team member to conact along with assigning to appointment in round robin booking
   */
  get shouldAssignContactToTeamMember(): boolean {
    return this._data.should_assign_contact_to_team_member
  }

  set shouldAssignContactToTeamMember(value: boolean) {
    this._data.should_assign_contact_to_team_member = value
  }

  /**
   * It tells whether to skip assigning contact if the contact already has an assigned user
   */
  get shouldSkipAssigningContactForExisting(): boolean {
    return this._data.should_skip_assigning_contact_for_existing
  }

  set shouldSkipAssigningContactForExisting(value: boolean) {
    this._data.should_skip_assigning_contact_for_existing = value
  }

  // For location calendar, it will be only item
  set calendars(calendars: any[]) {
    this._data.calendars = calendars
  }

  get calendars(): any[] {
    return this.isTiedToUsers ? [] : this._data.calendars
  }

  // For location only
  set conflictCalendars(conflictCalendars: string[]) {
    this._data.conflict_calendars = conflictCalendars
  }

  get conflictCalendars(): string[] {
    return this.isTiedToUsers ? this._data.conflict_calendars : []
  }

  // Default Available Days of Week
  get defaultAvailableDays() {
    return this._data.default_availabile_days
  }

  set defaultAvailableDays(value: string[]) {
    this._data.default_availabile_days = value
  }

  get defaultAvailableHours() {
    return this._data.default_availabile_hours
  }

  set defaultAvailableHours(value: { [key: string]: number }[]) {
    this._data.default_availabile_hours = value
  }

  public async save() {
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }

  public async applyMemberSelectionChangeOnServices() {
    const calendarServices = await Calendar.fetchAllByProviderId(
      this.id,
      this.locationId
    )

    for (let sIndex = 0; sIndex < calendarServices.length; sIndex++) {
      const calendarService = calendarServices[sIndex]

      //Add newly added team members to the service team members list
      for (let uIndex = 0; uIndex < this.userIds.length; uIndex++) {
        const userId = this.userIds[uIndex]
        if (
          !lodash.find(calendarService.teamMembers, {
            user_id: userId
          })
        ) {
          calendarService.teamMembers.push({
            user_id: userId,
            priority: 0,
            meeting_location: '',
            selected: true
          })
        }
      }

      // Remove deleted team members and Update existing team members from unselect to select
      calendarService.teamMembers = calendarService.teamMembers
        // Remove deleted ones
        .filter(x => this.userIds.includes(x.user_id))
        // Update existing ones
        .map(x => ({
          ...x,
          selected: true
        }))

      calendarService.isActive = true

      await calendarService.save()
    }
  }

  /**
   * Create a task to make the provider member's data objects ready in order to start serve
   * Like create a master calendar for them and setting-up timezone, and open hours.
   */
  public async crateTaskToReadyProviderUsers() {
    try {
      await axios.get(
        '/appointment/' + this.id + '/create_task_to_ready_provider_users'
      )
    } catch (err) { }
  }
}
