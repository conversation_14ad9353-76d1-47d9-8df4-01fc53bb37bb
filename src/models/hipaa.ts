import firebase from 'firebase/app'
import moment from 'moment-timezone'
import lodash from 'lodash'

export default class Hipaa {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('hipaa')
  }

  public static getById(hipaaId: string): Promise<Hipaa> {
    return new Promise((resolve, reject) => {
      Hipaa.collectionRef()
        .doc(hipaaId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Hipaa(snapshot))
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  public static getByCompanyIdRealtime(
    companyId: string
  ): firebase.firestore.Query {
    return Hipaa.collectionRef()
      .where('company_id', '==', companyId)
  }

  public static async getByCompanyId(
    companyId: string
  ): Promise<Hipaa[]> {
    const snapshot = await Hipaa.getByCompanyIdRealtime(companyId).get()
    return snapshot.docs.map(d => new Hipaa(d))
  }

  public save() {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment()
      _self._ref.set(this.data).then(_ => {
        resolve(_self)
      })
    })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = Hipaa.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment().utc()
      this.dateUpdated = moment().utc()
    }
  }

  get id(): string {
    return this._id
  }

  get ref() {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated)
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get designation(): string {
    return this._data.designation
  }

  set designation(designation: string) {
    this._data.designation = designation
  }

  get address(): string {
    return this._data.address
  }

  set address(address: string) {
    this._data.address = address
  }

  get organizationName(): string {
    return this._data.organization_name
  }

  set organizationName(organizationName: string) {
    this._data.organization_name = organizationName
  }

  get phone(): string {
    return this._data.phone
  }

  set phone(phone: string) {
    this._data.phone = phone
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
  }

  get contactName(): string {
    return this._data.contact_name
  }

  set contactName(contactName: string) {
    this._data.contact_name = contactName
  }
}
