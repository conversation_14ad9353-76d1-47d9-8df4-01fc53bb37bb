import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { pickBy } from 'lodash'

export enum FunnelLookupType {
  Funnel = 'funnel',
  Step = 'step',
  Page = 'page',
  Domain = 'domain'
}

export default class FunnelLookup {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('funnel_lookup')
  }

  public static getById(id: string): Promise<FunnelLookup> {
    return new Promise(async (resolve, reject) => {
      try {
        await FunnelLookup.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new FunnelLookup(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getByTypeId(typeId: string, locationId: string): Promise<FunnelLookup> {
    return new Promise((resolve, reject) => {
      FunnelLookup.collectionRef()
        .where('deleted', '==', false)
        .where('type_id', '==', typeId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.docs.length > 1) {
            reject({ err: 'More than one docs!' })
          } else if (snapshot.docs.length === 0) {
            reject({ err: 'No docs' })
          } else {
            const doc = snapshot.docs[0]
            resolve(new FunnelLookup(doc))
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByTypeId(typeId: string, locationId: string): Promise<FunnelLookup[]> {
    return new Promise((resolve, reject) => {
      FunnelLookup.collectionRef()
        .where('deleted', '==', false)
        .where('type_id', '==', typeId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new FunnelLookup(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByFunnel(funnelId: string, locationId): Promise<FunnelLookup[]> {
    return new Promise((resolve, reject) => {
      FunnelLookup.collectionRef()
        .where('deleted', '==', false)
        .where('funnel_id', '==', funnelId)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new FunnelLookup(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllRedirectsByLocationId(locationId: string): Promise<FunnelLookup[]> {
    return new Promise((resolve, reject) => {
      FunnelLookup.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .where('type', '==', 'redirect')
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new FunnelLookup(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getByDomainAndPath(
    domain: string,
    path: string,
    locationId: string
  ): Promise<FunnelLookup> {
    return new Promise((resolve, reject) => {
      FunnelLookup.collectionRef()
        .where('deleted', '==', false)
        .where('domain', '==', domain)
        .where('path', '==', path)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          if (snapshot.docs.length > 1) {
            reject({ message: 'More than one page has this URL, change the path of this page to get it to work.', code: 409 });
          } else if (snapshot.docs.length === 0) {
            reject({ message: `Sorry, we can't find the url you were looking for`, code: 404 });
          } else {
            resolve(new FunnelLookup(snapshot.docs[0]));
          }
        })
        .catch(err => {
          reject(err);
        });
    });
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    const init_data = {
      deleted: false,
      steps: []
    }

    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || { ...init_data }
    } else {
      this._ref = FunnelLookup.collectionRef().doc()
      this._data = { ...init_data }
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }

  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        _self._data.path_lowercase = _self.path ? _self.path.toLowerCase() : _self.path
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  get id(): string {
    return this._id
  }

  get domain(): string {
    return this._data.domain
  }

  set domain(domain: string) {
    this._data.domain = domain
  }

  // 1:1 relation with url of funnel/step/page url
  get path(): string {
    return this._data.path
  }

  set path(path: string) {
    this._data.path = path
  }

  get locationId() {
    return this._data.location_id
  }

  set locationId(locationId) {
    this._data.location_id = locationId
  }

  get funnelId() {
    return this._data.funnel_id
  }

  set funnelId(funnelId) {
    this._data.funnel_id = funnelId
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  // 1 : 1 relation
  // This one needs to be unique as for each funnel/step/page we will have a lookup instance
  get typeId(): string {
    return this._data.type_id
  }

  set typeId(typeId: string) {
    this._data.type_id = typeId
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get pathLowercase(): string {
    return this._data.path_lowercase
  }

  set pathLowercase(path: string) {
    this._data.path_lowercase = path
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }

  get action() {
    return this._data.action
  }

  set action(action) {
    this._data.action = action
  }

  get target() {
    return this._data.target
  }

  set target(target) {
    this._data.target = target
  }

  public delete() {
    return FunnelLookup.collectionRef()
      .doc(this.id)
      .delete()
  }
}
