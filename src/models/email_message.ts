import firebase from 'firebase/app';
import moment from 'moment-timezone';
import { MessageStatus } from '.';
import axios from 'axios'

export interface Events {
	clicked: number;
	complained: number;
	delivered: number;
	opened: number;
	permanent_fail: number;
	temporary_fail: number;
	unsubscribed: number;
}

export default class EmailMessage {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('email_messages');
	}

	public save(): Promise<EmailMessage> {
		const _self = this;
		return new Promise((resolve, reject) => {
			_self.dateUpdated = moment();
			_self._ref.set(this._data).then(() => {
				resolve(_self);
			});
		});
	}

	public delete() {
		return EmailMessage.collectionRef()
			.doc(this.id)
			.update({
				deleted: true,
			});
	}

	public static getById(itemId: string): Promise<EmailMessage> {
		return new Promise((resolve, reject) => {
			EmailMessage.collectionRef()
				.doc(itemId)
				.get()
				.then((snapshot: firebase.firestore.DocumentSnapshot) => {
					if (snapshot.exists) resolve(new EmailMessage(snapshot));
					resolve();
				})
				.catch((err) => {
					reject(err)
				});
		});
	}

	public static getByAltId(type: string, id: string, locationId: string): Promise<EmailMessage> {
		return new Promise((resolve, reject) => {
			EmailMessage.collectionRef()
				.where('meta.' + type + '.id', '==', id)
				.where('location_id', '==', locationId)
				.where('deleted', '==', false)
				.get()
				.then((snapshot) => {
					if (snapshot.empty) {
						return resolve(null);
					}
					return resolve(new EmailMessage(snapshot.docs[0]));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByLocationId(locationId: string, limit?: number): Promise<EmailMessage[]> {
		return new Promise<EmailMessage[]>((resolve, reject) => {
			let query = EmailMessage.collectionRef()
				.where('location_id', '==', locationId)
				.where('deleted', '==', false)
				.orderBy('date_updated');
			if (limit) {
				query = query.limit(limit);
			}
			query.get().then((snapshot) => {
				resolve(snapshot.docs.map((d) => new EmailMessage(d)));
			})
				.catch((err) => {
					reject(err)
				})
		});
	}

	public static getByThreadId(threadId: string, locationId): Promise<EmailMessage[]> {
		return new Promise<EmailMessage[]>((resolve, reject) => {
			EmailMessage.collectionRef()
				.where('thread_id', '==', threadId)
				.where('location_id', '==', locationId)
				.where('deleted', '==', false)
				.orderBy('date_updated')
				.get()
				.then((snapshot) => {
					resolve(snapshot.docs.map((d) => new EmailMessage(d)));
				})
				.catch((err) => {
					reject(err)
				});
		});
	}

	public static getByThreadIdRealtime(threadId: string, locationId: string) {
		return EmailMessage.collectionRef()
			.where('thread_id', '==', threadId)
			.where('deleted', '==', false)
			.where('location_id', '==', locationId)
			.orderBy('date_added', 'desc');
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._data = snapshot.data();
			this._ref = snapshot.ref;
		} else {
			this._ref = EmailMessage.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.deleted = false;
			this.dateAdded = moment();
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get ref(): firebase.firestore.DocumentReference {
		return this._ref;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get threadId(): string {
		return this._data.thread_id;
	}

	set threadId(threadId: string) {
		this._data.thread_id = threadId;
	}

	get subject(): string {
		return this._data.subject;
	}

	set subject(subject: string) {
		this._data.subject = subject;
	}

	get name(): string {
		return this._data.name;
	}

	set name(name: string) {
		this._data.name = name;
	}

	get from(): string {
		return this._data.from;
	}

	set from(from: string) {
		this._data.from = from;
	}

	get to(): string[] {
		return this._data.to;
	}

	set to(to: string[]) {
		this._data.to = to;
	}

	get cc(): string[] {
		return this._data.cc;
	}

	set cc(cc: string[]) {
		this._data.cc = cc;
	}

	get bcc(): string[] {
		return this._data.bcc;
	}

	set bcc(bcc: string[]) {
		this._data.bcc = bcc;
	}

	get replyTo(): string[] {
		return this._data.reply_to;
	}

	set replyTo(reply_to: string[]) {
		this._data.reply_to = reply_to;
	}

	get body(): string {
		return this._data.body;
	}

	set body(body: string) {
		this._data.body = body;
	}

	get files(): string[] {
		if (!this._data.files || !(this._data.files instanceof Array)) this._data.files = [];
		return this._data.files;
	}

	set files(files: string[]) {
		this._data.files = files;
	}

	get events(): Events {
		return this._data.events;
	}

	set events(events: Events) {
		this._data.events = events;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get deleted(): boolean {
		return this._data.deleted;
	}

	set deleted(deleted: boolean) {
		this._data.deleted = deleted;
	}

	get direction(): string {
		return this._data.direction;
	}

	set direction(direction: string) {
		this._data.direction = direction;
	}

	get snippet(): string {
		return this._data.snippet;
	}

	set snippet(snippet: string) {
		this._data.snippet = snippet;
	}

	get status(): MessageStatus {
		return this._data.status;
	}

	set status(status: MessageStatus) {
		this._data.status = status;
	}

  get meta() {
    if (!this._data.meta) this._data.meta = {}
    return this._data.meta
  }

  get bodyStorageUrl(): string {
    return this._data.bodyStorageUrl
  }

  set bodyStorageUrl(bodyStorageUrl: string) {
    this._data.bodyStorageUrl = bodyStorageUrl
  }

  get bodyDownloadUrl(): string {
    return this._data.bodyDownloadUrl
  }

  set bodyDownloadUrl(bodyDownloadUrl: string) {
    this._data.bodyDownloadUrl = bodyDownloadUrl
  }

  async getBody(): Promise<string> {
    //global instance send some Headers like token-id, which gets failed under CORS rules  
    var axiosInstance = axios.create()
    const emailContent: any = await axiosInstance.get(this.bodyDownloadUrl)
    return emailContent?.data?.body
  }
}
