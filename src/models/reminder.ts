import firebase from 'firebase/app';
import moment from 'moment-timezone';
import { Company, LineItem, Address, AuthUser } from '@/models';
import store from '../store';

export default class Reminder {
    public static collectionRef(): firebase.firestore.CollectionReference {
        return firebase.firestore().collection('reminders');
    }

    public static getById(id: string) {
        return new Promise((resolve, reject) => {
            Reminder.collectionRef()
                .doc(id)
                .get()
                .then((snapshot: firebase.firestore.DocumentSnapshot) => {
                    if (snapshot.exists) resolve(new Reminder(snapshot));
                    resolve();
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static getStreamById(id: string): firebase.firestore.DocumentReference {
        return Reminder.collectionRef().doc(id);
    }

    public static async fetchAllReminders(): Promise<firestore.Query> {
        const auth: AuthUser = await store.dispatch('auth/get');
        return Reminder.collectionRef()
            .where('deleted', '==', false)
            .where('company_id', '==', auth.companyId)
            .orderBy('date_added', 'desc');
    }

    public static async fetchAllRemindersBetween(
        calendarId: string,
        start: moment.Moment,
        end: moment.Moment,
    ): Promise<firestore.Query> {
        const auth: AuthUser = await store.dispatch('auth/get');
        return Reminder.collectionRef()
            .where('deleted', '==', false)
            .where('calendar_id', '==', calendarId)
            .where('start_date', '>=', start.toDate())
            .where('start_date', '<', end.toDate())
            .where('company_id', '==', auth.companyId)
            .orderBy('start_date', 'desc');
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
        } else {
            this._data = {};
            this.deleted = false;
            this.dateAdded = moment();
            this.status = 'new';
        }
    }

    get id(): string | undefined {
        return this._id;
    }

    get data(): firebase.firestore.DocumentData {
        return this._data;
    }

    get companyId(): string {
        return this._data.company_id;
    }

    set companyId(companyId: string) {
        this._data.company_id = companyId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = dateUpdated.toDate();
    }

    get deleted(): boolean {
        return this._data.deleted;
    }

    set deleted(deleted: boolean) {
        this._data.deleted = deleted;
    }

    get dirty(): boolean {
        return this._data.dirty;
    }

    set dirty(dirty: boolean) {
        this._data.dirty = dirty;
    }

    get startDate(): moment.Moment {
        return moment(this._data.start_date);
    }

    set startDate(startDate: moment.Moment) {
        this._data.start_date = startDate.toDate();
    }

    get type(): string {
        return this._data.type;
    }

    set type(type: string) {
        this._data.type = type;
    }

    get status(): string {
        return this._data.status;
    }

    set status(status: string) {
        this._data.status = status;
    }

    get appointmentId(): string {
        return this._data.appointment_id;
    }

    set appointmentId(appointmentId: string) {
        this._data.appointment_id = appointmentId;
    }

    get toPhone(): string {
        return this._data.to_phone;
    }

    set toPhone(toPhone: string) {
        this._data.to_phone = toPhone;
    }

    get toEmail(): string {
        return this._data.to_email;
    }

    set toEmail(toEmail: string) {
        this._data.to_email = toEmail;
    }

    get sentDate(): moment.Moment {
        return this._data.sent_date;
    }

    set sentDate(sentDate: moment.Moment) {
        this._data.sent_date = sentDate;
    }

    get confirmedDate(): moment.Moment {
        return this._data.confirmed_date;
    }

    set confirmedDate(confirmedDate: moment.Moment) {
        this._data.confirmed_date = confirmedDate;
    }

    public save() {
        this.dateUpdated = moment();
        this.dirty = true;
        if (this._ref) {
            this._ref.update(this._data);
        } else {
            this._ref = Reminder.collectionRef().doc();
            this._id = this._ref.id;
            this._ref.set(this._data);
        }
    }

    public delete() {
        return Reminder.collectionRef()
            .doc(this.id)
            .update({
                deleted: true,
            });
    }
}
