import firebase from 'firebase/app'
import 'firebase/database'
import * as moment from 'moment-timezone'
import * as CryptoJS from 'crypto-js'

export default class TwilioAccount {
  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = TwilioAccount.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this._data.numbers = {}
      this._data.forwarding_numbers = {}
      this._data.whisper_msg = {}
      this._data.play_warning = {}
      this._data.call_recording = {}
      this._data.number_pool_numbers_is_active = {}
      this._data.number_name = {}
      this.dateAdded = moment()
    }
  }

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('twilio_accounts')
  }

  public save(): Promise<TwilioAccount> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment()
      _self._ref.set(_self._data).then(_ => {
        resolve(_self)
      })
    })
  }

  public static getById(id) {
    return new Promise((resolve, reject) => {
      TwilioAccount.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new TwilioAccount(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static fetchByLocationId(locationId: string) {
    return TwilioAccount.collectionRef()
      .where('location_id', '==', locationId)
      .limit(1)
  }

  static getByLocation(locationId: string): Promise<TwilioAccount> {
    return new Promise((resolve, reject) => {
      TwilioAccount.collectionRef()
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve()
          }
          return resolve(new TwilioAccount(snapshot.docs[0]))
        })
        .catch(err => {
          return resolve(null)
        })
    })
  }

  public static getByCompanyId(companyId: string): Promise<TwilioAccount> {
    return new Promise((resolve, reject) => {
      TwilioAccount.collectionRef()
        .where('company_id', '==', companyId)
        .get()
        .then(snapshot => {
          if (!snapshot.empty) {
            resolve(new TwilioAccount(snapshot.docs[0]))
          } else {
            resolve()
          }
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByLocationId(locationId: string): Promise<TwilioAccount> {
    return new Promise((resolve, reject) => {
      TwilioAccount.collectionRef()
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (!snapshot.empty) {
            resolve(new TwilioAccount(snapshot.docs[0]))
          } else {
            resolve()
          }
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  static getByNumber(number): Promise<TwilioAccount> {
    return new Promise((resolve, reject) => {
      TwilioAccount.collectionRef()
        .where('numbers.' + number, '>', '')
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve()
          }
          return resolve(new TwilioAccount(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  static getByAccountSid(accountSid): Promise<TwilioAccount> {
    return new Promise<TwilioAccount>((resolve, reject) => {
      TwilioAccount.collectionRef()
        .where('account_sid', '==', accountSid)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return reject('No matching twilio account found for ' + accountSid)
          }

          return resolve(new TwilioAccount(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          return reject(err)
        })
    })
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return this._data
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(company_id: string) {
    this._data.company_id = company_id
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated)
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  get accountSID(): string {
    return this._data.account_sid
  }

  set accountSID(accountSID: string) {
    this._data.account_sid = accountSID
  }

  get token(): string {
    if (this._data.token)
      return CryptoJS.AES.decrypt(this._data.token, 'apples&oranges').toString(
        CryptoJS.enc.Utf8
      )
  }

  set token(token: string) {
    this._data.token = CryptoJS.AES.encrypt(token, 'apples&oranges').toString()
  }

  get numbers(): { [field: string]: string } {
    return this._data.numbers
  }

  get forwardingNumbers(): { [field: string]: string } {
    if (!this._data.forwarding_numbers) this._data.forwarding_numbers = {}
    return this._data.forwarding_numbers
  }

  set forwardingNumbers(values) {
    this._data.forwarding_numbers = values
  }

  get assignedToNumber(): { [field: string]: string } {
    if (!this._data.assigned_to_numbers) this._data.assigned_to_numbers = {}
    return this._data.assigned_to_numbers
  }

  set assignedToNumber(values) {
    this._data.assigned_to_numbers = values
  }

  get defaultOutboundNumber(): string {
    return this._data.default_outbound_number
  }

  set defaultOutboundNumber(defaultOutboundNumber: string) {
    this._data.default_outbound_number = defaultOutboundNumber
  }

  get callRecording(): { [field: string]: string } {
    if (!this._data.call_recording) this._data.call_recording = {}

    return this._data.call_recording
  }

  get numberPoolNumbersIsActive(): { [field: string]: boolean } {
    if (!this._data.number_pool_numbers_is_active)
      this._data.number_pool_numbers_is_active = {}
    return this._data.number_pool_numbers_is_active
  }

  set callRecording(values) {
    this._data.call_recording = values
  }

  get playWarningMessage(): { [field: string]: string } {
    if (!this._data.play_warning) this._data.play_warning = {}
    return this._data.play_warning
  }

  set playWarningMessage(values) {
    this._data.play_warning = values
  }

  get twimlAppId(): string {
    return this._data.twiml_app_id
  }

  set twimlAppId(twimlAppId: string) {
    this._data.twiml_app_id = twimlAppId
  }

  get apiKey(): string {
    return this._data.api_key
  }

  set apiKey(apiKey: string) {
    this._data.api_key = apiKey
  }

  get apiSecret(): string {
    return this._data.api_secret
  }

  set apiSecret(apiSecret: string) {
    this._data.api_secret = apiSecret
  }

  get whisper(): { [field: string]: string } {
    if (!this._data.whisper) this._data.whisper = {}
    return this._data.whisper
  }

  set whisper(values) {
    this._data.whisper = values
  }

  get whisperMessages(): { [field: string]: string } {
    if (!this._data.whisper_msg) this._data.whisper_msg = {}
    return this._data.whisper_msg
  }

  set whisperMessages(values) {
    this._data.whisper_msg = values
  }

  get incomingWhisper(): { [field: string]: boolean } {
    if (!this._data.incoming_whisper) this._data.incoming_whisper = {}
    return this._data.incoming_whisper
  }

  set incomingWhisper(values) {
    this._data.incoming_whisper = values
  }

  get incomingWhisperMessages(): { [field: string]: string } {
    if (!this._data.incoming_whisper_msg) this._data.incoming_whisper_msg = {}
    return this._data.incoming_whisper_msg
  }

  set incomingWhisperMessages(values) {
    this._data.incoming_whisper_msg = values
  }

  get enableCallerId(): { [field: string]: boolean } {
    if (!this._data.enable_callerid) this._data.enable_callerid = {}
    return this._data.enable_callerid
  }

  set enableCallerId(values) {
    this._data.enable_callerid = values
  }

  get voicemail(): { [field: string]: string } {
    if (!this._data.voicemail) this._data.voicemail = {}
    return this._data.voicemail
  }

  set voicemail(values) {
    this._data.voicemail = values
  }

  get voicemailTimeout(): { [field: string]: number } {
    if (!this._data.voicemail_timeout) this._data.voicemail_timeout = {}
    return this._data.voicemail_timeout
  }

  set voicemailTimeout(values) {
    this._data.voicemail_timeout = values
  }

  get voicemailOutbound(): { [field: string]: string } {
    if (!this._data.voicemail_outbound) this._data.voicemail_outbound = {}
    return this._data.voicemail_outbound
  }

  set voicemailOutbound(values) {
    this._data.voicemail_outbound = values
  }

  get voicemailOutboundTimeout(): { [field: string]: number } {
    if (!this._data.voicemail_outbound_timeout)
      this._data.voicemail_outbound_timeout = {}
    return this._data.voicemail_outbound_timeout
  }

  set voicemailOutboundTimeout(values) {
    this._data.voicemail_outbound_timeout = values
  }

  get numberPools(): string[] {
    return this._data.number_pools || []
  }

  set numberPools(poolIds: string[]) {
    this._data.number_pools = poolIds
  }

  get numberName(): { [field: string]: string } {
    if (!this._data.number_name) this._data.number_name = {}
    return this._data.number_name
  }

  set numberName(values) {
    this._data.number_name = values
  }

  get validateSms(): boolean {
    return this._data.validate_sms
  }
  set validateSms(validateSms: boolean) {
    this._data.validate_sms = validateSms
  }

  get createSubAccountsAutomatically(): boolean {
    return typeof this._data.auto_create_subacc === 'boolean'
      ? this._data.auto_create_subacc
      : true // When the field isn't there, it will fallback to true
  }

  set createSubAccountsAutomatically(createSubAccountsAutomatically: boolean) {
    this._data.auto_create_subacc = createSubAccountsAutomatically
  }

  get underGHLAccount(): boolean {
    return this._data.under_ghl_account || false;
  }

  set underGHLAccount(underGHL: boolean) {
    this._data.under_ghl_account = underGHL;
  }
}
