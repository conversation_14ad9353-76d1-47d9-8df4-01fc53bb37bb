export interface IEventStats {
  count: number
  opened: number
  clicked: number
  delivered: number
  complained: number
  failed: number
  unsubscribed: number
  replied: number
  isForSMTP?: boolean
  isEmailStats?: boolean
  isSMSStats?: boolean
}

export interface ICombinedStats {
  total:number | null | undefined;
  success: number | null | undefined;
  error: number | null | undefined;
  queued: number | null | undefined;
  providerStats?: IEventStats;
  noDrillDown?:boolean;
}
