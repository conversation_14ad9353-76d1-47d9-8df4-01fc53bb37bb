import moment, { Moment } from 'moment-timezone';
import firebase from "firebase";

export class ModelUtils {

  public static getDateOnRaw(raw: { [key: string]: any }, fieldName: string){
    try {
      if (!raw) return undefined;
      if (!fieldName) return undefined;
      const rawVal = raw[fieldName];
      if (!rawVal) return undefined;
      return moment(typeof rawVal === 'number' ? rawVal : typeof rawVal === 'string' ? moment(raw) : rawVal.toMillis());
    } catch (err) {
       console.log(`Error evaluating moment for ${fieldName} - raw ${raw}`);
    }
  }

  public static setDateOnRaw(raw: { [key: string]: any }, fieldName: string, m: Moment) {
    try {
      if (!raw) return;
      if (!fieldName) return;
      if (m && m.isValid()) {
        raw[fieldName] = firebase.firestore.Timestamp.fromMillis(m.valueOf());
      }
    } catch (err) {
      console.log(`Error evaluating moment for in setDateonRaw ${fieldName} - raw ${raw} - moment ${m}`);
   }
  }

}
