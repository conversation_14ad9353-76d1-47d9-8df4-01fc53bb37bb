import firebase from 'firebase/app';
import moment from 'moment-timezone';
import Location from './location';

export default class MailGunAccount {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('mailgun_accounts');
	}
	public save(): Promise<MailGunAccount> {
		const _self = this;
		return new Promise((resolve, reject) => {
			_self.dateUpdated = moment();
			_self._ref.set(_self.data).then((_) => {
				resolve(_self);
			});
		});
	}

	public static getById(id): Promise<MailGunAccount> {
		return new Promise((resolve, reject) => {
			MailGunAccount.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new MailGunAccount(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByLocationId(locationId: string): Promise<MailGunAccount> {
		return new Promise((resolve, reject) => {
			MailGunAccount.collectionRef()
				.where('location_id', '==', locationId)
				.get()
				.then((snapshot) => {
					if (snapshot.docs.length >= 1) {
						resolve(new MailGunAccount(snapshot.docs[0]));
					} else {
						resolve();
					}
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByLocationIdRealtime(locationId: string): firebase.firestore.Query {
		return MailGunAccount.collectionRef()
			.where('location_id', '==', locationId)
	}

	public static getByCompanyId(companyId: string): Promise<MailGunAccount> {
		return new Promise((resolve, reject) => {
			MailGunAccount.collectionRef()
				.where('company_id', '==', companyId)
				.get()
				.then((snapshot) => {
					if (snapshot.docs.length === 1) {
						resolve(new MailGunAccount(snapshot.docs[0]));
					} else {
						resolve();
					}
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByCompanyIdRealtime(companyId: string): firebase.firestore.Query {
		return MailGunAccount.collectionRef()
			.where('company_id', '==', companyId)
	}

	public static async getMailGunAccount(params: { locationId?: string; companyId?: string; fallback: boolean }) {
		let mailgunAccount = undefined;

		if (params.locationId) {
			mailgunAccount = await MailGunAccount.getByLocationId(params.locationId);
			if (mailgunAccount || !params.fallback) return mailgunAccount;
		}
		let companyId = params.companyId;
		if (!companyId && params.locationId) {
			const location = await Location.getById(params.locationId);
			companyId = location.companyId;
		}
		if (companyId) {
			mailgunAccount = await MailGunAccount.getByCompanyId(companyId);
			if (mailgunAccount || !params.fallback) return mailgunAccount;
		}
		return;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = MailGunAccount.collectionRef().doc();
			this._data = {};
			this._id = this._ref.id;
			this.dateAdded = moment();
		}
	}

	get ref() {
		return this._ref
	}

	get id(): string {
		return this._id;
	}

	get data(): { [key: string]: any } {
		return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get companyId(): string {
		return this._data.company_id;
	}

	set companyId(company_id: string) {
		this._data.company_id = company_id;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added);
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = dateAdded.toDate();
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated);
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = dateUpdated.toDate();
	}

	get apiKey(): string {
		return this._data.api_key;
	}

	set apiKey(apiKey: string) {
		this._data.api_key = apiKey;
	}

	get domain(): string {
		return this._data.domain;
	}

	set domain(domain: string) {
		this._data.domain = domain;
	}

	get forwardingAddresses(): string {
		return this._data.forwarding_addresses;
	}

	set forwardingAddresses(forwarding_addresses: string) {
		this._data.forwarding_addresses = forwarding_addresses;
	}

	get bccEmails(): string {
		return this._data.bcc_emails;
	}

	set bccEmails(bccEmails: string) {
		this._data.bcc_emails = bccEmails;
	}

	get validateEmails(): boolean {
		return this._data.validate_emails;
	}

	set validateEmails(validateEmails: boolean) {
		this._data.validate_emails = validateEmails;
	}

	get mailgunError(): string {
		return this._data.mailgun_error;
	}

	set mailgunError(mailgunError: string) {
		this._data.mailgun_error = mailgunError
	}
}
