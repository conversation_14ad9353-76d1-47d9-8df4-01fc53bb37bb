import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import { pickBy } from 'lodash'

export default class Order {
  get id(): string {
    return this._id
  }
  get snapshot() {
    return this._snapshot
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }
  get locationId() {
    return this._data.location_id
  }

  set locationId(locationId) {
    this._data.location_id = locationId
  }

  get funnelId(): string {
    return this._data.funnel_id
  }

  set funnelId(funnelId: string) {
    this._data.funnel_id = funnelId
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get stripeCustomerId(): string {
    return this._data.stripe_customer_id
  }

  set stripeCustomerId(stripeCustomerId: string) {
    this._data.stripe_customer_id = stripeCustomerId
  }
  get stepId(): string {
    return this._data.step_id
  }

  set stepId(stepId: string) {
    this._data.step_id = stepId
  }

  get funnelPageId(): string {
    return this._data.funnel_page_id
  }

  set funnelPageId(funnelPageId: string) {
    this._data.funnel_page_id = funnelPageId
  }

  get stripePlanId(): string {
    return this._data.stripe_plan_id
  }

  set stripePlanId(stripePlanId: string) {
    this._data.stripe_plan_id = stripePlanId
  }

  get stripeProductId(): string {
    return this._data.stripe_product_id
  }

  set stripeProductId(stripeProductId: string) {
    this._data.stripe_product_id = stripeProductId
  }

  get chargeId(): string {
    return this._data.charge_id
  }

  set chargeId(chargeId: string) {
    this._data.charge_id = chargeId
  }

  get amount(): string {
    return this._data.amount
  }

  set amount(amount: string) {
    this._data.amount = amount
  }

  get productId(): string {
    return this._data.product_id
  }

  set productId(productId: string) {
    this._data.product_id = productId
  }
  get paymentType(): string {
    return this._data.payment_type
  }

  set paymentType(paymentType: string) {
    this._data.payment_type = paymentType
  }

  get currency(): string {
    return this._data.currency
  }

  set currency(currency: string) {
    this._data.currency = currency
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('orders')
  }

  public static getAllByLocation(locationId: string): Promise<Order[]> {
    return new Promise((resolve, reject) => {
      Order.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Order(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByLocationAndFunnelId({
    locationId,
    funnelId,
    start,
    end
  }: {
    locationId: string
    funnelId: string
    start: moment.Moment
    end: moment.Moment
  }): firebase.firestore.Query {
    return Order.collectionRef()
      .where('location_id', '==', locationId)
      .where('funnel_id', '==', funnelId)
      .where('deleted', '==', false)
      .where('date_added', '>=', start.toDate())
      .where('date_added', '<', end.toDate())
      .orderBy('date_added', 'desc')
  }

  public static getAllByLocationFunnelIdAndStepId({
    locationId,
    funnelId,
    stepId,
    start,
    end
  }: {
    locationId: string
    funnelId: string
    stepId: string
    start: moment.Moment
    end: moment.Moment
  }): firebase.firestore.Query {
    return Order.collectionRef()
      .where('location_id', '==', locationId)
      .where('funnel_id', '==', funnelId)
      .where('step_id', '==', stepId)
      .where('deleted', '==', false)
      .where('date_added', '>=', start.toDate())
      .where('date_added', '<', end.toDate())
      .orderBy('date_added', 'desc')
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    const init_data = {
      deleted: false
    }

    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || { ...init_data }
      this._snapshot = snapshot
    } else {
      this._ref = Order.collectionRef().doc()
      this._data = { ...init_data }
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }
}
