import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

export default class ReviewRequestAggregate {
    public static collectionRef() {
        return firebase.firestore().collection('review_request_aggregate');
    }

    public static getByLocationYearMonthRealtime(locationId: string, month: moment.Moment) {
        return ReviewRequestAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('month', '==',month.toDate());
    }

    public static getByLocationWeekRealtime(locationId: string, week: moment.Moment) {
        return ReviewRequestAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('week', '==', week.toDate());
    }

    public static getByLocationYearMonthsRealtime(
        locationId: string,
        monthStart: moment.Moment,
        monthEnd: moment.Moment
    ) {
        return ReviewRequestAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('month', '>=', monthStart.toDate())
            .where('month', '<=', monthEnd.toDate());
    }

    public static getByLocationYearMonthsRealtimeWeekly(
        locationId: string,
        monthStart: moment.Moment,
        monthEnd: moment.Moment
    ) {
        return ReviewRequestAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('week', '>=', monthStart.toDate())
            .where('week', '<=', monthEnd.toDate());
    }

    public static getByLocationYearMonth(locationId: string, month: moment.Moment): Promise<ReviewRequestAggregate> {
        return new Promise((resolve, reject) => {
            ReviewRequestAggregate.getByLocationYearMonthRealtime(locationId, month).get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve();
                    if (snapshot.size > 1) reject();
                    resolve(new ReviewRequestAggregate(snapshot.docs[0]));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static getByLocationWeek(locationId: string, week: moment.Moment): Promise<ReviewRequestAggregate> {
        return new Promise((resolve, reject) => {
            ReviewRequestAggregate.getByLocationWeekRealtime(locationId, week)
                .get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve();
                    if (snapshot.size > 1) reject();
                    resolve(new ReviewRequestAggregate(snapshot.docs[0]));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    private _id: string;
    private _data: { [field: string]: any };
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._data = snapshot.data() || {};
            this._ref = snapshot.ref;
        } else {
            this._ref = ReviewRequestAggregate.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.dateAdded = moment().utc();
        }
    }

    public save(): Promise<ReviewRequestAggregate> {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self._data, { merge: true }).then((_) => {
                resolve(_self);
            });
        });
    }

    get id(): string {
        return this._id;
    }

    get data(): { [field: string]: any } {
        return this._data;
    }

    get ref() {
        return this._ref;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data['date_added']);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data['date_added'] = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data['date_updated']);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data['date_updated'] = dateUpdated.toDate();
    }

    get week(): moment.Moment {
        return moment(this._data['week']);
    }

    set week(week: moment.Moment) {
        this._data['week'] = week.toDate();
    }

    get locationId(): string {
        return this._data['location_id'];
    }

    set locationId(locationId: string) {
        this._data['location_id'] = locationId;
    }

    get month(): moment.Moment {
        return this._data['month'];
    }

    set month(month: moment.Moment) {
        this._data['month'] = month.toDate();
    }

    get totalRequests(): number {
        return this._data['total_requests'];
    }

    set totalRequests(totalRequests: number) {
        this._data['total_requests'] = totalRequests;
    }

    get leaderBoard(): { [key: string]: number } {
        return this._data['leader_board'];
    }

    set leaderBoard(leaderBoard: { [key: string]: number }) {
        this._data['leader_board'] = leaderBoard;
    }
}
