import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { Contact, Campaign, User, Opportunity, Status } from '.'

export interface StartCampaign {
  type: 'add_to_campaign'
  campaign_id: string
}

export interface StopCampaign {
  type: 'remove_from_campaign'
  campaign_id: string
}

export interface RemoveAssignedUser {
  type: 'remove_assigned_user';
}

export interface UpdateAppointmentStatus {
  type: 'update_appointment_status';
  status_type: string
}

export interface Set {
  type: 'set_value'
  model: 'contact' | 'lead'
  field: string
  value: any
}

export interface CreateOpportunity {
  type: 'create_opportunity'
  pipeline_id: string
  pipeline_stage_id: string
  monetary_value: number
  allow_backward: boolean
  opportunity_status: Status
  opportunity_source: string
  allow_multiple: boolean
}

export interface RemoveOpportunity {
  type: 'remove_opportunity';
  pipeline_id: string;
}

export interface ExecuteWebhook {
  type: 'execute_webhook'
  webhook_url: string
  webhook_request_type: 'GET' | 'POST'
}

export interface SendNotification {
  type: 'send_notification',
  title: string,
  body: string,
  userType: string,
  selectedUser: string
}
export interface DNDContact {
  type: 'dnd_contact'
  dnd_contact: string
}

export interface AddNotes {
  type: 'add_notes'
  html: string
}

export interface GoogleAdwords {
  type: 'google_adword',
  conversion_type: string,
  conversion_name: string
}

export interface GoogleAnalytics {
  type: 'google_analytics',
  tracking_id: string,
  event_action: string,
  event_category: string,
  event_value: string,
  event_label: string
}

export interface AssignUser {
  type: 'assign_user',
  user_list: string[],
  only_unassigned_contact: boolean,
  traffic_split: string,
  traffic_weightage: { [key: string]: string },
  total_index: number,
  traffic_index: { id: string, indexes: number[] }[]
}

export interface StripeOneTimeCharge {
  type: 'stripe_one_time_charge',
  stripe_customer_id: string,
  description: string,
  amount: string,
  currency: string
}

export interface MembershipGrantOffer {
  type: 'membership_grant_offer',
  offer_id: string
}

export interface MembershipRevokeOffer {
  type: 'membership_revoke_offer',
  offer_id: string
}

export interface UpdateContactField {
  type: 'update_contact_field',
  fields: { [key: string]: any }
}

export interface AddToBotConversation {
  type: 'add_to_bot_conversation',
  agent: string,
  projectId: string,
  option: string,
  message: string,
  assignedTo: string,
  calendarId: string,
  welcomeTemplate: string,
  thankyouTemplate: string
}

export type Action =
  | StartCampaign
  | RemoveAssignedUser
  | Set
  | CreateOpportunity
  | RemoveOpportunity
  | StopCampaign
  | ExecuteWebhook
  | DNDContact
  | AddNotes
  | SendNotification
  | AssignUser
  | StripeOneTimeCharge
  | MembershipGrantOffer
  | MembershipRevokeOffer
  | AddToBotConversation
  | UpdateAppointmentStatus

export enum TriggerType {
  FACEBOOK_LEAD_ADD = 'facebook_lead_add',
  CUSTOMER_REPLY = 'customer_reply',
  CUSTOMER_BOOKED_APPOINTMENT = 'customer_appointment',
  CUSTOMER_BOOKED_APPOINTMENT_V3 = 'customer_appointment_v3',
  PIPELINE_STAGE_UPDATED = 'pipeline_stage_updated',
  CAMPAIGN_ADDED = 'added_to_campaign',
  OPPORTUNITY_DECAY = 'opportunity_decay',
  APPOINTMENT = 'appointment',
  APPOINTMENT_V3 = 'appointment_v3',
  TRIGGER_LINK = 'trigger_link',
  FORM_SUBMISSION = 'form_submission',
  CONTACT_TAG = 'contact_tag',
  OPPORTUNITY_STATUS_CHANGED = 'opportunity_status_changed',
  FACEBOOK_LEAD_GEN = 'facebook_lead_gen',
  BIRTHDAY_REMINDER = 'birthday_reminder',
  CUSTOM_DATE_REMINDER = 'custom_date_reminder',
  MAILGUN_EMAIL_EVENT = 'mailgun_email_event',
  DND_CONTACT = 'dnd_contact',
  TASK_DUE_DATE_REMINDER = 'task_due_date_reminder',
  TWO_STEP_ORDER_FORM = 'two_step_form_submission',
  CALL_STATUS = 'call_status',
  SURVEY_SUBMISSION = 'survey_submission',
  PRODUCT_ACCESS_GRANTED = 'product_access_granted',
  PRODUCT_ACCESS_REMOVED = 'product_access_removed',
  USER_LOG_IN = 'user_log_in',
  PRODUCT_COMPLETED = 'product_completed',
  PRODUCT_PROGRESS_PERCENTAGE = 'product_progress_percentage',
  MEMBERSHIP_CONTACT_CREATED = 'membership_contact_created',
  TASK_ADDED = 'task_added',
  VALIDATION_ERROR = 'validation_error',
  OFFER_ACEESS_GRANTED = 'offer_access_granted',
  OFFER_ACEESS_REMOVED = 'offer_access_removed'
}
type _TriggerType =
  | 'facebook_lead_add'
  | 'customer_reply'
  | 'customer_appointment'
  | 'customer_appointment_v3'
  | 'pipeline_stage_updated'
  | 'added_to_campaign'
  | 'appointment'
  | 'appointment_v3'
  | 'trigger_link'
  | 'form_submission'
  | 'opportunity_decay'
  | 'contact_tag'
  | 'opportunity_status_changed'
  | 'facebook_lead_gen'
  | 'survey_submission'
  | 'birthday_reminder'
  | 'custom_date_reminder'
  | 'mailgun_email_event'
  | 'dnd_contact'
  | 'task_due_date_reminder'
  | 'two_step_form_submission'
  | 'call_status'
  | 'product_access_granted'
  | 'product_access_removed'
  | 'user_log_in'
  | 'product_completed'
  | 'product_progress_percentage'
  | 'membership_contact_created'
  | 'validation_error'

export interface Condition {
  id?: string
  field: string
  operator: string
  value: any
  title: string
}

export interface Params {
  contact?: Contact
  campaign?: Campaign
  user?: User
  opportunity?: Opportunity
}

export default class Trigger {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('triggers')
  }

  public async save() {
    this.dateUpdated = moment()
    await this._ref.set(this.data)
  }

  public static getById(id): Promise<Trigger> {
    return new Promise((resolve, reject) => {
      Trigger.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new Trigger(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getActiveByLocationAndType(
    locationId: string,
    type: _TriggerType
  ): Promise<Trigger[]> {
    return new Promise((resolve, reject) => {
      Trigger.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('active', '==', true)
        .where('type', '==', type)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Trigger(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByWorkflowId(workflowId: string, locationId: string): Promise<Trigger[]> {
    return new Promise((resolve, reject) => {
      return Trigger.collectionRef()
        .where('workflow_id', '==', workflowId)
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Trigger(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByLocationIdRealtime(
    locationId: string,
    belongsTo: string = 'default'
  ) {
    return Trigger.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .where('belongs_to', '==', belongsTo)
  }

  public static getByLocationId(locationId: string): Promise<Trigger[]> {
    return new Promise((resolve, reject) => {
      Trigger.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Trigger(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Trigger | undefined> {
    const snapshot = await Trigger.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Trigger(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = Trigger.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.active = false
      this.dateAdded = moment()
      this.belongsTo = 'default'
      this.conditions = []
      this.actions = []
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get ref() {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get belongsTo(): string {
    return this._data.belongs_to
  }

  set belongsTo(belongsTo: string) {
    this._data.belongs_to = belongsTo
  }

  get folderId(): string {
    return this._data.folder_id
  }

  set folderId(folder_id: string) {
    this._data.folder_id = folder_id
  }

  get workflowId(): string {
    return this._data.workflow_id
  }

  set workflowId(workflowId: string) {
    this._data.workflow_id = workflowId
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added
      ? moment(this._data.date_added.toMillis())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data.date_updated
      ? moment(this._data.date_updated.toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get active(): boolean {
    return this._data.active
  }

  set active(active: boolean) {
    this._data.active = active
  }

  get loopIdentified(): moment.Moment {
    if (this._data.loop_identified) {
      return moment(this._data.loop_identified.toMillis())
    } else {
      return undefined
    }
  }

  set loopIdentified(loopIdentified: moment.Moment) {
    this._data.loop_identified = firebase.firestore.Timestamp.fromMillis(
      loopIdentified.valueOf()
    )
  }

  get title(): string {
    return this._data.title
  }

  set title(title: string) {
    this._data.title = title
  }

  get description(): string {
    return this._data.description
  }

  set description(description: string) {
    this._data.description = description
  }

  get type(): _TriggerType {
    return this._data.type
  }

  set type(type: _TriggerType) {
    this._data.type = type
  }

  get conditions(): Condition[] {
    return this._data.conditions
  }

  set conditions(conditions: Condition[]) {
    this._data.conditions = conditions
  }

  get actions(): Action[] {
    return this._data.actions
  }

  set actions(actions: Action[]) {
    this._data.actions = actions
  }

  get zapId(): string {
    return this._data.zap_id
  }

  set zapId(zapId: string) {
    this._data.zap_id = zapId
  }

  get matchYear(): boolean {
    return this._data.match_year || false;
  }

  set matchYear(matchYear: boolean) {
    this._data.match_year = matchYear;
  }

  public static async createOpportunityOnResponse(
    locationId: string,
    campaignId: string,
    pipelineId: string,
    pipelineStageId: string,
    monetaryValue: number
  ) {
    const trigger = new Trigger()
    trigger.locationId = locationId
    trigger.type = 'customer_reply'
    trigger.active = true
    trigger.conditions = [
      {
        field: 'campaign.id',
        operator: '==',
        value: campaignId || ''
      }
    ]
    trigger.actions = [
      {
        type: 'create_opportunity',
        pipeline_id: pipelineId || '',
        pipeline_stage_id: pipelineStageId || '',
        monetary_value: monetaryValue || ''
      }
    ]
    await trigger.save()
    return trigger
  }

  public async delete() {
    this.deleted = true
    await this.save()
  }

  public async toggleActive() {
    this.active = !this.active
    await this.save()
  }
}
