import firebase from 'firebase/app'
import moment from 'moment-timezone'

export interface FunnelElement {
  id: string
  // Add more props
}

export interface meta {
  id: string
}

export default class FunnelSection {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('funnel_sections')
  }

  public static getById(id: string): Promise<FunnelSection> {
    return new Promise(async (resolve, reject) => {
      try {
        await FunnelSection.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new FunnelSection(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getByPageId(pageId: string, locationId: string): Promise<FunnelSection[]> {
    return new Promise(async (resolve, reject) => {
      try {
        await FunnelSection.collectionRef()
          .where('page_id', '==', pageId)
          .where('location_id', '==', locationId)
          .where('deleted', '==', false)
          .get()
          .then(snapshot => {
            if (snapshot.empty) resolve(null)
            resolve(snapshot.docs.map(s => new FunnelSection(s)))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getByFunnelId(funnelId: string, locationId: string): Promise<FunnelSection[]> {
    return new Promise(async (resolve, reject) => {
      try {
        await FunnelSection.collectionRef()
          .where('funnel_id', '==', funnelId)
          .where('deleted', '==', false)
          .where('location_id', '==', locationId)
          .get()
          .then(snapshot => {
            if (snapshot.empty) {
              resolve([])
            } else {
              resolve(snapshot.docs.map(s => new FunnelSection(s)))
            }
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = FunnelSection.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this.dateAdded = moment().utc()
      this.deleted = false
    }
  }

  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public cloneData(funnelSection: FunnelSection) {
    this._data = funnelSection.data
  }

  get id(): string {
    return this._id
  }

  get data() {
    return this._data
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get sequence(): number {
    return this._data.sequence
  }

  set sequence(sequence: number) {
    this._data.sequence = sequence
  }

  get pageId(): string {
    return this._data.page_id
  }

  set pageId(pageId: string) {
    this._data.page_id = pageId
  }

  get metaData(): meta {
    return this._data.meta_data
  }

  set metaData(metaData: meta) {
    this._data.meta_data = metaData
  }

  get elements(): any {
    return this._data.elements
  }

  set elements(elements: any) {
    this._data.elements = elements
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }
  public delete() {
    return FunnelSection.collectionRef()
      .doc(this.id)
      .delete()
  }
  get funnelId() {
    return this._data.funnel_id
  }

  set funnelId(funnelId) {
    this._data.funnel_id = funnelId
  }
}
