// import 'dart:async';

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:decimal/decimal.dart';
// import 'package:servicemax_flutter/util/helper.dart';

// class Payment {
//   static CollectionReference collectionRef =firebase.firestore.instance.collection("payments");

//   static const int STATUS_UNATTEMPTED = 0;
//   static const int STATUS_SUCCESS = 1;
//   static const int STATUS_FAILED = 2; // This is for cases where payment was later denied for fraud or other reasons
//   static const int STATUS_REFUND = 3;
//   static const int STATUS_CHARGEBACK = 4;
//   static const int STATUS_ERROR = 5; // This is For cases where payment was not authorized because of card errors
//   static const int STATUS_DELETED = 6;

//   static const int TYPE_CARD = 1;
//   static const int TYPE_CASH = 2;

//   String _id;
//   Map<String, dynamic> _data;

//   Payment.cash(String companyId, String contactId, String currency, String jobId, String amount) {
//     this._data = new Map<String, dynamic>();
//     this.companyId = companyId;
//     this.dateAdded = new DateTime.now();
//     this.dateUpdated = new DateTime.now();
//     this.deleted = false;
//     this.method = TYPE_CASH;
//     this.status = STATUS_SUCCESS;
//     this.currency = currency;
//     this.jobId = jobId;
//     this.amount = amount;
//     this.gross = amount;
//     this.contactId = contactId;
//     this.readableId = generateEasyUniqueString();
//   }

//   Payment.fromSnapshot(DocumentSnapshot snap) {
//     this._id = snap.documentID;
//     this._data = snap.data;
//   }

//   String get id => _id;

//   Map<String, dynamic> get data => _data;

//   String get companyId => _data["company_id"];

//   set companyId(String companyId) {
//     _data["company_id"] = companyId;
//   }

//   DateTime get dateAdded => _data["date_added"];

//   set dateAdded(DateTime dateAdded) {
//     _data["date_added"] = dateAdded;
//   }

//   DateTime get dateUpdated => _data["date_updated"];

//   set dateUpdated(DateTime dateUpdated) {
//     _data["date_updated"] = dateUpdated;
//   }

//   bool get deleted => _data["deleted"];

//   set deleted(bool deleted) {
//     _data["deleted"] = deleted;
//   }

//   String get readableId => _data["readable_id"];

//   set readableId(String readableId) {
//     _data["readable_id"] = readableId;
//   }

//   String get jobId => _data["job_id"];

//   set jobId(String jobId) {
//     _data["job_id"] = jobId;
//   }

//   String get contactId => _data["contact_id"];

//   set contactId(String contactId) {
//     _data["contact_id"] = contactId;
//   }

//   int get method => _data["method"];

//   set method(int method) {
//     _data["method"] = method;
//   }

//   String get methodId => _data["method_id"];

//   set methodId(String methodId) {
//     _data["method_id"] = methodId;
//   }

//   String get amount => _data["amount"] ?? "0.0";

//   set amount(String amount) {
//     _data["amount"] = amount;
//   }

//   String get currency => _data["currency"];

//   set currency(String currency) {
//     _data["currency"] = currency;
//   }

//   int get status => _data["status"];

//   set status(int status) {
//     _data["status"] = status;
//   }

//   String get referenceId => _data["reference_id"];

//   set referenceId(String referenceId) {
//     _data["reference_id"] = referenceId;
//   }

//   String get gross => _data["gross"];

//   set gross(String gross) {
//     _data["gross"] = gross;
//   }

//   String get providerId => _data["provider_id"];

//   set providerId(String providerId) {
//     _data["provider_id"] = providerId;
//   }

//   String get identification => _data["identification"];

//   set identification(String identification) {
//     _data["identification"] = identification;
//   }

//   save() async {
//     this.dateUpdated = new DateTime.now();
//     if (_id != null) {
//       collectionRef.document(_id).updateData(_data);
//       print("Update: " + _id + " " + _data.toString());
//     } else {
//       DocumentReference ref = collectionRef.document();
//       ref.setData(_data);
//       _id = ref.documentID;
//       print("Add: " + _id + " " + _data.toString());
//     }
//   }

//   static Future<Payment> getById(String id) async {
//     DocumentSnapshot snap = await collectionRef.document(id).snapshots.first;
//     return new Payment.fromSnapshot(snap);
//   }

//   static Stream<DocumentSnapshot> getStreamById(String id) {
//     return collectionRef.document(id).snapshots;
//   }

//   delete() async {
//     await collectionRef.document(id).updateData({
//       "deleted": true
//     });
//   }

//   static Stream<QuerySnapshot> fetchAllPaymentsForJob(String companyId, String jobId) {
//     return collectionRef
//         .where("deleted", isEqualTo: false)
//         .where("job_id", isEqualTo: jobId)
//         .where("company_id", isEqualTo: companyId)
//         .orderBy("date_added", descending: true)
//         .snapshots;
//   }

//   static Stream<QuerySnapshot> fetchAllSuccessfulPaymentsForJob(String companyId, String jobId) {
//     return collectionRef
//         .where("deleted", isEqualTo: false)
//         .where("job_id", isEqualTo: jobId)
//         .where("company_id", isEqualTo: companyId)
//         .where("status", isEqualTo: Payment.STATUS_SUCCESS)
//         .snapshots;
//   }

//   static String getTotal(List<Payment> payments) {
//     double total = 0.0;
//     payments.forEach((Payment payment) {
//       total += double.parse(payment.amount);
//     });
//     return total.toStringAsFixed(2);
//   }
// }
