import firebase from 'firebase/app';
import moment from 'moment-timezone';

export enum Action {
	SMS = 'sms',
	EMAIL = 'email',
	CALL = 'call',
	VOICEMAIL = 'voicemail',
}

export enum ResponseType {
	CLICK_THROUGH = 'click',
	REPLY = 'reply',
	READ = 'read',
	DELIVERED = 'delivered',
}

export default class CampaignActivity {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('campaign_activities');
	}

	public async save() {
		await this._ref.set(this._data);
	}

	public static getById(id): Promise<CampaignActivity> {
		return new Promise((resolve, reject) => {
			CampaignActivity.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new CampaignActivity(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByContactId(campaignId: string, contactId: string, locationId: string): Promise<CampaignActivity[]> {
		return new Promise((resolve, reject) => {
			CampaignActivity.collectionRef()
				.where('contact_id', '==', contactId)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignActivity(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByCampaignId(campaignId: string, contactId: string, locationId: string): Promise<CampaignActivity[]> {
		return new Promise((resolve, reject) => {
			CampaignActivity.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignActivity(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByContactIdAndCampaignId(campaignId: string, contactId: string, locationId: string): Promise<CampaignActivity[]> {
		return new Promise((resolve, reject) => {
			CampaignActivity.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('contact_id', '==', contactId)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignActivity(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static async log(
		locationId: string,
		campaignId: string,
		contactId: string,
		message: string,
		extras?: { campaignActionId: string; campaignVersionId: string; action: Action }
	) {
		const activity = new CampaignActivity();
		activity.locationId = locationId;
		activity.campaignId = campaignId;
		activity.contactId = contactId;
		activity.message = message;
		if (extras && extras.action) activity.action = extras.action;
		if (extras && extras.campaignActionId) activity.campaignActionId = extras.campaignActionId;
		if (extras && extras.campaignVersionId) activity.campaignVersionId = extras.campaignVersionId;
		await activity.save();
		return activity;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = CampaignActivity.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get ref(): firebase.firestore.DocumentReference {
		return this._ref;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get contactId(): string {
		return this._data.contact_id;
	}

	set contactId(contactId: string) {
		this._data.contact_id = contactId;
	}

	get campaignId(): string {
		return this._data.campaign_id;
	}

	set campaignId(campaignId: string) {
		this._data.campaign_id = campaignId;
	}

	get campaignActionId(): string {
		return this._data.campaign_action_id;
	}

	set campaignActionId(campaignActionId: string) {
		this._data.campaign_action_id = campaignActionId;
	}

	get campaignVersionId(): string {
		return this._data.campaign_version_id;
	}

	set campaignVersionId(campaignVersionId: string) {
		this._data.campaign_version_id = campaignVersionId;
	}

	get dateAdded() {
		return moment(this._data.date_added);
	}

	set dateAdded(dateAdded) {
		this._data.date_added = dateAdded.toDate();
	}

	get dateUpdated() {
		return moment(this._data.date_updated);
	}

	set dateUpdated(dateUpdated) {
		this._data.date_updated = dateUpdated.toDate();
	}

	get action(): Action {
		return this._data.action;
	}

	set action(action: Action) {
		this._data.action = action;
	}

	get message(): string {
		return this._data.message;
	}

	set message(message: string) {
		this._data.message = message;
	}

	get response(): ResponseType {
		return this._data.message;
	}

	set response(response: ResponseType) {
		this._data.response = response;
	}
}
