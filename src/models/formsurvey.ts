import firebase from 'firebase/app';
import moment from 'moment-timezone';

export default class Formsurvey {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('surveys');
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._data = snapshot.data() || {};
			this._ref = snapshot.ref;
		} else {
			this._ref = Formsurvey.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.deleted = false;
			this.dateAdded = moment().utc();
		}
	}

	public static getById(id: string): Promise<Formsurvey> {
		return new Promise((resolve, reject) => {
			Formsurvey.collectionRef()
				.doc(id)
				.get()
				.then((snapshot: firebase.firestore.DocumentSnapshot) => {
					if (snapshot.exists) resolve(new Formsurvey(snapshot));
					resolve();
				})
				.catch((err) => {
					reject(err);
				});
		});
	}

	public static getByLocationId(locationId: string): Promise<Formsurvey[]> {
		return new Promise((resolve, reject) => {
			Formsurvey.collectionRef()
				.where('location_id', '==', locationId)
				.where('deleted', '==', false)
				.get()
				.then((querySnapshot) => {
					resolve(querySnapshot.docs.map((d) => new Formsurvey(d)));
				});
		});
	}

	public static getQueryWithLocationId(locationId: string): firebase.firestore.Query {
		return Formsurvey.collectionRef()
			.where('deleted', '==', false)
			.where('location_id', '==', locationId);
	}

	public static getCountByLocationId(locationId: string): Promise<number> {
		return new Promise((resolve, reject) => {
			Formsurvey.collectionRef()
				.where('deleted', '==', false)
				.where('location_id', '==', locationId)
				.get()
				.then((snapshot) => {
					return resolve(snapshot.size);
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static async getByOriginId(
		locationId: string,
		originId: string
	): Promise<Formsurvey | undefined> {
		const snapshot = await Formsurvey.collectionRef()
			.where('location_id', '==', locationId)
			.where('origin_id', '==', originId)
			.where('deleted', '==', false)
			.get()
		if (!snapshot.empty) return new Formsurvey(snapshot.docs[0])
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get originId(): string {
		return this._data.origin_id
	}

	set originId(originId: string) {
		this._data.origin_id = originId
	}

	get deleted(): boolean {
		return this._data.deleted;
	}

	set deleted(deleted: boolean) {
		this._data.deleted = deleted;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get name(): string {
		return this._data.name;
	}

	set name(name: string) {
		this._data.name = name;
	}

	get formData(): any {
		return this._data.form_data;
	}

	set formData(formData: any) {
		this._data.form_data = formData;
	}

	public save() {
		const _self = this;
		return new Promise((resolve, reject) => {
			_self.dateUpdated = moment().utc();
			_self._ref.set(_self.data).then((_) => {
				resolve(_self);
			});
		});
	}
}
