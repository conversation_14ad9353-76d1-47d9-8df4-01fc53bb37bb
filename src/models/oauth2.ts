import firebase from 'firebase/app';
import 'firebase/database';

import * as moment from 'moment-timezone';
import * as lodash from 'lodash';
import { Calendar, LinkedCalendar } from '.';
export interface MindBody {
	studio_id?: string;
	activation_code?: string;
}

export interface Settings {
	mindbody: MindBody;
}

export default class OAuth2 {
	public static TYPE_QUICKBOOKS = 'quickbooksonline';
	public static TYPE_CLIO = 'clio';
	public static TYPE_DRCHRONO = 'drchrono';
	public static TYPE_TWITTER = 'twitter';
	public static TYPE_GOOGLE = 'google';
	public static TYPE_FACEBOOK = 'facebook';
	public static TYPE_HUBSPOT = 'hubspot';
	public static TYPE_LINKEDIN = 'linkedin';
	public static TYPE_STRIPE = 'stripe';
	public static TYPE_SIKKA = 'sikka';
	public static TYPE_PRACTICEPANTHER = 'practicepanther';
	public static TYPE_RINGCENTRAL = 'ringcentral';
	public static TYPE_MINDBODY = 'mindbody';
	public static TYPE_ZOOM = 'zoom';
  public static TYPE_OUTLOOK = 'outlook';
  public static TYPE_SHOPIFY = 'shopify';



	public static collectionRef():firebase.firestore.CollectionReference {
		return firebase.firestore().collection('oauth2');
	}

	public async save(): Promise<OAuth2> {
		this.dateUpdated = moment();
		await this.ref.set(this.data);
		return this;
	}

	static getById(id): Promise<OAuth2> {
		return new Promise((resolve, reject) => {
			OAuth2.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new OAuth2(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByLocationIdAndType(locationId: string, type: string) {
		return OAuth2.collectionRef()
			.where('locations.' + locationId, '==', true)
			.where('type', '==', type)
			.limit(1);
  }

  public static getAllByLocationIdAndType(locationId: string, type: string) {
		return OAuth2.collectionRef()
			.where('locations.' + locationId, '==', true)
			.where('type', '==', type);
	}

	public static getByLocationIdAndTypeOnce(locationId: string, type: string): Promise<OAuth2> {
		return new Promise((resolve, reject) => {
      OAuth2.getByLocationIdAndType(locationId, type)
        .get()
        .then((snapshot:firebase.firestore.QuerySnapshot) => {
          if (snapshot.size !== 1) {
            resolve();
          }
          resolve(new OAuth2(snapshot.docs[0]));
        })
        .catch((err) => {
          reject(err);
        });
		});
	}

	private _id: string;
	private _data:firebase.firestore.DocumentData;
	private _ref:firebase.firestore.DocumentReference;

	constructor(snapshot?:firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data() || {};
		} else {
			this._ref = OAuth2.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
		}
	}

	get id(): string {
		return this._id;
	}

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined);
  }

	get ref():firebase.firestore.DocumentReference {
		return this._ref;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added =firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated =firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get altId(): string {
		return this._data.alt_id;
	}

	set altId(alt_id: string) {
		this._data.alt_id = alt_id;
  }

  // get isPrimary(): boolean {
  //   return this._data.is_primary;
  // }

  // set isPrimary(isPrimary: boolean) {
  //   this._data.is_primary = isPrimary;
  // }

	get locations(): {} {
		if (!this._data.locations) {
			this._data.locations = {};
		}
		return this._data.locations;
	}

	set locations(locations: {}) {
		this._data.locations = locations;
	}

	get type(): string {
		return this._data.type;
	}

	set type(type: string) {
		this._data.type = type;
	}

  get addedBy(): string {
    return this._data.added_by;
  }

  set addedBy(added_by: string) {
    this._data.added_by = added_by;
  }

	get expires(): moment.Moment {
		return moment(this._data.expires);
	}

	set expires(expires: moment.Moment) {
		this._data.expires = expires.toDate();
	}

	get isExpired(): boolean {
		return this.expires ? moment().isAfter(this.expires) : false;
	}

	get accessToken(): string {
		return this._data.access_token;
	}

	set accessToken(accessToken: string) {
		this._data.access_token = accessToken;
	}

	get tokenType(): string {
		return this._data.token_type;
	}

	set tokenType(tokenType: string) {
		this._data.token_type = tokenType;
	}

	get ownerId(): string {
		return this._data['owner_id']
	}

	set ownerId(ownerId: string) {
		this._data['owner_id'] = ownerId
  }

  get shopifyStore(): string {
    return this._data['shopify_store']
  }

  set shopifyStore(shopifyStore: string) {
    this._data['shopify_store']= shopifyStore
  }

  get apiKey(): string {
    return this._data['api_key'];
  }

  set apiKey(apiKey: string) {
    this._data['api_key'] = apiKey;``
  }

  get apiPassword(): string {
    return this._data['api_password'];
  }

  set apiPassword(apiPassword: string) {
    this._data['api_password'] = apiPassword;
  }

  get sharedSecret(): string {
    return this._data['shared_secret'];
  }

  set sharedSecret(sharedSecret: string) {
    this._data['shared_secret'] = sharedSecret;
  }


	get users(): { [key: string]: boolean } {
		if (!this._data.users) this._data.users = {};
		return this._data.users;
	}

	set users(users: { [key: string]: boolean }) {
		this._data.users = users;
	}

	public removeLocation(location_id) {
		let existing = lodash.has(this.locations, location_id);
		if (existing) {
			delete this.locations[location_id];
		}
	}

	public addLocation(location_id) {
		this.locations[location_id] = true;
	}

	public removeUser(userId: string) {
		let existing = lodash.has(this.users, userId);
		if (existing) {
			delete this.users[userId];
		}
	}

	public addUser(userId: string) {
		this.users[userId] = true;
	}

	get settings(): Settings {
		if (!this._data.settings) this._data.settings = {};
		if (!this._data.settings.mindbody) this._data.settings.mindbody = {};

		return this._data.settings;
	}

	set settings(settings: Settings) {
		this._data.settings = settings;
	}

	private getGoogleConnectionError(google) {
		return google && google.error && ['Google connection is expired'].includes(google.error_message);
	}

	public async connectionError(locationId) {
		const calendars = await Calendar.getByOAuthId(this.id);
		const errorCalendar = calendars && calendars.find(key => key.locationId === locationId && key.linkedCalendars && this.getGoogleConnectionError(key.linkedCalendars.google));
		if (errorCalendar) {
			return {
				error: errorCalendar.linkedCalendars.google.error,
				error_message: errorCalendar.linkedCalendars.google.error_message
			}
		}

		const linkedCalendars = await LinkedCalendar.getByOAuthId(this.id);
		const errorLinkedCalendar = linkedCalendars && linkedCalendars.find(key => this.getGoogleConnectionError(key.google));
		if (errorLinkedCalendar) {
			return {
				error: errorLinkedCalendar.google.error,
				error_message: errorLinkedCalendar.google.error_message
			}
		}
	}
}
