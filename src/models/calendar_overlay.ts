import firebase from 'firebase/app';
import moment from 'moment-timezone';

export enum EventStatus {
    AVAILABLE = 'available',
    UNAVAILABLE = 'unavailable',
    BOOKED = 'booked',
}

export interface ClioEvent {
    id: string;
    data: { [key: string]: any };
}

export interface CalendarEvent {
    id: string;
    appointment_id?: string;
    start_time: String;
    end_time: String;
    status: EventStatus;
    clio?: ClioEvent;
}

export default class CalendarOverlay {
    public static collectionRef(): firebase.firestore.CollectionReference {
        return firebase.firestore().collection('calendar_overlays');
    }

    public static fetch(locationId: string, calendarId: string, startTime: moment.Moment): firebase.firestore.Query {
        return CalendarOverlay.collectionRef()
            .where('start_date', '==', startTime.toDate())
            .where('calendar_id', '==', calendarId)
            .where('location_id', '==', locationId);
    }

    public static async fetchFrozen(
        locationId: string,
        calendarId: string,
        startTime: moment.Moment
    ): Promise<CalendarOverlay | undefined> {
        const snapshot = await CalendarOverlay.collectionRef()
            .where('start_date', '==', startTime.toDate())
            .where('calendar_id', '==', calendarId)
            .where('location_id', '==', locationId)
            .get();
        if (!snapshot.empty) return new CalendarOverlay(snapshot.docs[0]);
    }

    public static create(locationId: string, startDate: moment.Moment, calendarId: string): CalendarOverlay {
        const overlay = new CalendarOverlay();
        overlay.locationId = locationId;
        overlay.startDate = startDate;
        overlay.calendarId = calendarId;
        return overlay;
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
        } else {
            this._ref = CalendarOverlay.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.dateAdded = moment();
        }
    }

    get id(): string | undefined {
        return this._id;
    }

    get data(): firebase.firestore.DocumentData {
        return this._data;
    }

    get ref() {
        return this._ref;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = dateUpdated.toDate();
    }

    get dirty(): boolean {
        return this._data.dirty;
    }

    set dirty(dirty: boolean) {
        this._data.dirty = dirty;
    }

    get startDate(): moment.Moment {
        return moment(this._data.start_date);
    }

    set startDate(startDate: moment.Moment) {
        this._data.start_date = startDate.toDate();
    }

    get calendarId(): string {
        return this._data.calendar_id;
    }

    set calendarId(calendarId: string) {
        this._data.calendar_id = calendarId;
    }

    get events(): CalendarEvent[] {
        if (!this._data.events) this._data.events = [];
        return this._data.events;
    }

    public async save() {
        this.dateUpdated = moment();
        await this._ref.set(this._data);
    }
}
