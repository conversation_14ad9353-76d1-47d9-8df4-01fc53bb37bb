import firebase from 'firebase/app';
import * as moment from 'moment-timezone';
import * as lodash from 'lodash';

export interface ServicePlan {
  name: string;
  wholesale_setup_price: number;
  wholesale_recur_price: number;
  msrp_setup_price: number;
  msrp_recur_price: number;
  highlights: string[];
  trial_days: number;
  min_duration_months: number;
  deleted: boolean;
  stripe_plan_id: string;
  stripe_sku_id: string;
}
export interface ServiceFAQ {
  que: string;
  ans: string;
  deleted: boolean;
}
export interface ServiceFiles {
  file_name: string;
  file_url: string;
  deleted: boolean;
}
export default class AgencyService {
  public static collectionRef() {
    return firebase.firestore().collection('agency_service');
  }

  public static async getById(id: string): Promise<AgencyService | undefined> {
    const snapshot = await AgencyService.collectionRef()
      .doc(id)
      .get();
    if (!snapshot.exists || snapshot.data().deleted) return undefined;
    return new AgencyService(snapshot);
  }

  public static async getAllServices(): Promise<AgencyService[]> {
    const snapshot = await AgencyService.collectionRef().where('deleted', '==', false).get();
    return snapshot.docs.map((d) => new AgencyService(d));
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = AgencyService.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment();
    }
  }

  get id(): string {
    return this._id;
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get iconUrl(): string {
    return this._data.icon_url;
  }

  set iconUrl(iconUrl: string) {
    this._data.icon_url = iconUrl;
  }

  get iconBgcolorName(): string {
    return this._data.icon_bgcolor_name;
  }

  set iconBgcolorName(iconBgcolorName: string) {
    this._data.icon_bgcolor_name = iconBgcolorName;
  }

  get videoUrl(): string {
    return this._data.video_url;
  }

  set videoUrl(videoUrl: string) {
    this._data.video_url = videoUrl;
  }

  get overview(): string {
    return this._data.overview;
  }

  set overview(overview: string) {
    this._data.overview = overview;
  }

  get description(): string {
    return this._data.description;
  }

  set description(description: string) {
    this._data.description = description;
  }

  get highlights(): string[] {
    return this._data.highlights;
  }

  set highlights(highlights: string[]) {
    this._data.highlights = highlights;
  }

  get faqs(): ServiceFAQ[] {
    return this._data.faqs;
  }

  set faqs(faqs: ServiceFAQ[]) {
    this._data.faqs = faqs;
  }

  get files(): ServiceFiles[] {
    return this._data.files;
  }

  set files(files: ServiceFiles[]) {
    this._data.files = files;
  }

  get plans(): ServicePlan[] {
    return this._data.plans;
  }

  set plans(plans: ServicePlan[]) {
    this._data.plans = plans;
  }

  get betaOnly(): boolean {
    return this._data.beta_only;
  }

  set betaOnly(betaOnly: boolean) {
    this._data.beta_only = betaOnly;
  }

  get agencyLevel(): boolean {
    return this._data.agency_level;
  }

  set agencyLevel(agencyLevel: boolean) {
    this._data.agency_level = agencyLevel;
  }

  get agencyAddonType(): string { // Possible values are: 'hipaa' , 'premium', 'jumpstart'
    return this._data.agency_addon_type;
  }

  set agencyAddonType(agencyAddonType: string) {
    this._data.agency_addon_type = agencyAddonType;
  }


  public async save(): Promise<AgencyService> {
    this.dateUpdated = moment();
    await this._ref.set(this.data);
    return this;
  }
}
