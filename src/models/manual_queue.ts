import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import * as lodash from 'lodash'

export default class ManualQueue {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('manual_queue')
  }

  public async save() {
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }

  public static getById(id): Promise<ManualQueue> {
    return new Promise((resolve, reject) => {
      ManualQueue.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new ManualQueue(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static async getbyUserIdAndLocationId(filters: {
    [key: string]: any
  }): Promise<ManualQueue[]> {
    const startAt = filters.limit * (filters.page - 1)
    return new Promise(async (resolve, reject) => {
      try {
        let query = ManualQueue.collectionRef()
          .where('location_id', '==', filters.locationId)
          .where('completed', '==', false)
          .where('in_progress', '==', false)
          .where('deleted', '==', false)
        if (filters.assignee && filters.assignee !== 'all') {
          query = query.where('user_id', '==', filters.assignee)
        }
        if (filters.campaignId && filters.campaignId !== 'all') {
          query = query.where('campaign_id', '==', filters.campaignId)
        }
        await query
          .orderBy('date_added')
          .startAt(startAt)
          .limit(filters.limit)
          .get()
          .then(snapshot => {
            if (snapshot.size == 0) resolve([])
            resolve(snapshot.docs.map(d => new ManualQueue(d)))
          })
          .catch(err => {
            console.error(JSON.stringify(err))
            reject(err.message)
          })
      } catch (ex) {
        console.error(ex)
        reject(ex)
      }
    })
  }

  public static async getByUserIdAndLocationIdRealtime(filters: {
    [key: string]: any
  }): Promise<firebase.firestore.Query> {
    let query = ManualQueue.collectionRef()
      .where('location_id', '==', filters.locationId)
      .where('completed', '==', false)
      .where('in_progress', '==', false)
      .where('deleted', '==', false)
    if (filters.assignee && filters.assignee !== 'all') {
      query = query.where('user_id', '==', filters.assignee)
    }
    if (filters.campaign && filters.campaign !== 'all') {
      query = query.where('campaign_id', '==', filters.campaign)
    }
    return query
  }

  public static async getbyLocationIdAndType(filters: {
    [key: string]: any
  }): Promise<ManualQueue[]> {
    const startAt = filters.limit * (filters.page - 1)
    return new Promise(async (resolve, reject) => {
      try {
        await ManualQueue.collectionRef()
          .where('location_id', '==', filters.locationId)
          .where('completed', '==', false)
          .where('in_progress', '==', false)
          .where('deleted', '==', false)
          .where('message_type', '==', filters.messageType)
          .orderBy('date_added')
          .startAt(startAt)
          .limit(filters.limit)
          .get()
          .then(snapshot => {
            if (snapshot.size == 0) resolve([])
            resolve(snapshot.docs.map(d => new ManualQueue(d)))
          })
          .catch(err => {
            console.error(JSON.stringify(err))
            reject(err.message)
          })
      } catch (ex) {
        console.error(ex)
        reject(ex)
      }
    })
  }

  public static async getbyLocationIdAndTypeRealtime(filters: {
    [key: string]: any
  }): Promise<firebase.firestore.Query> {
    let query = ManualQueue.collectionRef()
      .where('location_id', '==', filters.locationId)
      .where('completed', '==', false)
      .where('in_progress', '==', false)
      .where('deleted', '==', false)
      .where('message_type', '==', filters.messageType)
    if (filters.assignedTo) query = query.where('user_id', '==', filters.assignedTo);
    query = query.orderBy('date_added');
    return query;
  }

  public static async deleteRepliedActions(filters: { [key: string]: any }) {
    ManualQueue.collectionRef()
      .where('campaign_id', '==', filters.campaignId)
      .where('location_id', '==', filters.locationId)
      .where('contact_id', '==', filters.contactId)
      .where('deleted', '==', false)
      .get()
      .then(snapshot => {
        if (snapshot.size > 0) {
          snapshot.docs.map(async d => {
            await d.ref.update({ deleted: true })
          })
        }
      })
      .catch(err => {
        console.error(JSON.stringify(err))
      })
  }

  public static async removeActionsToContact(
    contactId: string,
    locationId: string
  ) {
    ManualQueue.collectionRef()
      .where('contact_id', '==', contactId)
      .where('in_progress', '==', false)
      .where('location_id', '==', locationId)
      .where('completed', '==', false)
      .where('deleted', '==', false)
      .get()
      .then(snapshot => {
        if (snapshot.size > 0) {
          snapshot.docs.map(async d => {
            await d.ref.update({ deleted: true })
          })
        }
      })
      .catch(err => {
        console.error(JSON.stringify(err))
      })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
      this._snapshot = snapshot
    } else {
      this._ref = ManualQueue.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment()
      this.deleted = false
      this.inProgress = false
      this.completed = false
    }
  }

  get id(): string {
    return this._id
  }

  get ref() {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get userId(): string {
    return this._data.user_id
  }

  set userId(userId: string) {
    this._data.user_id = userId
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get messageType(): number {
    return this._data.message_type
  }

  set messageType(messageType: number) {
    this._data.message_type = messageType
  }

  get extras(): Object {
    return this._data.extras
  }

  set extras(extras: Object) {
    this._data.extras = extras
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added
      ? moment(this._data.date_added.toMillis())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data.date_updated
      ? moment(this._data.date_updated.toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get completed(): boolean {
    return this._data.completed
  }

  set completed(completed: boolean) {
    this._data.completed = completed
  }

  get skipped(): boolean {
    return this._data.skipped
  }

  set skipped(skipped: boolean) {
    this._data.skipped = skipped
  }

  get inProgress(): boolean {
    return this._data.in_progress
  }

  set inProgress(inProgress: boolean) {
    this._data.in_progress = inProgress
  }

  get campaignId(): string {
    return this._data.campaign_id
  }

  set campaignId(campaignId: string) {
    this._data.campaign_id = campaignId
  }

  get campaignStatusId(): string {
    return this._data.campaign_status_id
  }

  set campaignStatusId(campaignStatusId: string) {
    this._data.campaign_status_id = campaignStatusId
  }

  get campaignStepId(): string {
    return this._data.campaign_step_id;
  }

  set campaignStepId(campaignStepId: string) {
    this._data.campaign_step_id = campaignStepId;
  }

  get preDeterminedMessageId(): string {
    return this._data.preDeterminedMessageId;
  }

  set preDeterminedMessageId(preDeterminedMessageId: string) {
    this._data.preDeterminedMessageId = preDeterminedMessageId;
  }

  get dateCompleted(): moment.Moment {
    return this._data.date_completed
      ? moment(this._data.date_completed.toMillis())
      : undefined
  }

  set dateCompleted(dateCompleted: moment.Moment) {
    this._data.date_completed = firebase.firestore.Timestamp.fromMillis(
      dateCompleted.valueOf()
    )
  }
}
