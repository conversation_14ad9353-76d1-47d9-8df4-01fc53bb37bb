import firebase from 'firebase/app'
import moment from 'moment-timezone'

export default class TaskRequest {
  public static STATUS_SCHEDULED = 'scheduled'
  public static STATUS_SUCCESS = 'success'
  public static STATUS_ERROR = 'error'

  public static METHOD_GET = 'get'
  public static METHOD_POST = 'post'
  public static METHOD_DELETE = 'delete'

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('task_request')
  }

  public static getById(id: string): Promise<TaskRequest> {
    return new Promise((resolve, reject) => {
      TaskRequest.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          if (snapshot.exists) resolve(new TaskRequest(snapshot))
          resolve()
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    })
  }

  public static async getByURL(url: string): Promise<TaskRequest | undefined> {
    const snapshot = await TaskRequest.collectionRef()
      .where('url', '==', url)
      .get()
    if (!snapshot.empty) return new TaskRequest(snapshot.docs[0])
    return undefined
  }

  public static async getByName(
    name: string
  ): Promise<TaskRequest | undefined> {
    const snapshot = await TaskRequest.collectionRef()
      .where('name', '==', name)
      .get()
    if (!snapshot.empty) return new TaskRequest(snapshot.docs[0])
    return undefined
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = TaskRequest.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }

  public async save(): Promise<TaskRequest> {
    this.dateUpdated = moment().utc()
    await this._ref.set(this._data)
    return this
  }

  get id(): string {
    return this._id
  }

  get data() {
    return this._data
  }

  get ref() {
    return this._ref
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get startTime(): moment.Moment | undefined {
    return this._data.start_time
      ? moment(this._data.start_time.toMillis())
      : undefined
  }

  set startTime(startTime: moment.Moment | undefined) {
    this._data.start_time = startTime
      ? firebase.firestore.Timestamp.fromMillis(startTime.valueOf())
      : undefined
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get method(): string {
    return this._data.method
  }

  set method(method: string) {
    this._data.method = method
  }

  get payload(): { [key: string]: any } {
    return this._data.payload
  }

  set payload(payload: { [key: string]: any }) {
    this._data.payload = payload
  }

  get status(): string {
    return this._data.status
  }

  set status(status: string) {
    this._data.status = status
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }
}
