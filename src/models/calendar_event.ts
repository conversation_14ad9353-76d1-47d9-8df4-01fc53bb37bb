import defaults from '@/config'
import axios from 'axios'
import firebase from 'firebase/app'
import handlebars from 'handlebars'
import * as moment from 'moment-timezone'
import store from '../store'
import { getTemplateParams } from '../util/template'
import Calendar from './calendar'
import Contact from './contact'
import Location from './location'
import { User } from '.'

export enum AppoinmentEventStatus {
  STATUS_NEW = 'new',
  STATUS_CONFIRMED = 'confirmed',
  STATUS_CANCELLED = 'cancelled',
  STATUS_SHOWED = 'showed',
  STATUS_NO_SHOW = 'noshow',
  STATUS_INVALID = 'invalid'
}

export enum EventStatus {
  AVAILABLE = 'available',
  UNAVAILABLE = 'unavailable',
  BOOKED = 'booked'
}

export interface Clio {
  id?: string
  data?: { [key: string]: any }
}

export interface DrChrono {
  id: string
  data?: { [key: string]: any }
}

export interface Google {
  id: string
  data?: { [key: string]: any }
}

export enum EventType {
  DRCHRONO = 'dr',
  CLIO = 'clio',
  GOOGLE_EVENT = 'g',
  GOOGLE_RECURRING = 'g_recurring',
  GOOGLE_ALLDAY = 'g_allday'
}

export enum UserAssignmentType {
  RoundRobin,
  PickUserManually
}

export const NOTES_SEPARATOR = '=========='

export enum AppointmentSource {
  APPOINTMENTS_PAGE = 'appointments_page', // Scheduling => Appointments page
  CALENDAR_PAGE = 'calendar_page', // Scheduling => Calendar view
  CONVERSATIONS_PAGE = 'conversations_page',
  CONTACTDETAILS_PAGE = 'contactdetails_page',
  OPPORTUNITIES_PAGE = 'opportunities_page',
  BOOKING_WIDGET = 'booking_widget', // Calendar booking widget
  THIRD_PARTY = 'third_party', // by third party apps by using public api

  MOBILE_APP = 'mobile_app',

  GOOGLE_CALENDAR = 'google_calendar' // While synching appointments that are originated from google calendar
}

export enum AppointmentSourceChannel {
  WEB_APP = 'web_app', // GHL Web App - Directly communicates to firestore API in most cases - (spm-ts repo)
  PRIVATE_API = 'private_api', // GHL Private API - This appointment related one is only used by booking widget as of now - (spm-appengine repo)
  PUBLIC_API = 'public_api', // GHL Public API - This is used by third parties - (ghl-public-apis repo)
  EVENT_SYNC = 'event_sync' // Synching appointments that are originated from third party calendars like google and so
}

export interface CreatedOrUpdatedBy {
  userId: string
  source: AppointmentSource
  channel: AppointmentSourceChannel
  timestamp: firebase.firestore.Timestamp
}

export default class CalendarEvent {
  public static collectionRef() {
    return firebase.firestore().collection('calendar_events')
  }

  public static fetchAllBookedcalendarEvents(
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('status', '==', EventStatus.BOOKED)
      .orderBy('date_updated', 'desc')
  }

  public static fetchAllBookedcalendarEventsByDateDesc(
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('status', '==', EventStatus.BOOKED)
      .orderBy('start_time', 'desc')
  }

  public static fetchAllBookedcalendarEventsByDateAsc(
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('status', '==', EventStatus.BOOKED)
      .orderBy('start_time', 'asc')
  }

  public static fetchAllBookedcalendarEventsByDateAddedDesc(
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('status', '==', EventStatus.BOOKED)
      .orderBy('date_added', 'desc')
  }

  public static fetchAllBookedcalendarEventsByDateAddedAsc(
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('status', '==', EventStatus.BOOKED)
      .orderBy('date_added', 'asc')
  }

  public static getById(id: string): Promise<CalendarEvent> {
    return new Promise((resolve, reject) => {
      CalendarEvent.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new CalendarEvent(snapshot))
          resolve()
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchFrozenWithAppointmentId(
    appointmentId: String,
    locationId: string
  ): Promise<CalendarEvent> {
    return new Promise<CalendarEvent>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('appointment_id', '==', appointmentId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          resolve(new CalendarEvent(snapshot.docs[0]))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchFrozenWithAltId(
    locationId: string,
    type: string,
    id: String
  ): Promise<CalendarEvent[]> {
    return new Promise<CalendarEvent[]>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('location_id', '==', locationId)
        .where(type + '.id', '==', id)
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(d => new CalendarEvent(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchFrozenWithCalendarIdAndAltId(
    locationId: string,
    calendarId: string,
    type: string,
    id: String
  ): Promise<CalendarEvent> {
    return new Promise<CalendarEvent>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('location_id', '==', locationId)
        .where('calendar_id', '==', calendarId)
        .where(type + '.id', '==', id)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          resolve(new CalendarEvent(snapshot.docs[0]))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchFutureEventForContact(
    locationId: string,
    contactId: string,
    start: moment.Moment
  ): Promise<CalendarEvent> {
    return new Promise<CalendarEvent>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('deleted', '==', false)
        .where(
          'appoinment_status',
          '==',
          AppoinmentEventStatus.STATUS_CONFIRMED
        )
        .where('start_time', '>=', start.toDate())
        .where('location_id', '==', locationId)
        .where('contact_id', '==', contactId)
        .orderBy('start_time', 'asc')
        .limit(1)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          resolve(new CalendarEvent(snapshot.docs[0]))
        })
        .catch(err => {
          reject(err)
        })
    })
  }
  public static async fetchEventForContact(
    locationId: string,
    contactId: string
  ): Promise<CalendarEvent[]> {
    return new Promise<CalendarEvent[]>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .where('status', '==', EventStatus.BOOKED)
        .where('contact_id', '==', contactId)
        .orderBy('start_time', 'asc')
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          resolve(snapshot.docs.map(d => new CalendarEvent(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static createUserCalendarEvent(
    locationId: string,
    userId: string,
    userCalendarId: string,
    startTime: moment.Moment,
    endTime: moment.Moment,
    status: EventStatus,
    extras?: { appointmentId?: string; name?: string; title?: string }
  ): CalendarEvent {
    const event = new CalendarEvent()
    event.locationId = locationId
    event.assignedUserId = userId
    event.userCalendarId = userCalendarId
    event.startTime = startTime
    event.endTime = endTime
    event.status = status
    // event.version = 2 // Value being set while retrieving providerId, check providerId gettter
    if (extras && extras.appointmentId)
      event.appointmentId = extras.appointmentId
    if (extras && extras.title) event.title = extras.title
    if (extras && extras.name) event.title = extras.name
    return event
  }

  public static createV2CalendarEvent(
    locationId: string,
    calendarId: string,
    startTime: moment.Moment,
    endTime: moment.Moment,
    status: EventStatus,
    extras?: { appointmentId?: string; name?: string; title?: string }
  ): CalendarEvent {
    const event = new CalendarEvent()
    event.locationId = locationId
    event.calendarId = calendarId
    event.startTime = startTime
    event.endTime = endTime
    event.status = status
    if (extras && extras.appointmentId)
      event.appointmentId = extras.appointmentId
    if (extras && extras.title) event.title = extras.title
    if (extras && extras.name) event.title = extras.name
    return event
  }

  public static createSimple(
    locationId: string,
    calendarId: string
  ): CalendarEvent {
    const event = new CalendarEvent()
    event.locationId = locationId
    event.calendarId = calendarId
    // event.version = 2
    return event
  }

  public static fetchAllEventsBetween(
    calendarId: string,
    start: moment.Moment,
    end: moment.Moment,
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('calendar_id', '==', calendarId)
      .where('start_time', '>=', start.toDate())
      .where('start_time', '<', end.toDate())
      .where('location_id', '==', locationId)
      .orderBy('start_time', 'desc')
  }

  public static fetchAllUserEventsBetween(
    assignedUserId: string,
    start: moment.Moment,
    end: moment.Moment,
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('assigned_user_id', '==', assignedUserId)
      .where('start_time', '>=', start.toDate())
      .where('start_time', '<', end.toDate())
      .where('location_id', '==', locationId)
      .orderBy('start_time', 'desc')
  }

  public static fetchAllProviderEventsBetween(
    calendarProviderId: string,
    start: moment.Moment,
    end: moment.Moment,
    locationId: string
  ): firebase.firestore.Query {
    return CalendarEvent.collectionRef()
      .where('deleted', '==', false)
      .where('calendar_provider_id', '==', calendarProviderId)
      .where('start_time', '>=', start.toDate())
      .where('start_time', '<', end.toDate())
      .where('location_id', '==', locationId)
      .orderBy('start_time', 'desc')
  }

  public static async fetchFrozenEventsBetween(
    calendarId: string,
    start: moment.Moment,
    end: moment.Moment,
    locationId: string
  ): Promise<CalendarEvent[]> {
    return new Promise<CalendarEvent[]>(async (resolve, reject) => {
      CalendarEvent.collectionRef()
        .where('deleted', '==', false)
        .where('start_time', '>=', start.toDate())
        .where('start_time', '<', end.toDate())
        .where('calendar_id', '==', calendarId)
        .where('location_id', '==', locationId)
        .orderBy('start_time', 'desc')
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve([])
          resolve(snapshot.docs.map(s => new CalendarEvent(s)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(
    snapshot?:
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
      | undefined
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._snapshot = snapshot
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot
      if (snapshot.start_time && moment.isMoment(snapshot.start_time)) {
        this.startTime = snapshot.start_time
        this.endTime = snapshot.end_time
      } else {
        if (snapshot.start_time) this.startTime = moment(snapshot.start_time)
        if (snapshot.end_time) this.endTime = moment(snapshot.end_time)
      }
    } else {
      this._ref = CalendarEvent.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this._data.attendees = {}
      this.dateAdded = moment()
      this.deleted = false
      this.local = true
      this.isFree = false
      this.version = 2
    }
    this.userAssignmentType =
      this.userAssignmentType || UserAssignmentType.RoundRobin
    
    if (['Etc/Greenwich', 'GMT'].includes(this.selectedTimezone)) {
      this.selectedTimezone = 'UTC'
    }
  }

  get id(): string | undefined {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return this._data
  }

  get snapshot(): firebase.firestore.DocumentSnapshot {
    return this._snapshot
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get title(): string {
    return this._data.title
  }

  set title(title: string) {
    this._data.title = title
  }

  get contactName(): string {
    return this._data.contact_name
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get calendarProviderId(): string {
    return this._data.calendar_provider_id
  }

  set calendarProviderId(value: string) {
    if (value && this.version === 2) this.version = 3
    this._data.calendar_provider_id = value
  }

  get calendarId(): string {
    return this._data.calendar_id
  }

  set calendarId(calendarId: string) {
    this._data.calendar_id = calendarId
  }

  get userCalendarId(): string {
    return this._data.user_calendar_id
  }

  set userCalendarId(value: string) {
    if (value && this.version === 2) this.version = 3
    this._data.user_calendar_id = value
  }

  get userAssignmentType() {
    return this._data.user_assignment_type
  }

  set userAssignmentType(value: UserAssignmentType) {
    this._data.user_assignment_type = value
  }

  get assignedUserId(): string {
    return this._data.assigned_user_id
  }

  set assignedUserId(value: string) {
    if (value && this.version === 2) this.version = 3
    this._data.assigned_user_id = value
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data['date_updated']
      ? typeof this._data['date_updated'] === 'number'
        ? moment(this._data['date_updated'])
        : moment(this._data['date_updated'].toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get createdBy(): CreatedOrUpdatedBy {
    return this._data.created_by
  }

  set createdBy(value: CreatedOrUpdatedBy) {
    this._data.created_by = value
  }

  get lastUpdatedBy(): CreatedOrUpdatedBy {
    return this._data.last_updated_by
  }

  set lastUpdatedBy(value: CreatedOrUpdatedBy) {
    this._data.last_updated_by = value
  }

  public setCreatedByAndOrLastUpdatedBy(
    isNewAppointment: boolean,
    userId: string,
    source: AppointmentSource,
    channel: AppointmentSourceChannel,
    timestamp: moment.Moment = moment()
  ) {
    if (isNewAppointment) {
      this.createdBy = {
        userId,
        source,
        channel,
        timestamp: firebase.firestore.Timestamp.fromMillis(timestamp.valueOf())
      }
    }
    this.lastUpdatedBy = {
      userId,
      source,
      channel,
      timestamp: firebase.firestore.Timestamp.fromMillis(timestamp.valueOf())
    }
  }

  get startTime(): moment.Moment {
    return this._data.start_time
      ? moment(this._data.start_time.toMillis())
      : undefined
  }

  set startTime(startTime: moment.Moment) {
    this._data.start_time = firebase.firestore.Timestamp.fromMillis(
      startTime.valueOf()
    )
  }

  get endTime(): moment.Moment {
    return this._data.end_time
      ? moment(this._data.end_time.toMillis())
      : undefined
  }

  set endTime(endTime: moment.Moment) {
    this._data.end_time = firebase.firestore.Timestamp.fromMillis(
      endTime.valueOf()
    )
  }

  get notes(): string {
    return this._data.notes
  }

  set notes(notes: string) {
    this._data.notes = notes
  }

  get version(): number {
    return this._data.version
  }

  set version(version: number) {
    this._data.version = version
  }

  get status(): EventStatus {
    return this._data.status
  }

  get appoinmentStatus(): AppoinmentEventStatus {
    return this._data.appoinment_status
  }

  set appoinmentStatus(appoinmentStatus: AppoinmentEventStatus) {
    this._data.appoinment_status = appoinmentStatus
  }

  set status(status: EventStatus) {
    this._data.status = status
  }

  get selectedTimezone(): string {
    return this._data.selected_timezone
  }

  set selectedTimezone(selectedTimezone: string) {
    this._data.selected_timezone = selectedTimezone
  }

  get appointmentId(): string {
    return this._data.appointment_id
  }

  set appointmentId(appointmentId: string) {
    this._data.appointment_id = appointmentId
  }

  get isRecurring(): boolean {
    return this._data.is_recurring
  }

  set isRecurring(isRecurring: boolean) {
    this._data.is_recurring = isRecurring
  }

  get RRule(): string {
    return this._data.rrule
  }

  set RRule(RRule: string) {
    this._data.rrule = RRule
  }

  get isFree(): boolean {
    return this._data.is_free
  }

  set isFree(isFree: boolean) {
    this._data.is_free = isFree
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get createdByUserId(): string {
    return this._data.user_id
  }

  get userId(): string {
    return this._data.user_id
  }

  set userId(userId: string) {
    this._data.user_id = userId
  }

  get insuranceProviderName(): string {
    return this._data.insurance_provider_name
  }

  set insuranceProviderName(insuranceProviderName: string) {
    this._data.insurance_provider_name = insuranceProviderName
  }

  get source(): string {
    return this._data.source
  }

  set source(source: string) {
    this._data.source = source
  }

  get channel(): string {
    return this._data.channel
  }

  set channel(channel: string) {
    this._data.channel = channel
  }

  get callId(): string {
    return this._data.call_id
  }

  set callId(callId: string) {
    this._data.call_id = callId
  }

  get existing(): boolean {
    return this._data.existing
  }

  set existing(existing: boolean) {
    this._data.existing = existing
  }

  get clio(): Clio {
    if (!this._data.clio) this._data.clio = {}
    return this._data.clio
  }

  set clio(clio: Clio) {
    this._data.clio = clio
  }

  get drchrono(): DrChrono {
    if (!this._data.drchrono) this._data.drchrono = {}
    return this._data.drchrono
  }

  set drchrono(drchrono: DrChrono) {
    this._data.drchrono = drchrono
  }

  get google(): Google {
    if (!this._data.google) this._data.google = {}
    return this._data.google
  }

  set google(google: Google) {
    this._data.google = google
  }

  get duration() {
    return this.endTime.diff(this.startTime, 'minutes')
  }

  get local(): boolean {
    return this._data.local
  }

  set local(local: boolean) {
    this._data.local = local
  }

  get address(): string {
    return this._data.address
  }

  set address(address: string) {
    this._data.address = address
  }

  get toIgnore(): boolean {
    return this._data.to_ignore
  }

  set toIgnore(toIgnore: boolean) {
    this._data.to_ignore = toIgnore
  }

  get editable(): boolean {
    // return !this.isRecurring && !this.toIgnore
    return (
      !this.isRecurring &&
      !this.toIgnore
    )
  }

  get uploadError(): boolean {
    return this._data.upload_error
  }

  get uploadErrorMessage(): string {
    return this._data.upload_error_message
  }

  set type(type: EventType) {
    this._data.type = type
  }

  get type(): EventType {
    return this._data.type
  }

  get isFullDay(): boolean {
    return this._data.is_full_day
  }

  set isFullDay(isFullDay: boolean) {
    this._data.is_full_day = isFullDay
  }

  public async getAdditionalNotes(
    location: Location,
    calendar: Calendar,
    contact: Contact
  ): Promise<string> {
    if (this.version != 2 || !this.local) return this.notes

    let notes = this.notes
      ? 'Note:-\n' + this.notes + '\n' + NOTES_SEPARATOR + '\n'
      : ''
    notes += calendar.notes

    try {
      notes = notes
        .replace('{{cancellation_link}}', '{{{cancellation_link}}}')
        .replace('{{reschedule_link}}', '{{{reschedule_link}}}')

      const whitelabelUrl = await store.dispatch('company/getWhitelabelDomain')
      const templateParams = {
        contact: contact.data,
        location: location.data,
        cancellation_link: this.getCancellationLink(
          whitelabelUrl,
          calendar.slug
        ),
        reschedule_link: this.getRescheduleLink(whitelabelUrl, calendar.slug)
      }

      notes = handlebars.compile(notes)(templateParams)
    } catch (err) {
      notes = ''
    }

    return notes

    // if (!allowCancel && !allowReschedule) return '';

    // let notes = this.notes;
    // if (!this.notes) {
    //   notes = '';
    // } else {
    //   notes = '\n';
    // }

    // notes += await this.getEventInfoNote(contactId);

    // if (notes) {
    //   notes += '\n';
    // }

    // notes += 'Need to make a change to this event?\n';
    // if (allowCancel) {
    //   notes += 'Cancel:-\n' + this.getCancellationLink();
    // }
    // if (allowReschedule) {
    //   notes += '\nReschedule:-\n' + this.getRescheduleLink();
    // }
    // return notes;
  }

  public async getEventInfoNote(locationId: string): Promise<string> {
    try {
      const location = await Location.getById(locationId)
      let notes = ''
      if (location.phone) {
        notes += 'Phone:- ' + location.phone + '\n'
      }
      if (location.website) {
        notes += 'Website:- ' + location.website + '\n'
      }

      return notes
    } catch (err) {
      return ''
    }
  }

  private calendarWidgetBaseURL(whitelabelUrl) {
    return defaults.mode === 'dev' || defaults.mode === 'staging'
      ? `${defaults.builderPreviewUrl}/widget/appointment`
      : `${whitelabelUrl}/widget/appointment`
  }

  public getCancellationLink(whitelabelUrl, calendarSlug) {
    if (calendarSlug) {
      return `${this.calendarWidgetBaseURL(
        whitelabelUrl
      )}/service/${calendarSlug}/cancel?event_id=${this.id}`
    }
    if (defaults.mode === 'dev') {
      return (
        defaults.builderPreviewUrl +
        '/widget/cancel-booking?event_id=' +
        this.id
      )
    } else {
      return whitelabelUrl + '/widget/cancel-booking?event_id=' + this.id
    }
  }

  public getRescheduleLink(whitelabelUrl, calendarSlug) {
    if (calendarSlug) {
      return `${this.calendarWidgetBaseURL(
        whitelabelUrl
      )}/service/${calendarSlug}?event_id=${this.id}`
    }
    if (defaults.mode === 'dev') {
      return (
        defaults.builderPreviewUrl +
        `/widget/booking/${this.calendarId}?event_id=${this.id}`
      )
    } else {
      return (
        whitelabelUrl + `/widget/booking/${this.calendarId}?event_id=${this.id}`
      )
    }
  }

  public async getRoundRobinUserId(calendarId, slotPicked, selectedTimezone) {
    try {
      const response = await axios.get('/appointment/get_round_robin_user_id', {
        params: {
          calendar_id: calendarId,
          selected_slot: moment(slotPicked).toISOString(),
          selected_timezone: selectedTimezone
        }
      })
      const { data, status } = response
      if (status === 200) {
        return data.user_id
      } else {
        return false
      }
    } catch (err) {
      console.log({ err })

      return false
    }
  }

  public async save() {
    let isNew = false
    if (!this.dateUpdated) isNew = true // If the event has never been updated, then this is the first appearance of it.
    this.dateUpdated = moment()
    // This is fix for HL Full Day events
     const isAllDayEvent = !this.startTime.isSame(this.endTime, 'day');
     let isFullDay = false;
     if (isAllDayEvent && (!this.type || this.type === EventType.GOOGLE_EVENT)) {
       this.type = EventType.GOOGLE_ALLDAY;
       isFullDay = true
     }
     if (!isAllDayEvent && this.type === EventType.GOOGLE_ALLDAY) {
       this.type = EventType.GOOGLE_EVENT;
       isFullDay = false;
    }
    // Let's make sure we're not overwriting this
    if(this.isFullDay === undefined || this.isFullDay === null) {
      this.isFullDay = isFullDay;
    }

    await this._ref.set(this.data)

    // if (isNew) this.notify()
    if (this.status === EventStatus.BOOKED) {
      this.upload()
    }
  }

  public async delete() {
    await this._ref.set({ deleted: true }, { merge: true })
    await this.upload()
  }

  public async cancel() {
    this.appoinmentStatus = AppoinmentEventStatus.STATUS_CANCELLED
    await this._ref.update({
      appoinment_status: AppoinmentEventStatus.STATUS_CANCELLED,
      date_updated: firebase.firestore.FieldValue.serverTimestamp()
   })
   if (this.status === EventStatus.BOOKED) {
    this.upload()
    }
  }

  public async confirm() {
    this.appoinmentStatus = AppoinmentEventStatus.STATUS_CONFIRMED
    await this._ref.update({
      appoinment_status: AppoinmentEventStatus.STATUS_CONFIRMED,
      date_updated: firebase.firestore.FieldValue.serverTimestamp()
   })
   if (this.status === EventStatus.BOOKED) {
    this.upload()
    }
  }

  /**
   * Upload - Which creates an asynchronous task by trigger the API end point
   * Since task is asynchronous, sometimes there will be slight delay to finish-up the actual upload
   */
  public async upload() {
    try {
      await axios.get('/appointment/' + this.id + '/upload')
    } catch (err) { }
  }

  public async notify() {
    try {
      await axios.get('/appointment/' + this.id + '/notify')
    } catch (err) { }
  }

  public async getEventTitle(
    contact?: Contact,
    location?: Location,
    calendar?: Calendar,
    user?: User
  ) {
    if (this.title) return this.title

    if (!location) {
      location = await Location.getById(this.locationId)
    }

    if (!calendar) {
      calendar = await Calendar.getById(this.calendarId)
    }

    // if (!user) {
    //   user = await User.getById(this.assignedUserId)
    // }

    if (!contact && this.contactId) {
      contact = await Contact.getById(this.contactId)
    }

    if (!calendar || !location) {
      return ''
    }

    try {
      let timezone = await location.getTimeZone()
      if (contact.timezone && location.disableContactTimezone === false)
        timezone = contact.timezone

      const templateParams = await getTemplateParams(contact, {
        appointment: this,
        calendar,
        location,
        timezone
      })
      const whitelabelUrl = await store.dispatch('company/getWhitelabelDomain')
      templateParams.cancellation_link = this.getCancellationLink(
        whitelabelUrl,
        calendar.slug
      )
      templateParams.reschedule_link = this.getRescheduleLink(
        whitelabelUrl,
        calendar.slug
      )
      return handlebars.compile(calendar.eventTitle)(templateParams)
    } catch (err) {
      console.error(err)
    }

    return ''
  }

  public async getEventAddress(
    contact?: Contact,
    location?: Location,
    calendar?: Calendar,
    user?: User
  ) {
    // This condition helps when this 'address' property is directly binded through v-model
    if (this.address) return this.address

    if (!location) {
      location = await Location.getById(this.locationId)
    }

    if (!calendar) {
      calendar = await Calendar.getById(this.calendarId)
    }

    if (!user && this.assignedUserId) {
      user = await User.getById(this.assignedUserId)
    }

    if (!contact && this.contactId) {
      contact = await Contact.getById(this.contactId)
    }

    if (!calendar || !location) {
      return ''
    }

    let meetingLocation = ''

    if (!this.assignedUserId) {
      meetingLocation = calendar.meetingLocation
    } else {
      if (
        calendar.teamMembers &&
        calendar.teamMembers.find(x => x.user_id === user.id)
      ) {
        let _user = calendar.teamMembers.find(x => x.user_id === user.id)
        meetingLocation = _user
          .meeting_location
        if (!meetingLocation) meetingLocation = user.locationWiseMeetingLocation[this.locationId]
        if(_user.is_zoom_added === true || _user.is_zoom_added === 'true') meetingLocation = ""
        if(this.data.zoom_meeting_id) {
          let calendarEvent = await CalendarEvent.getById(this.id)
          meetingLocation = calendarEvent.address
        }
      }
    }

    if (!meetingLocation) return ''

    try {
      const templateParams = await getTemplateParams(contact, {
        appointment: this,
        calendar,
        location
      })
      const whitelabelUrl = await store.dispatch('company/getWhitelabelDomain')
      templateParams.cancellation_link = this.getCancellationLink(
        whitelabelUrl,
        calendar.slug
      )
      templateParams.reschedule_link = this.getRescheduleLink(
        whitelabelUrl,
        calendar.slug
      )
      return handlebars.compile(meetingLocation)(templateParams)
    } catch (err) {
      console.error(err)
    }

    return ''
  }

  public getAddGoogleCalendarLink(whitelabelUrl): string {
    return whitelabelUrl + '/google/calendar/add-event/' + this.id
  }

  public getICSLink(whitelabelUrl): string {
    return whitelabelUrl + '/google/calendar/get-ics/' + this.id
  }
}
