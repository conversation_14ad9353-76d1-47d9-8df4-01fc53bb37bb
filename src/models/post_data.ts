import * as moment from 'moment-timezone';

export default class PostData {
    public static TYPE_TWITTER = 'twitter';
    public static TYPE_GOOGLE = 'google';
    public static TYPE_FACEBOOK = 'facebook';
    public static TYPE_LINKEDIN = 'linkedin';
    public static TYPE_INSTAGRAM = 'instagram';

    constructor(existingPost?: {}) {
        if (existingPost) {
            for (var k in existingPost) {
                if (k in this) {
                    this[k] = existingPost[k];
                }
            }
        } else {
            this.dateAdded = moment();
            this.dateUpdated = moment();
        }
    }

    get dateAdded(): moment.Moment {
        return moment(this.date_added);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this.date_updated);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this.date_updated = dateUpdated.toDate();
    }

    get datePosted(): moment.Moment {
        return moment(this.date_posted);
    }

    set datePosted(datePosted: moment.Moment) {
        this.date_posted = datePosted.toDate();
    }

    get type(): string {
        return this.type;
    }
    set type(type: string) {
        this.type = type;
    }

    get media(): {} {
        return this.media;
    }

    set media(media: {}) {
        this.media = media;
    }

    get text(): string {
        return this.text;
    }
    set text(text: string) {
        this.text = text;
    }

    get altId(): string {
        return this.alt_id;
    }
    set altId(altId: string) {
        this.alt_id = altId;
    }
}
