import firebase from 'firebase/app'
import moment from 'moment-timezone'
import lodash from 'lodash'

export enum CustomerType {
  DIRECT = 'direct',
  AGENCY = 'agency',
}

export enum Source {
  CLIO = 'clio',
  QBO = 'qbo',
  WEBSITE = 'website',
  DRCHRONO = 'drchrono',
  IOS = 'ios',
  ANDROID = 'android',
}
export interface CustomMenuLink {
  id: string
  icon: object
  title: string
  url: string
  show_on_company: boolean
  show_on_location: boolean
  locations: string[]
  open_mode: LinkOpenMode
}

export interface APIKey {
  [key: string]: string
}

export enum LinkOpenMode {
  IFRAME = 'iframe',
  NEW_TAB = 'newTab',
}

export interface AllowBetaAccess {
  workflow: boolean
  saasMode: boolean
  isvMode: boolean
  emailRebilling: boolean,
  tipalti:boolean
}

export interface ISaasSettings {
  agency_dashboard_visible_to: 'admin' | 'individual'
  stripe_connect_initiated_by: string
}

// export interface IIsvSettings {
//   isv_mode: boolean
// }
export interface WhitelabelAppLinks {
  android: string,
  ios: string
}

export interface WordpressAgencySettings{
  plan?:string,
  valve?:string,
  agency_price?:number,
  hl_price?:number,
  product_id?:string,
  subscriber_count?:number,
  subscriptions?:string[]
  subscribed_locations?:[{ [key: string]: any }]
  prices?:[{ [key: string]: any }]
}

type Status =
  | 'active'
  | 'active_trial'
  | 'active_trial-extension'
  | 'active_test-agency'
  | 'active_special'
  | 'active_overused'
  | 'active_failed-payment'
  | 'inactive_failed-payment'
  | 'inactive_paused'
  | 'inactive_cancelled'
  | 'inactive'
  | ''

export default class Company {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('companies')
  }

  public static getById(companyId: string): Promise<Company> {
    return new Promise((resolve, reject) => {
      Company.collectionRef()
        .doc(companyId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Company(snapshot))
          resolve()
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getStreamById(
    id: string
  ): firebase.firestore.DocumentReference {
    return Company.collectionRef().doc(id)
  }

  public save() {
    const _self = this
    if (!_self.customerType) {
      throw new Error('Problem while updating company')
    }

    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment()
      _self._ref.set(this.data).then(_ => {
        resolve(_self)
      })
    })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    params: { [key: string]: any } | firebase.firestore.DocumentSnapshot
  ) {
    if (params instanceof firebase.firestore.DocumentSnapshot) {
      this._id = params.id
      this._ref = params.ref
      this._data = params.data() || {}
    } else {
      this._id = params.id
      this._ref = Company.collectionRef().doc(params.id)
      this._data = params || {}
    }

    if (['Etc/Greenwich', 'GMT'].includes(this.timezone)) {
      this.timezone = 'UTC'
    }
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get secureData(): { [key: string]: any } {
    return lodash.pickBy(
      this._data,
      (v, key) =>
        v !== null &&
        v !== undefined &&
        !['api_keys', 'api_key_titles'].includes(key)
    )
  }

  get dateAdded(): moment.Moment {
    return moment(
      this._data['date_added'] ? this._data['date_added'].toMillis() : 1000
    )
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(
      this._data['date_updated'] ? this._data['date_updated'].toMillis() : 1000
    )
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  /**
   * Whether new calendar feature (Calendar v3) is turned on or not
   * If so, then agency admin has access to turn on or off isCalendarV3On flag for agency's locations
   */
  get isCalendarV3On(): boolean {
    return true // Now allowing v3 feature to all agencies // TODO: Remove flag later across the app
  }

  set isCalendarV3On(value: boolean) {
    this._data.is_calendar_v3_on = value
  }

  get dirty(): boolean {
    return this._data.dirty
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get logoURL(): string {
    return this._data.logo_url
  }

  set logoURL(logoURL: string) {
    this._data.logo_url = logoURL
  }

  get faviconURL(): string {
    return this._data.favicon_url
  }

  set faviconURL(faviconURL: string) {
    this._data.favicon_url = faviconURL
  }

  get website(): string {
    return this._data.website
  }

  set website(website: string) {
    this._data.website = website
  }

  get address(): string {
    return this._data.address
  }

  set address(address: string) {
    this._data.address = address
  }

  get city(): string {
    return this._data.city
  }

  set city(city: string) {
    this._data.city = city
  }

  get state(): string {
    return this._data.state
  }

  set state(state: string) {
    this._data.state = state
  }

  get postalCode(): string {
    return this._data.postal_code
  }

  set postalCode(postalCode: string) {
    this._data.postal_code = postalCode
  }

  get country(): string {
    return this._data.country
  }

  set country(country: string) {
    this._data.country = country
  }

  get currency(): string {
    return this._data.currency
  }

  set currency(currency: string) {
    this._data.currency = currency
  }

  get timezone(): string {
    return this._data.timezone
  }

  set timezone(timezone: string) {
    this._data.timezone = timezone
  }

  get phone(): string {
    return this._data.phone
  }

  set phone(phone: string) {
    this._data.phone = phone
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
  }

  get localeString(): string {
    return this._data.locale_string
  }

  set localeString(localeString: string) {
    this._data.locale_string = localeString
  }

  get plan(): number {
    return this._data.plan
  }

  set plan(plan: number) {
    this._data.plan = plan
  }

  get subdomain(): string {
    return this._data.subdomain
  }

  set subdomain(subdomain: string) {
    this._data.subdomain = subdomain
  }

  get domain(): string {
    return this._data.domain
  }

  set domain(domain: string) {
    this._data.domain = domain
  }

  get promoterId(): string {
    return this._data.promoter_id
  }

  set promoterId(promoterId: string) {
    this._data.promoter_id = promoterId
  }

  get sparedomain(): string {
    return this._data.spare_domain
  }

  set sparedomain(sparedomain: string) {
    this._data.spare_domain = sparedomain
  }

  get status(): Status {
    return this._data['status']
  }

  set status(status: Status) {
    this._data['status'] = status
  }

  get stripeId(): string {
    return this._data['stripe_id']
  }

  set stripeId(stripeId: string) {
    this._data['stripe_id'] = stripeId
  }

  get stripeConnectId(): string {
    return this._data['stripe_connect_id']
  }

  set stripeConnectId(stripeConnectId: string) {
    this._data['stripe_connect_id'] = stripeConnectId
  }

  get stripeConnectProcessed(): boolean {
    return this._data['stripe_connect_processed']
  }

  set stripeConnectProcessed(stripeConnectProcessed: boolean) {
    this._data['stripe_connect_processed'] = stripeConnectProcessed
  }

  set saasSettings(saasSettings: ISaasSettings) {
    this._data['saas_settings'] = saasSettings
  }

  get saasSettings(): ISaasSettings {
    return this._data['saas_settings']
  }

  // set isvSettings(isvSettings: IIsvSettings) {
  //   this._data['isv_settings'] = isvSettings
  // }

  // get isvSettings(): IIsvSettings {
  //   return this._data['isv_settings']
  // }

  get inIsvMode(): boolean {
    return this._data.in_isv_mode || false
  }

  set inIsvMode(inIsvMode: boolean) {
    this._data.in_isv_mode = inIsvMode
  }

  get inPureIsvMode(): boolean {
    // To Hide Twilio Tab from agency settings
    return this._data.in_pure_isv_mode || false
  }

  set inPureIsvMode(inPureIsvMode: boolean) {
    this._data.in_pure_isv_mode = inPureIsvMode
  }

  get stripeActivePlan(): string {
    return this._data['stripe_active_plan']
  }

  set stripeActivePlan(stripeActivePlan: string) {
    this._data['stripe_active_plan'] = stripeActivePlan
  }

  get dateInactivation(): moment.Moment {
    return moment(this._data['date_inactivation'].toMillis())
  }

  set dateInactivation(dateInactivation: moment.Moment) {
    this._data['date_inactivation'] = dateInactivation.toDate()
  }

  get affiliatePixelId(): string {
    return this._data['affiliate_pixel_id']
  }

  set affiliatePixelId(affiliatePixelId: string) {
    this._data['affiliate_pixel_id'] = affiliatePixelId
  }

  get cardLast4(): string {
    return this._data['card_last_4']
  }

  set cardLast4(cardLast4: string) {
    this._data['card_last_4'] = cardLast4
  }

  get cardBrand(): string {
    return this._data['card_brand']
  }

  set cardBrand(cardBrand: string) {
    this._data['card_brand'] = cardBrand
  }

  get customerType(): CustomerType {
    return this._data.customer_type
  }

  set customerType(customerType: CustomerType) {
    this._data.customer_type = customerType
  }

  get source(): Source {
    return this._data.source
  }

  set source(source: Source) {
    this._data.source = source
  }

  get referralId(): string {
    return this._data.referral_id
  }

  set referralId(referralId: string) {
    this._data.referral_id = referralId
  }

  get privacyPolicy(): string {
    return this._data.privacy_policy
  }

  set privacyPolicy(privacy_policy: string) {
    this._data.privacy_policy = privacy_policy
  }
  get services(): string {
    return this._data.services
  }

  get customJavascript(): string {
    return this._data['custom_javascript']
  }

  set customJavascript(custom_javascript: string) {
    this._data['custom_javascript'] = custom_javascript
  }

  get customCSS(): string {
    return this._data['custom_css']
  }

  set customCSS(custom_css: string) {
    this._data['custom_css'] = custom_css
  }

  get funnelAvailability(): boolean {
    return this._data['funnel_availability']
  }

  set funnelAvailability(funnel_availability: boolean) {
    this._data['funnel_availability'] = funnel_availability
  }

  get onboardingInfo(): { [key: string]: any } {
    if (this._data.onboarding_info === undefined)
      this._data.onboarding_info = {}
    return this._data.onboarding_info
  }

  set onboardingInfo(onboardingInfo: { [key: string]: any }) {
    this._data.onboarding_info = onboardingInfo
  }

  get hipaaCompliance(): boolean {
    return this._data['hipaa_compliance'] || false
  }

  set hipaaCompliance(hipaaCompliance: boolean) {
    this._data['hipaa_compliance'] = hipaaCompliance || false
  }

  get hipaaStatus(): string {
    return this._data['hipaa_status']
  }

  set hipaaStatus(hipaaStatus: string) {
    this._data['hipaa_status'] = hipaaStatus
  }

  set premiumUpgraded(premiumUpgraded: boolean) {
    this._data['premium_upgraded'] = premiumUpgraded
  }

  get premiumUpgraded(): boolean {
    return this._data['premium_upgraded']
  }

  get premiumUpgradeStatus(): string {
    // 'Under Review' || 'Subscribed' || 'inactive'
    return this._data['premium_upgrade_status']
  }

  set premiumUpgradeStatus(premiumUpgradeStatus: string) {
    this._data['premium_upgrade_status'] = premiumUpgradeStatus
  }

  /* Whitelabel-app-service specific */
  get whitelabelAppEnabled(): boolean {
    return this._data['whitelabel_app_enabled'] || false
  }

  set whitelabelAppEnabled(whitelabelAppEnabled: boolean) {
    this._data['whitelabel_app_enabled'] = whitelabelAppEnabled || false
  }

  get whitelabelAppStatus(): string {
    return this._data['whitelabel_app_status']
  }

  set whitelabelAppStatus(whitelabelAppStatus: string) {
    this._data['whitelabel_app_status'] = whitelabelAppStatus
  }

  get whitelabelAppPlan(): string {
    return this._data['whitelabel_app_plan']
  }

  set whitelabelAppPlan(whitelabelAppPlan: string) {
    this._data['whitelabel_app_plan'] = whitelabelAppPlan
  }

  get whitelabelAppOrderId(): string {
    // Agency_order_id of the paid request.
    return this._data['whitelabel_app_order_id']
  }

  set whitelabelAppOrderId(whitelabelAppOrderId: string) {
    this._data['whitelabel_app_order_id'] = whitelabelAppOrderId
  }
  /* Agency-jumpstart-service specific */
  get jumpstartSupported(): Date {
    return this._data['jumpstart_supported']
  }

  set jumpstartSupported(jumpstartSupported: Date) {
    this._data['jumpstart_supported'] = jumpstartSupported
  }

  get callTrackingEnabled(): boolean {
    return this._data.call_tracking_enabled || false
  }

  get marketplaceEnabled(): boolean {
    return this._data.marketplace_enabled
  }

  set marketplaceEnabled(marketplaceEnabled: boolean) {
    this._data.marketplace_enabled = marketplaceEnabled
  }

  get betaServicesEnabled(): boolean {
    return this._data.beta_services_enabled
  }

  set betaServicesEnabled(betaServicesEnabled: boolean) {
    this._data.beta_services_enabled = betaServicesEnabled
  }

  get twilioFreeCredits(): number {
    return this._data.twilio_free_credits
  }

  set twilioFreeCredits(twilioFreeCredits: number) {
    this._data.twilio_free_credits = twilioFreeCredits
  }

  get twilioTrialMode(): boolean {
    return this._data.twilio_trial_mode
  }

  set twilioTrialMode(twilioTrialMode: boolean) {
    this._data.twilio_trial_mode = twilioTrialMode
  }
  get defaultEmailService(): string {
    return this._data.default_email_service
  }

  get UpgradeAlertDismissed(): Date {
    return this._data['upgrade_alert_dismissed']
  }

  set UpgradeAlertDismissed(UpgradeAlertDismissed: Date) {
    this._data['upgrade_alert_dismissed'] = UpgradeAlertDismissed
  }

  set defaultEmailService(defaultEmailService: string) {
    this._data.default_email_service = defaultEmailService
  }

  get stripeSecretKey(): string {
    return this._data.stripe_key
  }

  set stripeSecretKey(stripeSecretKey: string) {
    this._data.stripe_key = stripeSecretKey
  }

  get yextReseller(): object {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    if (this._data.reseller.yext) {
      return this._data.reseller.yext
    }
    this._data.reseller.yext = {
      hl_price: 30,
      agency_price: 49,
      enabled: false,
    }
    return this._data.reseller.yext
  }

  set yextReseller(yextSetting: { [key: string]: any }) {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    this._data.reseller.yext = { ...yextSetting, hl_price: 30 } // hl_price:30 is important because client and try to override although we are only using it display purpose
  }

  get wordpressReseller(): WordpressAgencySettings {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    if (this._data.reseller.wordpress) {
      return this._data.reseller.wordpress
    }
    this._data.reseller.wordpress = { hl_price: 10, agency_price: 14.99 }
    return this._data.reseller.wordpress
  }

  set wordpressReseller(wpConfigs: { [key: string]: any }) {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    this._data.reseller.wordpress = { ...wpConfigs, hl_price: 20 } // hl_price:30 is important because client and try to override although we are only using it display purpose
  }

  get theme(): string {
    return this._data.theme
  }

  set theme(theme: string) {
    this._data.theme = theme
  }

  get elizaEnabled(): boolean {
    return this._data.eliza_enabled
  }

  set elizaEnabled(elizaEnabled: boolean) {
    this._data.eliza_enabled = elizaEnabled
  }

  get elizaFirstLocationEnabled(): boolean {
    return this._data.eliza_first_location_enabled
  }

  set elizaFirstLocationEnabled(elizaFirstLocationEnabled: boolean) {
    this._data.eliza_first_location_enabled = elizaFirstLocationEnabled
  }

  get customMenuLinks(): CustomMenuLink[] {
    if (
      lodash.isEmpty(this._data.custom_menu_links) ||
      !Array.isArray(this._data.custom_menu_links)
    ) {
      return []
    }
    return this._data.custom_menu_links
  }

  set customMenuLinks(customMenuLinks: CustomMenuLink[]) {
    this._data.custom_menu_links = customMenuLinks
  }

  public addCustomMenuLinks(customMenuLinks: CustomMenuLink[]) {
    customMenuLinks.forEach(item => {
      this.addCustomMenuLink(item)
    })
  }

  public removeCustomMenuLinks(customMenuLinks: CustomMenuLink[]) {
    customMenuLinks.forEach(item => {
      this.removeCustomMenuLink(item)
    })
  }

  public addCustomMenuLink(customMenuLink: CustomMenuLink) {
    const duplicatedItem = this.customMenuLinks.find(
      item => item.id === customMenuLink.id
    )
    if (!duplicatedItem)
      this.customMenuLinks = [...this.customMenuLinks, customMenuLink]
  }

  public removeCustomMenuLink(customMenuLink: CustomMenuLink) {
    this.customMenuLinks = this.customMenuLinks.filter(
      item => item.id === customMenuLink.id
    )
  }

  get apiKeys(): string[] {
    return this._data.api_keys
  }

  set apiKeys(apiKeys: string[]) {
    this._data.api_keys = apiKeys
  }

  get apiKeyTitles(): string[] {
    return this._data.api_key_titles
  }

  set apiKeyTitles(apiKeyTitles: string[]) {
    this._data.api_key_titles = apiKeyTitles
  }

  get allowBetaAccess(): AllowBetaAccess {
    return this._data.allowBetaAccess
  }

  set allowBetaAccess(allowBetaAccess: AllowBetaAccess) {
    this._data.allowBetaAccess = allowBetaAccess
  }

  get termsOfService(): boolean {
    return this._data.terms_of_service
  }

  set termsOfService(terms_of_service: boolean) {
    this._data.terms_of_service = terms_of_service
  }

  get termsOfServiceAcceptedBy(): string {
    return this._data.terms_of_service_accepted_by
  }

  set termsOfServiceAcceptedBy(userId: string) {
    this._data.terms_of_service_accepted_by = userId
  }

  get termsOfServiceAcceptedDate(): moment.Moment {
    return moment(
      this._data['terms_of_service_accepted_date']
        ? this._data['terms_of_service_accepted_date'].toMillis()
        : 1000
    )
  }

  set termsOfServiceAcceptedDate(dateApproved: moment.Moment) {
    this._data.terms_of_service_accepted_date = dateApproved.toDate()
  }

  get isReselling(): boolean {
    return this._data.is_reselling
  }

  set isReselling(reselling: boolean) {
    this._data.is_reselling = reselling
  }

  get isEnterprise(): boolean {
    return this._data.is_enterprise
  }

  set isEnterprise(isEnterprise: boolean) {
    this._data.is_enterprise = isEnterprise
  }

  get hideLaunchpad(): boolean {
    return this._data.hide_launchpad
  }

  set hideLaunchpad(hideLaunchpad: boolean) {
    this._data.hide_launchpad = hideLaunchpad
  }

  get experianceNpsEnabled(): boolean {
    return this._data.experiance_nps_enabled === undefined
      ? true
      : this._data.experiance_nps_enabled
  }

  set experianceNpsEnabled(experianceNpsEnabled: boolean) {
    this._data.experiance_nps_enabled = experianceNpsEnabled
  }

  get experianceGuidesEnabled(): boolean {
    return this._data.experiance_guides_enabled === undefined
      ? true
      : this._data.experiance_guides_enabled
  }

  set experianceGuidesEnabled(experianceGuidesEnabled: boolean) {
    this._data.experiance_guides_enabled = experianceGuidesEnabled
  }

  get experianceReportsEnabled(): boolean {
    return this._data.experiance_reports_enabled === undefined
      ? true
      : this._data.experiance_reports_enabled
  }

  set experianceReportsEnabled(experianceReportsEnabled: boolean) {
    this._data.experiance_reports_enabled = experianceReportsEnabled
  }

  get sideBarNotificationType(): string | undefined {
    if (this._data.sidebar_v2_notification_type) {
      return this._data.sidebar_v2_notification_type
    } else if (this._data.sidebar_enable) {
      return 'optional'
    }
    return undefined
  }

  set sideBarNotificationType(type: string) {
    this._data.sidebar_v2_notification_type = type
  }

  get whitelabelAppLinks(): WhitelabelAppLinks {
    return this._data.whitelabel_app_links
      ? this._data.whitelabel_app_links
      : {}
  }

  set whitelabelAppLinks(appLinks: WhitelabelAppLinks) {
    this._data.whitelabel_app_links = appLinks
  }

  get newImportFlow(): boolean {
    return this._data.new_import_flow
  }

  set newImportFlow(flag: boolean) {
    this._data.new_import_flow = flag
  }

  get relationshipNumber(): String {
    return this._data.relationship_number
  }

  set relationshipNumber(relationshipNumber: String) {
    this._data.relationship_number = relationshipNumber
  }
}
