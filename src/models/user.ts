import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import store from '../store'
import { AuthUser } from '../models'
import axios from 'axios'
import localStorage from 'store'
import moment from 'moment'

const bcrypt = require('bcryptjs')

const USER_SMARTLIST_SYNC_API = '/user/prepare_global_lists'
const USER_SMARTLIST_REMOVE_API = '/user/remove_lists'

export enum UserSource {
  Agency_Team = "agency_team", // Agency => Settings => Team Manage page
  Location_Team = "location_team",  // Location => Settings => Team Manage page
  LaunchPad = "launchPad",
  THIRD_PARTY = "third_party", // by third party apps by using public api
  MOBILE_APP = "mobile_app",
}

export enum UserSourceChannel {
  WEB_APP = "web_app", // GHL Web App - Directly communicates to firestore API in most cases - (spm-ts repo)
  PUBLIC_API = "public_api", // GHL Public API - This is used by third parties - (ghl-public-apis repo)  TRIGGER = 'trigger', // GHL trigger action update appointment status is used to update appointment status
}

export interface CreatedOrUpdatedOrDeletedBy {
  userId?: string;
  source: UserSource;
  channel: UserSourceChannel;
  timestamp: firebase.firestore.Timestamp;
}

export interface Permissions {
  campaigns_enabled?: boolean
  campaigns_read_only?: boolean
  contacts_enabled?: boolean
  workflows_enabled?: boolean
  workflows_read_only?: boolean
  triggers_enabled?: boolean
  funnels_enabled?: boolean
  websites_enabled?: boolean
  opportunities_enabled?: boolean
  dashboard_stats_enabled?: boolean
  bulk_requests_enabled?: boolean
  appointments_enabled?: boolean
  reviews_enabled?: boolean
  online_listings_enabled?: boolean
  phone_call_enabled?: boolean
  conversations_enabled?: boolean
  assigned_data_only?: boolean
  adwords_reporting_enabled?: boolean
  membership_enabled?: boolean
  facebook_ads_reporting_enabled?: boolean
  attributions_reporting_enabled?: boolean
  agent_reporting_enabled?: boolean
  bot_service?: boolean
  settings_enabled?: boolean
}

export interface ISaasSettings {
  stripe_connect_initiated: boolean
}

const indexwiseWeekdays = {
  '0': 'Sunday',
  '1': 'Monday',
  '2': 'Tuesday',
  '3': 'Wednesday',
  '4': 'Thursday',
  '5': 'Friday',
  '6': 'Saturday',
}

export class PrivateUser {
  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(data: firebase.firestore.DocumentSnapshot | User) {
    if (data instanceof firebase.firestore.DocumentSnapshot) {
      this._id = data.id
      this._ref = data.ref
      this._data = data.data()
    } else {
      this._ref = data.privateDataRef
      this._id = this._ref.id
      this._data = {}
    }
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get passwordHash(): string {
    return this._data['password_hash']
  }

  set passwordHash(passwordHash: string) {
    this._data['password_hash'] = bcrypt.hashSync(passwordHash, 10)
  }

  get isEmailVerified(): boolean {
    return this._data['is_email_verified']
  }

  set isEmailVerified(isEmailVerified: boolean) {
    this._data['is_email_verified'] = isEmailVerified
  }

  get emailVerificationToken(): boolean {
    return this._data['email_verification_token']
  }

  set emailVerificationToken(emailVerificationToken: boolean) {
    this._data['email_verification_token'] = emailVerificationToken
  }

  get passwordResetToken(): string {
    return this._data['password_reset_token']
  }

  set passwordResetToken(passwordResetToken: string) {
    this._data['password_reset_token'] = passwordResetToken
  }

  get smsOTP(): string {
    return this._data['sms_otp']
  }

  set smsOTP(smsOTP: string) {
    this._data['sms_otp'] = smsOTP
  }

  verifyPassword(stringPassword: string) {
    return bcrypt.compareSync(stringPassword, this.passwordHash)
  }

  public save(): Promise<PrivateUser> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self._ref.set(_self.data, { merge: true }).then(_ => {
        resolve(_self)
      })
    })
  }
}

export default class User {
  public static ROLE_ADMIN = 'admin'
  public static ROLE_USER = 'user'

  public static TYPE_AGENCY = 'agency'
  public static TYPE_ACCOUNT = 'account'

  public previousRole = null

  public static getAllUsers(companyId: string): firebase.firestore.Query {
    return User.collectionRef()
      .where('company_id', '==', companyId)
      .where('deleted', '==', false)
      .orderBy('first_name_lower_case')
  }

  public static async fetchAllUsers(): Promise<firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get')
    return User.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', auth.companyId)
      .orderBy('first_name_lower_case')
  }

  public static async fetchAllLocationUsers(
    locationId: string
  ): Promise<firestore.Query> {
    return User.collectionRef().where('locations.' + locationId, '>=', '')
  }

  public static async fetchAllAgencyUsers(): Promise<firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get')
    return User.collectionRef()
      .where('deleted', '==', false)
      .where('type', '==', User.TYPE_AGENCY)
      .where('company_id', '==', auth.companyId)
      .orderBy('first_name_lower_case')
  }

  // public static getById(userId: string): Promise<User> {
  //     return new Promise((resolve, reject) => {
  //         User.collectionRef()
  //             .doc(userId)
  //             .get()
  //             .then((snapshot:firebase.firestore.DocumentSnapshot) => {
  //                 resolve(new User({ ...snapshot.data(), id: snapshot.id }));
  //             });
  //     });
  // }

  public static getById(userId: string): Promise<User> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
        .doc(userId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists)
            resolve(new User({ ...snapshot.data(), id: snapshot.id }))
          resolve()
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static findUserByEmail(
    companyId: string,
    email: string
  ): Promise<User> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
        .where('company_id', '==', companyId)
        .where('email', '==', email)
        .where('is_active', '==', true)
        .limit(1)
        .get()
        .then(snapshot => {
          if (snapshot.empty) reject()
          resolve(new User(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByLocation(locationId: string): Promise<User[]> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
        .where('locations.' + locationId, '>=', '')
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new User(d)))
        })
        .catch(err => reject(err))
    })
  }

  public static getAgencyUsersByCompanyId(companyId: string): Promise<User[]> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
      .where('deleted', '==', false)
      .where('type', '==', User.TYPE_AGENCY)
      .where('company_id', '==', companyId)
      .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new User(d)))
        })
        .catch(err => reject(err))
    })
  }

  public static getByLocationCompany(
    locationId: string,
    companyId: string
  ): Promise<User[]> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
        .where('company_id', '==', companyId)
        .where('locations.' + locationId, '==', '')
        .where('role', '==', this.ROLE_ADMIN)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new User(d)))
        })
        .catch(err => reject(err))
    })
  }

  public static getByPhone(locationId: string, phone: string): Promise<User[]> {
    return new Promise((resolve, reject) => {
      User.collectionRef()
        .where('locations.' + locationId, '==', '')
        .where('phone', '==', phone)
        .where('deleted', '==', false)
        .limit(1)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new User(d)))
        })
        .catch(err => reject(err))
    })
  }

  public static async getFilteredSortedUsersByLocation(
    locationId: string
  ): Promise<User[]> {
    const users = await User.getByLocation(locationId)
    const activeAccountUsers: User[] = lodash.filter(users, { deleted: false })
    return lodash.sortBy(activeAccountUsers, ['firstName'])
  }

  public static getStreamById(
    id: string
  ): firebase.firestore.DocumentReference {
    return User.collectionRef().doc(id)
  }

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('users')
  }

  // private static colours: string[] = [
  //     '#5A8770',
  //     '#B2B7BB',
  //     '#6FA9AB',
  //     '#F5AF29',
  //     '#0088B9',
  //     '#F18636',
  //     '#D93A37',
  //     '#A6B12E',
  //     '#5C9BBC',
  //     '#F5888D',
  //     '#9A89B5',
  //     '#407887',
  //     '#9A89B5',
  //     '#5A8770',
  //     '#D33F33',
  //     '#A2B01F',
  //     '#F0B126',
  //     '#0087BF',
  //     '#F18636',
  //     '#0087BF',
  //     '#B2B7BB',
  //     '#72ACAE',
  //     '#9C8AB4',
  //     '#5A8770',
  //     '#EEB424',
  //     '#407887',
  // ];

  private static colours: string[] = [
    '#188bf6',
    '#1976d2',
    '#e93d3d',
    '#ffbc00',
    '#37ca37',
    '#876cff',
    '#17cfbc',
    '#ff3e7f',
    '#ff7402',
    '#7be43b',
  ]

  private _data: { [key: string]: any }
  private _id: string
  private _ref: firebase.firestore.DocumentReference

  constructor(
    params?: { [key: string]: any } | firebase.firestore.DocumentSnapshot
  ) {
    if (params instanceof firebase.firestore.DocumentSnapshot) {
      this._id = params.id
      this._ref = params.ref
      this._data = params.data() || {}
    } else if (params) {
      this._data = params
      this._id = params.id
      this._ref = User.collectionRef().doc(this._id)
    } else {
      this._ref = User.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this._data['locations'] = {}
      this._data['isPasswordPending'] = false
      this.dateAdded = moment().utc()
      this.dateUpdated = moment().utc()
      this.isActive = true
      this.deleted = false
      this.role = User.ROLE_ADMIN
    }

    this.locationWiseMeetingLocation = this.locationWiseMeetingLocation || {}
    this.locationWiseZoomAdded = this.locationWiseZoomAdded || {}
    this.locationWiseTimezone = this.locationWiseTimezone || {}
    this.locationWiseOpenHours = this.locationWiseOpenHours || {}
    if (this.isUserAvailabilityOn === undefined) {
      this.isUserAvailabilityOn = true
    }
    this.isUserAvailabilityOn = this.isUserAvailabilityOn

    for (const location in this.locationWiseTimezone) {
      if (['Etc/Greenwich', 'GMT'].includes(this.locationWiseTimezone[location])) this.locationWiseTimezone[location] = 'UTC'
    }
  }

  get id(): string {
    return this._id
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get privateDataRef(): firebase.firestore.DocumentReference {
    return this._ref.collection('private_info').doc('data')
  }

  get isActive(): boolean {
    return this._data['is_active']
  }

  set isActive(isActive: boolean) {
    this._data['is_active'] = isActive
  }

  /**
   * Whether default provider and service settings view is card view or tabler view
   * Saving state of user's choice for subsequent requests
   */
  get isCalendarSettingsViewCardView(): boolean {
    return this._data.is_calendar_settings_view_card_view !== undefined
      ? this._data.is_calendar_settings_view_card_view
      : true
  }

  set isCalendarSettingsViewCardView(value: boolean) {
    this._data.is_calendar_settings_view_card_view = value
  }

  get isCalendarPagePreviousVersionOn(): boolean {
    return this._data.is_calendar_page_previous_versionOn !== undefined
      ? this._data.is_calendar_page_previous_versionOn
      : false
  }

  set isCalendarPagePreviousVersionOn(value: boolean) {
    this._data.is_calendar_page_previous_versionOn = value
  }

  get isAppointmentsPagePreviousVersionOn(): boolean {
    return this._data.is_appointments_page_previous_versionOn !== undefined
      ? this._data.is_appointments_page_previous_versionOn
      : false
  }

  set isAppointmentsPagePreviousVersionOn(value: boolean) {
    this._data.is_appointments_page_previous_versionOn = value
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated)
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  get firstName(): string {
    return this._data.first_name
  }

  set firstName(firstName: string) {
    this._data.first_name = firstName
    this._data.first_name_lower_case = firstName.toLowerCase()
  }

  get firstNameLowerCase(): string {
    return this._data.first_name_lower_case
  }

  get lastName(): string {
    return this._data.last_name
  }

  get lastLoginTime(): moment.Moment | undefined {
    return this._data['last_login_time'] ? moment(this._data['last_login_time'].toMillis()) : undefined
  }

  set lastLoginTime(lastLoginTime: moment.Moment) {
    this._data['last_login_time'] = lastLoginTime ? firebase.firestore.Timestamp.fromMillis(lastLoginTime.valueOf()) : undefined
  }

  set lastName(lastName: string) {
    this._data.last_name = lastName
    this._data.last_name_lower_case = lastName.toLowerCase()
  }

  get lastNameLowerCase(): string {
    return this._data.last_name_lower_case
  }

  get name(): string {
    return [this.firstName, this.lastName].filter(val => val).join(' ')
  }

  get fullName(): string {
    return [this.title, this.firstName, this.lastName].filter(d => d).join(' ')
  }

  get shortName(): string {
    return [this.title, this.lastName].filter(d => d).join(' ')
  }

  get localeString(): string {
    return this._data.locale_string
  }

  set localeString(localeString: string) {
    this._data.locale_string = localeString
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
    if (email) {
      this._data.email_lower_case = email.toLowerCase()
    }
  }

  get phone(): string {
    return this._data.phone
  }

  set phone(phone: string) {
    this._data.phone = phone
  }

  get twilioPhone(): { [key: string]: string } {
    if (!this._data.twilio_phone) this._data.twilio_phone = {}
    return this._data.twilio_phone
  }

  set twilioPhone(values: { [key: string]: string }) {
    this._data.twilio_phone = values
  }

  get userCalendar(): { [key: string]: string } {
    if (!this._data.user_calendar) this._data.user_calendar = {}
    return this._data.user_calendar
  }

  set userCalendar(values: { [key: string]: string }) {
    this._data.user_calendar = values
  }

  get userEmailSignature(): { [key: string]: {} } {
    if (!this._data.user_email_signature) this._data.user_email_signature = {}
    return this._data.user_email_signature
  }

  set userEmailSignature(values: { [key: string]: {} }) {
    this._data.user_email_signature = values
  }

  get extension(): string {
    return this._data.extension
  }

  set extension(extension: string) {
    this._data.extension = extension
  }

  get lastKnownLocation(): firebase.firestore.GeoPoint {
    return this._data.last_known_location
  }

  set lastKnownLocation(location: firebase.firestore.GeoPoint) {
    this._data.last_known_location = location
  }

  get payRate(): string {
    return this._data.pay_rate
  }

  set payRate(payRate: string) {
    this._data.pay_rate = payRate
  }

  get role(): string {
    return this._data.role
  }

  set role(role: string) {
    this._data.role = role
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  get title(): string {
    return this._data.title
  }

  set title(title: string) {
    this._data.title = title
  }

  get ssn(): string {
    return this._data.ssn
  }

  set ssn(ssn: string) {
    this._data.ssn = ssn
  }

  get address1(): string {
    return this._data.address1
  }

  set address1(address1: string) {
    this._data.address1 = address1
  }
  get city(): string {
    return this._data.city
  }

  set city(city: string) {
    this._data.city = city
  }
  get state(): string {
    return this._data.state
  }

  set state(state: string) {
    this._data.state = state
  }
  get postalCode(): string {
    return this._data.postal_code
  }

  set postalCode(postalCode: string) {
    this._data.postal_code = postalCode
  }
  get country(): string {
    return this._data.country
  }

  set country(country: string) {
    this._data.country = country
  }

  get addressGeo(): firebase.firestore.GeoPoint {
    return this._data.address_geo
  }

  set addressGeo(addressGeo: firebase.firestore.GeoPoint) {
    this._data.address_geo = addressGeo
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get dirty(): boolean {
    return this._data.dirty
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty
  }

  /**
   * Location wise timezone
   * Ex: {'locationId': location specific timezone}
   * */
  get locationWiseTimezone(): { [key: string]: string } {
    return this._data.location_wise_timezone;
  }

  set locationWiseTimezone(value: { [key: string]: string }) {
    this._data.location_wise_timezone = value;
  }

  /**
   * Location wise meeting location
   * Ex: {'locationId': location specific meeting location}
   * */
  get locationWiseMeetingLocation(): { [key: string]: string } {
    return this._data.location_wise_meeting_location
  }

  set locationWiseMeetingLocation(value: { [key: string]: string }) {
    this._data.location_wise_meeting_location = value
  }

  get locationWiseZoomAdded(): { [key: string]: string } {
    return this._data.location_wise_zoom_added
  }

  set locationWiseZoomAdded(value: { [key: string]: string }) {
    this._data.location_wise_zoom_added = value
  }

  get isUserAvailabilityOn(): boolean {
    return true // We've decided to make this flag always turned on.
    // return this._data.is_user_vailability_on
  }

  set isUserAvailabilityOn(value: boolean) {
    this._data.is_user_vailability_on = value
  }

  get isPasswordPending(): boolean {
    return this._data.isPasswordPending
  }

  set isPasswordPending(value: boolean) {
    this._data.isPasswordPending = value
  }

  /**
   * Location wise open hours
   * Ex: {'locationId': location open hours}
   * */
  get locationWiseOpenHours(): { [key: string]: { [key: string]: any }[] } {
    return this._data.location_wise_open_hours
  }

  set locationWiseOpenHours(value: {
    [key: string]: { [key: string]: any }[]
  }) {
    this._data.location_wise_open_hours = value
  }

  get locationWiseDayOfTheWeekwiseAvailability(): {
    [key: string]: { [key: string]: any }
  } {
    const _locationWiseDayOfTheWeekwiseAvailability: {
      [key: string]: { [key: string]: any }
    } = {}
    Object.keys(this.locationWiseOpenHours).forEach(locationId => {
      const _dayOfTheWeekwiseAvailability = {}
      if (this.locationWiseOpenHours[locationId]) {
        this.locationWiseOpenHours[locationId].forEach(x => {
          _dayOfTheWeekwiseAvailability[indexwiseWeekdays[x.days_of_the_week]] =
            x.hours
        })
      }
      _locationWiseDayOfTheWeekwiseAvailability[
        locationId
      ] = _dayOfTheWeekwiseAvailability
    })
    return _locationWiseDayOfTheWeekwiseAvailability
  }

  repeatTheOpenHoursForOtherWeekDays(locationId: string, dayOfWeek: number) {
    const pickedDayOfWeekOpenHours = this.locationWiseOpenHours[
      locationId
    ].find(x => x.days_of_the_week[0] === dayOfWeek)
    this.locationWiseOpenHours[locationId] = this.locationWiseOpenHours[
      locationId
    ].map(x => {
      return {
        days_of_the_week: x.days_of_the_week,
        hours: lodash.cloneDeep(pickedDayOfWeekOpenHours.hours),
      }
    })
  }

  get locations(): { [key: string]: string } {
    return this._data.locations
  }

  set locations(locations: { [key: string]: string }) {
    this._data.locations = locations
  }

  get profilePhoto(): string {
    return this._data.profile_photo
  }

  set profilePhoto(profilePhoto: string) {
    this._data.profile_photo = profilePhoto
  }

  get emailProviderId(): string {
    return this._data.email_provider_id
  }

  set emailProviderId(emailProviderId: string) {
    this._data.email_provider_id = emailProviderId
  }

  get emailSignature(): string {
    return this._data.email_signature
  }

  set emailSignature(emailSignature: string) {
    this._data.email_signature = emailSignature
  }

  set calendar(calendar: string) {
    this._data.calendar = calendar
  }

  get calendar(): string {
    return this._data.calendar
  }

  set postedForSaas(postedForSaas: boolean) {
    this._data.posted_for_saas = postedForSaas
  }

  get postedForSaas(): boolean {
    return this._data.posted_for_saas
  }

  set saasSettings(saasSettings: ISaasSettings) {
    this._data['saas_settings'] = saasSettings
  }

  get saasSettings(): ISaasSettings {
    return this._data['saas_settings']
  }

  get lastPasswordChange(): moment.Moment {
    return moment(this._data.last_password_change.toMillis())
  }

  public async passwordChanged() {
    localStorage.set('loginDate', moment().toDate()) // Prevent user from being logged out from current session when changing password
    await this.ref.update({
      last_password_change: firebase.firestore.Timestamp.now(),
      attempt: 0,
      isPasswordPending: false,
    })
  }

  public addLocation(locationId: string, notificationType: string) {
    if (!this._data.locations) {
      this._data.locations = {}
    }
    this._data.locations[locationId] = notificationType
  }

  public removeLocation(locationId: string) {
    delete this._data.locations[locationId]
  }

  get initials(): string {
    let initials = ''
    if (this.firstName) initials += this.firstName.substring(0, 1).toUpperCase()
    if (this.lastName) initials += this.lastName.substring(0, 1).toUpperCase()
    return initials
  }

  get permissions(): Permissions {
    if (!this._data.permissions)
      this._data.permissions = {
        campaigns_enabled: true,
        campaigns_read_only: false,
        workflows_enabled: true,
        workflows_read_only: false,
        contacts_enabled: true,
        triggers_enabled: true,
        opportunities_enabled: true,
        settings_enabled: true,
        tags_enabled: true,
        lead_value_enabled: true,
        dashboard_stats_enabled: true,
        bulk_requests_enabled: true,
        appointments_enabled: true,
        reviews_enabled: true,
        online_listings_enabled: true,
        phone_call_enabled: true,
        conversations_enabled: true,
        marketing_enabled: true,
        bot_service: false,
        websites_enabled: true,
        membership_enabled: true,
      }
    if (this._data.permissions.funnels_enabled === undefined) {
      this._data.permissions.funnels_enabled = true
    }

    if (this._data.permissions.websites_enabled === undefined) {
      this._data.permissions.websites_enabled =
        this.type === User.TYPE_AGENCY && this.role === User.ROLE_ADMIN
    }

    if (this._data.permissions.membership_enabled === undefined) {
      this._data.permissions.membership_enabled =
        this.type === User.TYPE_AGENCY && this.role === User.ROLE_ADMIN
    }

    if (this._data.permissions.attributions_reporting_enabled === undefined) {
      this._data.permissions.attributions_reporting_enabled =
        this.type === User.TYPE_AGENCY && this.role === User.ROLE_ADMIN
    }

    return this._data.permissions
  }

  set permissions(permissions: Permissions) {
    this._data.permissions = permissions
  }

  // get locationPermissions(): { [locationId: string]: Permissions } {
  //   return this._data.location_permissions || {}
  // }

  // set locationPermissions(allLocationPermissions: {
  //   [locationId: string]: Permissions
  // }) {
  //   this._data.location_permissions = allLocationPermissions
  // }

  get isAdmin(): boolean {
    return this.role === 'admin'
  }

  get canAccessAll(): boolean {
    return this.permissions.assigned_data_only !== true
  }

  get isAssignedTo(): boolean {
    return !this.isAdmin && !this.canAccessAll
  }

  get canViewOpportunities(): boolean {
    return (
      this.isAdmin ||
      (this.permissions && this.permissions.opportunities_enabled)
    )
  }
  get canCreateReviewRequest(): boolean {
    return (
      this.isAdmin || (this.permissions && this.permissions.reviews_enabled)
    )
  }
  get canViewAppointments(): boolean {
    return (
      this.isAdmin ||
      (this.permissions && this.permissions.appointments_enabled)
    )
  }

  set createdBy(value: CreatedOrUpdatedOrDeletedBy) {
    this._data.created_by = value
  }

  get lastUpdatedBy(): CreatedOrUpdatedOrDeletedBy {
    return this._data.last_updated_by
  }

  set lastUpdatedBy(value: CreatedOrUpdatedOrDeletedBy) {
    this._data.last_updated_by = value
  }

  get deletedBy(): CreatedOrUpdatedOrDeletedBy {
    return this._data.deleted_by
  }

  set deletedBy(value: CreatedOrUpdatedOrDeletedBy) {
    this._data.deleted_by = value
  }

  public async setCreatedByAndOrLastUpdatedBy(
    isNewUser: boolean,
    source: UserSource,
    channel: UserSourceChannel,
    timestamp: moment.Moment = moment(),
    userId?: string,
  ) {
    const auth: AuthUser = await store.dispatch('auth/get')

    if (isNewUser) {
      this.createdBy = {
        userId: userId || auth?.userId,
        source,
        channel,
        timestamp: firebase.firestore.Timestamp.fromMillis(timestamp.valueOf())
      }
    }
    this.lastUpdatedBy = {
      userId: userId || auth?.userId,
      source,
      channel,
      timestamp: firebase.firestore.Timestamp.fromMillis(timestamp.valueOf())
    }

    if (this.deleted) {
      this.deletedBy = {
        userId: userId || auth?.userId,
        source,
        channel,
        timestamp: firebase.firestore.Timestamp.fromMillis(timestamp.valueOf())
      }
    }
  }

  // get profileColor(): string {
  // 	const firstCharIndex = (this.firstName || 'A').toUpperCase().charCodeAt(0);
  // 	const secondCharIndex = (this.lastName || 'A').toUpperCase().charCodeAt(0);
  // 	const colourIndex = Math.floor((firstCharIndex + secondCharIndex) / 2) % 25;
  // 	return User.colours[colourIndex];
  // }

  get profileColor(): string {
    let str = this.fullName
    if (this.phone) str += this.phone
    if (this.email) str += this.email
    let hash = 0
    if (str.length == 0) return '#afb8bc'
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash)
      hash = hash & hash // Convert to 32bit integer
    }
    // var colourIndex = Math.abs(hash) % 10;
    // return User.colours[colourIndex];
    const shortened = Math.abs(hash) % 360
    return 'hsl(' + shortened + ',35%, 60%)'
  }

  public save(): Promise<User> {
    const _self = this
    return new Promise((resolve, reject) => {
      this.dateUpdated = moment().utc()
      this.dirty = true
      this._ref
        .set(this.data)
        .then(() => {
          _self.previousRole = _self.role
          resolve(_self)
        })
        .catch(err => reject(err))
    })
  }

  public async syncGlobalListsForAllLocation() {
    await axios.post(USER_SMARTLIST_SYNC_API, { user_id: this.id })
  }

  public async syncGlobalListsForLocation(locationId: string) {
    if (!locationId) return
    await axios.post(USER_SMARTLIST_SYNC_API, {
      user_id: this.id,
      location_id: locationId,
    })
  }

  public async removeAllSmartListsForLocation(locationId: string) {
    if (!locationId) return
    await axios.post(USER_SMARTLIST_REMOVE_API, {
      user_id: this.id,
      location_id: locationId,
      remove_all: true,
    })
  }

  public async removeGlobalListsForLocation(locationId: string) {
    if (!locationId) return
    await axios.post(USER_SMARTLIST_REMOVE_API, {
      user_id: this.id,
      location_id: locationId,
      remove_global: true,
    })
  }

  get voicemailFile(): { [key: string]: string } {
    if (!this._data.voicemail_file) this._data.voicemail_file = {}
    return this._data.voicemail_file
  }

  set voicemailFile(voicemailFile: { [key: string]: string }) {
    this._data.voicemail_file = voicemailFile
  }

  get incomingCallTimeout(): { [key: string]: number } {
    if (!this._data.incoming_call_timeout) this._data.incoming_call_timeout = {}
    return this._data.incoming_call_timeout
  }

  set incomingCallTimeout(incomingCallTimeout: { [key: string]: number }) {
    this._data.incoming_call_timeout = incomingCallTimeout
  }

  get token(): string {
    return this._data['token']
  }

  set token(token: string) {
    this._data['token'] = token
  }

  get launchpadCompletedDate(): moment.Moment | undefined {
    return this._data['launchpad_completed_date']
      ? moment(this._data['launchpad_completed_date'].toMillis())
      : undefined
  }

  set launchpadCompletedDate(date: moment.Moment | undefined) {
    if (date) {
      this._data[
        'launchpad_completed_date'
      ] = firebase.firestore.Timestamp.fromMillis(date.valueOf())
    } else {
      this._data['launchpad_completed_date'] = date
    }
  }

  get automationPermission(): { campaigns: boolean; workflows: boolean } {
    const response = {
      campaigns: true,
      workflows: true,
    }
    if (this.permissions) {
      response.campaigns = this.permissions.campaigns_enabled
      if (this.permissions.workflows_read_only !== undefined) response.workflows = this.permissions.workflows_enabled
      else response.workflows = this.permissions.campaigns_enabled // If there is no setting for Workflow permission, replicate Campaign permission
    }
    return response
  }

  get loginCount(): number {
    return this._data['login_count'] ? this._data['login_count'] : 0
  }

  set loginCount(count: number) {
    this._data['login_count'] = count
  }

  public automationString(plural = true): string {
    let string = 'Campaigns / Workflows'
    if (this.permissions) {
      if (
        this.automationPermission.campaigns &&
        this.automationPermission.workflows
      )
        string = 'Campaigns / Workflows'
      else if (this.automationPermission.campaigns) string = 'Campaigns'
      else if (this.automationPermission.workflows) string = 'Workflows'
    }

    if (!plural) string = string.replaceAll('s', '')

    return string
  }

  get sideBarVersion(): string | undefined {
    return this._data.sidebar_version
  }

  set sideBarVersion(version: string) {
    this._data.sidebar_version = version
  }

  get pinnedLocations(): string[] | undefined {
    return this._data.pinned_locations || []
  }

  set pinnedLocations(locations: string[]) {
    this._data.pinned_locations = locations
  }
}
