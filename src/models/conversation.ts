import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { MessageType } from '../models'
import config from '../config'
import axios from 'axios'
import store from '../store'
​
export default class Conversation {
  public static TYPE_PHONE = 1
  public static TYPE_EMAIL = 2
  public static TYPE_FB_MESSENGER = 3
  public static TYPE_REVIEW = 4
​
  public static collectionRef() {
    return firebase.firestore().collection('conversations')
  }
​
  public static getStreamById(
    id: string
  ): firebase.firestore.DocumentReference {
    return Conversation.collectionRef().doc(id)
  }
​
  public static getAllConversations(
    locationId: string,
    inbox: boolean
  ): firebase.firestore.Query {
    let query = Conversation.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
    if (inbox) {
      query = query.where('inbox', '==', true)
    }
    return query.orderBy('last_message_date', 'desc')
  }
​
  public static getAllConversationsRealtime(
    locationId: string,
    inbox?: boolean
  ): firebase.firestore.Query {
    let query = Conversation.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
    if (inbox) {
      query = query.where('inbox', '==', true)
    }
    return query.orderBy('date_updated', 'desc')
  }
​
  public static getById(id: string): Promise<Conversation> {
    return new Promise((resolve, reject) => {
      Conversation.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Conversation(snapshot))
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
​
  public static getByContactId(contactId: string, locationId: string): Promise<Conversation[]> {
    return new Promise((resolve, reject) => {
      Conversation.collectionRef()
        .where('deleted', '==', false)
        .where('contact_id', '==', contactId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(d => new Conversation(d)))
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
​
  public static getByContactIdRealTime(
    contactId: string,
    locationId: string
  ): firebase.firestore.Query {
    return Conversation.collectionRef()
      .where('deleted', '==', false)
      .where('contact_id', '==', contactId)
      .where('location_id', '==', locationId)
  }
​
  public static getByContactIdAndType(
    contactId: string,
    type: number,
    locationId: string
  ): Promise<Conversation | undefined> {
    return new Promise((resolve, reject) => {
      Conversation.collectionRef()
        .where('deleted', '==', false)
        .where('contact_id', '==', contactId)
        .where('type', '==', type)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          else if (snapshot.size > 1) reject()
          resolve(new Conversation(snapshot.docs[0]))
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
​
  public static async getConversationsFromES(
    locationId: string,
    inbox: boolean,
    term: string,
    startAfterDate: undefined | number,
    assignedTo: undefined | string,
    contactId?: undefined | string,
    unread?: boolean
  ): Promise<Conversation[]> {
    let url = `${config.baseUrl}/search/conversation/${
      inbox ? 'inbox' : 'all'
    }?location_id=${locationId}&conversation_type=TYPE_PHONE,TYPE_REVIEW`
​
    if (term) {
      url += `&q=${term}`
    }
​
    if (startAfterDate) {
      url += `&startAfterDate=${startAfterDate}`
    }
​
    if (assignedTo) {
      url += `&assigned_to=${assignedTo}`
    }
​
    if (contactId) {
      url += `&contact_id=${contactId}`
    }

    if (unread) {
      url += `&unread=${unread}`
    }
    return axios
      .get(url)
      .then(response => {
        return response.data.hits.hits
      })
      .catch(err => {
        return err
      })
  }
​
  public static async getConversationsByIdES(
    locationId: string,
    id: undefined | string
  ): Promise<Conversation[]> {
    let url = `${
      config.baseUrl
    }/search/conversation/all?location_id=${locationId}`
​
    if (id) {
      url += `&id=${id}`
    }
    return axios
      .get(url)
      .then(response => {
        return response.data.hits.hits
      })
      .catch(err => {
        return err
      })
  }
​
  public static create(
    locationId: string,
    contactId: string,
    type: number,
    extras?: {
      channel?: string
      lastMessageBody?: string
      lastMessageType?: MessageType
      assignedTo?: string
    }
  ): Conversation {
    const conversation = new Conversation()
    conversation.contactId = contactId
    conversation.locationId = locationId
    conversation.type = type
    if (extras && extras.channel) conversation.channel = extras.channel
    if (extras && extras.lastMessageBody)
      conversation.lastMessageBody = extras.lastMessageBody
    if (extras && extras.lastMessageType) {
      conversation.lastMessageType = extras.lastMessageType
      if (
        [
          MessageType.TYPE_SMS,
          MessageType.TYPE_CALL,
          MessageType.TYPE_EMAIL,
          MessageType.TYPE_WEBCHAT,
          MessageType.TYPE_FACEBOOK
        ].indexOf(extras.lastMessageType) !== -1
      )
        conversation.inbox = true
    }
    if (extras && extras.assignedTo)
      conversation.assignedTo = extras.assignedTo
    return conversation
  }
​
  public static async createAndSave(
    locationId: string,
    contactId: string,
    type: number,
    extras?: {
      channel?: string
      lastMessageBody?: string
      lastMessageType?: MessageType,
      assignedTo?: string
    }
  ): Promise<Conversation> {
    const conversation = Conversation.create(
      locationId,
      contactId,
      type,
      extras
    )
    await conversation.save()
    return conversation
  }
​
  public static getByLocationContactType(
    locationId: string,
    type: number
  ): firebase.firestore.Query {
    return Conversation.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('type', '==', type)
  }
​
  public static last(
    fn
  ): (
    locationId: string,
    inbox: boolean,
    term: string
  ) => Promise<Conversation[]> {
    var lastToken = { cancel: function() {} } // start with no op
    return function() {
      lastToken.cancel()
      var args = Array.prototype.slice.call(arguments)
      args.push(lastToken)
      return fn.apply(this, args)
    }
  }
​
  private _data: firebase.firestore.DocumentData
  private _id: string
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot
​
  constructor(
    snapshot?: firebase.firestore.DocumentSnapshot | { [key: string]: any }
  ) {
    if (snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
      this._snapshot = snapshot
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot
      this._ref = Conversation.collectionRef().doc(snapshot.id)
      this._data.date_added = new firebase.firestore.Timestamp(
        Math.floor(snapshot.date_added / 1000),
        0
      )
      this._data.date_updated = new firebase.firestore.Timestamp(
        Math.floor(snapshot.date_updated / 1000),
        0
      )
      this._data.last_message_date = new firebase.firestore.Timestamp(
        Math.floor(snapshot.last_message_date / 1000),
        0
      )
    } else {
      this._ref = Conversation.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment().utc()
    }
  }
​
  get id(): string {
    return this._id
  }
​
  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }
​
  get snapshot() {
    return this._snapshot
  }
​
  get data(): firebase.firestore.DocumentData {
    return this._data
  }
​
  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }
​
  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }
​
  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }
​
  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }
​
  get lastMessageDateTimestamp(): number {
    return this._data.last_message_date
  }
​
  get lastMessageDate(): moment.Moment {
    return moment(this._data.last_message_date.toMillis())
  }
​
  set lastMessageDate(lastMessageDate: moment.Moment) {
    this._data.last_message_date = firebase.firestore.Timestamp.fromMillis(
      lastMessageDate.valueOf()
    )
  }
​
  get deleted(): boolean {
    return this._data.deleted
  }
​
  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }
​
  get locationId(): string {
    return this._data.location_id
  }
​
  set locationId(locationId: string) {
    this._data.location_id = locationId
  }
​
  get contactId(): string {
    return this._data.contact_id
  }
​
  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }
​
  get userId(): string {
    return this._data.user_id
  }
​
  set userId(userId: string) {
    this._data.user_id = userId
  }
​
  get assignedTo(): string {
    return this._data.assigned_to
  }
​
  set assignedTo(assignedTo: string) {
    this._data.assigned_to = assignedTo
  }
​
  get type(): number {
    return this._data.type
  }
​
  set type(type: number) {
    this._data.type = type
  }
​
  get altId(): string {
    return this._data.alt_id
  }
​
  set altId(altId: string) {
    this._data.alt_id = altId
  }
​
  get channel(): string {
    return this._data.channel
  }
​
  set channel(channel: string) {
    this._data.channel = channel
  }
​
  get lastMessageBody(): string {
    return this._data.last_message_body
  }
​
  set lastMessageBody(lastMessageBody: string) {
    this._data.last_message_body = lastMessageBody
  }
​
  get lastMessageType(): MessageType {
    return this._data.last_message_type
  }
​
  set lastMessageType(lastMessageType: MessageType) {
    this._data.last_message_type = lastMessageType
  }
​
  get unreadCount(): number {
    return this._data.unread_count || 0
  }
​
  set unreadCount(unreadCount: number) {
    this._data.unread_count = unreadCount
  }

  get reviewId(): string {
    return this._data.review_id;
  }

  set reviewId(reviewId: string) {
    this._data.review_id = reviewId;
  }

  get gmbPageId(): string {
    return this._data.gmb_page_id;
  }

  set gmbPageId(gmbPageId: string) {
    this._data.gmb_page_id = gmbPageId;
  }

  get reviewerName(): string {
    return this._data.reviewer_name;
  }

  set reviewerName(reviewerName: string) {
    this._data.reviewer_name = reviewerName;
  }

  get starRating(): number {
    return this._data.star_rating;
  }

  set starRating(starRating: number) {
    this._data.star_rating = starRating;
  }

  public async toggleRead() {
    if (this.unreadCount === 0) {
      this.unreadCount = 1
      this.ref.update({
        unread_count: 1,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
    } else {
      this.unreadCount = 0
      store.dispatch('conversation/markAsReadRemoveFromUnreads', { conversationId: this.id })
      this.ref.update({
        unread_count: 0,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
    }
  }
​
  public async toggleArchive() {
    if (this.inbox) {
      this.inbox = false
      this.ref.update({
        inbox: false,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
    } else {
      this.inbox = true
      this.ref.update({
        inbox: true,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
    }
  }
​
//   public async archive() {
//     this.inbox = false
//     await this.save()
//   }
// ​
//   public async unarchive() {
//     this.inbox = true
//     await this.save()
//   }
​
  get inbox(): boolean {
    return this._data.inbox
  }
​
  set inbox(inbox: boolean) {
    this._data.inbox = inbox
  }
​
  public save(): Promise<Conversation> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.data.date_updated = firebase.firestore.FieldValue.serverTimestamp();
      _self._ref.set(this._data, { merge: true }).then(() => {
        resolve(_self)
      })
    })
  }
​
  public delete() {
    return Conversation.collectionRef()
      .doc(this.id)
      .update({
        deleted: true
      })
  }
}
