import firebase from 'firebase/app';
import * as moment from 'moment-timezone';
import Location from './location';
import { Window, CampaignTemplate, EmailTemplate, SMSTemplate, CallTemplate, VoicemailTemplate } from './campaign';

export enum CampaignActionStatus {
	QUEUED = 'queued',
	FINISHED = 'finished',
	CANCELLED = 'cancelled',
}

export enum ActionChannel {
	SMS = 'sms',
	EMAIL = 'email',
	CALL = 'call',
	VOICEMAIL = 'voicemail',
}

export interface SMSMeta {
	conversation_id: string; // Conversation.id
	message_id: string; // The Message.id field
}

export interface EmailMeta {
	type: 'gmail';
	alt_id: string;
	thread_id: string;
	email: string;
}

type Meta = SMSMeta | EmailMeta;

export default class CampaignAction {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('campaign_actions');
	}

	public save() {
		const _self = this;
		return new Promise((resolve, reject) => {
			_self.dateUpdated = moment().utc();
			_self._ref.set(_self.data).then((_) => {
				resolve(_self);
			});
		});
	}

	public static getById(id): Promise<CampaignAction> {
		return new Promise((resolve, reject) => {
			CampaignAction.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new CampaignAction(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByContactId(campaignId: string, contactId: string, locationId: string): Promise<CampaignAction[]> {
		return new Promise((resolve, reject) => {
			CampaignAction.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('contact_id', '==', contactId)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignAction(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByStatus(campaignId: string, status: CampaignActionStatus, locationId: string): Promise<CampaignAction[]> {
		return new Promise((resolve, reject) => {
			CampaignAction.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('location_id', '==', locationId)
				.where('status', '==', status)
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignAction(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByContactAndStatus(contactId: string, status: CampaignActionStatus, locationId: string): Promise<CampaignAction[]> {
		return new Promise((resolve, reject) => {
			CampaignAction.collectionRef()
				.where('contact_id', '==', contactId)
				.where('location_id', '==', locationId)
				.where('status', '==', status)
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignAction(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByMeta(field: string, value: any, locationId: string): Promise<CampaignAction> {
		return new Promise((resolve, reject) => {
			CampaignAction.collectionRef()
				.where('meta.' + field, '==', value)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.limit(1)
				.get()
				.then((snapshot) => {
					if (snapshot.empty) {
						return resolve(null);
					}
					return resolve(new CampaignAction(snapshot.docs[0]));
				})
				.catch((err) => {
					console.error(JSON.stringify(err));
					reject(err);
				});
		});
	}

	public static async createV2(
		locationId: string,
		campaignId: string,
		contactId: string,
		campaignVersionId: string,
		campaignStatusId: string,
		channel: ActionChannel,
		meta: Meta
	) {
		const action = new CampaignAction();
		action.locationId = locationId;
		action.campaignId = campaignId;
		action.contactId = contactId;
		action.channel = channel;
		action.campaignVersionId = campaignVersionId;
		action.campaignStatusId = campaignStatusId;
		action.meta = meta;
		action.status = CampaignActionStatus.FINISHED;
		await action.save();
		return action;
	}

	// public static async create(
	// 	locationId: string,
	// 	campaignId: string,
	// 	contactId: string,
	// 	template: CampaignTemplate<EmailTemplate | SMSTemplate | CallTemplate | VoicemailTemplate>,
	// 	window: Window,
	// 	extras?: { offset?: number; campaignVersionId?: string }
	// ) {
	// 	const action = new CampaignAction();
	// 	action.locationId = locationId;
	// 	action.campaignId = campaignId;
	// 	action.contactId = contactId;
	// 	action.template = template;
	// 	if (extras && extras.campaignVersionId) action.campaignVersionId = extras.campaignVersionId;
	// 	await action.save();
	// 	const startTime = await this.getScheduledTime(locationId, template, window);
	// 	if (extras && extras.offset) startTime.add(extras.offset, 'minutes');
	// 	const task = await TaskRequest.create('POST', '/campaign/' + campaignId + '/action/' + action.id + '/send', undefined, startTime);
	// 	action.taskId = task.id;
	// 	await action.save();
	// 	return action;
	// }

	public static async getScheduledTime(locationId: string, template: { [key: string]: any }, window: Window) {
		const location = await Location.getById(locationId);
		const timezone = await location.getTimeZone();
		const scheduledTime = moment.tz(timezone);
		if (template.start_after.action_in) {
			scheduledTime.add(template.start_after.action_in, 'seconds');
		}
		console.log('Scheduled time ' + scheduledTime.format());
		if (window) {
			if (window.start && window.end) {
				console.log(window);
				const startParts = window.start.split(':');
				const endParts = window.end.split(':');
				const startWindow = scheduledTime.clone().set({ hours: parseInt(startParts[0]), minutes: parseInt(startParts[1]) });
				const endWindow = scheduledTime.clone().set({ hours: parseInt(endParts[0]), minutes: parseInt(endParts[1]) });
				if (scheduledTime.isBefore(startWindow)) {
					scheduledTime.set({ hour: parseInt(startParts[0]), minutes: parseInt(startParts[1]), seconds: 0 });
				} else if (scheduledTime.isAfter(endWindow)) {
					scheduledTime.add(1, 'day');
					scheduledTime.set({ hour: parseInt(startParts[0]), minutes: parseInt(startParts[1]), seconds: 0 });
				}
				console.log('After window check ' + scheduledTime.format());
			}
			console.log('days ' + window.days + ' current: ' + scheduledTime.day());
			if (window.days && window.days.length > 0) {
				while (window.days.indexOf(scheduledTime.day()) === -1) {
					console.log(scheduledTime.day() + ' not in ' + window.days);
					scheduledTime.add(1, 'day');
				}
				console.log('After day check ' + scheduledTime.format());
			}
		}
		console.log('Scheduled time ' + scheduledTime.format());
		return scheduledTime;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = CampaignAction.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
			this.status = CampaignActionStatus.QUEUED;
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get ref(): firebase.firestore.DocumentReference {
		return this._ref;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get contactId(): string {
		return this._data.contact_id;
	}

	set contactId(contactId: string) {
		this._data.contact_id = contactId;
	}

	get campaignId(): string {
		return this._data.campaign_id;
	}

	set campaignId(campaignId: string) {
		this._data.campaign_id = campaignId;
	}

	get dateAdded() {
		return moment(this._data.date_added);
	}

	set dateAdded(dateAdded) {
		this._data.date_added = dateAdded.toDate();
	}

	get dateUpdated() {
		return moment(this._data.date_updated);
	}

	set dateUpdated(dateUpdated) {
		this._data.date_updated = dateUpdated.toDate();
	}

	get status(): CampaignActionStatus {
		return this._data.status;
	}

	set status(status: CampaignActionStatus) {
		this._data.status = status;
	}

	get channel(): ActionChannel {
		return this._data.channel;
	}

	set channel(channel: ActionChannel) {
		this._data.channel = channel;
	}

	get nextStep(): string {
		return this._data.next_step;
	}

	set nextStep(nextStep: string) {
		this._data.next_step = nextStep;
	}

	get campaignStatusId(): string {
		return this._data.campaign_status_id;
	}

	set campaignStatusId(campaignStatusId: string) {
		this._data.campaign_status_id = campaignStatusId;
	}

	get campaignVersionId(): string {
		return this._data.campaign_version_id;
	}

	set campaignVersionId(campaignVersionId: string) {
		this._data.campaign_version_id = campaignVersionId;
	}

	get taskId(): string {
		return this._data.task_id;
	}

	set taskId(taskId: string) {
		this._data.task_id = taskId;
	}

	get template(): CampaignTemplate<EmailTemplate | SMSTemplate | CallTemplate | VoicemailTemplate> {
		return this._data.template;
	}

	set template(template: CampaignTemplate<EmailTemplate | SMSTemplate | CallTemplate | VoicemailTemplate>) {
		this._data.template = template;
	}

	get meta(): Meta {
		if (!this._data.meta) {
			this._data.meta = {};
		}

		return this._data.meta;
	}

	set meta(meta: Meta) {
		this._data.meta = meta;
	}

	// public async cancel() {
	// 	console.log('Task id ' + this.taskId);
	// 	const request = await TaskRequest.getById(this.taskId);
	// 	await request.delete();
	// 	console.log('Deleted task');
	// 	this.status = CampaignActionStatus.CANCELLED;
	// 	await this.save();
	// 	console.log('Cancelled request');
	// }
}
