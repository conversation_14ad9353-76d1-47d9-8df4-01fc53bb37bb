import firebase from 'firebase/app'
import moment from 'moment-timezone'
import store from '../store'
import { AuthUser, Conversation } from '../models'
import axios from 'axios'

export enum MessageType {
  TYPE_CALL = 1,
  TYPE_SMS = 2,
  TYPE_EMAIL = 3,
  TYPE_SMS_REVIEW_REQUEST = 4,
  TYPE_WEBCHAT = 5,
  TYPE_SMS_NO_SHOW_REQUEST = 6,
  TYPE_CAMPAIGN_SMS = 7,
  TYPE_CAMPAIGN_CALL = 8,
  TYPE_CAMPAIGN_EMAIL = 9,
  TYPE_CAMPAIGN_VOICEMAIL = 10,
  TYPE_FACEBOOK = 11,
  TYPE_CAMPAIGN_FACEBOOK = 12,
  TYPE_CAMPAIGN_MANUAL_CALL = 13,
  TYPE_CAMPAIGN_MANUAL_SMS = 14,
  TYPE_GMB = 15,
  TYPE_CAMPAIGN_GMB = 16,
  TYPE_REVIEW = 17,
  TYPE_INSTAGRAM = 18
}

export enum InstagramDMType {
  TYPE_STORY_REPLY = 1,
  TYPE_STORY_MENTION = 2
}

export enum MessageContentType {
  TEXT = 'text/plain',
  HTML = 'text/html'
}

export enum MessageDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

export enum MessageStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  CONNECTED = 'connected'
}

export enum CallStatus {
  COMPLETED = 'completed',
  ANSWERED = 'answered',
  BUSY = 'busy',
  NO_ANSWER = 'no-answer',
  FAILED = 'failed',
  CANCELED = 'canceled'
}

export interface EmailThread {
  id?: string
  subject?: string
  unread?: boolean
  replied?: boolean
  last_message_timestamp?: Date
  first_message_timestamp?: Date
  message_ids?: string[]
  snippet?: string
}

export interface Meta {
  email: EmailThread,
  manual_call?: { id: string }
  isv?: {
    code?: Number
    error?: boolean
    agencyMsg?: string
    msg?: string
  }
}

type Status = CallStatus | MessageStatus

export default class Message {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('messages')
  }

  public static getById(itemId: string): Promise<Message> {
    return new Promise((resolve, reject) => {
      Message.collectionRef()
        .doc(itemId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Message(snapshot))
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    })
  }

  public static getStreamById(
    itemId: string
  ): firebase.firestore.DocumentReference {
    return Message.collectionRef().doc(itemId)
  }

  public static getAllMessages(
    conversationId: string,
    locationId: string
  ): firebase.firestore.Query {
    return this.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .where('conversation_id', '==', conversationId)
      .orderBy('date_added', 'desc')
  }

  public static getLastMessage(
    conversationId: string,
    locationId: string
  ): firebase.firestore.Query {
    return this.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('conversation_id', '==', conversationId)
      .orderBy('date_added', 'desc')
      .limit(1)
  }

  public static getNewMessages(
    conversationId: string,
    locationId: string
  ): firebase.firestore.Query {
    return this.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('conversation_id', '==', conversationId)
      .where('date_added', '>', new Date())
      .orderBy('date_added', 'desc')
      .limit(1)
  }

  public static create(
    locationId: string,
    contactId: string,
    conversationId: string,
    userId: string,
    messageType: number,
    body: string,
    direction: string,
    contentType: string,
    extras?: {
      status?: MessageStatus
      attachments?: string[],
      manualCallId?: string
    }
  ): Message {
    const message = new Message()
    message.locationId = locationId
    message.contactId = contactId
    message.userId = userId
    message.conversationId = conversationId
    message.type = messageType
    message.direction = direction
    message.status =
      extras && extras.status ? extras.status : MessageStatus.PENDING
    if (body) message.body = body
    if (contentType) message.contentType = contentType
    if (extras && extras.attachments) {
      message.attachments = extras.attachments
    }
    if (extras && extras.manualCallId) {
      message.meta.manual_call = {
        id: extras.manualCallId
      }
    }
    return message
  }

  public static async send(
    locationId: string,
    contactId: string,
    type: MessageType,
    body?: string,
    contentType?: MessageContentType,
    attachments?: string[],
    manualCallId?: string,
    extras?: {
      assignedTo?: string
    }
  ) {
    let conversationType = Conversation.TYPE_PHONE
    switch (type) {
      case MessageType.TYPE_SMS:
      case MessageType.TYPE_SMS_REVIEW_REQUEST:
      case MessageType.TYPE_CALL:
      case MessageType.TYPE_CAMPAIGN_SMS:
      case MessageType.TYPE_FACEBOOK:
      case MessageType.TYPE_CAMPAIGN_CALL:
        conversationType = Conversation.TYPE_PHONE
    }
    let conversation = await Conversation.getByContactIdAndType(
      contactId,
      conversationType,
      locationId
    )
    if (!conversation) {
      conversation = await Conversation.createAndSave(
        locationId,
        contactId,
        conversationType,
        extras
      )
    }
    return Message.sendWithConversation(
      conversation,
      contactId,
      body,
      type,
      contentType,
      attachments,
      manualCallId
    )
  }

  public static async sendWithConversation(
    conversation: Conversation,
    contactId: string,
    body: string,
    messageType: MessageType,
    contentType: MessageContentType,
    attachments?: string[],
    manualCallId?: string
  ) {
    const auth: AuthUser = await store.dispatch('auth/get')
    let conversationUpdate = {};
    if (contentType === MessageContentType.TEXT) {
      conversationUpdate['last_message_body'] = body
    }
    conversationUpdate['last_message_type'] = messageType
    if (
      [
        MessageType.TYPE_SMS,
        MessageType.TYPE_CALL,
        MessageType.TYPE_EMAIL,
        MessageType.TYPE_WEBCHAT,
        MessageType.TYPE_FACEBOOK
      ].indexOf(messageType) !== -1
    )
      conversationUpdate['inbox'] = true

    const message = Message.create(
      conversation.locationId,
      contactId,
      conversation.id,
      auth.userId,
      messageType,
      body,
      MessageDirection.OUTBOUND,
      contentType,
      {
        attachments,
        manualCallId
      }
    )

    let server_timestamp = firebase.firestore.FieldValue.serverTimestamp();
    conversationUpdate['date_updated'] = server_timestamp;
    conversationUpdate['last_message_date'] = server_timestamp;

    const batch = firebase.firestore().batch()
    batch.update(conversation.ref, conversationUpdate);
    batch.set(message.ref, message.data)
    await batch.commit()
    if ([MessageType.TYPE_CALL].indexOf(messageType) === -1) {
      axios.post('/message/schedule_message', {
        message_id: message.id
      })
    }

    return message
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(snapshot?:
    | firebase.firestore.DocumentSnapshot
    | firebase.firestore.QueryDocumentSnapshot
    | { [key: string]: any }) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
      this._snapshot = snapshot
    } else if (snapshot) {
      this._id = snapshot.id
      this._ref = Message.collectionRef().doc(snapshot.id)
      this._data = snapshot
      if (snapshot.date_added && snapshot.date_added._seconds) {
        this._data.date_added = new firebase.firestore.Timestamp(snapshot.date_added._seconds, snapshot.date_added._nanoseconds)
      }
      if (snapshot.date_updated && snapshot.date_updated._seconds) {
        this._data.date_updated = new firebase.firestore.Timestamp(snapshot.date_updated._seconds, snapshot.date_updated._nanoseconds)
      }
    } else {
      this._ref = Message.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.data.date_added = firebase.firestore.FieldValue.serverTimestamp();
      this.data.date_updated = firebase.firestore.FieldValue.serverTimestamp();
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return this._data
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get conversationId(): string {
    return this._data.conversation_id
  }

  set conversationId(conversationId: string) {
    this._data.conversation_id = conversationId
  }

  get agentName(): string {
    return this._data.agentName;
  }

  set agentName(agentName: string) {
    this._data.agentName = agentName;
  }

  get initials(): string {
    if (!this.agentName) return ''
    const parts = this.agentName.split(' ')
    let initials = parts[0].substring(0, 1).toUpperCase()

    if (parts.length > 1) {
      initials += parts[parts.length - 1].substring(0, 1).toUpperCase()
    }
    return initials
  }

  get profileColor() {
    let str = this.agentName
    var hash = 0
    if (str.length == 0) return hash
    for (var i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash)
      hash = hash & hash // Convert to 32bit integer
    }
    var shortened = Math.abs(hash) % 360
    return 'hsl(' + shortened + ',35%, 60%)'
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get userId(): string {
    return this._data.user_id
  }

  set userId(userId: string) {
    this._data.user_id = userId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get type(): number {
    return this._data.type
  }

  set type(type: number) {
    this._data.type = type
  }

  get altId(): string {
    return this._data.alt_id
  }

  set altId(altId: string) {
    this._data.alt_id = altId
  }

  get body(): string {
    return this._data.body
  }

  set body(body: string) {
    this._data.body = body
  }

  get direction(): string {
    return this._data.direction
  }

  set direction(direction: string) {
    this._data.direction = direction
  }

  get status(): Status {
    return this._data.status
  }

  set status(status: Status) {
    this._data.status = status
  }

  get attachments(): string[] {
    return this._data.attachments
  }

  set attachments(attachments: string[]) {
    this._data.attachments = attachments
  }

  get contentType(): string {
    return this._data.content_type
  }

  set contentType(contentType: string) {
    this._data.content_type = contentType
  }

  get reviewerName(): string {
    return this._data.reviewer_name;
  }

  set reviewerName(reviewerName: string) {
    this._data.reviewer_name = reviewerName;
  }

  get starRating(): number {
    return this._data.star_rating;
  }

  set starRating(starRating: number) {
    this._data.star_rating = starRating;
  }

  get source(): number {
    return this._data.source;
  }

  set source(source: number) {
    this._data.source = source;
  }

  public save(): Promise<Message> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.data.date_updated = firebase.firestore.FieldValue.serverTimestamp();
      _self._ref.set(this._data, { merge: true }).then(() => {
        resolve(_self)
      })
    })
  }

  public delete() {
    return Message.collectionRef()
      .doc(this.id)
      .update({
        deleted: true
      })
  }

  get meta(): Meta {
    if (!this._data.meta) this._data.meta = {}
    if (!this._data.meta.email) this._data.meta.email = {}
    return this._data.meta
  }

  get instagramDMType(): number {
    return this._data.instagram_dm_type;
  }

  set instagramDMType(instagramDMType: number) {
    this._data.instagram_dm_type = instagramDMType;
  }
}
