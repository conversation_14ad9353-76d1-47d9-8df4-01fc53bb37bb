import firebase from 'firebase/app'
import moment from 'moment-timezone'
import pickBy from 'lodash/pickBy'
export default class EmailBuilder {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('email_builders')
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
    } else {
      this._ref = EmailBuilder.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
    }
  }

  public static getById(id: string): Promise<EmailBuilder> {
    return new Promise((resolve, reject) => {
      EmailBuilder.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          resolve(new EmailBuilder(snapshot))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getByLocationId(locationId: string): Promise<EmailBuilder[]> {
    return new Promise((resolve, reject) => {
      EmailBuilder.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new EmailBuilder(d)))
        })
    })
  }

  public static getQueryWithLocationId(
    locationId: string
  ): firebase.firestore.Query {
    return EmailBuilder.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<EmailBuilder | undefined> {
    const snapshot = await EmailBuilder.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new EmailBuilder(snapshot.docs[0])
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get builderVersion(): string {
    return this._data.builder_version
  }

  set builderVersion(version: string) {
    this._data.builder_version = version
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get emailBuilderData(): any {
    return this._data.email_data
  }

  set emailBuilderData(emailBuilderData: any) {
    this._data.email_data = emailBuilderData
  }

  get downloadUrl(): string {
    return this._data.downloadUrl
  }

  set downloadUrl(downloadUrl: string) {
    this._data.downloadUrl = downloadUrl
  }

  get storageUrl(): string {
    return this._data.storageUrl
  }

  set storageUrl(storageUrl: string) {
    this._data.storageUrl = storageUrl
  }

  get htmlDownloadUrl(): string {
    return this._data.download_url
  }

  set htmlDownloadUrl(url: string) {
    this._data.download_url = url
  }
  get fromName(): string {
    return this._data.from_name
  }

  set fromName(name: string) {
    this._data.from_name = name
  }

  get fromAddress(): string {
    return this._data.from_address
  }

  set fromAddress(address: string) {
    this._data.from_address = address
  }

  get subjectLine(): string {
    return this._data.subject_line
  }

  set subjectLine(subject: string) {
    this._data.subject_line = subject
  }

  get isArchived(): boolean {
    return this._data.archived
  }

  set isArchived(isArchived: boolean) {
    this._data.archived = isArchived
  }
  

  public save() {
    return new Promise((resolve, reject) => {
      this.dateUpdated = moment().utc()
      this._ref.set(this.data).then(() => {
        resolve(this)
      })
    })
  }
}
