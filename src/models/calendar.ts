import firebase from 'firebase/app'
import * as lodash from 'lodash'
import { CalendarEvent, CustomValue } from '../models'
import handlebars from 'handlebars'
import axios from 'axios'
import * as moment from 'moment-timezone'

export type allowBookingAfter = 'hour' | 'day' | 'week'

export interface LinkedCalendars {
  clio?: ClioCalendar
  drchrono?: DrChrono
  google?: GoogleCalendar
}

export interface ClioCalendar {
  id: string
  name: string
}

export interface DrChrono {
  id: string
  name: string
  sync_all_doctors?: boolean
  doctor_id: string
  last_sync?: string
}

export interface GoogleCalendar {
  id: string
  name: string
  access_role: string
  watch_id: string
  expiration: number
  user_id: string
  oauth_id?: string
  last_sync?: string
  error?: number;
  error_message?: string;
}

interface TeamMember {
  user_id: string
  priority: number
  meeting_location: string
  selected: boolean,
  is_zoom_added: boolean | string,
  zoom_oauth_id: string
}

export interface Stripe {
  amount: number
  currency: number
  charge_description?: string
}

export enum SyncOption {
  TWOWAY = 'twoway',
  ONEWAY = 'oneway',
  DISABLE_TRIGGER = 'disable_trigger',
  SMART = 'smart'
}

export enum EventType {
  RoundRobin_OptimizeForAvailability = 'RoundRobin_OptimizeForAvailability',
  RoundRobin_OptimizeForEqualDistribution = 'RoundRobin_OptimizeForEqualDistribution',
  Collective = 'Collective',
  Group = 'Group'
}

export enum FormSubmitType {
  RedirectURL = 'RedirectURL',
  ThankYouMessage = 'ThankYouMessage'
}

export enum ActiveStepType {
  Step1 = 1,
  Step2 = 2,
  Step3 = 3,
  Complete = 'Complete'
}

const indexwiseWeekdays = {
  '0': 'Sunday',
  '1': 'Monday',
  '2': 'Tuesday',
  '3': 'Wednesday',
  '4': 'Thursday',
  '5': 'Friday',
  '6': 'Saturday'
}

export default class Calendar {
  public static NotesText_Title_RescheduleOrCancellation =
    'Need to make a change to this event?\n'
  public static NotesText_Reschedule = 'Reschedule:-\n{{reschedule_link}}\n\n'
  public static NotesText_Cancellation = 'Cancel:-\n{{cancellation_link}}'
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('calendars')
  }

  /**
   * Get all calendar services by passing calendar provider Id
   */
  public static async fetchAllByProviderId(
    providerId: string,
    locationId: string
  ): Promise<Calendar[]> {
    return new Promise<Calendar[]>(async (resolve, reject) => {
      const snapshot = await Calendar.collectionRef()
        .where('location_id', '==', locationId)
        .where('provider_id', '==', providerId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new Calendar(d)))
    })
  }

  public static async getAllByLocation(
    locationId: string
  ): Promise<Calendar[]> {
    return new Promise<Calendar[]>(async (resolve, reject) => {
      // const unsubscribe = Calendar.collectionRef()
      //     .where('location_id', '==', locationId)
      //     .onSnapshot((snapshot) => {
      //         unsubscribe();
      //         resolve(snapshot.docs.map((d) => new Calendar(d)));
      //     });
      const snapshot = await Calendar.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new Calendar(d)))
    })
  }

  public static fetchAllByLocation(
    locationId: string
  ): firebase.firestore.Query {
    return Calendar.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static getById(id: string): Promise<Calendar> {
    return new Promise((resolve, reject) => {
      Calendar.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Calendar(snapshot))
          resolve()
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Calendar | undefined> {
    const snapshot = await Calendar.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Calendar(snapshot.docs[0])
  }

  public static async uploadAppoitmentsWithError(id: string) {
    try {
      await axios.get(
        '/google/calendar/retry-calendar-appointment?calendar_id=' + id
      )
    } catch (err) { }
  }

  /**
   * Get service by passing slug name
   * Service slug should be only unique across all services of it's provider
   */
  public static async getBySlugName(
    providerId: string,
    slug: string
  ): Promise<Calendar> {
    return new Promise<Calendar>(async (resolve, reject) => {
      const snapshot = await Calendar.collectionRef()
        .where('provider_id', '==', providerId)
        .where('slug', '==', slug)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) resolve()
          return resolve(new Calendar(snapshot.docs[0]))
        })
    })
  }

  public static getByOAuthId(OAuthId: string): Promise<Calendar[]> {
    return new Promise(async(resolve, reject) => {
      try {
        await Calendar.collectionRef()
          .where('linked_calendars.google.oauth_id', '==', OAuthId)
          .where('deleted', '==', false)
          .get()
          .then(snapshot => {
            if (snapshot.empty) resolve();
            return resolve(snapshot.docs.map(d => new Calendar(d)));
          })
          .catch(err => {
            reject(err);
          });
      } catch (ex) {
        reject(ex);
      }
    });
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot
      this._ref = Calendar.collectionRef().doc(snapshot.id)
      if (
        snapshot.date_added &&
        snapshot.date_added instanceof firebase.firestore.Timestamp === false
      ) {
        snapshot.date_added = new firebase.firestore.Timestamp(
          snapshot.date_added.seconds,
          snapshot.date_added.nanoseconds
        )
        if (snapshot.date_updated.seconds) {
          snapshot.date_updated = new firebase.firestore.Timestamp(
            snapshot.date_updated.seconds,
            snapshot.date_updated.nanoseconds
          )
        }
      }
    } else {
      this._ref = Calendar.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment().utc()
      this.deleted = false
      this.openHours = []
      this.syncOption = SyncOption.ONEWAY
      this.addServiceActiveStep = ActiveStepType.Step1
    }
    this._data.event_color = this._data.event_color // To make this field vue reactive when event_color field not exist in _data
    this._data.linked_calendars = this.linkedCalendars // To make this linkedCalendars's fields to go through observalbes (new Calendar()) to beecome vue reactive since existing non-observable fields (added not throuh vue reactive like initializing through getters) never become observables even through the Vue.set

    this.name = this.name
    this.description = this.description
    if (this.isActive === undefined) {
      this.isActive = true
    }
    this.deleted = this.deleted || false
    this._data.location_id = this._data.location_id
    if (this._data.team_members && Array.isArray(this._data.team_members))
      this._data.team_members = this._data.team_members
    else this._data.team_members = []
    this._data.event_type =
      this._data.event_type || EventType.RoundRobin_OptimizeForAvailability

    this.openHours = this.openHours || []
    if (!this.name) { //Initialise slots only when creating calendar
      for (var i = 1; i < 6; i++) {
        this.openHours.push({
          days_of_the_week: [i],
          hours: [
            {
              open_hour: 8,
              open_minute: 0,
              close_hour: 17,
              close_minute: 0
            }
          ]
        })
      }
    }
    this.enableOfficeHours = this.enableOfficeHours
    if (this.enableOfficeHours === undefined) {
      this.enableOfficeHours = true
    }

    this.slug = this.slug
    this.eventColor = this.eventColor

    this.slotDuration = this.slotDuration || 30
    this.slotInterval = this.slotInterval || 30
    this.appoinmentPerSlot = this.appoinmentPerSlot || 1

    this.allowBookingAfterUnit = this.allowBookingAfterUnit || 'days'
    this.allowBookingForUnit = this.allowBookingForUnit || 'days'

    if (this.autoConfirm === undefined) {
      this.autoConfirm = true
    }

    if (this.googleInvitationEmails === undefined) {
      this.googleInvitationEmails = false
    }
    this.formSubmitThanksMessage =
      this.formSubmitThanksMessage ||
      'Thank you for your appointment request. We will contact you shortly to confirm your request. Please call our office at {{contactMethod}} if you have any questions.'

    this.stripe = this.stripe || <Stripe>{}
    this.eventColor = this.eventColor || '#039BE5'
    this.stickyContact = this.stickyContact || false
    this.formSubmitType = this.formSubmitType || FormSubmitType.ThankYouMessage

    this.allowReschedule = this.allowReschedule
    this.allowCancellation = this.allowCancellation
    this.shouldSendAlertEmailsToAssignedMember = this.shouldSendAlertEmailsToAssignedMember
    if (this.shouldSendAlertEmailsToAssignedMember === undefined) {
      this.shouldSendAlertEmailsToAssignedMember = true
    }
    this.pixelID = this.pixelID
    this._data.linked_calendars = this.linkedCalendars
    this.formId = this.formId
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added
      ? moment(this._data.date_added.toDate())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromDate(
      dateAdded.toDate()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data.date_updated
      ? moment(this._data.date_updated.toDate())
      : this.dateAdded
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromDate(
      dateUpdated.toDate()
    )
  }

  get providerId(): string {
    return this._data.provider_id
  }

  set providerId(providerId: string) {
    this._data.provider_id = providerId
  }

  get description(): string {
    return this._data.description
  }

  set description(value: string) {
    this._data.description = value
  }

  get isActive(): boolean {
    return this._data.is_active
  }

  set isActive(value: boolean) {
    this._data.is_active = value
  }

  get slug(): string {
    return this._data.slug
  }

  set slug(value: string) {
    this._data.slug = value
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  // TODO: (MP) Delete for later

  // set userIds(value: string[]) {
  //   this._data.user_ids = value
  // }

  // get userIds(): string[] {
  //   return this._data.user_ids
  // }

  get teamMembers(): TeamMember[] {
    return this._data.team_members
  }

  set teamMembers(value: TeamMember[]) {
    this._data.team_members = value
  }

  get enableOfficeHours() {
    return true; // Make office hours always true, anyway user availability will be worked seamlessly just by setting up office hours open for 24 hours.
    // return this._data.enable_office_hours
  }

  set enableOfficeHours(value: boolean) {
    this._data.enable_office_hours = value
  }

  get dayOfTheWeekwiseAvailability() {
    const _dayOfTheWeekwiseAvailability = {}
    if (this._data.open_hours) {
      this._data.open_hours.forEach(x => {
        _dayOfTheWeekwiseAvailability[indexwiseWeekdays[x.days_of_the_week]] =
          x.hours
      })
    }
    return _dayOfTheWeekwiseAvailability
  }

  repeatTheOpenHoursForOtherWeekDays(dayOfWeek: number) {
    const pickedDayOfWeekOpenHours = this.openHours.find(
      x => x.days_of_the_week[0] === dayOfWeek
    )
    this.openHours = this.openHours.map(x => {
      return {
        days_of_the_week: x.days_of_the_week,
        hours: lodash.cloneDeep(pickedDayOfWeekOpenHours.hours)
      }
    })
  }

  // Default Available Days of Week
  get defaultAvailableDays() {
    return this._data.default_availabile_days
  }

  set defaultAvailableDays(value: string[]) {
    this._data.default_availabile_days = value
  }

  get defaultAvailableHours() {
    return this._data.default_available_hours
  }

  set defaultAvailableHours(value: { [key: string]: number }[]) {
    this._data.default_available_hours = value
  }

  get repeatingDaysOfTheWeekAvailability() {
    return this._data.repeating_days_of_the_week_availability
  }

  set repeatingDaysOfTheWeekAvailability(value: { [key: string]: [] }) {
    this._data.repeating_days_of_the_week_availability = value
  }

  get specificDatesAvailability() {
    return this._data.specific_dates_availability
  }

  set specificDatesAvailability(value: { [key: string]: [] }) {
    this._data.specific_dates_availability = value
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get slotDuration(): number {
    return this._data.slot_duration
  }

  set slotDuration(slotDuration: number) {
    this._data.slot_duration = slotDuration
  }

  get slotInterval(): number {
    return this._data.slot_interval
      ? this._data.slot_interval
      : this.slotDuration
  }

  set slotInterval(slotInterval: number) {
    this._data.slot_interval = slotInterval
  }

  get slotBuffer(): number {
    return this._data.slot_buffer
  }

  set slotBuffer(slotBuffer: number) {
    this._data.slot_buffer = slotBuffer
  }

  get openHours(): { [key: string]: any } {
    return this._data.open_hours
  }

  set openHours(openHours: { [key: string]: any }) {
    this._data.open_hours = openHours
  }

  get alertEmail(): string {
    return this._data.alert_email
  }

  set alertEmail(alertEmail: string) {
    this._data.alert_email = alertEmail
  }

  get thankyouURL(): string {
    return this._data.thank_you_url
  }

  set thankyouURL(thankyouURL: string) {
    this._data.thank_you_url = thankyouURL
  }

  get onewaySyncAppointment(): boolean {
    return this._data.oneway_sync_appointment
  }

  set onewaySyncAppointment(onewaySyncAppointment: boolean) {
    this._data.oneway_sync_appointment = onewaySyncAppointment
  }

  get linkedCalendars(): LinkedCalendars {
    if (!this._data.linked_calendars) this._data.linked_calendars = {}
    if (!this._data.linked_calendars.clio) this._data.linked_calendars.clio = {}
    if (!this._data.linked_calendars.drchrono)
      this._data.linked_calendars.drchrono = {}
    if (!this._data.linked_calendars.google)
      this._data.linked_calendars.google = {}
    return this._data.linked_calendars
  }

  get lastSync(): moment.Moment {
    return this.linkedCalendars.google.last_sync
      ? moment(this.linkedCalendars.google.last_sync)
      : this.linkedCalendars.drchrono.last_sync
        ? moment(this.linkedCalendars.drchrono.last_sync)
        : undefined
  }

  get allowAdditionalNotes() {
    return (
      lodash.has(this.linkedCalendars, 'clio.id') === false &&
      lodash.has(this.linkedCalendars, 'drchrono.id') === false
    )
  }

  get autoConfirm(): boolean {
    if (this._data.auto_confirm === undefined) return true
    return this._data.auto_confirm
  }

  set autoConfirm(autoConfirm: boolean) {
    this._data.auto_confirm = autoConfirm
  }

  get appoinmentPerDay(): number {
    return this._data.appoinment_per_day
  }

  set appoinmentPerDay(appoinmentPerDay: number) {
    this._data.appoinment_per_day = appoinmentPerDay
  }

  get allowBookingFor(): number {
    return this._data.allow_booking_for
  }

  set allowBookingFor(allowBookingFor: number) {
    this._data.allow_booking_for = allowBookingFor
  }

  get allowBookingForUnit(): string {
    return this._data.allow_booking_for_unit
  }

  set allowBookingForUnit(allowBookingForUnit: string) {
    this._data.allow_booking_for_unit = allowBookingForUnit
  }

  get allowBookingAfter(): number {
    return this._data.allow_booking_after
  }

  set allowBookingAfter(allowBookingAfter: number) {
    this._data.allow_booking_after = allowBookingAfter
  }

  get allowBookingAfterUnit(): string {
    return this._data.allow_booking_after_unit
  }

  set allowBookingAfterUnit(allowBookingAfterUnit: string) {
    this._data.allow_booking_after_unit = allowBookingAfterUnit
  }

  get appoinmentPerSlot(): number {
    return this._data.appoinment_per_slot === undefined
      ? 1
      : this._data.appoinment_per_slot
  }

  set appoinmentPerSlot(appoinmentPerSlot: number) {
    this._data.appoinment_per_slot = appoinmentPerSlot
  }

  get pixelID(): string {
    return this._data.pixel_id
  }

  set pixelID(pixelID: string) {
    this._data.pixel_id = pixelID
  }

  get allowCancellation(): boolean {
    return this._data.allow_cancellation === undefined
      ? true
      : this._data.allow_cancellation
  }

  set allowCancellation(allowCancellation: boolean) {
    this._data.allow_cancellation = allowCancellation
  }

  get allowReschedule(): boolean {
    return this._data.allow_reschedule === undefined
      ? true
      : this._data.allow_reschedule
  }

  set allowReschedule(allowReschedule: boolean) {
    this._data.allow_reschedule = allowReschedule
  }

  get formSubmitType(): FormSubmitType {
    return (
      this._data.form_submit_type ||
      (this.thankyouURL
        ? FormSubmitType.RedirectURL
        : FormSubmitType.ThankYouMessage)
    )
  }

  set formSubmitType(value: FormSubmitType) {
    this._data.form_submit_type = value
  }

  // After form submit either it shows the formSubmitThanksMessage or redirect widget page to the formSubmitRedirectURL
  get formSubmitRedirectURL(): string {
    return this._data.form_submit_redirect_url || this.thankyouURL
  }

  set formSubmitRedirectURL(value: string) {
    this._data.form_submit_redirect_url = value
  }

  // After form submit either it shows the formSubmitThanksMessage or redirect widget page to the formSubmitRedirectURL
  get formSubmitThanksMessage(): string {
    return this._data.form_submit_thanks_message || this.thanksMessage
  }

  set formSubmitThanksMessage(value: string) {
    this._data.form_submit_thanks_message = value
  }

  /**
   * Whether to send appointment alert emails to assigned team member or not
   */
  get shouldSendAlertEmailsToAssignedMember(): boolean {
    return this._data.should_send_alert_emails_to_assigned_member
  }

  set shouldSendAlertEmailsToAssignedMember(value: boolean) {
    this._data.should_send_alert_emails_to_assigned_member = value
  }

  /**
   * This flag informs to Google Calendar whether to send invitation emails to Google Calendar guests or not after an appointment got pushed to Google.
   */
  get googleInvitationEmails(): boolean {
    return this._data.google_invitation_emails
  }

  set googleInvitationEmails(value: boolean) {
    this._data.google_invitation_emails = value
  }

  private getDefaultNotesText() {
    let defaultText = 'Phone:- {{contact.phone}}\nEmail:- {{contact.email}}\n\n'
    defaultText +=
      this.allowReschedule || this.allowCancellation
        ? Calendar.NotesText_Title_RescheduleOrCancellation
        : ''
    defaultText += this.allowReschedule ? Calendar.NotesText_Reschedule : ''
    defaultText += this.allowCancellation ? Calendar.NotesText_Cancellation : ''
    return defaultText
  }

  get eventType(): EventType {
    return this._data.event_type
  }

  set eventType(value: EventType) {
    this._data.event_type = value
  }

  get addServiceActiveStep(): ActiveStepType {
    return this._data.add_service_active_step
  }

  set addServiceActiveStep(value: ActiveStepType) {
    this._data.add_service_active_step = value
  }

  get meetingLocation(): string {
    return this._data.meeting_location
  }

  public async getComputedMeetingLocation() {
    try {
      if (
        !this._data.meeting_location ||
        !this._data.meeting_location.includes('{{')
      ) {
        return this._data.meeting_location
      }

      return CustomValue.getMapByLocationId(this.locationId).then(
        templateParams => {
          return handlebars.compile(this.meetingLocation)(templateParams)
        }
      )
    } catch (err) {
      return undefined
    }
  }

  set meetingLocation(meetingLocation: string) {
    this._data.meeting_location = meetingLocation
  }

  get syncOption(): string {
    return this._data.sync_option
      ? this._data.sync_option
      : this.onewaySyncAppointment
        ? SyncOption.DISABLE_TRIGGER
        : SyncOption.TWOWAY
  }

  set syncOption(syncOption: string) {
    this._data.sync_option = syncOption
  }

  get stripe(): Stripe {
    return this._data.stripe
  }

  set stripe(stripe: Stripe) {
    this._data.stripe = stripe
  }

  get isLivePaymentMode() {
    return this._data.is_live_payment_mode;
  }

  set isLivePaymentMode(mode: boolean) {
    this._data.is_live_payment_mode = mode
  }

  get stickyContact(): boolean {
    return this._data.sticky_contact !== undefined
      ? this._data.sticky_contact
      : false
  }

  set stickyContact(stickyContact: boolean) {
    this._data.sticky_contact = stickyContact
  }

  get formId(): string {
    return this._data.form_id
  }

  set formId(formId: string) {
    this._data.form_id = formId
  }

  get codeBlock(): string {
    return this._data.code_block
  }

  set codeBlock(codeBlock: string) {
    this._data.code_block = codeBlock
  }

  get thanksMessage(): string {
    return this._data.thanks_message
      ? this._data.thanks_message
      : 'Thank you for your appointment request. We will contact you shortly to confirm your request. Please call our office at {{contactMethod}} if you have any questions.'
  }

  set thanksMessage(thanksMessage: string) {
    this._data.thanks_message = thanksMessage
  }

  get notes(): string {
    return this._data.notes !== undefined
      ? this._data.notes
      : 'Phone:- {{contact.phone}}\nEmail:- {{contact.email}}\n\nNeed to make a change to this event?\nReschedule:-\n{{reschedule_link}}\n\nCancel:-\n{{cancellation_link}}'
  }

  set notes(notes: string) {
    this._data.notes = notes
  }

  get eventTitle(): string {
    return this._data.event_title === undefined
      ? '{{contact.name}}'
      : this._data.event_title
  }

  set eventTitle(eventTitle: string) {
    this._data.event_title = eventTitle
  }

  get eventColor(): string | undefined {
    return this._data.event_color
  }

  set eventColor(eventColor: string | undefined) {
    this._data.event_color = eventColor
  }

  // If stop update is true we will not process any data on webhook, however if there are calendars linked to the same calendar we will.
  // get stopUpdate(): boolean {
  //   return this._data.stop_update === undefined ? false : this._data.stop_update;
  // }

  // set stopUpdate(stopUpdate: boolean) {
  //   this._data.stop_update = stopUpdate;
  // }

  public async save() {
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }

  /**
   * For V3, Linked calendar works differently from V2
   */
  public async deleteLinkedGoogleCalendarV2() {
    if (!this.linkedCalendars.google.id) {
      return
    }

    // this.linkedCalendars.google = undefined;
    if (lodash.has(this.linkedCalendars, 'google.id')) {
      try {
        await axios.get('/google/calendar/unlink?calendar_id=' + this.id)
      } catch (err) { }
    }
  }

  /**
   * Placing this at back-end just to avoid sporadic issues
   * Sometimes page refresh when the deletion in progress, may cause the deletion process gets stopped/failed in middle
   */
  public async deleteCalendarAndEvents() {
    try {
      await axios.get(
        `/google/calendar/delete_calendar_and_events_v2_or_v3?calendar_id=${this.id}`
      )
    } catch (err) { }
  }

  public async runCalendarSync() {
    try {
      if (this.linkedCalendars.google.id) {
        this.runGoogleCalendarSync()
      } else if (this.linkedCalendars.drchrono.id) {
        this.runDrChronoCalendarSync()
      }
    } catch (err) { }
  }

  public async runGoogleCalendarSync() {
    try {
      await axios.get('/google/calendar/sync?calendar_id=' + this.id)
    } catch (err) { }
  }

  public async runDrChronoCalendarSync() {
    try {
      await axios.get(
        '/drchrono/runAppointmentsSyncCalendar?calendar_id=' + this.id
      )
    } catch (err) { }
  }
}
