import firebase from 'firebase/app';
import moment from 'moment-timezone';
import { AuthUser, LineItem, Address } from '@/models';
import store from '../store';

export default class Job {
  public static OPEN = 0;
  public static IN_PROGRESS = 1;
  public static COMPLETE = 2;
  public static INVOICED = 3;
  public static FULLY_PAID = 4;

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('jobs');
  }

  public static getById(id: string) {
    return new Promise((resolve, reject) => {
      Job.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Job({ ...snapshot.data(), id: snapshot.id }));
          resolve();
        })
        .catch((err) => {
          reject(err)
        });
    });
  }

  public static getStreamById(id: string): firebase.firestore.DocumentReference {
    return Job.collectionRef().doc(id);
  }

  public static async fetchAllEstimates(): Promise<firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get');
    return Job.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', auth.companyId)
      .orderBy('date_added', 'desc');
  }

  public static fetchAllEstimatesForClient(contactId: string, companyId: string): firebase.firestore.Query {
    return Job.collectionRef()
      .where('deleted', '==', false)
      .where('contact_id', '==', contactId)
      .where('company_id', '==', companyId)
      .orderBy('date_added', 'desc');
  }

  public static delete(id: string) {
    const job = store.getters['job/getById'](id);
    job.deleted = true;
    job.save();
  }

  private _data: { [key: string]: any };
  private _lineItems: LineItem[];

  constructor(params?: { [key: string]: any }) {
    if (params) {
      this._data = params;
      this._data.line_items = params.line_items || [];
      this._lineItems = this._data.line_items.map((item: { [key: string]: any }) => {
        return new LineItem(item);
      });
    } else {
      // TODO fetch company object and construct
      this._data = {};
      // this.companyId = params.id;
      this.dateAdded = moment();
      this.dateUpdated = moment();
      this.deleted = false;
      this.status = Job.OPEN;
      // this.currency = params.currency;
      this.lineItems = [];
      this.attachments = [];
    }
  }

  get id(): string | undefined {
    return this._data.id;
  }

  get data(): { [key: string]: any } {
    return this._data;
  }

  get companyId(): string | undefined {
    return this._data.company_id;
  }

  set companyId(companyId: string | undefined) {
    this._data.company_id = companyId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added);
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate();
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated);
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate();
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get dirty(): boolean {
    return this._data.dirty;
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty;
  }

  get documentNumber(): string {
    return this._data.document_number;
  }

  set documentNumber(documentNumber: string) {
    this._data.document_number = documentNumber;
  }

  get contactId(): string {
    return this._data.contact_id;
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId;
  }

  get lineItems(): LineItem[] {
    return this._lineItems;
  }

  set lineItems(lineItems: LineItem[]) {
    this._lineItems = lineItems;
    this._data.line_items = this._lineItems.map((item: LineItem) => item.data);
  }

  public addLineItem(item: LineItem) {
    this._lineItems.push(item);
    this._data.line_items = this._lineItems.map((i: LineItem) => i.data);
  }

  public removeLineItem(item: LineItem) {
    const index: number = this._lineItems.indexOf(item);
    if (index !== -1) {
      this._lineItems.splice(index, 1);
      this._data.line_items = this._lineItems.map((i: LineItem) => i.data);
    }
  }

  get dateIssued(): moment.Moment {
    return moment(this._data.date_issued);
  }

  set dateIssued(dateIssued: moment.Moment) {
    this._data.date_issued = dateIssued.toDate();
  }

  get dueDate(): moment.Moment {
    return moment(this._data.date_due);
  }

  set dueDate(dueDate: moment.Moment) {
    this._data.date_due = dueDate.toDate();
  }

  get notes(): string {
    return this._data.notes;
  }

  set notes(notes: string) {
    this._data.notes = notes;
  }

  get status(): number {
    return this._data.status;
  }

  set status(status: number) {
    this._data.status = status;
  }

  get address1(): string | undefined {
    return this._data.address1;
  }

  set address1(address1: string | undefined) {
    this._data.address1 = address1;
  }

  get city(): string | undefined {
    return this._data.city;
  }

  set city(city: string | undefined) {
    this._data.city = city;
  }

  get state(): string | undefined {
    return this._data.state;
  }

  set state(state: string | undefined) {
    this._data.state = state;
  }

  get postalCode(): string | undefined {
    return this._data.postal_code;
  }

  set postalCode(postalCode: string | undefined) {
    this._data.postal_code = postalCode;
  }

  get country(): string | undefined {
    return this._data.country;
  }

  set country(country: string | undefined) {
    this._data.country = country;
  }

  get addressGeo(): firebase.firestore.GeoPoint | undefined {
    return this._data.address_geo;
  }

  set addressGeo(addressGeo: firebase.firestore.GeoPoint | undefined) {
    this._data.address_geo = addressGeo;
  }

  get billingEmail(): string {
    return this._data.billing_email;
  }

  set billingEmail(billingEmail: string) {
    this._data.billing_email = billingEmail;
  }

  get appointmentId(): string {
    return this._data.appointment_id;
  }

  set appointmentId(appointmentId: string) {
    this._data.appointment_id = appointmentId;
  }

  get title(): string {
    return this._data.title;
  }

  set title(title: string) {
    this._data.title = title;
  }

  get total(): string {
    return this._data.total || '0.0';
  }

  set total(total: string) {
    this._data.total = total;
  }

  get privateNote(): string {
    return this._data.private_note;
  }

  set privateNote(privateNote: string) {
    this._data.private_note = privateNote;
  }

  get taxId(): string {
    return this._data.tax_id;
  }

  set taxId(taxId: string) {
    this._data.tax_id = taxId;
  }

  get currency(): string {
    return this._data.currency;
  }

  set currency(currency: string) {
    this._data.currency = currency;
  }

  get onMyWayId(): string {
    return this._data.on_my_way_id;
  }

  set onMyWayId(onMyWayId: string) {
    this._data.on_my_way_id = onMyWayId;
  }

  get onMyWayDate(): moment.Moment {
    return moment(this._data.on_my_way_date);
  }

  set onMyWayDate(onMyWayDate: moment.Moment) {
    this._data.on_my_way_date = onMyWayDate.toDate();
  }

  get sendJobDate(): moment.Moment {
    return moment(this._data.send_job_date);
  }

  set sendJobDate(sendJobDate: moment.Moment) {
    this._data.send_job_date = sendJobDate.toDate();
  }

  get finishedDate(): moment.Moment {
    return moment(this._data.finished_date);
  }

  set finishedDate(finishedDate: moment.Moment) {
    this._data.finished_date = finishedDate.toDate();
  }

  get estimateId(): string {
    return this._data.estimate_id;
  }

  set estimateId(estimateId: string) {
    this._data.estimate_id = estimateId;
  }

  get attachments(): string[] {
    return this._data.attachments;
  }

  set attachments(attachments: string[]) {
    this._data.attachments = attachments;
  }

  set address(address: Address) {
    this.address1 = address.address1;
    this.city = address.city;
    this.state = address.state;
    this.country = address.country;
    this.postalCode = address.postalCode;
    this.addressGeo = address.addressGeo;
  }

  get address() {
    return new Address({
      address1: this.address1,
      city: this.city,
      state: this.state,
      country: this.country,
      postalCode: this.postalCode,
      addressGeo: this.addressGeo,
    });
  }

  public save() {
    this.dateUpdated = moment();
    this.dirty = true;
    store.dispatch('job/addUpdate', this.data);
  }

  //   Future<String> get totalPayments async {
  //     QuerySnapshot snapshot = await Payment.fetchAllSuccessfulPaymentsForJob(companyId, id).first;
  //     double amount = 0.0;
  //     snapshot.documents.forEach((DocumentSnapshot doc){
  //       amount+=double.parse(doc.data["amount"]);
  //     });
  //     return amount.toStringAsFixed(2);
  //   }

  //   Future<String> get totalAmountTime async {
  //     QuerySnapshot snapshot = await TimeEntry.getTimeEntriesForDocument(this.companyId, TimeEntry.DOCUMENT_TYPE_JOB, this.id).snapshots.first;
  //     Map<String, dynamic> response = TimeEntry.getUsageForEntries(snapshot.documents);
  //     return response["total_amount_minutes"].toStringAsFixed(2);
  //   }

  //   Future<String> get itemsAndTaxesTotal async {
  //     double subTotal = 0.0;
  //     double netTaxableAmount = 0.0;
  //     String _taxAmount;
  //     _lineItems.forEach((LineItem item) {
  //       if (item.amount != null) {
  //         double amount = double.parse(item.amount);
  //         subTotal += amount;
  //         if (item.taxable) {
  //           netTaxableAmount += amount;
  //         }
  //       }
  //     });
  //     if (this.taxId != null) {
  //       Tax tax = await Tax.getById(this.taxId);
  //       _taxAmount =
  //           (netTaxableAmount * double.parse(tax.rate) / 100).toStringAsFixed(2);
  //     } else {
  //       _taxAmount = "0";
  //     }
  //     return (subTotal + double.parse(_taxAmount)).toStringAsFixed(2);
  //   }
}
