import * as moment from 'moment-timezone'
import PostData from './post_data'
import firebase from 'firebase/app'

export default class Post {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('posts')
  }

  public static TYPE_TWITTER = 'twitter'
  public static TYPE_GOOGLE = 'google'
  public static TYPE_FACEBOOK = 'facebook'
  public static TYPE_LINKEDIN = 'linkedin'
  public static TYPE_INSTAGRAM = 'instagram'
  public static TYPE_GOOGLEPLUS = 'googleplus'

  public static STATUS_READY = 'ready'
  public static STATUS_TO_REVIEW = 'to_review'
  public static STATUS_POSTED = 'posted'

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = Post.collectionRef().doc()
      this._id = this._ref.id
      this._data = new PostData()
      this.twitter = new PostData()
      this.facebook = new PostData()
      this.linkedin = new PostData()
      this.instagram = new PostData()
    }
  }

  public save(): Promise<Post> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self._ref.set(_self._data, { merge: true }).then(_ => {
        resolve(_self)
      })
    })
  }

  static getById(id): Promise<Post> {
    return new Promise((resolve, reject) => {
      Post.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new Post(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByLocationIdAndStatus(locationId: string, status: string) {
    return Post.collectionRef()
      .where('location_id', '==', locationId)
      .where('status', '==', status)
  }

  public static getByReviewId(reviewId: string, locationId: string) {
    return Post.collectionRef()
      .where('review_id', '==', reviewId)
      .where('location_id', '==', locationId)
  }

  public static getByLocationIdAndType(
    locationId: string,
    type: string
  ): Promise<Post> {
    return new Promise((resolve, reject) => {
      Post.collectionRef()
        .where('location_id', '==', locationId)
        .where('type', '==', type)
        .limit(1)
        .get()
        .then(querySnapshot => {
          querySnapshot.size === 1
            ? resolve(new Post(querySnapshot.docs[0]))
            : reject()
        })
    })
  }

  public static getByAccountIdAndType(
    accountId: string,
    type: string,
    locationId: string
  ): Promise<Post> {
    return new Promise((resolve, reject) => {
      Post.collectionRef()
        .where('alt_id', '==', accountId)
        .where('location_id', '==', locationId)
        .where('type', '==', type)
        .limit(1)
        .get()
        .then(querySnapshot => {
          querySnapshot.size === 1
            ? resolve(new Post(querySnapshot.docs[0]))
            : reject()
        })
    })
  }

  public addPost(post: PostData, type: string) {
    this[type] = post
    this.networks[type] = moment()
      .utc()
      .unix()
  }

  public removePost(post: PostData) {
    this[post.type] = {}
    this.networks[post.type] = 0
  }

  get id(): string {
    return this._id
  }

  get data(): { [field: string]: any } {
    return this._data
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get reviewId(): string {
    return this._data.review_id
  }

  set reviewId(reviewId: string) {
    this._data.review_id = reviewId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data['date_added'])
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data['date_added'] = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data['date_updated'])
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data['date_updated'] = dateUpdated.toDate()
  }

  get deleted(): boolean {
    return this._data['deleted']
  }
  set deleted(deleted: boolean) {
    this._data['deleted'] = deleted
  }

  get dirty(): boolean {
    return this._data['dirty']
  }
  set dirty(dirty: boolean) {
    this._data['dirty'] = dirty
  }

  get status(): string {
    return this._data['status']
  }
  set status(status: string) {
    this._data['status'] = status
  }

  get networks(): {} {
    return this._data.networks
  }

  set networks(networks: {}) {
    this._data.networks = networks
  }

  get twitter(): PostData {
    return this._data.twitter
  }

  set twitter(twitter: PostData) {
    this._data.twitter = twitter
  }

  get facebook(): PostData {
    return this._data.facebook
  }

  set facebook(facebook: PostData) {
    this._data.twitter = facebook
  }

  get linkedin(): PostData {
    return this._data.linkedin
  }

  set linkedin(linkedin: PostData) {
    this._data.linkedin = linkedin
  }

  get instagram(): PostData {
    return this._data.instagram
  }

  set instagram(instagram: PostData) {
    this._data.instagram = instagram
  }

  get scheduledTime(): moment.Moment {
    return moment(this._data['scheduled_time'])
  }

  set scheduledTime(scheduledTime: moment.Moment) {
    this._data['date_updated'] = scheduledTime.toDate()
  }
}
