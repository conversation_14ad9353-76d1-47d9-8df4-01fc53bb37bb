// import 'dart:async';

// import 'package:cloud_firestore/cloud_firestore.dart';

// class Tax {
//   static CollectionReference collectionRef =firebase.firestore.instance.collection("tax_rates");

//   String _id;
//   Map<String, dynamic> _data;

//   Tax() {
//     this._data = new Map<String, dynamic>();
//     this.deleted = false;
//     this.dateAdded = new DateTime.now();
//   }

//   Tax.fromSnapshot(DocumentSnapshot snap) {
//     this._id = snap.documentID;
//     this._data = snap.data;
//   }

//   String get id => _id;

//   String get companyId => _data["company_id"];

//   set companyId(String companyId) {
//     _data["company_id"] = companyId;
//   }

//   String get name => _data["name"];

//   set name(String name) {
//     _data["name"] = name;
//   }

//   String get rate => _data["rate"];

//   set rate(String rate) {
//     _data["rate"] = rate;
//   }

//   bool get deleted => _data["deleted"];

//   set deleted(bool deleted) {
//     _data["deleted"] = deleted;
//   }

//   DateTime get dateAdded => _data["date_added"];

//   set dateAdded(DateTime dateAdded) {
//     _data["date_added"] = dateAdded;
//   }

//   DateTime get dateUpdated => _data["date_updated"];

//   set dateUpdated(DateTime dateUpdated) {
//     _data["date_updated"] = dateUpdated;
//   }

//   save() async {
//     this.dateUpdated = new DateTime.now();
//     if (_id != null) {
//       collectionRef.document(_id).updateData(_data);
//       print("Update: " + _id + " " + _data.toString());
//     } else {
//       DocumentReference ref = collectionRef.document();
//       ref.setData(_data);
//       _id = ref.documentID;
//       print("Add: " + _id + " " + _data.toString());
//     }
//   }

//   static Future<Tax> getById(String id) async {
//     DocumentSnapshot snap = await collectionRef.document(id).snapshots.first;
//     return new Tax.fromSnapshot(snap);
//   }

//   static Stream<DocumentSnapshot> getStreamById(String id) {
//     return collectionRef.document(id).snapshots;
//   }

//   static Future<QuerySnapshot> getDefaultTax(String companyId) {
//     return getAllTaxes(companyId).first;
//   }

//   static Stream<QuerySnapshot> getAllTaxes(String companyId) {
//     return collectionRef.where("deleted", isEqualTo: false).where("company_id", isEqualTo: companyId).orderBy("date_added").snapshots;
//   }

//   delete() async {
//     await collectionRef.document(id).updateData({
//       "deleted": true
//     });
//   }
// }
