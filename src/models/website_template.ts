import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { CloneAccount, Funnel } from '.';
import { FunnelType } from './funnel';

export default class WebsiteTemplate extends CloneAccount {
  public static collectionRef() {
    return firebase.firestore().collection('website_template');
  }

  public static async getById(id: string): Promise<WebsiteTemplate | undefined> {
    const snapshot = await WebsiteTemplate.collectionRef()
      .doc(id)
      .get();
    if (!snapshot.exists || snapshot.data().deleted) return undefined;
    return new WebsiteTemplate(snapshot);
  }

  public static getByCategoryRealtime(categoryId: string): firebase.firestore.Query {
    return WebsiteTemplate.collectionRef()
      .where('deleted', '==', false)
      .where('category_id', '==', categoryId)
  }

  public static async getByCategoryId(categoryId: string): Promise<WebsiteTemplate[] | undefined> {
    const snapshot = await WebsiteTemplate.getByCategoryRealtime(categoryId).get();
    return snapshot.docs.map((d) => new WebsiteTemplate(d));
  }

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    super(snapshot);

    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = WebsiteTemplate.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment();
    }
  }

  public static async createNewTemplate(companyId: string,
    locationId: string,
    categoryId: string,
    name: string,
    description?: string,
    livePreview?: string,
    image?: string
  ) {
    const websiteTemplate = new WebsiteTemplate();
    websiteTemplate.companyId = companyId;
    websiteTemplate.locationId = locationId;
    websiteTemplate.name = name;
    websiteTemplate.categoryId = categoryId;
    websiteTemplate.type = 'own';
    websiteTemplate.description = description;
    websiteTemplate.livePreview = livePreview;
    websiteTemplate.image = image;

    websiteTemplate.templateType = await this.figureOutTemplateType(locationId);
    await websiteTemplate.runDehydrate();
    await websiteTemplate.save();
    return websiteTemplate;
  }

  private static async figureOutTemplateType(locationId: string): Promise<string> {
    const funnels = await Funnel.getAllByLocation(locationId)
    if (!funnels || funnels.length === 0 || funnels.length > 1) {
      // we can't have more than one funnel in a template
      throw new Error(`Expected only one funnel in location, found ${funnels.length} funnels/websites`)
    }

    const funnel = funnels[0]
    return funnel.type || FunnelType.Website
  }

  get categoryId(): string {
    return this._data.category_id;
  }

  set categoryId(categoryId: string) {
    this._data.category_id = categoryId;
  }

  get livePreview(): string {
    return this._data.live_preview;
  }

  set livePreview(livePreview: string) {
    this._data.live_preview = livePreview;
  }

  get image(): string {
    return this._data.image;
  }

  set image(image: string) {
    this._data.image = image;
  }

  get description(): string {
    return this._data.description;
  }

  set description(description: string) {
    this._data.description = description;
  }

  get templateType(): string {
    return this._data.templateType
  }

  set templateType(type: string) {
    this._data.templateType = type
  }
}
