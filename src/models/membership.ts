import axios, { AxiosRequestConfig } from 'axios'
import config from '../config'
import { IKeyVal } from '../pmd/pages/smartlist/vm/interfaces';
import { ok } from 'assert';

interface IProduct {
  product_name ?: string;
  product_id: string;
}

interface IOffer {
  offer_name ?: string;
  offer_id: string;
}

interface IMemebership {
  offers?: IOffer[];
  products?: IProduct[];
}

export class LocationMembership {

  private locationId: string;

  public offers : ReadonlyArray<IOffer> = [];
  public products: ReadonlyArray<IProduct> = [];

  private constructor(locationId: string){
    this.locationId = locationId;
  }

  public static async forLocation(locationId: string){

    const membership = new LocationMembership(locationId);
    let dto: IMemebership = null;
    try {
      console.log('fetching membership')
      let response = await axios.get<IMemebership>(`${config.membershipUrl}/smart-list/offers-products/${locationId}` , {
        headers: {
          /*Authorization: 'Bearer ' + accessToken,*/
          ContentType: 'application/json'
        }
      });
      dto = response.data;
    } catch (err) {
      console.log(`Error fetching membership information for location - ${locationId}`);
      console.log(err);
    }
    if (dto && dto.offers) membership.offers = dto.offers;
    if (dto && dto.products) membership.products = dto.products;
    return membership;
  }
}
