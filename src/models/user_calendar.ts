import firebase from 'firebase/app'
import * as lodash from 'lodash'
import axios from 'axios'
import * as moment from 'moment-timezone'

export default class UserCalendar {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('calendars_v3')
  }

  public static async getAllByLocation(
    locationId: string
  ): Promise<UserCalendar[]> {
    return new Promise<UserCalendar[]>(async (resolve, reject) => {
      const snapshot = await UserCalendar.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new UserCalendar(d)))
    })
  }

  public static fetchAllByLocation(
    locationId: string
  ): firebase.firestore.Query {
    return UserCalendar.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static getById(id: string): Promise<UserCalendar> {
    return new Promise((resolve, reject) => {
      UserCalendar.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new UserCalendar(snapshot))
          resolve()
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  /**
   * Get All Calendars by passing user id and location id
   */
  public static async getByUserAndLocation(
    userId: string,
    locationId: string
  ): Promise<UserCalendar[]> {
    return new Promise<UserCalendar[]>(async (resolve, reject) => {
      const snapshot = await UserCalendar.collectionRef()
        .where('location_id', '==', locationId)
        .where('user_id', '==', userId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new UserCalendar(d)))
    })
  }

  /**
   * Passing logged in user id as well because master calendar needs to be created if not yet created one for the logged in user.
   * Master calendar is always one per location and per user, once it's created it'll never be removed
   */
  public static async getUserwiseMasterCalendarIdsByLocation(
    loggedInUserId: string,
    locationId: string
  ): Promise<{ [key: string]: string }> {
    const userwiseMasterCalendarIds: { [key: string]: string } = {}
    const allLocationMasterCalendars = await UserCalendar.getAllByLocation(locationId)
    const locationMasterCalendars = allLocationMasterCalendars ? allLocationMasterCalendars.filter(key => !key.isCheckForConflictsCalendar) : [];
    locationMasterCalendars.forEach(x => {
      userwiseMasterCalendarIds[x.userId] = x.id
    })

    if (!userwiseMasterCalendarIds[loggedInUserId]) {
      // Creating a master calendar for logged in user for this location if not yet created one
      const masterCalendar = new UserCalendar()
      masterCalendar.userId = loggedInUserId
      masterCalendar.locationId = locationId
      masterCalendar.isCheckForConflictsCalendar = false // False means it's master calendar, and the same gonna be used for 'Add To UserCalendar' as well
      await masterCalendar.save()
      userwiseMasterCalendarIds[loggedInUserId] = masterCalendar.id
    }

    return userwiseMasterCalendarIds
  }

  /**
   * Get Calendars by passing ids
   */
  public static getByIds(calendarIds: string[]): Promise<UserCalendar[]> {
    return new Promise(async (resolve, reject) => {
      try {
        var promises = calendarIds.map(function (id) {
          return UserCalendar.collectionRef()
            .doc(id)
            .get()
        })
        Promise.all(promises).then(function (snapshots) {
          resolve(snapshots.filter(x => x.exists).map(x => new UserCalendar(x)))
        })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static async uploadAppoitmentsWithError(id: string) {
    try {
      await axios.get(
        '/google/calendar/retry-calendar-appointment?calendar_id=' + id
      )
    } catch (err) { }
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot
      this._ref = UserCalendar.collectionRef().doc(snapshot.id)
      if (
        snapshot.date_added &&
        snapshot.date_added instanceof firebase.firestore.Timestamp === false
      ) {
        snapshot.date_added = new firebase.firestore.Timestamp(
          snapshot.date_added.seconds,
          snapshot.date_added.nanoseconds
        )
        if (snapshot.date_updated.seconds) {
          snapshot.date_updated = new firebase.firestore.Timestamp(
            snapshot.date_updated.seconds,
            snapshot.date_updated.nanoseconds
          )
        }
      }
    } else {
      this._ref = UserCalendar.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
      this.dateUpdated = moment()
    }
    this.defaultCalendarServiceId = this.defaultCalendarServiceId
    this.canTriggerRun = this.canTriggerRun
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added ? this._data.date_added.toMillis() : '')
  }

  set dateAdded(value: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      value.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(
      this._data.date_updated ? this._data.date_updated.toMillis() : ''
    )
  }

  set dateUpdated(value: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      value.valueOf()
    )
  }

  //#region - Realted to linked calendar
  get linkedCalendarId(): string {
    return this._data.linked_calendar_id
  }

  set linkedCalendarId(value: string | undefined) {
    this._data.linked_calendar_id = value
  }

  public deleteLinkedCalendarRelatedProps() {
    delete this._data.linked_calendar_id

    // Deleting google info (which is added for denormalization purpose) apart from the linked calendar mapping
    delete this._data.account_name
    delete this._data.google_calendar_id
    delete this._data.summary
  }

  public deleteUnLinkedProp() {
    delete this._data.unlinked
  }

  get unlinked(): boolean {
    return this._data.unlinked
  }

  set unlinked(value: boolean) {
    this._data.unlinked = value
  }

  /**
   * This helps to sync events from already existing calendar which got linked to the same linked calendar
   * This flag helps only if there more than one calenadar which all got linked to the same linked calendar.
   */
  get isNewlyLinked(): boolean {
    return this._data.is_newly_linked
  }

  set isNewlyLinked(value: boolean) {
    this._data.is_newly_linked = value
  }

  /**
   * Though it's available in LinkedCalendar, denormalizing this just to avoid round trips to server.
   */
  get googleCalendarId(): string {
    return this._data.google_calendar_id
  }

  set googleCalendarId(value: string | undefined) {
    this._data.google_calendar_id = value
  }

  /**
   * Account Name - Google account name (gmail id) or DrChrono account name or Clio account name
   * Edge case - If two calendars from same user do have same account name which means user having multiple LinkedCalendars with same account name
   */
  get accountName(): string {
    return this._data.account_name
  }

  set accountName(value: string) {
    this._data.account_name = value
  }
  //#endregion

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string | undefined) {
    this._data.location_id = locationId
  }

  get userId(): string {
    return this._data.user_id
  }

  set userId(value: string) {
    this._data.user_id = value
  }

  /**
   * Assigning the same calendar name that coming from Google or DrChronos or Clio
   */
  get summary(): string {
    return this._data.summary
  }

  set summary(value: string) {
    this._data.summary = value
  }

  /**
   * Whether it's a check for conflicts calendar or not
   * True - 'Check for Conflicts calendar'
   * False - 'Add To calendar' (Master Calendar)
   */
  get isCheckForConflictsCalendar(): boolean {
    return this._data.is_check_for_conflicts_calendar
  }

  set isCheckForConflictsCalendar(value: boolean) {
    this._data.is_check_for_conflicts_calendar = value
  }

  /**
   * This is user's main calendar which will never be deleted
   */
  get isMasterCalendar(): boolean {
    return this.isCheckForConflictsCalendar === false
  }

  /**
   * Default Calendar Service Id - To which calendar service by default an appointment and contact creation should be linked when an event coming from google/clio/drchronos
   */
  get defaultCalendarServiceId(): string {
    return this._data.default_calendar_service_id
  }

  set defaultCalendarServiceId(value: string) {
    this._data.default_calendar_service_id = value
  }

  /**
   * Can Trigger Run - This flag helps to decide whether trigger can be run or not after an appointment and contact creation
   */
  get canTriggerRun(): boolean {
    return this._data.can_trigger_run
  }

  set canTriggerRun(value: boolean) {
    this._data.can_trigger_run = value
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  public async save(silent?: boolean) {
    if (!silent) this.dateUpdated = moment()
    await this._ref.set(this.data)
  }

  /**
   * It won't delete Master Calendar (Primary Calendar)
   */
  public async deleteCalendarAndEvents(
    deleteEventsIncludingAppointments: boolean = true
  ) {
    if (this.isMasterCalendar) {
      // You can't delete master calendar as well as appointments, only you can delete typical events which are not appointments
      deleteEventsIncludingAppointments = false
    }
    try {
      await axios.get(
        `/google/calendar/delete_calendar_and_events?calendar_id=${this.id}&events_including_appointments=${deleteEventsIncludingAppointments}`
      )
    } catch (err) { }
  }
}
