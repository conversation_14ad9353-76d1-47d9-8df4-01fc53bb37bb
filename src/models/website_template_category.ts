import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { pickBy } from 'lodash'

export enum WebsiteTemplateCategoryType {
  Funnels = 'funnels',
  Websites = 'websites'
}

export default class WebsiteTemplateCategory {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('website_template_category')
  }

  public static getById(id: string): Promise<WebsiteTemplateCategory> {
    return new Promise(async (resolve, reject) => {
      try {
        await WebsiteTemplateCategory.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new WebsiteTemplateCategory(snapshot))
          })
          .catch(err => {
            // reject(err);
            console.error(err)
            reject({
              message: "Sorry, we can't find the category you were looking for."
            })
          })
      } catch (ex) {
        // reject(ex);
        console.error(ex)
        reject({ message: 'An internal error occured!' })
      }
    })
  }

  public static getAllRealtime(): firebase.firestore.Query {
    return WebsiteTemplateCategory.collectionRef().where('deleted', '==', false)
  }

  public static async getAllCategories(): Promise<WebsiteTemplateCategory[]> {
    const snapshot = await WebsiteTemplateCategory.getAllRealtime().get()
    return snapshot.docs.map(d => new WebsiteTemplateCategory(d))
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    const init_data = {
      deleted: false
    }

    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || { ...init_data }
    } else {
      this._ref = WebsiteTemplateCategory.collectionRef().doc()
      this._data = { ...init_data }
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }

  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get description(): string {
    return this._data.description
  }

  set description(description: string) {
    this._data.description = description
  }

  get image(): string {
    return this._data.image
  }

  set image(image: string) {
    this._data.image = image
  }

  get video(): string {
    return this._data.video
  }

  set video(video: string) {
    this._data.video = video
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }
}
