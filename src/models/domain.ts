import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { pickBy } from 'lodash'

export default class Domain {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('domains')
  }

  public static getById(id: string): Promise<Domain> {
    return new Promise(async (resolve, reject) => {
      try {
        await Domain.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new Domain(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    const init_data = {
      deleted: false,
      steps: []
    }

    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || { ...init_data }
    } else {
      this._ref = Domain.collectionRef().doc()
      this._data = { ...init_data }
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }

  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getAllByLocation(locationId: string): Promise<Domain[]> {
    return new Promise((resolve, reject) => {
      Domain.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Domain(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByUrl(
    url: string,
    locationId: string
  ): Promise<Domain[]> {
    return new Promise((resolve, reject) => {
      Domain.collectionRef()
        .where('deleted', '==', false)
        .where('url', '==', url)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Domain(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  // Name and URL are same for a domain

  get name(): string {
    return this._data.url
  }

  set name(name: string) {
    this._data.url = name
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }

  get verified(): boolean {
    return this._data.verified
  }

  set verified(verified: boolean) {
    this._data.verified = verified
  }

  get locationId() {
    return this._data.location_id
  }

  set locationId(locationId) {
    this._data.location_id = locationId
  }

  get companyId() {
    return this._data.company_id
  }

  set companyId(companyId) {
    this._data.company_id = companyId
  }
  get defaultDomain(): boolean {
    return this._data.default_domain
  }

  set defaultDomain(status: boolean) {
    this._data.default_domain = status
  }

  get robotsTxtCode(): string {
    return this._data.robots_txt_code
  }

  set robotsTxtCode(code: string) {
    this._data.robots_txt_code = code
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get defaultPage(): string {
    return this._data.default_page
  }

  set defaultPage(defaultPageId: string) {
    this._data.default_page = defaultPageId
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }
}
