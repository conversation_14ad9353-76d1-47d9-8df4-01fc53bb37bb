import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { v4 as uuid } from 'uuid'
import store from '../store'
import AuthUser from './auth_user'
import Company from './company'
import mix from '../util/mix'
import LocationsService from '../services/LocationsService'

type Dict = { [key: string]: any }

export interface ProspectInfo {
  first_name?: string
  last_name?: string
  email?: string
}

export interface GMBSettingInfo {
  displayName?: string
  contactName?: string
  contactEmail?: string
  brandName?: string
  gmbNumber?: string
  welcomeMessage?: string
  logoURL?: string
  privacyPolicyUrl?: string
}

export interface GMBMessagingStaus {
  messaging_status?: boolean
  calling_status?: boolean
}

export interface SMSSettings {
  image?: string
  image_enabled?: boolean
  message?: string
  review_link?: string
  after_check_in_hours?: number
  enabled?: boolean
  no_show_enabled?: boolean
  no_show_image_enabled?: boolean
  no_show_message?: string
  no_show_image?: string
  birthday_enabled?: boolean
  birthday_image_enabled?: boolean
  birthday_message?: string
  birthday_image?: string
}

export interface ReviewSettings {
  review_request_behaviour?: string
}

export interface WidgetSettings {
  bgcolor?: string
  titlecolor?: string
  ratingcolor?: string
  headertext?: string
}

export interface TextWidgetSettings {
  heading?: string
  useEmailField: boolean
  autoCountryCode: boolean
  showPrompt: boolean
  enableRevisitMessage: boolean
  subHeading: string
  legalMsg: string
  promptMsg: string
  successMsg: string
  revisitPromptMsg: string
  thankYouMsg: string
  supportContact: string
  countryCode: string
  promptAvatar: string
  widgetPrimaryColor: string
  agencyName: string
  agencyWebsite: string
  showAgencyBranding: boolean
}

export interface ChatWidgetSettings {
  user_id?: string
  intromessage?: string
  botquestion?: string
  enabled?: boolean
}

export interface EmailSettings {
  heading?: string
  message?: string
  positive?: string
  negative?: string
  image?: string
  review_link?: string
  subject?: string
  after_check_in_hours?: number
  enabled?: boolean
  mark_invalid_hard_bounce?: boolean
}

export interface Shopify {
  id?: string;
  name?: string;
  domain?: string;
}

export interface SocialSettings {
  facebook_enabled?: boolean
  facebook_stars?: number
  facebook_post_limit?: number
  twitter_enabled?: boolean
  twitter_stars?: number
  twitter_post_limit?: number
  directories_enabled?: boolean
  directories_stars?: number
  directories_post_limit?: number
  googleplus_enabled?: boolean
  googleplus_stars?: number
  googleplus_post_limit?: number
  searchengine_enabled?: boolean
  searchengine_stars?: number
  searchengine_post_limit?: number
}

export interface ClioSettings {
  custom_field?: string
}

export interface DrChronoSettings {
  office_id?: string
}

export interface AttributionSettings {
  interaction?: string
}

export interface QBOSettings {
  trigger_on_invoice?: boolean
}

export interface ITwilioRebillingSettings {
  enabled?: boolean
  markup?: number
}

export interface IMailgunRebillingSettings {
  markup: number
  price: number
  enabled: boolean
}

interface IComplementaryCredits {
  amount: number
  type: 'oneTime' | 'monthly'
}

export interface ILocationPermissions {
  adwords_reporting_enabled?: boolean
  appointments_enabled?: boolean
  attributions_reporting_enabled?: boolean
  agent_reporting_enabled?: boolean
  bot_service_enabled?: boolean
  bulk_requests_enabled?: boolean
  campaigns_enabled?: boolean // will stay false
  contacts_enabled?: boolean // crm
  conversations_enabled?: boolean // crm
  dashboard_stats_enabled?: boolean
  email_builder_enabled?: boolean
  facebook_ads_reporting_enabled?: boolean
  facebook_messenger_enabled?: boolean
  forms_enabled?: boolean
  funnels_enabled?: boolean
  // google_ads_reporting_enabled?: boolean
  gmb_call_tracking_enabled?: boolean
  gmb_messaging_enabled?: boolean
  html_builder_enabled?: boolean
  membership_enabled?: boolean
  marketing_enabled?: boolean
  // online_listings_enabled?: boolean;
  opportunities_enabled?: boolean
  phone_call_enabled?: boolean
  reviews_enabled?: boolean
  settings_enabled?: boolean
  sms_email_templates_enabled?: boolean
  surveys_enabled?: boolean
  tags_enabled?: boolean
  text_to_pay_enabled?: boolean
  triggers_enabled?: boolean
  trigger_links_enabled?: boolean
  web_chat_enabled?: boolean
  websites_enabled?: boolean
  workflows_enabled?: boolean
}

export type SAAS_PRODUCT =
  | '2-way-text-messaging'
  | 'gmb-messaging'
  | 'web-chat'
  | 'reputation-management'
  | 'facebook-messenger'
  | 'gmb-call-tracking'
  | 'missed-call-text-back'
  | 'text-to-pay'
  | 'calendar'
  | 'crm'
  | 'opportunities'
  | 'email-marketing'
  | 'form-builder'
  | 'survey-builder'
  | 'trigger-links'
  | 'html-builder'
  | 'sms-email-templates'
  | 'funnels'
  | 'websites'
  | 'workflow'
  | 'membership'
  | 'all-reports'
  | 'triggers'
  | 'campaigns'

export interface CreatedOrUpdatedBy {
  userId?: string
  source: string
  channel: string
}

export interface ISaasSettings {
  saas_mode: 'activated' | 'setup_pending' | 'not_activated'
  stripe_customer_id?: string
  twilio_rebilling: ITwilioRebillingSettings
  mailgun_rebilling: IMailgunRebillingSettings
  complementary_credits?: IComplementaryCredits
  saas_products?: SAAS_PRODUCT[]
  stripe_plan_details?: {
    subscription_id: string
    product_id: string
    price_id: string
  }
  saas_plan_id?: string
}

export interface Settings {
  email: EmailSettings
  sms: SMSSettings
  review: ReviewSettings
  social: SocialSettings
  clio: ClioSettings
  drchrono: DrChronoSettings
  widget: WidgetSettings
  qbo: QBOSettings
  chatwidget: ChatWidgetSettings
  attribution: AttributionSettings
  textwidget: TextWidgetSettings
  saas_settings: ISaasSettings
}

export interface ProductStatus extends Dict {
  reviews?: boolean
  listings?: boolean
  conversations?: boolean
  social?: boolean
}

export interface GoogleMyBusiness {
  id?: string
  name?: string
}

export enum LocationStatus {
  PROSPECT = 'prospect',
  ACCOUNT = 'account',
}

export interface Stripe {
  publishable_key: string
  secret_key: string
}

export interface VoicemailFile {
  url: string
  name: string
}

export interface AllowBetaAccess {
  workflow: boolean
  outlook: boolean
  payments: boolean
}

export interface LaunchpadStatus {
  is_chat_widget_integrated?: boolean
  is_chat_widget_disconnected?: boolean
  is_mobile_app_installed?: boolean
  is_stripe_disconnected?: boolean
  is_stripe_connected?: boolean
}

export interface WordpressLocationSettings {
  backup_files?: [{ [key: string]: any }]
  meta?:{ [key: string]: any },
  wp_id?:string,
  status?:string,
  status_message?:string,
  sub_domains?:string[]
  installation_type?:string,
  domain?:string
  staging_domain?:string
}

function isFirestoreDelete(value) {
  return value && typeof value === 'object' && value?._delegate?._methodName === 'FieldValue.delete'
}

export default class Location {
  public static requiredFields = ['company_id']

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('locations')
  }

  // public static getById(locationId: string): Promise<Location> {
  // 	return new Promise((resolve, reject) => {
  // 		const unsubscribe = Location.collectionRef()
  // 			.doc(locationId)
  // 			.onSnapshot((snapshot:firebase.firestore.DocumentSnapshot) => {
  // 				unsubscribe();
  // 				resolve(new Location(snapshot));
  // 			});
  // 	});
  // }

  public static getById(id): Promise<Location> {
    return new Promise((resolve, reject) => {
      Location.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new Location(snapshot))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getByIdRealtime(locationId: string) {
    return Location.collectionRef().doc(locationId)
  }

  public static async getByCompanyIdRealtime() {
    const auth: AuthUser = await store.dispatch('auth/get')
    return LocationsService.search(auth.companyId, false)
  }

  public static async getByCompanyIdQuery(query) {
    const auth: AuthUser = await store.dispatch('auth/get')
    return LocationsService.searchQuery(auth.companyId, false, query)
  }

  public static async getByCompanyId(): Promise<Location[]> {
    return new Promise<Location[]>(async (resolve, reject) => {
      ;(await Location.getByCompanyIdRealtime())
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new Location(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getStreamById(
    locationId: string
  ): firebase.firestore.DocumentReference {
    return Location.collectionRef().doc(locationId)
  }

  public static async fetchLocationBySnapshotId(snapshotId: string) {
    const auth: AuthUser = await store.dispatch('auth/get')
    const snapshot = await Location.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', auth.companyId)
      .where('snapshots_loaded', 'array-contains', snapshotId)
      .orderBy('name_lower_case')
      .get()
    return snapshot.docs.map(l => new Location(l))
  }

  public static async fetchAllLocationsRealtimeFirestore(): Promise<firebase.firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get')
    return this.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', auth.companyId)
      .where('status', '==', LocationStatus.ACCOUNT)
      .orderBy('name_lower_case')
  }

  public static async fetchAllLocationsRealtime() {
    const auth: AuthUser = await store.dispatch('auth/get')
    return LocationsService.search(auth.companyId, false, LocationStatus.ACCOUNT)
  }

  public static async fetchAllLocations(): Promise<Location[]> {
    return new Promise<Location[]>(async (resolve, reject) => {
      Location.fetchAllLocationsRealtime()
        .then(response => {
          resolve(
            response.locations.map(location => {
              return { ...location, id: location._id }
            })
          )
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchLocationRealtime(): Promise<firebase.firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get')
    return this.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', auth.companyId)
      .where('status', '==', LocationStatus.ACCOUNT)
      .limit(1)
  }

  public static async fetchLocation(): Promise<Location[]> {
    return new Promise<Location[]>(async (resolve, reject) => {
      ;(await Location.fetchLocationRealtime())
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new Location(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  private enablePermissionForSaasProduct(saasProduct: SAAS_PRODUCT) {
    /**
     * bot_service_enabled
     * campaigns_enabled
     * triggers_enabled
     */
    switch (saasProduct) {
      case '2-way-text-messaging': {
        // this.permissions.contacts_enabled = true
        this.permissions.conversations_enabled = true
        this.permissions.phone_call_enabled = true
        break
      }
      case 'all-reports': {
        this.permissions.adwords_reporting_enabled = true
        this.permissions.agent_reporting_enabled = true
        this.permissions.attributions_reporting_enabled = true
        this.permissions.facebook_ads_reporting_enabled = true
        // this.permissions.google_ads_reporting_enabled = true
        break
      }
      case 'calendar': {
        this.permissions.appointments_enabled = true
        break
      }
      case 'crm': {
        // contacts enabling again under crm, also enabled under 2way messaging
        this.permissions.contacts_enabled = true
        this.permissions.dashboard_stats_enabled = true
        this.permissions.bulk_requests_enabled = true
        break
      }
      case 'email-marketing': {
        this.permissions.email_builder_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'facebook-messenger': {
        this.permissions.facebook_messenger_enabled = true
        break
      }
      case 'funnels': {
        this.permissions.funnels_enabled = true
        break
      }
      case 'gmb-call-tracking': {
        this.permissions.gmb_call_tracking_enabled = true
        break
      }
      case 'gmb-messaging': {
        this.permissions.gmb_messaging_enabled = true
        break
      }
      case 'membership': {
        this.permissions.membership_enabled = true
        break
      }
      case 'missed-call-text-back': {
        // what is missed call? conversations is already enabled under 2way texting
        this.permissions.conversations_enabled = true
        break
      }
      case 'opportunities': {
        this.permissions.opportunities_enabled = true
        break
      }
      case 'reputation-management': {
        this.permissions.reviews_enabled = true
        // this.permissions.online_listings_enabled = true
        break
      }
      case 'text-to-pay': {
        this.permissions.text_to_pay_enabled = true
        break
      }
      case 'web-chat': {
        this.permissions.web_chat_enabled = true
        break
      }
      case 'websites': {
        this.permissions.websites_enabled = true
        break
      }
      case 'workflow': {
        this.permissions.workflows_enabled = true
        break
      }
      case 'form-builder': {
        this.permissions.forms_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'survey-builder': {
        this.permissions.surveys_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'trigger-links': {
        this.permissions.trigger_links_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'sms-email-templates': {
        this.permissions.sms_email_templates_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'html-builder': {
        this.permissions.html_builder_enabled = true
        this.permissions.marketing_enabled = true
        break
      }
      case 'triggers': {
        this.permissions.triggers_enabled = true
        break
      }
      case 'campaigns': {
        this.permissions.campaigns_enabled = true
        break
      }
      default: {
        console.info(`Unhandled product: ${saasProduct}`)
      }
    }
  }

  public setPermissionsBasedOnSaasProducts(): ILocationPermissions {
    const saasProducts = this.settings.saas_settings.saas_products

    this.permissions = {
      campaigns_enabled: false, // will stay false
      contacts_enabled: false, // crm
      workflows_enabled: false,
      triggers_enabled: false,
      trigger_links_enabled: false,
      text_to_pay_enabled: false,
      forms_enabled: false,
      funnels_enabled: false,
      websites_enabled: false,
      opportunities_enabled: false,
      dashboard_stats_enabled: false,
      bulk_requests_enabled: false,
      appointments_enabled: false,
      reviews_enabled: false,
      html_builder_enabled: false,
      marketing_enabled: false,
      // online_listings_enabled: false,
      phone_call_enabled: false,
      conversations_enabled: false, // crm
      adwords_reporting_enabled: false,
      membership_enabled: false,
      tags_enabled: true,
      settings_enabled: true,
      surveys_enabled: false,
      sms_email_templates_enabled: false,
      email_builder_enabled: false,
      agent_reporting_enabled: false,
      facebook_ads_reporting_enabled: false,
      attributions_reporting_enabled: false,
      // google_ads_reporting_enabled: false,
      bot_service_enabled: false,
      gmb_call_tracking_enabled: false,
      gmb_messaging_enabled: false,
      facebook_messenger_enabled: false,
      web_chat_enabled: false,
    } as ILocationPermissions

    for (const product of saasProducts) {
      this.enablePermissionForSaasProduct(product)
    }

    return this.permissions
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    params?: firebase.firestore.DocumentSnapshot | { [key: string]: any }
  ) {
    if (params instanceof firebase.firestore.DocumentSnapshot) {
      this._id = params.id
      this._ref = params.ref
      this._data = params.data() || {}
    } else if (params) {
      this._id = params.id
      if(params.date_added?.seconds && params.date_added instanceof firebase.firestore.Timestamp === false){
        params.date_added = new firebase.firestore.Timestamp(params.date_added.seconds, params.date_added.nanoseconds);
      }
      this._ref = Location.collectionRef().doc(params.id)
      this._data = params || {}
    } else {
      this._ref = Location.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.prospectInfo = {}

      const twilioRebilling = {
        enabled: false,
        markup: 10,
      } as ITwilioRebillingSettings

      const saas_settings = {
        saas_mode: 'not_activated',
        twilio_rebilling: twilioRebilling,
      } as ISaasSettings

      this.settings = {
        sms: {
          enabled: true,
        },
        email: {
          enabled: true,
        },
        review: {
          review_request_behaviour: 'immediate',
        },
        social: {
          facebook_enabled: true,
          facebook_stars: 4,
          facebook_post_limit: 3,
          twitter_enabled: true,
          twitter_stars: 4,
          twitter_post_limit: 3,
          directories_enabled: true,
          directories_stars: 4,
          directories_post_limit: 3,
          googleplus_enabled: true,
          googleplus_stars: 4,
          googleplus_post_limit: 3,
          searchengine_enabled: true,
          searchengine_stars: 4,
          searchengine_post_limit: 3,
        },
        widget: {},
        qbo: {},
        chatwidget: {},
        clio: {},
        drchrono: {},
        attribution: {},
        saas_settings,
      }
      this.status = LocationStatus.PROSPECT
      this.productStatus = {
        reviews: false,
        listings: false,
        conversations: false,
        social: false,
      }
      this.deleted = false
      this.dateAdded = moment().utc()
    }

    if (['Etc/Greenwich', 'GMT'].includes(this.timezone)) {
      this._data = mix(this._data, { timezone: 'UTC' })
    }

    this.isCalendarV3On = !!this.isCalendarV3On
    // TODO: The below section should be uncommented, if calendarV3 is required by default
    // if (this.isCalendarV3On === undefined) {
    //   this.isCalendarV3On = true
    // }

    this.isUserAvailabilityOn = !!this.isUserAvailabilityOn
    // TODO: The below section should be uncommented, if user availability feature is required by default
    // if (this.isUserAvailabilityOn === undefined) {
    //   this.isUserAvailabilityOn = true
    // }
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get showAddEliza(): boolean {
    return this._data.show_add_eliza
  }

  set showAddEliza(showAddEliza: boolean) {
    this._data.show_add_eliza = showAddEliza
  }

  get showTwilioSummary(): boolean {
    return this._data.show_twilio_summary
  }

  set showTwilioSummary(showTwilioSummary: boolean) {
    this._data.show_twilio_summary = showTwilioSummary
  }

  get botService(): boolean {
    return this._data.bot_service
  }

  set botService(botService: boolean) {
    this._data.bot_service = botService
  }

  get permissions(): ILocationPermissions {
    if (!this._data.permissions) {
      const permissions = {} as ILocationPermissions

      permissions.adwords_reporting_enabled = true
      permissions.appointments_enabled = true
      permissions.attributions_reporting_enabled = true
      permissions.agent_reporting_enabled = true
      permissions.bot_service_enabled = true
      permissions.bulk_requests_enabled = true
      permissions.campaigns_enabled = true
      permissions.contacts_enabled = true
      permissions.conversations_enabled = true
      permissions.dashboard_stats_enabled = true
      permissions.email_builder_enabled = true
      permissions.facebook_ads_reporting_enabled = true
      permissions.facebook_messenger_enabled = true
      permissions.forms_enabled = true
      permissions.funnels_enabled = true
      // permissions.google_ads_reporting_enabled = true
      permissions.gmb_call_tracking_enabled = true
      permissions.gmb_messaging_enabled = true
      permissions.html_builder_enabled = true
      permissions.membership_enabled = true
      permissions.marketing_enabled = true
      permissions.opportunities_enabled = true
      permissions.phone_call_enabled = true
      permissions.reviews_enabled = true
      permissions.settings_enabled = true
      permissions.sms_email_templates_enabled = true
      permissions.surveys_enabled = true
      permissions.text_to_pay_enabled = true
      permissions.tags_enabled = true
      permissions.triggers_enabled = true
      permissions.trigger_links_enabled = true
      permissions.web_chat_enabled = true
      permissions.websites_enabled = true
      permissions.workflows_enabled = true

      this._data.permissions = permissions
    }

    return this._data.permissions
  }

  set permissions(permissions: ILocationPermissions) {
    this._data.permissions = permissions
  }

  get firstShotEnabled(): boolean {
    return this._data.first_shot_enabled
  }

  set firstShotEnabled(firstShotEnabled: boolean) {
    this._data.first_shot_enabled = firstShotEnabled
  }

  get defaultLogicalEliza(): string {
    return this._data.default_logical_eliza
  }

  set defaultLogicalEliza(defaultLogicalEliza: string) {
    this._data.default_logical_eliza = defaultLogicalEliza
  }

  get ignoreElizaTag(): string {
    return this._data.ignore_eliza_tag
  }

  set ignoreElizaTag(ignoreElizaTag: string) {
    this._data.ignore_eliza_tag = ignoreElizaTag
  }
  get sendElizaTag(): string {
    return this._data.send_eliza_tag
  }

  set sendElizaTag(sendElizaTag: string) {
    this._data.send_eliza_tag = sendElizaTag
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get fbTokenExpired(): boolean {
    return this._data.fb_token_expired
  }

  set fbTokenExpired(fbTokenExpired: boolean) {
    this._data.fb_token_expired = fbTokenExpired
  }

  get gmbTokenExpired(): boolean {
    return this._data.gmb_token_expired
  }

  set gmbTokenExpired(gmbTokenExpired: boolean) {
    this._data.gmb_token_expired = gmbTokenExpired
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
    this._data.name_lower_case = name.toLocaleLowerCase()
  }

  get address(): string {
    return this._data.address
  }

  set address(address: string) {
    this._data.address = address
  }

  get city(): string {
    return this._data.city
  }

  set city(city: string) {
    this._data.city = city
  }

  get state(): string {
    return this._data.state
  }

  set state(state: string) {
    this._data.state = state
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
  }

  get phone(): string {
    return this._data.phone
  }

  set phone(phone: string) {
    this._data.phone = phone
  }

  get postalCode(): string {
    return this._data.postal_code
  }

  set postalCode(postalCode: string) {
    this._data.postal_code = postalCode
  }

  get country(): string {
    return this._data.country
  }

  set country(country: string) {
    this._data.country = country
  }

  get addressGeo(): firebase.firestore.GeoPoint | undefined {
    return this._data.address_geo
  }

  set addressGeo(addressGeo: firebase.firestore.GeoPoint | undefined) {
    this._data.address_geo = addressGeo
  }

  get website(): string {
    return this._data.website
  }

  set website(website: string) {
    this._data.website = website
  }

  get searchTerm(): string {
    return this._data.search_term
  }

  set searchTerm(searchTerm: string) {
    this._data.search_term = searchTerm
  }

  get facebookUrl(): string {
    return this._data.facebook_url
  }

  set facebookUrl(facebookUrl: string) {
    this._data.facebook_url = facebookUrl
  }

  get facebookPageId(): string {
    return this._data.facebook_page_id
  }

  set facebookPageId(facebookPageId: string) {
    this._data.facebook_page_id = facebookPageId
  }

  get facebookAdAccount(): string {
    return this._data.facebook_ad_account
  }

  set facebookAdAccount(facebookAdAccount: string) {
    this._data.facebook_ad_account = facebookAdAccount
  }

  get linkedInPageId(): string {
    return this._data.linkedin_page_id
  }

  set linkedInPageId(linkedInPageId: string) {
    this._data.linkedin_page_id = linkedInPageId
  }

  set googlePlacesId(googlePlacesId: string) {
    this._data.google_places_id = googlePlacesId
  }

  get googlePlacesId(): string {
    return this._data.google_places_id
  }

  get gmb(): GoogleMyBusiness {
    if (!this._data.gmb) this._data.gmb = {}
    return this._data.gmb
  }

  get facebookPageToken(): string {
    return this._data.facebook_page_token
  }

  set facebookPageToken(facebookPageToken: string) {
    this._data.facebook_page_token = facebookPageToken
  }

  get facebookPageName(): string {
    return this._data.facebook_page_name
  }

  set facebookPageName(facebookPageName: string) {
    this._data.facebook_page_name = facebookPageName
  }

  get googlePlus(): string {
    return this._data.google_plus
  }

  set googlePlus(googlePlus: string) {
    this._data.google_plus = googlePlus
  }

  get twitter(): string {
    return this._data.twitter
  }

  set twitter(twitter: string) {
    this._data.twitter = twitter
  }

  get linkedIn(): string {
    return this._data.linked_in
  }

  set linkedIn(linkedIn: string) {
    this._data.linked_in = linkedIn
  }

  get pinterest(): string {
    return this._data.pinterest
  }

  set pinterest(pinterest: string) {
    this._data.pinterest = pinterest
  }

  get instagram(): string {
    return this._data.instagram
  }

  set instagram(instagram: string) {
    this._data.instagram = instagram
  }

  get youtube(): string {
    return this._data.youtube
  }

  set youtube(youtube: string) {
    this._data.youtube = youtube
  }

  get blogRss(): string {
    return this._data.blog_rss
  }

  set blogRss(blogRss: string) {
    this._data.blog_rss = blogRss
  }

  get yelp(): string {
    return this._data.yelp
  }

  set yelp(yelp: string) {
    this._data.yelp = yelp
  }

  get foursquare(): string {
    return this._data.foursquare
  }

  set foursquare(foursquare: string) {
    this._data.foursquare = foursquare
  }

  get reviewLink(): string {
    return this._data.review_link
  }

  set reviewLink(reviewLink: string) {
    this._data.review_link = reviewLink
  }

  get logoUrl(): string {
    return this._data.logo_url
  }

  set logoUrl(logoUrl: string) {
    this._data.logo_url = logoUrl
  }

  get settings(): Settings {
    if (!this._data.settings) this._data.settings = {}
    if (!this._data.settings.sms) this._data.settings.sms = {}
    if (!this._data.settings.email) this._data.settings.email = {}
    if (!this._data.settings.review) this._data.settings.review = {}
    if (!this._data.settings.drchrono) this._data.settings.drchrono = {}
    if (!this._data.settings.social) this._data.settings.social = {}
    if (!this._data.settings.clio) this._data.settings.clio = {}
    if (!this._data.settings.widget) this._data.settings.widget = {}
    if (!this._data.settings.qbo) this._data.settings.qbo = {}
    if (!this._data.settings.chatwidget) this._data.settings.chatwidget = {}
    if (!this._data.settings.attribution) this._data.settings.attribution = {}
    if (!this._data.settings.textwidget) this._data.settings.textwidget = {}
    if (!this._data.settings.saas_settings) {
      const twilioRebilling = {
        enabled: false,
        markup: 10,
      } as ITwilioRebillingSettings

      this._data.settings.saas_settings = {
        saas_mode: 'not_activated',
        twilio_rebilling: twilioRebilling,
      } as ISaasSettings
    }

    return this._data.settings
  }

  set settings(settings: Settings) {
    this._data.settings = settings
  }

  get yextId(): string {
    return this._data.yext_id
  }

  set yextId(yextId: string) {
    this._data.yext_id = yextId
  }

  get categories(): string[] {
    return this._data.categories
  }

  set categories(categories: string[]) {
    this._data.categories = categories
  }

  get status(): string {
    return this._data.status
  }

  set status(status: string) {
    this._data.status = status
  }

  get prospectInfo(): ProspectInfo {
    return this._data.prospect_info
  }

  set prospectInfo(prospectInfo: ProspectInfo) {
    this._data.prospect_info = prospectInfo
  }

  get gmbSettingInfo(): GMBSettingInfo {
    return this._data.gmb_setting_info
  }

  set gmbSettingInfo(GMBSettingInfo: GMBSettingInfo) {
    this._data.gmb_setting_info = GMBSettingInfo
  }

  get gmbMessagingStatus(): GMBMessagingStaus {
    return this._data.gmb_messaging_status
  }

  set gmbMessagingStatus(gmbMessagingStaus: GMBMessagingStaus) {
    this._data.gmb_messaging_status = gmbMessagingStaus
  }

  get gmbMessagingScopeExist(): boolean {
    return this._data.gmb_business_scope_exist
  }

  set gmbMessagingScopeExist(gmbMessagingScopeExist: boolean) {
    this._data.gmb_business_scope_exist = gmbMessagingScopeExist
  }

  get productStatus(): ProductStatus {
    return this._data.product_status
  }

  set productStatus(productStatus: ProductStatus) {
    this._data.product_status = productStatus
  }

  public async convertToAccount() {
    this.status = LocationStatus.ACCOUNT
    await this.save()
  }

  get twitterUrl(): string {
    return this._data.twitter_url
  }

  set twitterUrl(twitterUrl: string) {
    this._data.twitter_url = twitterUrl
  }

  get scanReportId(): string {
    return this._data.scan_report_id
  }

  set scanReportId(scanReportId: string) {
    this._data.scan_report_id = scanReportId
  }

  get socialSettings(): { [key: string]: any } {
    if (!this.settings.social) this.settings.social = {}
    return this.settings.social
  }

  get clioSettings(): { [key: string]: any } {
    if (!this.settings.clio) this.settings.clio = {}
    return this.settings.clio
  }

  get drChronoSettings(): { [key: string]: any } {
    if (!this.settings.drchrono) this.settings.drchrono = {}
    return this.settings.drchrono
  }

  get googleAnalyticsAccountId(): string {
    return this._data.google_analytics_account_id
  }

  set googleAnalyticsAccountId(googleAnalyticsAccountId: string) {
    this._data.google_analytics_account_id = googleAnalyticsAccountId
  }

  get googleAnalyticsViewId(): string {
    return this._data.google_analytics_view_id
  }

  set googleAnalyticsViewId(googleAnalyticsViewId: string) {
    this._data.google_analytics_view_id = googleAnalyticsViewId
  }

  get googleAnalyticsWebPropertyId(): string {
    return this._data.google_analytics_web_property_id
  }

  set googleAnalyticsWebPropertyId(googleAnalyticsWebPropertyId: string) {
    this._data.google_analytics_web_property_id = googleAnalyticsWebPropertyId
  }

  get googleAdwordsId(): string {
    return this._data.google_adwords_id
  }

  set googleAdwordsId(googleAdwordsId: string) {
    this._data.google_adwords_id = googleAdwordsId
  }

  get googleAdwordsMCCId(): string {
    return this._data.google_adwords_mcc_id
  }

  set googleAdwordsMCCId(googleAdwordsCCId: string) {
    this._data.google_adwords_mcc_id = googleAdwordsCCId
  }

  get timezone(): string {
    return this._data.timezone
  }

  set timezone(timezone: string) {
    this._data.timezone = timezone
  }

  /**
   * Whether new calendar feature (Calendar v3) is turned on or not
   */
  get isCalendarV3On(): boolean {
    return this._data.is_calendar_v3_on
  }

  set isCalendarV3On(value: boolean) {
    this._data.is_calendar_v3_on = value
  }

  /**
   * Whether UserAvailability feature is turned on or not.
   * Allow location users to set their own availability from their profile page. This really helps when calendar service team members (users) are working in different time zones globally. You can call this Global Availability.
   */
  get isUserAvailabilityOn(): boolean {
    return this._data.is_user_availability_on
  }

  set isUserAvailabilityOn(value: boolean) {
    this._data.is_user_availability_on = value
  }

  get snapshotsLoaded(): string[] {
    return this._data['snapshots_loaded']
  }

  get apiKey(): string {
    // if (!this._data.api_key) {
    //   this._data.api_key = uuid()
    //   this.save()
    // }
    return this._data.api_key
  }

  set apiKey(apiKey: string) {
    this._data.api_key = apiKey
  }

  get formatedName() {
    let name = this.name
    if (this.address || this.city || this.state) name += ' -- '
    if (this.address) name += this.address
    if (this.address && this.city) name += ', '
    if (this.city) name += this.city
    if (this.city && this.state) name += ', '
    if (this.state) name += this.state
    return name
  }

  get fullAddressLine(): string {
    let address = ''
    if (this.address) {
      address += this.address + ' '
    }
    if (this.city) {
      address += this.city + ' '
    }
    if (this.state) {
      address += this.state + ' '
    }
    if (this.postalCode) {
      address += this.postalCode
    }
    return address
  }

  get stripe(): Stripe {
    return this._data.stripe
  }

  set stripe(stripe: Stripe) {
    this._data.stripe = stripe
  }

  get stripeConnectId(): string {
    return this._data['stripe_connect_id']
  }

  set stripeConnectId(stripeConnectId: string) {
    this._data['stripe_connect_id'] = stripeConnectId
  }

  get primaryGoogleConnectionId(): string {
    return this._data.primary_google_connection_id
  }

  set primaryGoogleConnectionId(primaryGoogleConnectionId: string) {
    this._data.primary_google_connection_id = primaryGoogleConnectionId
  }

  get defaultEmailService(): string {
    return this._data.default_email_service
  }

  set defaultEmailService(defaultEmailService: string) {
    this._data.default_email_service = defaultEmailService
  }

  get allowDuplicateContact(): boolean {
    return this._data.allow_duplicate_contact !== undefined
      ? this._data.allow_duplicate_contact
      : false
  }

  set allowDuplicateContact(allowDuplicateContact: boolean) {
    this._data.allow_duplicate_contact = allowDuplicateContact
  }

  get allowDuplicateOpportunity(): boolean {
    return this._data.allow_duplicate_opportunity !== undefined
      ? this._data.allow_duplicate_opportunity
      : false
  }

  set allowDuplicateOpportunity(allowDuplicateOpportunity: boolean) {
    this._data.allow_duplicate_opportunity = allowDuplicateOpportunity
  }

  get disableContactTimezone(): boolean {
    return this._data.disable_contact_timezone !== undefined
      ? this._data.disable_contact_timezone
      : false
  }

  set disableContactTimezone(disableContactTimezone: boolean) {
    this._data.disable_contact_timezone = disableContactTimezone
  }

  // get validatePhoneNumbers(): boolean {
  //   return this._data.validate_phone_numbers !== undefined
  //     ? this._data.validate_phone_numbers
  //     : false
  // }

  // set validatePhoneNumbers(validatePhoneNumbers: boolean) {
  //   this._data.validate_phone_numbers = validatePhoneNumbers
  // }

  get allowFacebookNameMerge(): boolean {
    return this._data.allow_facebook_name_merge !== undefined
      ? this._data.allow_facebook_name_merge
      : false
  }

  set allowFacebookNameMerge(allowFacebookNameMerge: boolean) {
    this._data.allow_facebook_name_merge = allowFacebookNameMerge
  }

  get esUpdateStatus(): string {
    return this._data.es_update_status
  }

  set esUpdateStatus(val: string) {
    this._data.es_update_status = val
  }

  get hipaaCompliance(): boolean {
    return this._data['hipaa_compliance'] || false
  }

  set hipaaCompliance(hipaaCompliance: boolean) {
    this._data['hipaa_compliance'] = hipaaCompliance || false
  }

  get startDayOfWeek(): number {
    if (this._data.start_day_of_week === undefined) return 0
    return this._data.start_day_of_week
  }

  set startDayOfWeek(value: number) {
    this._data.start_day_of_week = value
  }

  get language(): string {
    return this._data.language || '' // Default to '' which is English
  }

  set language(val: string) {
    this._data.language = val
  }

  get use24hFormat(): boolean {
    return this._data.use_24h_format || false
  }

  set use24hFormat(use24hFormat: boolean) {
    this._data.use_24h_format = use24hFormat || false
  }

  get allowBetaAccess(): AllowBetaAccess {
    return this._data.allowBetaAccess
  }

  set allowBetaAccess(allowBetaAccess: AllowBetaAccess) {
    this._data.allowBetaAccess = allowBetaAccess
  }

  get hasPaymentsBetaAccess() {
    return this.allowBetaAccess && this.allowBetaAccess.payments;
  }

  public async getTimeZone() {
    if (this.timezone) return this.timezone
    const company = await Company.getById(this.companyId)
    if (company.timezone) {
      this.timezone = company.timezone
      await this.save()
      return this.timezone
    }
    return ''
  }

  public save() {
    const _self = this

    //  || isFirestoreDelete(_self.data[x]
    if (_self.data && Location.requiredFields.some(x => !_self.data[x])) { // Make sure that we don't use the same syntax when we add boolean field which will contains false
      throw Error('Required field missing for location');
    }

    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc()
      _self._ref.set(_self.data).then(_ => {
        resolve(_self)
      })
    })
  }

  public delete() {
    return Location.collectionRef().doc(this.id).update({
      deleted: true,
    })
  }

  private static colours: string[] = [
    '#5A8770',
    '#B2B7BB',
    '#6FA9AB',
    '#F5AF29',
    '#0088B9',
    '#F18636',
    '#D93A37',
    '#A6B12E',
    '#5C9BBC',
    '#F5888D',
    '#9A89B5',
    '#407887',
    '#9A89B5',
    '#5A8770',
    '#D33F33',
    '#A2B01F',
    '#F0B126',
    '#0087BF',
    '#F18636',
    '#0087BF',
    '#B2B7BB',
    '#72ACAE',
    '#9C8AB4',
    '#5A8770',
    '#EEB424',
    '#407887',
  ]

  get initials(): string {
    const initials = ''
    const parts = this.name.split(/(\s+)/)
    // if (this.name) initials += this.name.substring(0, 1).toUpperCase();
    // if (this.lastName) initials += this.lastName.substring(0, 1).toUpperCase();
    return 'PPAO'
  }

  get profileColor(): string {
    // const firstCharIndex = (this.firstName || 'A').toUpperCase().charCodeAt(0) - 65;
    // const secondCharIndex = (this.lastName || 'A').toUpperCase().charCodeAt(0) - 65;
    // const colourIndex = Math.round((firstCharIndex + secondCharIndex) / 2) % 25;
    return Location.colours[1]
  }

  get forwardingAddresses(): string {
    return this._data.forwarding_addresses
  }

  set forwardingAddresses(forwarding_addresses: string) {
    this._data.forwarding_addresses = forwarding_addresses
  }

  get bccEmails(): string {
    return this._data.bcc_emails
  }

  set bccEmails(bccEmails: string) {
    this._data.bcc_emails = bccEmails
  }

  get forwardToAssignedUser(): boolean {
    return this._data.forward_to_assigned_user
  }

  set forwardToAssignedUser(forwardToAssignedUser: boolean) {
    this._data.forward_to_assigned_user = forwardToAssignedUser
  }

  get voicemailFile(): VoicemailFile {
    return this._data.voicemail_file || {}
  }

  set voicemailFile(voicemailFile: VoicemailFile) {
    this._data.voicemail_file = voicemailFile
  }

  get incomingCallTimeout(): number {
    return this._data.incoming_call_timeout
  }

  set incomingCallTimeout(incomingCallTimeout: number) {
    this._data.incoming_call_timeout = incomingCallTimeout
  }

  get facebookIgnoreMessages(): boolean {
    return this._data.facebook_ignore_messages
  }

  set facebookIgnoreMessages(facebookIgnoreMessages: boolean) {
    this._data.facebook_ignore_messages = facebookIgnoreMessages
  }

  get instagramPageId(): string {
    return this._data.instagram_page_id
  }

  set instagramPageId(instagramPageId: string) {
    this._data.instagram_page_id = instagramPageId
  }

  get instagramIgnoreMessages(): boolean {
    return this._data.instagram_ignore_messages
  }

  set instagramIgnoreMessages(instagramIgnoreMessages: boolean) {
    this._data.instagram_ignore_messages = instagramIgnoreMessages
  }

  get stripeConnectMode(): string {
    return this._data.stripe_connect_mode || 'live'
  }

  set stripeConnectMode(stripeConnectMode: string) {
    this._data.stripe_connect_mode = stripeConnectMode
  }

  get testStripeConnectId(): string {
    return this._data['test_stripe_connect_id']
  }

  set testStripeConnectId(testStripeConnectId: string) {
    this._data['test_stripe_connect_id'] = testStripeConnectId
  }

  get launchpadTooltip(): boolean {
    return this._data.launchpadTooltip
  }

  set launchpadTooltip(launchpadTooltip: boolean) {
    this._data.launchpadTooltip = launchpadTooltip
  }

  get isGmbDisconnected(): boolean {
    return this._data.is_gmb_disconnected
  }

  set isGmbDisconnected(isGmbDisconnected: boolean) {
    this._data.is_gmb_disconnected = isGmbDisconnected
  }

  get isFbDisconnected(): boolean {
    return this._data.is_fb_disconnected
  }

  set isFbDisconnected(isFbDisconnected: boolean) {
    this._data.is_fb_disconnected = isFbDisconnected
  }

  get launchpad(): LaunchpadStatus {
    return this._data.launchpad || {}
  }

  set launchpad(launchpad: LaunchpadStatus) {
    this._data.launchpad = launchpad
  }

  get lastUpdatedBy(): CreatedOrUpdatedBy {
    return this._data.last_updated_by
  }

  set lastUpdatedBy(value: CreatedOrUpdatedBy) {
    this._data.last_updated_by = value
  }

  get recentGoogleConnectionId(): string | undefined {
    return this._data.recent_google_connection_id
  }

  set recentGoogleConnectionId(recentGoogleConnectionId: string | undefined) {
    this._data.recent_google_connection_id = recentGoogleConnectionId
  }

  get depreciatedFeatures(): {[key: string]: any}[] {
    return this._data.depreciated_features ? this._data.depreciated_features : []
  }

  set depreciatedFeatures(features: {[key: string]: any}[]) {
    this._data.depreciated_features = features
  }

  get yextReseller(): object {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    if (this._data.reseller.yext) {
      return this._data.reseller.yext
    }
    this._data.reseller.yext = { location_enabled: true }
    return this._data.reseller.yext
  }

  set yextReseller(yextSetting: { [key: string]: any }) {
    if (!this._data.reseller) {
      this._data.reseller = {}
    }
    this._data.reseller.yext = { ...yextSetting, hl_price: 30 } // hl_price:30 is important because client and try to override although we are only using it display purpose
  }


  get wordpressReseller(): WordpressLocationSettings {
    if(!this._data.reseller){
      this._data.reseller = {};
    }
    if(this._data.reseller.wordpress){
      return this._data.reseller.wordpress
    }
    this._data.reseller.wordpress = { location_enabled:true}
    return  this._data.reseller.wordpress;
  }

  set wordpressReseller(wpSetting: { [key: string]: any }) {
    if(!this._data.reseller){
      this._data.reseller = {};
    }
    this._data.reseller.wordpress = {...wpSetting, hl_price:10 }; // hl_price:30 is important because client and try to override although we are only using it display purpose
  }

  get enableMissedCallText(): boolean {
    if (
      moment('2021-08-13').isSameOrBefore(this.dateAdded.format('YYYY-MM-DD'))
    ) {
      return this._data.enable_missed_call_text !== undefined
        ? this._data.enable_missed_call_text
        : true
    }
    return this._data.enable_missed_call_text !== undefined
      ? this._data.enable_missed_call_text
      : false
  }

  set enableMissedCallText(enableMissedCallText: boolean) {
    this._data.enable_missed_call_text = enableMissedCallText
  }

  get missedCallText(): string {
    return (
      this._data.missed_call_text ||
      'Hi this is {{location.name}}, I saw that we just missed your call how can I help?'
    )
  }

  set missedCallText(missedCallText: string) {
    this._data.missed_call_text = missedCallText
  }

  get shopify (): Shopify {
    return this._data.shopify;
  }

  set shopify (shopify: Shopify) {
    this._data.shopify = shopify;
  }
}
