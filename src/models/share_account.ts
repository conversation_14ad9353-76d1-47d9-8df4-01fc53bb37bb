import firebase from 'firebase/app'
import moment from 'moment-timezone'
import Custom<PERSON><PERSON> from './custom_field'
import { v4 as uuid } from 'uuid'
import { CloneAccount } from '.'

export default class ShareAccount {
  public static collectionRef() {
    return firebase.firestore().collection('share_account')
  }

  public static async getById(id: string): Promise<ShareAccount | undefined> {
    const snapshot = await ShareAccount.collectionRef()
      .doc(id)
      .get()
    if (!snapshot.exists || snapshot.data().deleted) return undefined
    return new ShareAccount(snapshot)
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(params?: { [key: string]: any } | firebase.firestore.DocumentSnapshot) {
    if (params instanceof firebase.firestore.DocumentSnapshot) {
      this._id = params.id
      this._data = params.data() || {}
      this._ref = params.ref
    } else if (params) {
      this._id = params.id
      this._ref = ShareAccount.collectionRef().doc(params.id)
      this._data = params || {}
    } else {
      this._ref = ShareAccount.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
    }
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get redeemedBy(): string {
    return this._data.redeemed_by
  }

  set redeemedBy(redeemedBy: string) {
    this._data.redeemed_by = redeemedBy
  }

  get snapshotId(): string {
    return this._data.snapshot_id
  }

  set snapshotId(snapshotId: string) {
    this._data.snapshot_id = snapshotId
  }

  get accountData(): { [key: string]: any } {
    return this._data.account_data
  }

  set accountData(accountData: { [key: string]: any }) {
    this._data.account_data = accountData
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get type(): 'email' | 'link' | 'permanent_link' {
    return this._data.type
  }

  set type(type: 'email' | 'link' | 'permanent_link') {
    this._data.type = type
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
  }

  public async save(): Promise<ShareAccount> {
    this.dateUpdated = moment()
    await this._ref.set(this.data)
    return this
  }

  public async redeem(companyId: string) {
    const cloneAccount = new CloneAccount()
    cloneAccount.companyId = companyId
    cloneAccount.name = this.name
    cloneAccount.type = 'imported'
    cloneAccount.accountData = this.accountData
    await cloneAccount.save()
    if (this.type !== 'permanent_link') this.redeemedBy = companyId
    await this.save()
  }

  public static async share(
    account: CloneAccount,
    extras: { type: 'email' | 'link'; email?: string }
  ) {
    const share = new ShareAccount()
    share.companyId = account.companyId
    share.name = account.name
    share.type = extras.type
    if (extras.email) share.email = extras.email
    share.accountData = account.accountData
    await share.save()
    return share
  }
}
