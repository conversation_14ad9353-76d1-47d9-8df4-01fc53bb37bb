// import  firebase from 'firebase/app';
// import moment from 'moment-timezone';
// import { CampaignData } from './campaign';

// export default class CampaignVersion {
// 	public static collectionRef():firebase.firestore.CollectionReference {
// 		return firebase.firestore().collection('campaign_versions');
// 	}

// 	public async save() {
// 		await this._ref.set(this._data);
// 	}

// 	public static getById(id): Promise<CampaignVersion> {
// 		return new Promise((resolve, reject) => {
// 			CampaignVersion.collectionRef()
// 				.doc(id)
// 				.get()
// 				.then((snapshot) => {
// 					resolve(new CampaignVersion(snapshot));
// 				})
// 				.catch((err) => {
// 					console.error(err);
// 					reject(err);
// 				});
// 		});
// 	}

// 	public static async getByCampaignId(campaignId: string): Promise<CampaignVersion> {
// 		const snapshot = await CampaignVersion.collectionRef()
// 			.where('campaign_id', '==', campaignId)
// 			.orderBy('date_added', 'desc')
// 			.limit(1)
// 			.get();
// 		if (!snapshot.empty) return new CampaignVersion(snapshot.docs[0]);
// 		return undefined;
// 	}

// 	private _id: string;
// 	private _data:firebase.firestore.DocumentData;
// 	private _ref:firebase.firestore.DocumentReference;

// 	constructor(snapshot?:firebase.firestore.DocumentSnapshot) {
// 		if (snapshot) {
// 			this._id = snapshot.id;
// 			this._ref = snapshot.ref;
// 			this._data = snapshot.data();
// 		} else {
// 			this._ref = CampaignVersion.collectionRef().doc();
// 			this._id = this._ref.id;
// 			this._data = {};
// 			this.dateAdded = moment();
// 		}
// 	}

// 	get id(): string {
// 		return this._id;
// 	}

// 	get data():firebase.firestore.DocumentData {
// 		return this._data;
// 	}

// 	get locationId(): string {
// 		return this._data.location_id;
// 	}

// 	set locationId(locationId: string) {
// 		this._data.location_id = locationId;
// 	}

// 	get campaignId(): string {
// 		return this._data.campaign_id;
// 	}

// 	set campaignId(campaignId: string) {
// 		this._data.campaign_id = campaignId;
// 	}

// 	get dateAdded(): moment.Moment {
// 		return moment(this._data.date_added.toMillis());
// 	}

// 	set dateAdded(dateAdded: moment.Moment) {
// 		this._data.date_added =firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
// 	}

// 	get campaignData(): CampaignData {
// 		return this._data.campaign_data;
// 	}

// 	set campaignData(campaignData: CampaignData) {
// 		this._data.campaign_data = campaignData;
// 	}
// }
