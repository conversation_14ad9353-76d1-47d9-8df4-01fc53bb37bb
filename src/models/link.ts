import firebase from 'firebase/app'
import moment from 'moment-timezone'

export default class Link {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('links')
  }

  public save(): Promise<Link> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment()
      _self._ref.set(_self.data).then(_ => {
        resolve(_self)
      })
    })
  }

  public static getById(linkId: string): Promise<Link> {
    return new Promise((resolve, reject) => {
      Link.collectionRef()
        .doc(linkId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Link(snapshot))
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    })
  }

  public static fetchAllByLocation(
    locationId: string
  ): firebase.firestore.Query {
    return Link.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('name')
  }

  public static getByLocationId(locationId: string): Promise<Link[]> {
    return new Promise((resolve, reject) => {
      Link.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new Link(d)))
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Link | undefined> {
    const snapshot = await Link.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Link(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = Link.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment()
      this.deleted = false
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get dateAdded(): moment.Moment {
    return moment.unix(this._data.date_added.seconds)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated)
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get redirectTo(): string {
    return this._data.redirect_to
  }

  set redirectTo(redirect_to: string) {
    this._data.redirect_to = redirect_to
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  getFinalURL(contactId: string): string {
    let json = { link: this._data.redirectTo, contact_id: contactId }
    let finalURL =
      'https://msgsndr.com/r/1/' +
      Buffer.from(JSON.stringify(json)).toString('base64')
    return finalURL
  }
}
