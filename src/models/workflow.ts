import firebase from 'firebase/app';
import moment from 'moment-timezone';
import WorkflowVersion from './workflow_version';
import { EmailTemplate, ActionCondition, SMSTemplate, CallTemplate, VoicemailTemplate, WaitTemplate } from './campaign';
import Axios from 'axios';
import config from '../config';

export type WorkflowStatus = 'draft' | 'published';

export type MessageTemplate = EmailTemplate | SMSTemplate | CallTemplate | VoicemailTemplate | MessengerTemplate | IfTemplate | ElseTemplate | WaitTemplate;

export interface Window {
	condition: ActionCondition;
	start: string;
	end: string;
	days: number[];
}

export interface WorkflowData {
	window?: Window;
	templates?: WorkflowTemplate<MessageTemplate>[];
}

export interface WorkflowTemplate<T> {
	id: string;
	type: 'sms' | 'email' | 'call' | 'voicemail' | 'messenger' | 'if_else' | 'wait';
	name: string;
	window?: Window;
	attributes: T;
	next?: string | string[];
	parent?: string;
	order: number;
	cat: string;
}

export interface MessengerTemplate {
	attachments?: string[];
	body: string;
}

export interface IfTemplate {
	if: boolean,
	operator?: 'and' | 'or',
	segments: IfSegmentTemplate[]
}

export interface IfSegmentTemplate {
	operator?: 'and' | 'or',
	conditions: IfConditionTemplate[]
}

export interface IfConditionTemplate {
	conditionType: string,
	conditionSubType: string,
	conditionOperator: string,
	conditionValue: string
}

export interface ElseTemplate {
	else: boolean
}

export interface WorkflowStatusModel {
  id: string;
  contactId: string;
  workflowId: string;
  locationId: string;
  enrollType: string; // trigger, workflow
  currentStepId: string;
  currentStepName: string;
  status: string;
  executeOn: Date;
  meta: any;
  waitingFor: string;
  sequence: number;
}

export default class Workflow {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('workflows');
	}

  // Used by old spm-ts workflow code
	// public static getById(id: string, locationId: string): Promise<Workflow> {
	// 	return new Promise((resolve, reject) => {
  //     Axios.get(`${config.workflowServiceURL}/${locationId}/${id}`)
  //     .then((res) => {
  //       const workflow = new Workflow()
  //       workflow._data = res
  //       return resolve(workflow)
  //     })
  //     .catch((err) => {
  //       console.error(err);
  //       reject(err.message);
  //     });
	// 	});
	// }

  // Used by old spm-ts workflow code
	// public static getQueryWithLocationId(locationId: string): firebase.firestore.Query {
	// 	return Workflow.collectionRef()
	// 		.where('deleted', '==', false)
	// 		.where('location_id', '==', locationId)
	// 		.orderBy('name');
	// }

	public static getWithLocationId(locationId: string): Promise<Workflow[]> {
		return new Promise((resolve, reject) => {
      Axios.get(`${config.workflowServiceURL}/${locationId}/`)
      .then((res) => {
        return resolve(res.data)
      })
      .catch((err) => {
        console.error(err);
        reject(err.message);
      });
		});
	}

  // Used by old spm-ts workflow code
	// public static getByLocationId(locationId: string): Promise<Workflow[]> {
	// 	return new Promise((resolve, reject) => {
	// 		Workflow.collectionRef()
	// 			.where('deleted', '==', false)
	// 			.where('location_id', '==', locationId)
	// 			.orderBy('name')
	// 			.get()
	// 			.then((snapshot) => {
	// 				return resolve(snapshot.docs.map((d) => new Workflow(d)));
	// 			})
	// 			.catch((err) => {
	// 				console.error(err);
	// 				reject(err.message);
	// 			});
	// 	});
	// }

  // Used by old spm-ts workflow code
	// Confirm with Varun
	// public static getCountByLocationId(locationId: string): Promise<number> {
	// 	return new Promise((resolve, reject) => {
	// 		Workflow.collectionRef()
	// 			.where('deleted', '==', false)
	// 			.where('location_id', '==', locationId)
	// 			.get()
	// 			.then((snapshot) => {
	// 				return resolve(snapshot.size);
	// 			})
	// 			.catch((err) => {
	// 				console.error(err);
	// 				reject(err.message);
	// 			});
	// 	});
	// }

	public static getWorkflowStatusesByContactId(contactId: string, locationId: string): Promise<WorkflowStatusModel[]> {
		return new Promise((resolve, reject) => {
      Axios.get(`${config.workflowServiceURL}/${locationId}/contact/${contactId}/statuses`)
				.then((res) => {
          if (res.data.count > 0) {
            return resolve(res.data.workflowStatuses)
          } else {
            return reject([]);
          }
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

  public static stopWorkflowStatusExecution(contactId: string, locationId: string, workflowId: string, userId: string, currentPage: string): Promise<any> {
		return new Promise((resolve, reject) => {
      Axios.post(`${config.workflowServiceURL}/${locationId}/contact/${contactId}/stop-execution`, {
        removedFrom: {
          userId,
          channel: 'web_app',
          source: currentPage
        },
        workflowId
      })
				.then((res) => {
          return resolve(res)
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data() || {};
		} else {
			this._ref = Workflow.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.deleted = false;
			this.dateAdded = moment();
			this.status = 'draft';
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get deleted() {
		return this._data.deleted;
	}

	set deleted(deleted) {
		this._data.deleted = deleted;
	}

	get status(): WorkflowStatus {
		return this._data.status;
	}

	set status(status: WorkflowStatus) {
		this._data.status = status;
	}

	get name(): string {
		return this._data.name;
	}

	set name(name: string) {
		this._data.name = name;
	}

	get workflowData(): WorkflowData {
		if (!this._data.workflow_data) this._data.workflow_data = {};
		return this._data.workflow_data;
	}

	set workflowData(workflowData: WorkflowData) {
		this._data.workflow_data = workflowData;
	}

	public async publish() {
		if (this.status === 'draft') {
			const version = new WorkflowVersion();
			version.locationId = this.locationId;
			version.workflowId = this.id;
			version.workflowData = this.workflowData;
			await version.save();
			this.status = 'published';
			await this.save();
		}
	}

	public save() {
		const _self = this;
		return new Promise((resolve, reject) => {
			_self.dateUpdated = moment().utc();
			_self._ref.set(_self.data).then((_) => {
				resolve(_self);
			});
		});
	}
}
