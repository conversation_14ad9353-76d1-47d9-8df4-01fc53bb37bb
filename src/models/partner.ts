import firebase from 'firebase/app';
import 'firebase/database';
import * as moment from 'moment-timezone';
import * as lodash from 'lodash';

export default class Partner {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('partners');
  }

  public static getById(id): Promise<Partner> {
    return new Promise((resolve, reject) => {
      Partner.collectionRef()
        .doc(id)
        .get()
        .then((snapshot) => {
          resolve(new Partner(snapshot));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static async getByPartnerName(
    locationId: string,
    name: string
  ): Promise<Partner | undefined> {
    const snapshot = await Partner.collectionRef()
      .where('name', '==', name)
      .where('deleted', '==', false)
      .get();
    return snapshot.docs.length > 0 ? new Partner(snapshot.docs[0]) : undefined;
  }

  private _id: string;
  private _data: { [field: string]: any };
  private _ref: firebase.firestore.DocumentReference;
  private _snapshot: firebase.firestore.DocumentSnapshot;

  constructor(
    snapshot?:
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
      | undefined
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id;
      this._data = snapshot.data();
      this._ref = snapshot.ref;
      this._snapshot = snapshot;
    } else if (snapshot && snapshot.id) {
      this._id = snapshot.id;
      this._data = snapshot;
      this._ref = Partner.collectionRef().doc(snapshot.id);
      if (snapshot.date_added) {
        this.dateAdded =
          typeof this._data.date_added === 'number'
            ? moment(snapshot.date_added)
            : moment(
                new firebase.firestore.Timestamp(
                  snapshot.date_added._seconds,
                  snapshot.date_added._nanoseconds
                ).toMillis()
              );
      }
      this.dateUpdated = moment();
    } else {
      this._ref = Partner.collectionRef().doc();
      this._id = this._ref.id;
      this._data = snapshot && !snapshot.id ? snapshot : {};
      // for adding new record from UI which has everything in it except id
      this.dateAdded = moment();
      this.dateUpdated = moment();
      this.deleted = false;
    }
  }

  public async save() {
    await this._ref.set(this._data);
  }

  public getESData() {
    if (!this.id) {
      throw new Error('Cannot fetch es data for an partner object without id');
    }
    const duplicateAsIs = [
      `name`,
      `city`,
      `state`,
      `country`,
      `rank`,
      'profile_photo',
      'review_display',
      'review_total'
    ];
    const es: { [key: string]: any } = lodash.pick(this.data, duplicateAsIs);
    es.tie_breaker_id = this.id;
    es.description =
      this.description &&
      this.description.length &&
      this.description.length > 50
        ? `${this.description.substr(0, 50)}...`
        : this.description;
    if (this.services && this.services.length) {
      es.services = this.services.filter((a) => typeof a === 'string');
      // Store only string objects in elastic search. Composite objects are a no go.
    }
    if (this.industries && this.industries.length) {
      es.industries = this.industries.filter((a) => typeof a === 'string');
      // Store only string objects in elastic search. Composite objects are a no go.
    }
    return es;
  }

  get id(): string {
    return this._id;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added
      ? typeof this._data.date_added === 'number'
        ? moment(this._data.date_added)
        : moment(this._data.date_added.toMillis())
      : undefined;
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    );
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    );
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get email(): string {
    return this._data.email;
  }

  set email(email: string) {
    this._data.email = email;
  }

  get company(): string {
    return this._data.company;
  }

  set company(company: string) {
    this._data.company = company;
  }

  get locationId(): string {
    return this._data.locationId;
  }

  set locationId(id: string) {
    this._data.locationId = id;
  }

  get languages(): string[] {
    if (!this._data.services) {
      this._data.languages = [];
    }
    return this._data.languages;
  }

  set languages(items: string[]) {
    this._data.languages = items;
  }

  get experience(): string {
    return this._data.experience;
  }

  set experience(experience: string) {
    this._data.experience = experience;
  }

  get description(): string {
    return this._data.description;
  }

  set description(description: string) {
    this._data.description = description;
  }

  get profilePhoto(): string {
    return this._data.profile_photo;
  }

  set profilePhoto(profilePhoto: string) {
    this._data.profile_photo = profilePhoto;
  }

  get socialMedia(): string[] {
    if (!this._data.services) {
      this._data.social_media = [];
    }
    return this._data.social_media;
  }

  set socialMedia(items: string[]) {
    this._data.social_media = items;
  }

  get services(): string[] {
    if (!this._data.services) {
      this._data.services = [];
    }
    return this._data.services;
  }

  set services(items: string[]) {
    this._data.services = items;
  }

  get industries(): string[] {
    if (!this._data.industries) {
      this._data.industries = [];
    }
    return this._data.industries;
  }

  set industries(items: string[]) {
    this._data.industries = items;
  }

  get rank(): string {
    return this._data.rank;
  }

  set rank(rank: string) {
    this._data.rank = rank;
  }

  get city(): string {
    return this._data.city;
  }

  set city(city: string) {
    this._data.city = city;
  }

  get state(): string {
    return this._data.state;
  }

  set state(state: string) {
    this._data.state = state;
  }

  get country(): string {
    return this._data.country;
  }

  set country(country: string) {
    this._data.country = country;
  }

  get reviewDisplay(): number {
    return this._data.review_display;
  }

  set reviewDisplay(num: number) {
    this._data.review_display = num;
  }

  get reviewTotal(): number {
    return this._data.review_total;
  }

  set reviewTotal(num: number) {
    this._data.review_total = num;
  }

  get reviews(): any {
    return this._data.reviews;
  }

  set reviews(reviews: any) {
    this._data.reviews = reviews;
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get badges(): string[] {
    return this._data.badges;
  }

  set badges(badges: string[]) {
    this._data.badges = badges;
  }
}
