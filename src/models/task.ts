import firebase from 'firebase/app';
import moment from 'moment-timezone';
import Activity, { ActionType, ActivityType } from './activity';

export default class Task {
  public static collectionRef() {
    return firebase.firestore().collection('tasks');
  }

  public static getTasksByUserId(locationId: string, userId: string): firebase.firestore.Query {
    return Task.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('assigned_to', '==', userId)
      .orderBy('date_added', 'desc');
  }

  public static getTasksByAccountId(locationId: string): firebase.firestore.Query {
    return Task.collectionRef()
      .where('deleted', '==', false)
      .where('account_id', '==', locationId)
      .orderBy('date_added', 'desc');
  }

  public static getAllTasksByLocationId(locationId: string): firebase.firestore.Query {
    return Task.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('date_added', 'desc');
  }

  public static getTasksByContactId(contactId: string, locationId: string): firebase.firestore.Query {
    return Task.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('contact_id', '==', contactId)
      .orderBy('date_added', 'desc');
  }

  public static getTasksByContactIdPending(contactId: string, locationId: string): firebase.firestore.Query {
    return Task.collectionRef()
      .where('deleted', '==', false)
      .where('contact_id', '==', contactId)
      .where('completed', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('date_added', 'desc');
  }

  public static getFrozenTasksByContactIdPending(contactId: string, locationId: string): Promise<Task[]> {
    return new Promise<Task[]>(async (resolve, reject) => {
      Task.getTasksByContactIdPending(contactId, locationId)
        .get()
        .then((snapshot) => {
          resolve(snapshot.docs.map((s) => new Task(s)));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static getById(id: string): Promise<Task> {
    return new Promise((resolve, reject) => {
      Task.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Task(snapshot));
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  public static async add(params: {
    companyId?: string;
    locationId?: string;
    userId: string;
    title: string;
    assignedTo?: string;
    dueDate?: moment.Moment;
    body: string;
    contactId?: string;
    accountId?: string;
  }) {
    const task = new Task();
    if (params.companyId) task.companyId = params.companyId;
    if (params.locationId) task.locationId = params.locationId;
    if (params.accountId) task.accountId = params.accountId;
    if (params.contactId) task.contactId = params.contactId;
    task.title = params.title;
    task.dateUpdated = moment().utc();
    if (params.body) task.body = params.body;
    if (params.assignedTo) task.assignedTo = params.assignedTo;
    if (params.dueDate) task.dueDate = params.dueDate;

    const activity = new Activity();
    if (params.companyId) activity.companyId = params.companyId;
    if (params.locationId) activity.locationId = params.locationId;
    if (params.accountId) activity.accountId = params.accountId;
    if (params.contactId) activity.contactId = params.contactId;
    activity.type = ActivityType.TASK;
    activity.actionType = ActionType.ADDED;
    activity.activityData = {
      body: params.title,
    };
    activity.dateUpdated = moment().utc();

    const batch = firebase.firestore().batch();
    batch.set(task.ref, task.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }

  private _data: firebase.firestore.DocumentData;
  private _id: string;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = Task.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.completed = false;
      this.dateAdded = moment().utc();
    }
  }

  get id(): string {
    return this._id;
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toDate());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromDate(dateAdded.toDate());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toDate());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromDate(dateUpdated.toDate());
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get accountId(): string {
    return this._data.account_id;
  }

  set accountId(accountId: string) {
    this._data.account_id = accountId;
  }

  get contactId(): string {
    return this._data.contact_id;
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId;
  }

  get userId(): string {
    return this._data.user_id;
  }

  set userId(userId: string) {
    this._data.user_id = userId;
  }

  get assignedTo(): string {
    return this._data.assigned_to;
  }

  set assignedTo(assignedTo: string) {
    this._data.assigned_to = assignedTo;
  }

  get dueDate(): moment.Moment {
    return this._data.due_date ? moment(this._data.due_date.toDate()) : this._data.due_date;
  }

  set dueDate(dueDate: moment.Moment) {
    this._data.due_date = firebase.firestore.Timestamp.fromDate(dueDate.toDate());
  }

  get body(): string {
    return this._data.body;
  }

  set body(body: string) {
    this._data.body = body;
  }

  get title(): string {
    return this._data.title;
  }

  set title(title: string) {
    this._data.title = title;
  }

  get completed(): boolean {
    return this._data.completed;
  }

  set completed(completed: boolean) {
    this._data.completed = completed;
    if (completed) {
      this.dateCompleted = moment();
    }
  }

  get dateCompleted(): moment.Moment {
    return moment(this._data.date_completed.toDate());
  }

  set dateCompleted(dateCompleted: moment.Moment) {
    this._data.date_completed = firebase.firestore.Timestamp.fromDate(dateCompleted.toDate());
  }

  public async update(userId: string) {
    const activity = new Activity();
    if (this.companyId) activity.companyId = this.companyId;
    if (this.locationId) activity.locationId = this.locationId;
    if (this.accountId) activity.accountId = this.accountId;
    if (this.contactId) activity.contactId = this.contactId;
    activity.userId = userId;
    activity.type = ActivityType.TASK;
    activity.actionType = ActionType.UPDATED;
    activity.activityData = {
      body: this.title,
    };
    activity.dateUpdated = moment();

    const batch = firebase.firestore().batch();
    batch.set(this.ref, this.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }

  public save(): Promise<Task> {
    const _self = this;
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment();
      _self._ref.set(this.data).then(() => {
        resolve(_self);
      });
    });
  }

  public async delete(userId: string) {
    this.deleted = true;

    const activity = new Activity();
    if (this.companyId) activity.companyId = this.companyId;
    if (this.locationId) activity.locationId = this.locationId;
    if (this.accountId) activity.accountId = this.accountId;
    if (this.contactId) activity.contactId = this.contactId;
    activity.userId = userId;
    activity.type = ActivityType.TASK;
    activity.actionType = ActionType.DELETED;
    activity.activityData = {
      body: this.title,
    };
    activity.dateUpdated = moment();

    const batch = firebase.firestore().batch();
    batch.set(this.ref, this.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }
}
