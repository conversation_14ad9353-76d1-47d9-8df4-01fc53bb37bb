import firebase from 'firebase/app'
import router from '../routes'
import vuex from '../store'

const countryLocale = [
  {
    code: 'US',
    locale: 'en-US',
    currency: { symbol: '$', separator: ',' },
    dateFormat: 'M' // middle-endian
  },
  {
    code: 'AU',
    locale: 'en-AU',
    currency: { symbol: 'A$', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'GB',
    locale: 'en-GB',
    currency: { symbol: '£', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'CA',
    locale: 'en-CA',
    currency: { symbol: 'C$', separator: ' ' },
    dateFormat: 'M' // middle-endian
  },
  {
    code: 'NZ',
    locale: 'en-NZ',
    currency: { symbol: '$', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'DE',
    locale: 'de-DE',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'DK',
    locale: 'da-DK',
    currency: { symbol: 'kr.', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'IN',
    locale: 'en-IN',
    currency: { symbol: '₹', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'FR',
    locale: 'fr-FR',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'BR',
    locale: 'pt-BR',
    currency: { symbol: 'R$', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'NO',
    locale: 'no-NO',
    currency: { symbol: 'kr', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'JE',
    locale: 'en-JE',
    currency: { symbol: '£', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'PR',
    locale: 'en-PR',
    currency: { symbol: '$', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'KE',
    locale: 'en-KE',
    currency: { symbol: 'Sh', separator: ' ' },
    dateFormat: 'M' // little-endian
  },
  {
    code: 'IE',
    locale: 'en-IE',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'PL',
    locale: 'en-PL',
    currency: { symbol: 'zł', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'EE',
    locale: 'et-EE',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'MY', //malaysian ringgit
    locale: 'ms-MY',
    currency: { symbol: 'RM', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'MX', //Mexican
    locale: 'es-MX',
    currency: { symbol: '$', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'HK', // Hong Kong
    locale: 'en-HK',
    currency: { symbol: 'HK$', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'ZA', // South Africa
    locale: 'en-ZA',
    currency: { symbol: 'R', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'PH', // Philippines
    locale: 'en-PH',
    currency: { symbol: '₱', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'NL', // Netherlands
    locale: 'en-NL',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'SI', // Slovenia
    locale: 'sl-SI',
    currency: { symbol: '€', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'PA', // Panama
    locale: 'es-PA',
    currency: { symbol: 'B/.', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'SG', // Singapore
    locale: 'en-SG',
    currency: { symbol: 'S$', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'CL', // Chile
    locale: 'es-CL',
    currency: { symbol: '$', separator: ',' },
    dateFormat: 'L' // little-endian
  },
  {
    code: 'ES', // Spain
    locale: 'es-ES',
    currency: { symbol: '€', separator: ' ' },
    dateFormat: 'L' // little-endian
  }
]

const getCountryCode = () => {
  // Returns the country code either from the location ID in the route, or from the company
  const currentLocationId = router.currentRoute.params.location_id
  if (currentLocationId) {
    const location: { [key: string]: any } = vuex.getters['locations/getById'](
      currentLocationId
    )
    if (location && location.country) {
      return location.country
    }
  }
  const company: { [key: string]: any } = vuex.getters['company/get']
  if (company && company.country) {
    return company.country
  }
  return 'US'
}

const getCountryInfo = (type: string) => {
  // Returns the value for the provided key in the countryLocale object
  const countryCode = getCountryCode()

  let found = countryLocale.find(country => country.code === countryCode)

  if (!found) found = countryLocale.find(country => country.code === 'US')
  return found[type]
}

const getCountryDateFormat = (type: boolean | string) => {
  // Returns the correct date format for the location/company locale
  const dateFormat = getCountryInfo('dateFormat')

  if (type === 'extended-weekday') {
    return dateFormat === 'M'
      ? 'ddd, MMM DD YYYY, hh:mm a'
      : 'ddd, DD MMM YYYY, hh:mm a'
  } else if (type === 'extended-normal') {
    return dateFormat === 'M' ? 'MMM DD YYYY, hh:mm a' : 'DD MMM YYYY, hh:mm a';
  } else if (type === 'weekday, month dateth') {
    return dateFormat === 'M' ? 'D, MMM dsu' : 'D, dsu MMM';
  } else if (type === 'normal') {
    return dateFormat === 'M' ? 'MMM Do, hh:mm a' : 'Do MMM, hh:mm a'
  } else if (type === 'month date, year') {
    return dateFormat === 'M' ? 'MMM Do, Y' : 'Do MMM, Y';
  } else if(type === 'month date year') {
    return dateFormat === 'M' ? 'MMM D, Y' : 'D MMM, Y';
  } else if (type === 'month date year, time') {
    return dateFormat === 'M' ? 'MMM D Y, h:mma' : 'D MMM Y, h:mma';
  } else if (type === 'month date extended-year') {
    return dateFormat === 'M' ? 'MMM Do YYYY' : 'Do MMM YYYY';
  } else if (type === 'month date extended-year, time') {
    return dateFormat === 'M' ? 'MMM Do YYYY, h:mma' : 'Do MMM YYYY, h:mma';
  } else if (type === 'basic-mdy') {
    return dateFormat === 'M' ? 'MMM DD YYYY' : 'DD MMM YYYY';
  }else if (type === 'basic-mdy-date-picker') {
    return dateFormat === 'M' ? 'MMM dd yyyy' : 'dd MMM yyyy';
  }
   else if (type === 'basic-mdy-slashed') {
    return dateFormat === 'M' ? 'M/d/yyyy' : 'd/M/yyyy';
  } else if (type === 'time-dynamic') {
    return dateFormat === 'M' ? 'hh:mm A' : 'HH:mm';
  } else {
    return dateFormat === 'M' ? 'ddd, MMM Do' : 'ddd, Do MMM';
  }
}

export { countryLocale, getCountryCode, getCountryInfo, getCountryDateFormat }

export default class Address {
  public address1?: string
  public city?: string
  public state?: string
  public postalCode?: string
  public country?: string
  public addressGeo?: firebase.firestore.GeoPoint

  constructor(address: {
    address1?: string
    city?: string
    state?: string
    postalCode?: string
    country?: string
    addressGeo?: firebase.firestore.GeoPoint
  }) {
    this.address1 = address.address1
    this.city = address.city
    this.state = address.state
    this.postalCode = address.postalCode
    this.country = address.country
    this.addressGeo = address.addressGeo
  }

  get fullAddressLine(): string {
    let address: string = ''
    if (this.address1) {
      address += this.address1 + ', '
    }
    if (this.city) {
      address += this.city + ' '
    }
    if (this.state) {
      address += this.state + ' '
    }
    if (this.postalCode) {
      address += this.postalCode
    }
    return address
  }
}
