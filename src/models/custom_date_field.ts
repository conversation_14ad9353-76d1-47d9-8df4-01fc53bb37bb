import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

export default class CustomDateField {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('custom_date_fields');
  }

  public async save() {
    this.dateUpdated = moment().utc();
    await this._ref.set(this.data);
  }

  public static async getByContactIdAndCustomFieldId(contactId: string, customFieldId: string, locationId: string): Promise<CustomDateField> {
    return new Promise<CustomDateField>(async (resolve, reject) => {
      try {
        await CustomDateField.collectionRef()
          .where('contact_id', '==', contactId)
          .where('location_id', '==', locationId)
          .where('custom_field_id', '==', customFieldId)
          .get()
          .then((snapshot) => {
            if (snapshot.empty) return resolve();
            resolve(new CustomDateField(snapshot.docs[0]));
          })
          .catch((err) => {
            reject(err);
          })
      } catch (err) {
        reject(err);
      }
    });
  }

  public static async removeCustomDateFields(customFieldId: string, locationId: string) {
    const limit = 100;
    let snapshot: firebase.firestore.QuerySnapshot;
    do {
      snapshot = await CustomDateField.collectionRef()
        .where('custom_field_id', '==', customFieldId)
        .where('location_id', '==', locationId)
        .limit(limit)
        .get();
      if (snapshot.size > 0) {
        await Promise.all(snapshot.docs.map(async (doc) => await doc.ref.delete()));
      }
    } while (snapshot.size === limit);
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;
  private _snapshot: firebase.firestore.DocumentSnapshot;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot | { [key: string]: any } | undefined) {
    if (snapshot instanceof firebase.firestore.DocumentSnapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data() || {};
      this._snapshot = snapshot;
    } else if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot;
      this._ref = CustomDateField.collectionRef().doc(snapshot.id);
      if (snapshot.date_added) this.dateAdded = moment(new firebase.firestore.Timestamp(snapshot.date_added._seconds, snapshot.date_added._nanoseconds).toMillis());
      if (snapshot.date_updated) this.dateUpdated = moment(new firebase.firestore.Timestamp(snapshot.date_updated._seconds, snapshot.date_updated._nanoseconds).toMillis());
      if (snapshot.date_value) this.dateValue = moment(new firebase.firestore.Timestamp(snapshot.date_value._seconds, snapshot.date_value._nanoseconds).toMillis());
    } else {
      this._ref = CustomDateField.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.dateAdded = moment().utc();
      this.deleted = false;
    }
  }

  get id(): string {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added ? moment(this._data.date_added.toMillis()) : undefined;
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
  }

  get dateUpdated(): moment.Moment {
    return this._data.date_updated ? moment(this._data.date_updated.toMillis()) : undefined;
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
  }

  get contactId(): string {
    return this._data.contact_id;
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId;
  }

  get dateValue(): moment.Moment {
    return this._data['date_value'] ? moment(this._data['date_value'].toMillis()) : undefined;
  }

  set dateValue(dateValue: moment.Moment) {
    this._data['date_value'] = firebase.firestore.Timestamp.fromMillis(dateValue.valueOf());
    this.dayValue = dateValue.date();
    this.monthValue = dateValue.month();
  }

  get monthValue(): number {
    return this._data.month_value;
  }

  set monthValue(monthValue: number) {
    this._data.month_value = monthValue;
  }

  get dayValue(): number {
    return this._data.day_value;
  }

  set dayValue(dayValue: number) {
    this._data.day_value = dayValue;
  }

  get customFieldId(): string {
    return this._data.custom_field_id;
  }

  set customFieldId(customFieldId: string) {
    this._data.custom_field_id = customFieldId;
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }
}
