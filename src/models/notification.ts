import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import { AuthUser } from '@/models'
import store from '../store'

type DataMessagePayload = {
  [key: string]: string
}

export interface SMSData extends DataMessagePayload {
  conversationId: string
  messageId: string
  contactId: string
  userId: string
  body: string
  fromNumber: string
  fromName: string
}

export interface FacebookMessenger extends DataMessagePayload {
  conversationId: string
  messageId: string
  contactId: string
  body: string
  fromName: string
}

export interface ReviewData extends DataMessagePayload {
  source: string
  reviewId: string
  reviewerName: string
  starRating: string
  body: string
}

export interface CallData extends DataMessagePayload {
  fromNumber: string
  toNumber: string
  contactId: string
  newCustomer: string
}

export interface AppointmentData extends DataMessagePayload {
  name: string
  appointmentId: string
  contactId: string
}

export enum NotificationType {
  NOTIFICATION_TYPE_SMS = 'sms',
  NOTIFICATION_TYPE_FACEBOOK_MESSENGER = 'fb_messenger',
  NOTIFICATION_TYPE_REVIEW = 'review',
  NOTIFICATION_TYPE_CALL = 'call',
  NOTIFICATION_TYPE_REVIEW_REPLY = 'reviewReply',
  NOTIFICATION_TYPE_APPOINTMENT = 'appointment',
  NOTIFICATION_TYPE_EMAIL = 'email',
  NOTIFICATION_TYPE_SILENT = 'silent',
  NOTIFICATION_CUSTOM = 'custom',
  NOTIFICATION_TYPE_SNAPSHOT = 'snapshot',
  NOTIFICATION_TYPE_VOICEMAIL = 'voicemail',
  NOTIFICATION_TYPE_GMB = 'gmb',
  NOTIFICATION_TYPE_INSTAGRAM = 'instagram'
}

export default class Notification {
  public static collectionRef() {
    return firebase.firestore().collection('notifications')
  }

  public static markRead(notificationId: string) {
    return this.collectionRef()
      .doc(notificationId)
      .set({ read: true }, { merge: true })
  }

  public static add(
    locationId: string,
    userId: string,
    type: NotificationType,
    data: SMSData | FacebookMessenger | ReviewData | CallData
  ): Promise<Notification> {
    const notification = new Notification()
    notification.userId = userId
    notification.locationId = locationId
    notification.type = type
    notification.notificationData = data
    return notification.save()
  }

  public static async getByUserId(limit = 25, startAt?: firebase.firestore.DocumentSnapshot): Promise<firebase.firestore.Query> {
    const auth: AuthUser = await store.dispatch('auth/get')
    let query = Notification.collectionRef()
      .where('user_id', '==', auth.userId)
      .orderBy('date_added', 'desc')
      .limit(limit)
    if (startAt) query = query.startAt(startAt)
    return query
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = Notification.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.read = false
      this.dateAdded = moment().utc()
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return this._data
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get userId(): string {
    return this._data.user_id
  }

  set userId(userId: string) {
    this._data.user_id = userId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  get notificationData(): { [key: string]: any } {
    return this._data.notification_data
  }

  set notificationData(notificationData: { [key: string]: any }) {
    this._data.notification_data = notificationData
  }

  get read(): boolean {
    return this._data.read
  }

  set read(read: boolean) {
    this._data.read = read
  }

  public save(): Promise<Notification> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc()
      _self._ref
        .set(_self._data, { merge: true })
        .then(_ => {
          resolve(_self)
        })
        .catch(err => {
          reject(err)
        })
    })
  }
}
