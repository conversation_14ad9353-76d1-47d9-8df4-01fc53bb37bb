import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

interface RatingsBreakDown {
    '1'?: number;
    '2'?: number;
    '3'?: number;
    '4'?: number;
    '5'?: number;
}

export default class ReviewAggregate {
    public static collectionRef() {
        return firebase.firestore().collection('review_aggregate');
    }

    public static getByLocationYearMonthRealtime(locationId: string, month: moment.Moment) {
        return ReviewAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('month', '==', month.toDate());
    }

    public static getByLocationYearMonthsRealtime(
        locationId: string,
        monthStart: moment.Moment,
        monthEnd: moment.Moment
    ) {
        return ReviewAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('month', '>=', monthStart.toDate())
            .where('month', '<=', monthEnd.toDate());
    }

    public static getByLocationWeekRealtime(locationId: string, week: moment.Moment) {
        return ReviewAggregate.collectionRef()
            .where('location_id', '==', locationId)
            .where('week', '==', week.toDate());
    }

    public static getByLocationYearMonth(locationId: string, month: moment.Moment): Promise<ReviewAggregate> {
        return new Promise((resolve, reject) => {
            ReviewAggregate.getByLocationYearMonthRealtime(locationId, month)
                .get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve();
                    if (snapshot.size > 1) reject();
                    resolve(new ReviewAggregate(snapshot.docs[0]));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static getByLocationWeek(locationId: string, week: moment.Moment): Promise<ReviewAggregate> {
        return new Promise((resolve, reject) => {
            ReviewAggregate.getByLocationWeekRealtime(locationId, week)
                .get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve();
                    if (snapshot.size > 1) reject();
                    resolve(new ReviewAggregate(snapshot.docs[0]));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    private _id: string;
    private _data: { [field: string]: any };
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._data = snapshot.data() || {};
            this._ref = snapshot.ref;
        } else {
            this._ref = ReviewAggregate.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.dateAdded = moment().utc();
        }
    }

    public save(): Promise<ReviewAggregate> {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self._data, { merge: true }).then((_) => {
                resolve(_self);
            });
        });
    }

    get id(): string {
        return this._id;
    }

    get data(): { [field: string]: any } {
        return this._data;
    }

    get ref() {
        return this._ref;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data['date_added']);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data['date_added'] = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data['date_updated']);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data['date_updated'] = dateUpdated.toDate();
    }

    get locationId(): string {
        return this._data['location_id'];
    }

    set locationId(locationId: string) {
        this._data['location_id'] = locationId;
    }

    get week(): moment.Moment {
        return moment(this._data['week']);
    }

    set week(week: moment.Moment) {
        this._data['week'] = week.toDate();
    }

    get month(): moment.Moment {
        return this._data['month'];
    }

    set month(month: moment.Moment) {
        this._data['month'] = month.toDate();
    }

    get totalReviews(): number {
        return this._data['total_reviews'];
    }

    set totalReviews(totalReviews: number) {
        this._data['total_reviews'] = totalReviews;
    }

    get positiveReviews(): number {
        return this._data['positive_reviews'];
    }

    set positiveReviews(positiveReviews: number) {
        this._data['positive_reviews'] = positiveReviews;
    }

    get negativeReviews(): number {
        return this._data['negative_reviews'];
    }

    set negativeReviews(negativeReviews: number) {
        this._data['negative_reviews'] = negativeReviews;
    }

    get totalStars(): number {
        return this._data['total_stars'];
    }

    set totalStars(totalStars: number) {
        this._data['total_stars'] = totalStars;
    }

    get sources(): number[] {
        return this._data['sources'];
    }

    set sources(sources: number[]) {
        this._data['sources'] = sources;
    }

    get ratingsBreakDown(): RatingsBreakDown {
        return this._data['ratings_break_down'];
    }

    set ratingsBreakDown(ratingsBreakDown: RatingsBreakDown) {
        this._data['ratings_break_down'] = ratingsBreakDown;
    }
}
