import firebase from 'firebase/app'
import moment from 'moment-timezone'

export default class MediaFiles {
  public static collectionRef() {
    return firebase.firestore().collection('media_files')
  }

  public static getByLocationIdRealtime(
    locationId: string
  ): firebase.firestore.Query {
    return MediaFiles.collectionRef()
      .where('deleted', '==', 'false')
      .where('location_id', '==', locationId)
  }

  public static async getAllByLocation(
    locationId: string
  ): Promise<MediaFiles[]> {
    return new Promise<MediaFiles[]>(async (resolve, reject) => {
      const snapshot = await MediaFiles.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new MediaFiles(d)))
    })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
    } else {
      this._ref = MediaFiles.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
    }
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  get readCount(): number {
    return this._data.read_count
  }

  set readCount(readCount: number) {
    this._data.read_count = readCount
  }

  public async save(): Promise<MediaFiles> {
    this.dateUpdated = moment()
    await this._ref.set(this.data)
    return this
  }
  public static async add(param: {
    locationId: string
    name: string
    type: string
    url: string
    readCount: number
  }) {
    const mediaFiles = new MediaFiles()
    mediaFiles.locationId = param.locationId
    mediaFiles.name = param.name
    mediaFiles.type = param.type
    mediaFiles.readCount = param.readCount
    mediaFiles.url = param.url
    await mediaFiles.save()
  }
}
