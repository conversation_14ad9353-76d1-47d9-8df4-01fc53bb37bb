import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { MessageTemplate } from './campaign'
import { Contact, User } from '@/models'
import handlebars from 'handlebars'

export default class Template {
  public static TYPE_SMS = 'sms'
  public static TYPE_EMAIL = 'email'

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('templates')
  }

  public static getById(templateId: string): Promise<Template> {
    return new Promise((resolve, reject) => {
      Template.collectionRef()
        .doc(templateId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          resolve(new Template(snapshot))
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    })
  }

  public static getByLocation(locationId: string): Promise<Template[]> {
    return new Promise((resolve, reject) => {
      Template.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .orderBy('date_added', 'desc')
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new Template(d)))
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    })
  }

  public static fetchByLocationId(locationId: string) {
    return Template.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('name')
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Template | undefined> {
    const snapshot = await Template.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Template(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(params?: firebase.firestore.DocumentSnapshot) {
    if (params) {
      this._id = params.id
      this._ref = params.ref
      this._data = params.data() || {}
    } else {
      this._ref = Template.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment().utc()
    }
  }

  get id(): string | undefined {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get type(): string {
    if (this._data['type'] == undefined) return Template.TYPE_SMS
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }

  get template(): MessageTemplate {
    return this._data.template
  }

  set template(template: MessageTemplate) {
    this._data.template = template
  }

  get urlAttachments(): string[] {
    return this._data.urlAttachments
  }

  set urlAttachments(urlAttachments: string[]) {
    this._data.urlAttachments = urlAttachments
  }

  public async save() {
    this.dateUpdated = moment()
    await this._ref.set(this.data)
  }

  public async delete() {
    this.deleted = true
    await this.save()
  }

  static format(body, extras: { contact: Contact; user?: User }) {
    let templateParams = Template.getTemplateParams(extras.contact, extras.user)
    body = handlebars.compile(body)(templateParams)
    return body
  }

  static getTemplateParams(
    contact: Contact,
    user?: User
  ): { [key: string]: any } {
    const templateParams: { [key: string]: any } = {
      user: {},
      contact: {}
    }
    templateParams.contact = {
      first_name: contact.firstName || '',
      last_name: contact.lastName || '',
      name: contact.name || '',
      phone: contact.phone || '',
      email: contact.email || '',
      city: contact.city || ''
    }
    if (user) {
      templateParams.user = {
        first_name: user.firstName || '',
        last_name: user.lastName || '',
        name: user.fullName || '',
        phone: user.phone || '',
        email: user.email || ''
      }
    }
    return templateParams
  }
}
