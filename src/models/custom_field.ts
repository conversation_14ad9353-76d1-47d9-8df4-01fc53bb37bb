import firebase from 'firebase/app'
import moment from 'moment-timezone'
import lodash from 'lodash'

export interface InstanceSpecs {
  skipRef?: boolean;
}

export interface Stage {
  id: string
  name: string
  win_probability?: number
  position: number
}

/**
 * Available Field Types
 */
export enum FieldType {
  TEXT = 'Text',
  LARGE_TEXT = 'Large Text',
  NUMERICAL = 'numerical',
  PHONE = 'Phone',
  MONETORY = 'Monetary',
  CHECKBOX = 'Checkbox',
  SINGLE_OPTIONS = 'Single Options',
  MULTIPLE_OPTIONS = 'Multiple Options',
  RADIO = 'Radio',
  DATE = 'Date',
  TEXTBOX_LIST = 'Textbox List',
  FILE_UPLOAD = "File Upload",
  SIGNATURE = "signature"

  /*URL = 'URL',
  FLOAT = 'float',
  PERCENTAGE = 'percentage',
  TIME = 'Time',
  TIME_RANGE = 'Time Range',
  DATE_RANGE = 'Date Range'*/
}

/**
 * Available On
 */

export enum AvailableOn {
  LEAD = 'lead',
  PERSON = 'person',
  OPPORTUNITY = 'opportunity',
  COMPANY = 'company',
  PROJECT = 'project',
  TASK = 'task'
}

/**
 * Options for field type supporting multiple options
 */
export interface PickListOption {
  id?: string
  option: string
  deleted?: boolean
}

/**
 * Custom Field Interface
 */
export interface ICustomField {
  field_name: string
  placeholder: string
  data_type: FieldType
  field_key: string
  location_id: string
  deleted?: boolean
  required?: boolean
  picklist_options: string[]
  picklist_options_image: string[]
  model: FieldModel
  position?: number
  description: string
  multiple_files_allowed?: boolean,
  multi_file_limit: number,
  multi_file_custom_value_type: string
}

export type FieldModel = 'contact' | 'opportunity'

export default class CustomField {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('custom_fields')
  }

  public async save() {
    if (!this._ref) {
      throw new Error('This custom field object is not meant to be modified')
    }
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }

  public static getById(id): Promise<CustomField> {
    return new Promise((resolve, reject) => {
      CustomField.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new CustomField(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<CustomField | undefined> {
    const snapshot = await CustomField.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new CustomField(snapshot.docs[0])
  }

  public static getByLocationId(locationId: string): firebase.firestore.Query {
    return CustomField.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('position', 'asc')
  }

  public static getByLocationIdAndModel(
    locationId: string,
    model: FieldModel
  ): firebase.firestore.Query {
    return CustomField.collectionRef()
      .where('location_id', '==', locationId)
      .where('model', '==', model)
      .where('deleted', '==', false)
      .orderBy('position', 'asc')
  }

  public static async getByLocationIdAndType(
    locationId: string,
    model: FieldModel
  ): Promise<CustomField[]> {
    return new Promise<CustomField[]>(async (resolve, reject) => {
      const snapshot = await CustomField.collectionRef()
        .where('location_id', '==', locationId)
        .where('model', '==', model)
        .where('deleted', '==', false)
        .orderBy('position', 'asc')
        .get()
      resolve(snapshot.docs.map(d => new CustomField(d)))
    })
  }

  public static async getByLocationIdAndDataType(
    locationId: string,
    dataType: string
  ): Promise<CustomField[]> {
    return new Promise<CustomField[]>(async (resolve, reject) => {
      const snapshot = await CustomField.collectionRef()
        .where('location_id', '==', locationId)
        .where('data_type', '==', dataType)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map(d => new CustomField(d)))
    })
  }

  public static async getByFieldKey(
    locationId: string,
    fieldKey: string
  ): Promise<CustomField[]> {
    const snapshot = await CustomField.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .where('field_key', '==', fieldKey)
      .get()
    return snapshot.docs.map(d => new CustomField(d))
  }

  protected _id: string
  protected _data: firebase.firestore.DocumentData
  protected _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot, specs?: InstanceSpecs) {

    const skipRefInit = specs && specs.skipRef === true;

    if (snapshot instanceof firebase.firestore.DocumentSnapshot) {
      this._id = snapshot.id
      this._ref = skipRefInit ? null : snapshot.ref
      this._data = Object.assign({}, snapshot.data());
    } else {
      this._ref = skipRefInit ? null : CustomField.collectionRef().doc()
      this._id = this._ref ? this._ref.id : null;
      this._data = {}
      this.dateAdded = moment()
      this.deleted = false
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.field_name
  }

  set name(name: string) {
    this._data.field_name = name
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get placeholder(): string {
    return this._data.placeholder
  }

  set placeholder(placeholder: string) {
    this._data.placeholder = placeholder
  }

  get dataType(): FieldType {
    // "Checkbox", "Currency", “Date", "Dropdown", "Float", "MultiSelect", "Percentage", “String", "Text", "URL"
    return this._data.data_type
  }

  set dataType(fieldType: FieldType) {
    this._data.data_type = fieldType
  }

  get fieldKey(): string {
    return this._data.field_key
  }

  set fieldKey(fieldKey: string) {
    this._data.field_key = fieldKey
  }

  get picklistOptions(): string[] {
    return this._data.picklist_options
  }

  set picklistOptions(options: string[]) {
    this._data.picklist_options = options
  }

  get availableOn(): string[] {
    //“lead”, “person”, “opportunity”, “company”, "project", "task"
    return this._data.available_on
  }

  set availableOn(availableOn: string[]) {
    this._data.available_on = availableOn
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get model(): FieldModel {
    return this._data.model
  }

  set model(model: FieldModel) {
    this._data.model = model
  }

  get position(): number {
    return this._data.position
  }

  set position(position: number) {
    this._data.position = position
  }

  set allowCustomOption(allow: boolean) {
    this._data.allow_custom_option = allow
  }

  get allowCustomOption(): boolean {
    return this._data.allow_custom_option
  }

  get picklistOptionsImage(): string[] {
    return this._data.picklist_options_image
  }

  set picklistOptionsImage(options: string[]) {
    this._data.picklist_options_image = options
  }
  get description(): string[] {
    return this._data.description
  }

  set description(options: string[]) {
    this._data.description = options
  }

  get multiple_files_allowed(): boolean {
    return this._data.multiple_files_allowed
  }

  set multiple_files_allowed(selection: boolean) {
    this._data.multiple_files_allowed = selection
  }

  get multi_file_limit(): number {
    return this._data.multi_file_limit
  }

  set multi_file_limit(limit: number) {
    this._data.multi_file_limit = limit
  }

  get multi_file_custom_value_type(): string {
    return this._data.multi_file_custom_value_type
  }

  set multi_file_custom_value_type(type: string) {
    this._data.multi_file_custom_value_type = type
  }

  /*
    To-Do Add:

    "custom_fields": [
    {
      "custom_field_definition_id": 100764,
      "value": null
    },
    {
      "custom_field_definition_id": 103481,
      "value": null
    }
  ],
  to objects that need custom fields

    */
}

export class ViewOnlyCustomField extends CustomField {

  private constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    super(snapshot, { skipRef: true }) // create empty custom field
    this._ref = null; // very important dont forget this.
    // ref property should not be intialized otherwise it will break the Vuex
  }

  public static getInstance(snapshot?: firebase.firestore.DocumentSnapshot) {
    return new ViewOnlyCustomField(snapshot);
  }

  public async save() {
    throw new Error('Cannot save on view only custom field');
  }


}
