import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

export default class ScanReport {
    public static collectionRef() {
        return firebase.firestore().collection('scan_report');
    }

    static getById(id: string): Promise<ScanReport> {
        return new Promise((resolve, reject) => {
            ScanReport.collectionRef()
                .doc(id)
                .get()
                .then((snapshot) => {
                    resolve(new ScanReport(snapshot));
                })
                .catch((err) => {
                    console.error(err);
                    reject(err);
                });
        });
    }

    public static getStreamById(locationId: string): firebase.firestore.DocumentReference {
        return ScanReport.collectionRef().doc(locationId);
    }

    public static getByLocationId(locationId: string): Promise<ScanReport> {
        return new Promise((resolve, reject) => {
            ScanReport.collectionRef()
                .where('location_id', '==', locationId)
                .limit(1)
                .get()
                .then((querySnapshot) => {
                    querySnapshot.size === 1 ? resolve(new ScanReport(querySnapshot.docs[0])) : reject();
                })
                .catch(function (error) {
                    console.error('Error getting documents: ', error);
                    reject(error);
                });
        });
    }

    public static create(locationId: string) {
        const scanReport = new ScanReport();
        scanReport.locationId = locationId;
        return scanReport.save();
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
        } else {
            this._ref = ScanReport.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.deleted = false;
            this.dateAdded = moment().utc();
        }
    }

    public save() {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self._data, { merge: true }).then((_) => {
                resolve(_self);
            });
        });
    }

    get id(): string {
        return this._id;
    }

    get data(): firebase.firestore.DocumentData {
        return this._data;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = dateUpdated.toDate();
    }

    get deleted(): boolean {
        return this._data.deleted;
    }

    set deleted(deleted: boolean) {
        this._data.deleted = deleted;
    }

    get dirty(): boolean {
        return this._data.dirty;
    }

    set dirty(dirty: boolean) {
        this._data.dirty = dirty;
    }

    get name(): string {
        return this._data.name;
    }

    set name(name: string) {
        this._data.name = name;
    }

    get googlePlacesData(): any {
        return this._data.google_places_data;
    }

    set googlePlacesData(googlePlacesData: any) {
        this._data.google_places_data = googlePlacesData;
    }

    get yextData(): any {
        return this._data.yext_data;
    }

    set yextData(yextData: any) {
        this._data.yext_data = yextData;
    }

    get adwordsData(): any {
        return this._data.adwords_data;
    }

    set adwordsData(adwordsData: any) {
        this._data.adwords_data = adwordsData;
    }

    get lighthouseReportUrl(): string {
        return this._data.lighthouse_report_url;
    }

    set lighthouseReportUrl(lighthouseReportUrl: string) {
        this._data.lighthouse_report_url = lighthouseReportUrl;
    }

    get lighthouseData(): any {
        return this._data.lighthouse_data;
    }

    set lighthouseData(lighthouseData: any) {
        this._data.lighthouse_data = lighthouseData;
    }

    get mobileScreenShot(): string {
        return this._data.mobile_screenshot;
    }

    set mobileScreenShot(mobileScreenShot: string) {
        this._data.mobile_screenshot = mobileScreenShot;
    }

    get desktopScreenShot(): string {
        return this._data.desktop_screenshot;
    }

    set desktopScreenShot(desktopScreenShot: string) {
        this._data.desktop_screenshot = desktopScreenShot;
    }

    get facebookReviewCount(): number {
        return this._data.facebook_review_count;
    }

    set facebookReviewCount(facebookReviewCount: number) {
        this._data.facebook_review_count = facebookReviewCount;
    }

    get facebookStarRating(): number {
        return this._data.facebook_star_rating;
    }

    set facebookStarRating(facebookStarRating: number) {
        this._data.facebook_star_rating = facebookStarRating;
    }

    get facebookData(): any {
        return this._data.facebook_data;
    }

    set facebookData(facebookData: any) {
        this._data.facebook_data = facebookData;
    }

    get twitterData(): any {
        return this._data.twitter_data;
    }

    set twitterData(twitterData: any) {
        this._data.twitter_data = twitterData;
    }
}
