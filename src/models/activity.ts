import firebase from 'firebase/app';
import moment from 'moment-timezone';

type Product = 'listing' | 'review' | 'conversation';

export enum ActivityType {
  NOTE = 'note',
  TASK = 'task',
  PRODUCT = 'product'
}

export interface NoteData {
  body: string;
}

export interface TaskData {
  body: string;
}

export interface ProductData {
  type: Product;
}

export enum ActionType {
  ADDED = 'added',
  UPDATED = 'updated',
  DELETED = 'deleted',
}

export default class Activity {
  public static collectionRef() {
    return firebase.firestore().collection('activities');
  }

  public static getAllActivitiesByAccountId(accountId: string): firebase.firestore.Query {
    return Activity.collectionRef()
      .where('account_id', '==', accountId)
      .orderBy('date_added', 'desc');
  }

  public static getAllActivitiesForContact(contactId: string, locationId: string): firebase.firestore.Query {
    return Activity.collectionRef()
      .where('contact_id', '==', contactId)
      .where('location_id', '==', locationId)
      .orderBy('date_added', 'desc');
  }

  public static getById(id: string): Promise<Activity> {
    return new Promise((resolve, reject) => {
      Activity.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Activity(snapshot));
          resolve();
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  public static async product(companyId: string, locationId: string, userId: string, product: Product, actionType: ActionType) {
    const activity = new Activity();
    activity.companyId = companyId;
    activity.locationId = locationId;
    activity.userId = userId;
    activity.type = ActivityType.PRODUCT;
    activity.actionType = actionType;
    activity.activityData = {
      type: product,
    };
    activity.dateUpdated = moment().utc();
    await activity.save();
  }

  private _data: firebase.firestore.DocumentData;
  private _id: string;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = Activity.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.dateAdded = moment().utc();
    }
  }

  get id(): string {
    return this._id;
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toDate());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromDate(dateAdded.toDate());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toDate());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromDate(dateUpdated.toDate());
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get accountId(): string {
    return this._data.account_id;
  }

  set accountId(accountId: string) {
    this._data.account_id = accountId;
  }

  get userId(): string {
    return this._data.user_id;
  }

  set userId(userId: string) {
    this._data.user_id = userId;
  }

  get contactId(): string {
    return this._data.contact_id;
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId;
  }

  get actionType(): ActionType {
    return this._data.action_type;
  }

  set actionType(actionType: ActionType) {
    this._data.action_type = actionType;
  }

  get type(): ActivityType {
    return this._data.type;
  }

  set type(type: ActivityType) {
    this._data.type = type;
  }

  get activityData(): { [key: string]: any } {
    return this._data.activity_data;
  }

  set activityData(activityData: { [key: string]: any }) {
    this._data.activity_data = activityData;
  }

  get sessionData(): { [key: string]: any } {
    return this._data.session_data;
  }

  set sessionData(sessionData: { [key: string]: any }) {
    this._data.session_data = sessionData;
  }

  public save(): Promise<Activity> {
    const _self = this;
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc();
      _self._ref.set(this._data).then(() => {
        resolve(_self);
      });
    });
  }
}
