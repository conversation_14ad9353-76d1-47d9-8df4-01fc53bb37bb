import Item from './item';

export default class LineItem {
  private _data: { [key: string]: any };

  constructor(param?: { [key: string]: any } | Item) {
    if (!param) {
      this._data = {};
    } else if (param instanceof Item) {
      this._data = {};
      this.name = param.name;
      this.description = param.description;
      this.itemId = param.id || '';
      this.price = param.price;
      this.cost = param.cost;
      this.taxable = param.taxable;
      this.type = param.type;
      this.calculateAmount();
    } else {
      this._data = param;
    }
  }

  get data(): object {
    return this._data;
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get description(): string {
    return this._data.description;
  }

  set description(description: string) {
    this._data.description = description;
  }

  get amount(): string {
    return this._data.amount || '0';
  }

  set amount(amount: string) {
    this._data.amount = amount;
  }

  get itemId(): string {
    return this._data.item_id;
  }

  set itemId(itemId: string) {
    this._data.item_id = itemId;
  }

  get quantity(): string {
    return this._data.quantity || '0';
  }

  set quantity(quantity: string) {
    this._data.quantity = quantity;
    this.calculateAmount();
  }

  get price(): string {
    return this._data.price || '0';
  }

  set price(price: string) {
    this._data.price = price;
  }

  get cost(): string {
    return this._data.cost || '0';
  }

  set cost(cost: string) {
    this._data.cost = cost;
  }

  get type(): string {
    return this._data.type;
  }

  set type(type: string) {
    this._data.type = type;
  }

  get taxable(): boolean {
    return this._data.taxable;
  }

  set taxable(taxable: boolean) {
    this._data.taxable = taxable;
  }

  public calculateAmount() {
    return (Number(this.quantity) * Number(this.price)).toFixed(2);
  }

  public clone(): LineItem {
    return new LineItem(this._data);
  }
}
