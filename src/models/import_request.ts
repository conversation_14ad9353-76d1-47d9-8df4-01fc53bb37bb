import firebase from 'firebase/app';
import moment from 'moment-timezone';
import Contact from './contact';

export enum ImportStatus {
    PENDING = 'pending',
    SUCCESS = 'success',
    UPLOADING = 'uploading',
    REVERT = 'revert',
}

export enum ImportType {
    CSV = 'csv',
    BULK_IMPORT = 'bulk_import',
    BULK_UPDATE = 'bulk_update',
    BULK_DELETE = 'bulk_delete',
    BULK_VALIDATION = 'bulk_validation'
}

export default class ImportRequest {
    public static collectionRef(): firebase.firestore.CollectionReference {
        return firebase.firestore().collection('import_requests');
    }

    public save() {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self.data).then((_) => {
                resolve(_self);
            });
        });
    }

    public static getById(id): Promise<ImportRequest> {
        return new Promise((resolve, reject) => {
            ImportRequest.collectionRef()
                .doc(id)
                .get()
                .then((snapshot) => {
                    resolve(new ImportRequest(snapshot));
                })
                .catch((err) => {
                    console.error(err);
                    reject(err);
                });
        });
    }

    public static getByIdRealTime(id): firebase.firestore.DocumentReference {
        return ImportRequest.collectionRef().doc(id)
    }

    public static fetchAll(locationId: string): firebase.firestore.Query {
        return ImportRequest.collectionRef()
            .where('location_id', '==', locationId)
            .orderBy('date_added', 'desc');
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data();
        } else {
            this._ref = ImportRequest.collectionRef().doc();
            this._id = this._ref.id;
            this._data = {};
            this.dateAdded = moment();
            this.status = ImportStatus.PENDING;
            this.skipped = 0;
            this.failed = 0;
            this.created = 0;
            this.deleted = 0;
        }
    }

    get id(): string {
        return this._id;
    }

    get data(): firebase.firestore.DocumentData {
        return this._data;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get userId(): string {
        return this._data.user_id;
    }

    set userId(userId: string) {
        this._data.user_id = userId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added.toMillis());
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated.toMillis());
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
    }

    get created() {
        return this._data.created;
    }

    set created(created) {
        this._data.created = created;
    }

    get skipped(): number {
        return this._data.skipped;
    }

    set skipped(skipped: number) {
        this._data.skipped = skipped;
    }

    get failed(): number {
        return this._data.failed;
    }

    set failed(failed: number) {
        this._data.failed = failed;
    }

    get updated(): number {
        return this._data.updated;
    }

    set updated(updated: number) {
        this._data.updated = updated;
    }

    get deleted(): number {
        return this._data.deleted;
    }

    set deleted(deleted: number) {
        this._data.deleted = deleted;
    }

    get status(): string {
        return this._data.status;
    }

    set status(status: string) {
        this._data.status = status;
    }

    get error(): string {
        return this._data.error;
    }

    set error(error: string) {
        this._data.error = error;
    }

    get type(): string {
        return this._data.type;
    }

    set type(type: string) {
        this._data.type = type;
    }

    get filePath(): string {
        return this._data.file_path;
    }

    set filePath(filePath: string) {
        this._data.file_path = filePath;
    }

    get importRequestId(): string {
        return this._data.import_request_id;
    }

    set importRequestId(id: string) {
        this._data.import_request_id = id;
    }

    public async undoImport(locationId: string) {
        const limit = 100;
        let snapshot: firebase.firestore.QuerySnapshot;
        do {
            snapshot = await Contact.collectionRef()
                .where('import_id', '==', this.id)
                .where('location_id', '==', locationId)
                .limit(limit)
                .get();
            if (snapshot.size > 0) {
                await Promise.all(snapshot.docs.map(async (doc) => await doc.ref.delete()));
            }
        } while (snapshot.size === limit);
        this.status = ImportStatus.REVERT;
        await this.save();
    }
}
