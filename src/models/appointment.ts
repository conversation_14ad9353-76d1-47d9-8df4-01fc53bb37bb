import firebase from 'firebase/app';
import moment from 'moment-timezone';

export default class Appointment {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('appointments');
  }

  public static async getById(id: string) {
    return new Promise((resolve, reject) => {
      const unsubscribe = Appointment.collectionRef()
        .doc(id)
        .onSnapshot((snapshot: firebase.firestore.DocumentSnapshot) => {
          unsubscribe();
          resolve(new Appointment(snapshot));
        });
    });
    // const snapshot = await Appointment.collectionRef().doc(id).get();
    // return new Appointment(snapshot);
  }

  public static getStreamById(id: string): firebase.firestore.DocumentReference {
    return Appointment.collectionRef().doc(id);
  }

  public static getAllByDay(userId: string, day: moment.Moment): firebase.firestore.Query {
    const endDay = day.add(1, 'day');
    return Appointment.collectionRef()
      .where('deleted', '==', false)
      .where('attendees.' + userId, '>=', day.toDate())
      .where('attendees.' + userId, '<=', endDay.toDate())
      .orderBy('attendees.' + userId);
  }

  public static getAllWithUserId(userId: string, day: moment.Moment): firebase.firestore.Query {
    return Appointment.collectionRef()
      .where('deleted', '==', false)
      .where('attendees.' + userId, '>=', day.toDate())
      .orderBy('attendees.' + userId);
  }

  public static getAllWithCompanyId(companyId: string, day: moment.Moment): firebase.firestore.Query {
    return Appointment.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', companyId)
      .where('start_date', '>=', day.toDate())
      .orderBy('start_date');
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data() || {};
    } else {
      this._data = {};
      this._data.attendees = {};
      this.deleted = false;
      this.dateAdded = moment();
    }
  }

  get id(): string | undefined {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added);
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate();
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated);
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate();
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get dirty(): boolean {
    return this._data.dirty;
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty;
  }

  get startDate(): moment.Moment {
    return moment(this._data.start_date);
  }

  set startDate(startDate: moment.Moment) {
    this._data.start_date = startDate.toDate();
  }

  get endDate(): moment.Moment {
    return moment(this._data.end_date);
  }

  set endDate(endDate: moment.Moment) {
    this._data.end_date = endDate.toDate();
  }

  get estimateId(): string {
    return this._data.estimate_id;
  }

  set estimateId(estimateId: string) {
    this._data.estimate_id = estimateId;
  }

  get jobId(): string {
    return this._data.job_id;
  }

  set jobId(jobId: string) {
    this._data.job_id = jobId;
  }

  get arrivalWindow(): number {
    return this._data.arrival_window;
  }

  set arrivalWindow(arrivalWindow: number) {
    this._data.arrival_window = arrivalWindow;
  }

  get attendees(): string[] {
    return lodash.keys(this._data.attendees);
  }

  public addAttendee(userId: string) {
    this._data.attendees[userId] = this.startDate;
  }

  public removeAttendee(userId: string) {
    delete this.data.attendees[userId];
  }

  public save() {
    this.dateUpdated = moment();
    this.dirty = true;
    if (this._ref) {
      this._ref.update(this._data);
    } else {
      this._ref = Appointment.collectionRef().doc();
      this._id = this._ref.id;
      this._ref.set(this._data);
    }
  }

  public delete() {
    return Appointment.collectionRef()
      .doc(this.id)
      .update({
        deleted: true,
      });
  }
}
