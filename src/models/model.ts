import firebase from 'firebase/app';
import lodash from 'lodash';
import moment from 'moment-timezone';
export const ModelType = {
  firestore: 'firestore',
  es: 'es',
  raw: 'raw'
};

export default abstract class Model {
  protected _data: { [key: string]: any }
  protected _id: string
  protected _ref: firebase.firestore.DocumentReference
  protected _snapshot: firebase.firestore.DocumentSnapshot
  protected _oldData: { [key: string]: any }
  protected newRecord: boolean
  protected modelType: ModelType
  protected keepESFields: boolean

  static collectionRef() {
    throw "Define collection ref";
  }

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
      this._snapshot = snapshot
      this.modelType = ModelType.firestore
      this._oldData = lodash.cloneDeep(this._data);
    } else if (snapshot) {
      this._id = snapshot.id
      this._data = Object.assign({}, snapshot)
      this._oldData = lodash.cloneDeep(this._data);
      this._ref = this.constructor.collectionRef().doc(snapshot.id)
      if (typeof snapshot.date_added === 'number' || typeof snapshot.date_updated === 'number') { // It is ES Result
        if (snapshot.date_added) {
          this.dateAdded = moment(snapshot.date_added)
        }
        if (snapshot.date_updated) {
          this.dateUpdated = moment(snapshot.date_updated)
        }
      } else if (snapshot.date_added && snapshot.date_added instanceof firebase.firestore.Timestamp === false) {
        snapshot.date_added = new firebase.firestore.Timestamp(snapshot.date_added.seconds, snapshot.date_added.nanoseconds)
        if (snapshot.date_updated.seconds) {
          snapshot.date_updated = new firebase.firestore.Timestamp(snapshot.date_updated.seconds, snapshot.date_updated.nanoseconds)
        }
      }
      if (snapshot.keepESFields) {
        this.keepESFields = true
        delete this._data.keepESFields
        delete this._oldData.keepESFields
      }
    } else {
      this._ref = this.constructor.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this._oldData = {}
      this.dateAdded = moment()
      this.dateUpdated = moment()
      this.newRecord = true
    }
  }

  get id(): string {
    return this._id
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get ref() {
    return this._ref
  }

  public getSnapshot() {
    return this._snapshot
  }

  get dateAdded(): moment.Moment {
    return this._data['date_added']
      ? typeof this._data['date_added'] === 'number'
        ? moment(this._data['date_added'])
        : moment(this._data['date_added'].toMillis())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data['date_added'] = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data['date_updated']
      ? typeof this._data['date_updated'] === 'number'
        ? moment(this._data['date_updated'])
        : moment(this._data['date_updated'].toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data['date_updated'] = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  public async save(): Promise<T> {
    if (this.keepESFields) {
      console.error('please remove ES fields', this.id)
    }

    if (this.newRecord) {
      await this._ref.set(this.data)
      this.newRecord = false
      this._oldData = lodash.cloneDeep(this.data)
    } else {
      const data = lodash.pickBy(this.data, (value, key) => {
        return lodash.isEqual(value, this._oldData[key]) === false;
      })

      if (data['skip_trigger']) data['skip_trigger'] = this.data.skip_trigger      

      if (Object.keys(data).length > 0) {
        const dateUpdated = firebase.firestore.FieldValue.serverTimestamp()
        // this._data.date_updated = dateUpdated
        data.date_updated = dateUpdated
      }

      try {
        // console.log('model changes', data, this._data, this._oldData)
        if (data) {
          await this._ref.update(data)

          this.newRecord = false
          this._oldData = lodash.cloneDeep(this.data)

        }
      } catch (err) {
        console.log('Error in saving diff > model save')
        console.log(err)
        throw err
      }
    }
  }

  public async delete() {
    return this._ref
      .update({
        deleted: true,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
  }

  public getOldValue(key: String) {
    return this._oldData[key]
  }
}
