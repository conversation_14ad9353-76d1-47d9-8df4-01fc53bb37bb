import firebase from 'firebase/app';
import moment from 'moment-timezone';

export default class SnapshotTemplate {
  public static collectionRef() {
    return firebase.firestore().collection('snapshot_template');
  }

  public static async getById(id: string): Promise<SnapshotTemplate | undefined> {
    const snapshot = await SnapshotTemplate.collectionRef()
      .doc(id)
      .get();
    if (!snapshot.exists || snapshot.data().deleted) return undefined;
    return new SnapshotTemplate(snapshot);
  }

  public static async getTemplates(): Promise<SnapshotTemplate[]> {
    const snapshot = await SnapshotTemplate.collectionRef().where('deleted', '==', false).get();
    return snapshot.docs.map((d) => new SnapshotTemplate(d));
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = SnapshotTemplate.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment();
      this.dateUpdated = moment();
    }
  }

  get id(): string {
    return this._id;
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    );
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    );
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get category(): string {
    return this._data.category;
  }

  set category(category: string) {
    this._data.category = category;
  }

  get imageUrl(): string {
    return this._data.image_url;
  }

  set imageUrl(imageUrl: string) {
    this._data.image_url = imageUrl;
  }

  get videoUrl(): string {
    return this._data.video_url;
  }

  set videoUrl(videoUrl: string) {
    this._data.video_url = videoUrl;
  }

  get fbAdImageUrl(): string {
    return this._data.fb_ad_image_url;
  }

  set fbAdImageUrl(fbAdImageUrl: string) {
    this._data.fb_ad_image_url = fbAdImageUrl;
  }

  get overview(): string {
    return this._data.overview;
  }

  set overview(overview: string) {
    this._data.overview = overview;
  }

  get checklistUrl(): string {
    return this._data.checklist_url;
  }

  set checklistUrl(checklistUrl: string) {
    this._data.checklist_url = checklistUrl;
  }

  get snapshotUrl(): string {
    return this._data.snapshot_url;
  }

  set snapshotUrl(snapshotUrl: string) {
    this._data.snapshot_url = snapshotUrl;
  }

  public async save(): Promise<SnapshotTemplate> {
    await this._ref.set(this.data);
    return this;
  }
}
