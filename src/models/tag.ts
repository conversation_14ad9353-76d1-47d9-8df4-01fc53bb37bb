import firebase from 'firebase/app';
import moment from 'moment-timezone';
import LocationsService from '../services/LocationsService'

export default class Tag {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('tags');
	}

	public async save() {
		await this._ref.set(this._data);
	}

	public static getById(id): Promise<Tag> {
		return new Promise((resolve, reject) => {
			Tag.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new Tag(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

    public static async getByLocationId(locationId: string): Promise<Tag[]> {
		return new Promise((resolve, reject) => {
            LocationsService.getTags(locationId)
            .then((response) => {
                return resolve(response.tags)
            })
            .catch((err) => {
                return resolve([]);
            })
		})
	}

	public static async getByLocationIdFirestore(locationId: string): Promise<Tag[]> {
		return new Promise((resolve, reject) => {
			Tag.collectionRef()
				.where('location_id', '==', locationId)
				.where('deleted', '==', false)
				.orderBy('name', 'asc')
				.get()
				.then(snapshot => {
					if (snapshot.empty) return resolve([]);
					return resolve(snapshot.docs.map(d => new Tag(d)));
				})
				.catch(err => {
					console.error(err);
					return resolve([]);
				});
		})
	}

	public static async getByLocationIdAndName(locationId: string, name: string): Promise<Tag | undefined> {
		const snapshot = await Tag.collectionRef()
			.where('location_id', '==', locationId)
			.where('name', '==', name)
			.where('deleted', '==', false)
			.get();
		return snapshot.docs.length > 0 ? new Tag(snapshot.docs[0]) : undefined;
	}

	public static async createTag(locationId: string, name: string) {
		const tag = new Tag();
		tag.locationId = locationId;
		tag.name = name;
		await tag.save();
		return tag;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = Tag.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
			this.deleted = false;
		}
	}

	get ref() {
		return this._ref
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get dateAdded() {
		return moment(this._data.date_added);
	}

	set dateAdded(dateAdded) {
		this._data.date_added = dateAdded.toDate();
	}

	get name(): string {
		return this._data.name;
	}

	set name(name: string) {
		this._data.name = name;
	}

	get deleted(): boolean {
		return this._data.deleted;
	}

	set deleted(deleted: boolean) {
		this._data.deleted = deleted;
	}
}
