import firebase from 'firebase/app'
import moment from 'moment-timezone'


export enum FolderType {
  TRIGGERS = 'triggers',
  CAMPAIGNS = 'campaigns',
}

type _FolderType =
  | 'triggers'
  | 'campaigns'


export interface Condition {
  id?: string
  location_id: number
  date_added: number
  deleted: string
  name: string
  type: _FolderType
}

export default class Folder {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('folders')
  }

  public async save() {
    this.dateUpdated = moment()
    await this._ref.set(this.data)
  }

  public static getById(id): Promise<Folder> {
    return new Promise((resolve, reject) => {
      Folder.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new Folder(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getActiveByLocationAndType(
    locationId: string,
    type: _FolderType
  ): Promise<Folder[]> {
    return new Promise((resolve, reject) => {
      Folder.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .where('type', '==', type)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Folder(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static getByLocationIdRealtime(
    locationId: string,
  ) {
    return Folder.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static getByLocationId(locationId: string): Promise<Folder[]> {
    return new Promise((resolve, reject) => {
      Folder.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Folder(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Folder | undefined> {
    const snapshot = await Folder.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Folder(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = Folder.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
      this.conditions = []
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get ref() {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get locationId(): string {
    return this._data.location_id
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get dateAdded(): moment.Moment {
    return this._data.date_added
      ? moment(this._data.date_added.toMillis())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data.date_updated
      ? moment(this._data.date_updated.toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get name(): String {
    return this._data.name
  }

  set name(name: String) {
    this._data.name = name
  }

  get type(): _FolderType {
    return this._data.type
  }

  set type(type: _FolderType) {
    this._data.type = type
  }

  get conditions(): Condition[] {
    return this._data.conditions
  }

  set conditions(conditions: Condition[]) {
    this._data.conditions = conditions
  }

  public async delete() {
    this.deleted = true
    await this.save()
  }
}
