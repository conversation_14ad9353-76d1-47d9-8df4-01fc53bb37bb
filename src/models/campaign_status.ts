import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

type Status = 'running' | 'finished' | 'replied';

export default class CampaignStatus {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('campaign_status');
	}

	public async save() {
		this.dateUpdated = moment();
		await this._ref.set(this._data);
	}

	public static getById(id): Promise<CampaignStatus> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new CampaignStatus(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getByContactId(contactId: string): Promise<CampaignStatus[]> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.where('contact_id', '==', contactId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignStatus(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByContactAndStatus(contactId: string, status: Status): Promise<CampaignStatus[]> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.where('contact_id', '==', contactId)
				.where('status', '==', status)
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignStatus(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static fetchByContactAndStatus(contactId: string, status: Status, locationId: string) {
		return CampaignStatus.collectionRef()
			.where('contact_id', '==', contactId)
			.where('location_id', '==', locationId)
			.where('status', '==', status);
	}

	public static fetchByContact(contactId: string, locationId: string) {
		return CampaignStatus.collectionRef()
			.where('contact_id', '==', contactId)
			.where('location_id', '==', locationId);
	}

	public static fetchByCampaignId(campaignId: string, locationId: string) {
		return CampaignStatus.collectionRef()
			.where('campaign_id', '==', campaignId)
			.where('location_id', '==', locationId)
			.orderBy('date_added', 'desc');
	}

	public static fetchByCampaignIdRealtime(campaignId: string, locationId: string) {
		return CampaignStatus.collectionRef()
			.where('campaign_id', '==', campaignId)
			.where('location_id', '==', locationId)
			.orderBy('date_updated', 'desc');
	}

	public static getByCampaignId(campaignId: string, locationId: string): Promise<CampaignStatus[]> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignStatus(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static fetchByCampaignIdAndStatus(campaignId: string, status: string, locationId: string) {
		return CampaignStatus.collectionRef()
			.where('campaign_id', '==', campaignId)
			.where('status', '==', status)
			.where('location_id', '==', locationId)
			.orderBy('date_added', 'desc');
	}

	public static getByCampaignIdAndStage(campaignId: string, stage: string, locationId: string): Promise<CampaignStatus[]> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('stage', '==', stage)
				.where('location_id', '==', locationId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					return resolve(snapshot.docs.map((d) => new CampaignStatus(d)));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static getByContactIdAndCampaignId(campaignId: string, contactId: string, locationId: string): Promise<CampaignStatus> {
		return new Promise((resolve, reject) => {
			CampaignStatus.collectionRef()
				.where('campaign_id', '==', campaignId)
				.where('location_id', '==', locationId)
				.where('contact_id', '==', contactId)
				.orderBy('date_added', 'desc')
				.get()
				.then((snapshot) => {
					if (snapshot.empty) resolve();
					return resolve(new CampaignStatus(snapshot.docs[0]));
				})
				.catch((err) => {
					console.error(err);
					reject(err.message);
				});
		});
	}

	public static async create(locationId: string, campaignId: string, campaignVersionId: string, contactId: string, extras?: { userId?: string }) {
		const status = new CampaignStatus();
		status.locationId = locationId;
		status.campaignId = campaignId;
		status.contactId = contactId;
		status.campaignVersionId = campaignVersionId;
		if (extras && extras.userId) status.userId = extras.userId;
		await status.save();
		return status;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;
	private _snapshot: firebase.firestore.DocumentSnapshot;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot | { [key: string]: any } | undefined) {
		if (snapshot instanceof firebase.firestore.DocumentSnapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
			this._snapshot = snapshot;
		} else if (snapshot) {
			this._id = snapshot.id;
			this._data = snapshot;
			this._ref = CampaignStatus.collectionRef().doc(snapshot.id);
			if (snapshot.date_added) this.dateAdded = moment(snapshot.date_added);
			if (snapshot.date_updated) this.dateUpdated = moment(snapshot.date_updated);
			if (snapshot.event_time) this.eventTime = moment(snapshot.event_time);
		} else {
			this._ref = CampaignStatus.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
			this.status = 'running';
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get ref(): firebase.firestore.DocumentReference {
		return this._ref;
	}

	get snapshot() {
		return this._snapshot;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get contactId(): string {
		return this._data.contact_id;
	}

	set contactId(contactId: string) {
		this._data.contact_id = contactId;
	}

	get campaignId(): string {
		return this._data.campaign_id;
	}

	set campaignId(campaignId: string) {
		this._data.campaign_id = campaignId;
	}

	get campaignVersionId(): string {
		return this._data.campaign_version_id;
	}

	set campaignVersionId(campaignVersionId: string) {
		this._data.campaign_version_id = campaignVersionId;
	}

	get userId(): string | undefined {
		return this._data.user_id;
	}

	set userId(userId: string) {
		this._data.user_id = userId;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get status(): Status {
		return this._data.status;
	}

	set status(status: Status) {
		this._data.status = status;
	}

	get templateId(): string {
		return this._data.template_id;
	}

	set templateId(templateId: string) {
		this._data.template_id = templateId;
	}

	get taskId(): string {
		return this._data.task_id;
	}

	set taskId(taskId: string) {
		this._data.task_id = taskId;
	}

	get form(): { [key: string]: any } {
		return this._data.form;
	}

	set form(form: { [key: string]: any }) {
		this._data.form = form;
	}

	get executionLog(): any {
		return this._data.meta && this._data.meta.execution_log;
	}

	set executionLog(executionLog: any) {
		this._data.meta.execution_log = executionLog;
	}

	get eventTime(): moment.Moment {
		return moment(this._data.event_time.toMillis());
	}

	set eventTime(eventTime: moment.Moment) {
		this._data.event_time = firebase.firestore.Timestamp.fromMillis(eventTime.valueOf());
	}

	get replied(): boolean {
		return this._data.replied;
	}

	set replied(replied: boolean) {
		this._data.replied = replied;
	}

	get internalSource(): { [key: string]: any } {
		return this._data.internal_source;
	}

	set internalSource(internalSource: { [key: string]: any }) {
		this._data.internal_source = internalSource;
	}

	public clone() {
		const campaignStatus = new CampaignStatus();
		campaignStatus._data = this._data;
		campaignStatus._id = this._id;
		campaignStatus._ref = this._ref;
		return campaignStatus;
	}
}
