import firebase from 'firebase/app';
import moment from 'moment-timezone';
import { Company, LineItem, Address, AuthUser } from '@/models';

export enum ReviewStatus {
    NEW = 'new',
    SENT = 'sent',
    DELIVERED = 'delivered',
    UNDELIVERED = 'undelivered',
    FAILED = 'failed',
    READ = 'read',
    OPENED = 'opened',
}

export enum ReviewRequestMethod {
    SMS = 'sms',
    EMAIL = 'email',
}

export default class ReviewRequest {
    public static collectionRef(): firebase.firestore.CollectionReference {
        return firebase.firestore().collection('review_requests');
    }

    public static getById(id: string) {
        return new Promise((resolve, reject) => {
            ReviewRequest.collectionRef()
                .doc(id)
                .get()
                .then((snapshot: firebase.firestore.DocumentSnapshot) => {
                    if (snapshot.exists) resolve(new ReviewRequest(snapshot));
                    resolve();
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static getStreamById(id: string): firebase.firestore.DocumentReference {
        return ReviewRequest.collectionRef().doc(id);
    }

    public static fetchAllRequests(locationId: string, limit?: number): firebase.firestore.Query {
        let query = ReviewRequest.collectionRef()
            .where('deleted', '==', false)
            .where('location_id', '==', locationId);
        if (limit) {
            query = query.limit(limit);
        }
        return query.orderBy('date_added', 'desc');
    }

    public static async fetchAllRequestsBetween(
        locationId: string,
        start: moment.Moment,
        end: moment.Moment
    ): Promise<firestore.Query> {
        return ReviewRequest.collectionRef()
            .where('deleted', '==', false)
            .where('date_added', '>=', start.toDate())
            .where('date_added', '<', end.toDate())
            .where('location_id', '==', locationId)
            .orderBy('date_added', 'desc');
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
        } else {
            this._ref = ReviewRequest.collectionRef().doc();
            this._id = this._ref.id;
            this._data = {};
            this.deleted = false;
            this.dateAdded = moment().utc();
            this.status = ReviewStatus.NEW;
            this.repeatCount = 0;
            this.repeats = [];
        }
    }

    get id(): string {
        return this._id;
    }

    get data(): { [key: string]: any } {
        return this._data;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added.toMillis());
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated.toMillis());
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
    }

    get deleted(): boolean {
        return this._data.deleted;
    }

    set deleted(deleted: boolean) {
        this._data.deleted = deleted;
    }

    get status(): ReviewStatus {
        return this._data.status;
    }

    set status(status: ReviewStatus) {
        this._data.status = status;
    }

    get contactId(): string {
        return this._data.contact_id;
    }

    set contactId(contactId: string) {
        this._data.contact_id = contactId;
    }

    get repeatCount(): number {
        return this._data.repeat_count || 0;
    }

    set repeatCount(count: number) {
        this._data.repeat_count = count;
    }

    get repeats(): [any] {
        return this._data.repeats || [];
    }

    set repeats(times:any) {
        this._data.repeats =  times;
    }

    get userId(): string {
        return this._data.user_id;
    }

    set userId(userId: string) {
        this._data.user_id = userId;
    }

    get messageId(): string {
        return this._data.message_id;
    }

    set messageId(messageId: string) {
        this._data.message_id = messageId;
    }

    get method(): ReviewRequestMethod {
        return this._data.method;
    }

    set method(method: ReviewRequestMethod) {
        this._data.method = method;
    }

    get meta(): { [key: string]: any } {
        if (!this._data.meta) this._data.meta = {};
        return this._data.meta;
    }

    get workflowId(): string {
        return this._data.workflow_id;
    }

    set workflowId(workflowId: string) {
        this._data.workflow_id = workflowId;
    }

    public save(): Promise<ReviewRequest> {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self._data, { merge: true }).then(() => {
                resolve(_self);
            });
        });
    }

    public delete() {
        return ReviewRequest.collectionRef()
            .doc(this.id)
            .update({
                deleted: true,
            });
    }
}
