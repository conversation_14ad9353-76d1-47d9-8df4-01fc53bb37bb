import firebase from 'firebase/app'
import moment from 'moment-timezone'

import { DefaultElizaFAQ } from '@/util/default_eliza_faq';
import * as lodash from 'lodash';
export default class LogicalEliza {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('logical_eliza')
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._data = snapshot.data() || {}
      this._ref = snapshot.ref
    } else {
      this._ref = LogicalEliza.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
    }
  }

  public static getById(id: string): Promise<LogicalEliza> {
    return new Promise((resolve, reject) => {
      LogicalEliza.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          resolve(new LogicalEliza(snapshot))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static fetchByLocationId(locationId: string): Promise<LogicalEliza[]> {
    return new Promise((resolve, reject) => {
      LogicalEliza.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(querySnapshot => {
          resolve(querySnapshot.docs.map(d => new LogicalEliza(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async getDefaultEliza(
    locationId: string
  ): Promise<LogicalEliza> {
    return new Promise((resolve, reject) => {
      LogicalEliza.collectionRef()
        .where('location_id', '==', locationId)
        .where('default_eliza', '==', true)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve(null)
          }
          return resolve(new LogicalEliza(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static async getByLocationId(
    locationId: string
  ): Promise<firebase.firestore.Query> {
    return LogicalEliza.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get ref() {
    return this._ref
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get elizaId(): string {
    return this._data.eliza_id
  }

  set elizaId(elizaId: string) {
    this._data.eliza_id = elizaId
  }

  get logicalName(): string {
    return this._data.logical_name
  }

  set logicalName(logicalName: string) {
    this._data.logical_name = logicalName
  }

  get faq(): any {
    return this._data.faq
  }

  set faq(faq: any) {
    this._data.faq = faq
  }

  get defaultEliza(): boolean {
    return this._data.default_eliza;
  }

  set defaultEliza(defaultEliza: boolean) {
    this._data.default_eliza = defaultEliza;
  }

  public save() {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc()
      _self._ref.set(_self.data).then(_ => {
        resolve(_self)
      })
    })
  }
}
