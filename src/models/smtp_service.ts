import firebase from 'firebase/app';
import moment from 'moment-timezone';
import * as CryptoJS from 'crypto-js'

export interface Providers {
  provider: string
  providerName: string
  email: string
  password: string
  port: number
  server: string
  default: boolean
}

export type ProviderType = 'gmail' | 'sendgrid' | 'yahoo mail' | 'outlook' | 'other' | 'mailgun'

export default class SMTPService {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('smtp_service');
  }

  public async save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getById(id): Promise<SMTPService> {
    return new Promise((resolve, reject) => {
      SMTPService.collectionRef()
        .doc(id)
        .get()
        .then((snapshot) => {
          resolve(new SMTPService(snapshot));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static async getByLocationId(locationId: string): Promise<SMTPService[]> {
    return new Promise((resolve, reject) => {
      SMTPService.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) return resolve([]);
          return resolve(snapshot.docs.map(d => new SMTPService(d)));
        })
        .catch(err => {
          console.error(err);
          return resolve([]);
        });
    })
  }

  public static async getByEmailId(email: string): Promise<SMTPService[]> {
    return new Promise((resolve, reject) => {
      SMTPService.collectionRef()
        .where('email', '==', email)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) return resolve([]);
          return resolve(snapshot.docs.map(d => new SMTPService(d)));
        })
        .catch(err => {
          console.error(err);
          return resolve([]);
        });
    })
  }

  public static fetchByLocationIdRealtime(
    locationId: string
  ): firebase.firestore.Query {
    return SMTPService.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
  }

  public static fetchByCompanyIdRealtime(
    companyId: string
  ): firebase.firestore.Query {
    return SMTPService.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', companyId)
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot | { [key: string]: any }) {
    if (snapshot instanceof firebase.firestore.DocumentSnapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._ref = SMTPService.collectionRef().doc(snapshot.id)
      this._data = snapshot || {}
    } else {
      this._ref = SMTPService.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.dateAdded = moment();
      this.enableReplyTracking = true;
      this.deleted = false;
    }
  }

  get ref() {
    return this._ref
  }

  get id(): string {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get dateAdded() {
    return moment(this._data.date_added);
  }

  set dateAdded(dateAdded) {
    this._data.date_added = dateAdded.toDate();
  }

  get dateUpdated() {
    return moment(this._data.date_added);
  }

  set dateUpdated(dateUpdated) {
    this._data.date_added = dateUpdated.toDate();
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get providerType(): ProviderType {
    return this._data.provider_type;
  }

  set providerType(providerType: ProviderType) {
    this._data.provider_type = providerType;
  }

  get providerName(): string {
    return this._data.provider_name;
  }

  set providerName(providerName: string) {
    this._data.provider_name = providerName;
  }

  get email(): string {
    return this._data.email;
  }

  set email(email: string) {
    this._data.email = email;
  }

  get username(): string {
    return this._data.username;
  }

  set username(username: string) {
    this._data.username = username;
  }

  get password(): string {
    if (this._data.password)
      return CryptoJS.AES.decrypt(this._data.password, 'apples&oranges').toString(
        CryptoJS.enc.Utf8
      )
  }

  set password(password: string) {
    this._data.password = CryptoJS.AES.encrypt(password, 'apples&oranges').toString();
  }

  get port(): number {
    return this._data.port;
  }

  set port(port: number) {
    this._data.port = port;
  }

  get server(): string {
    return this._data.server;
  }

  set server(server: string) {
    this._data.server = server;
  }

  get smtpServiceExpired(): boolean {
    return this._data.smtp_service_expired !== undefined
      ? this._data.smtp_service_expired
      : false;
  }

  set smtpServiceExpired(smtpServiceExpired: boolean) {
    this._data.smtp_service_expired = smtpServiceExpired;
  }

  get smtpServiceError(): string {
    return this._data.smtp_service_error;
  }

  set smtpServiceError(smtpServiceError: string) {
    this._data.smtp_service_error = smtpServiceError;
  }

  get enableReplyTracking(): boolean {
    return this._data.enable_reply_tracking !== undefined
      ? this._data.enable_reply_tracking
      : true;
  }

  set enableReplyTracking(enableReplyTracking: boolean) {
    this._data.enable_reply_tracking = enableReplyTracking;
  }
}
