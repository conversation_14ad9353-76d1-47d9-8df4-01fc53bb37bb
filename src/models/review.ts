import firebase from 'firebase/app';
import * as moment from 'moment-timezone';
import { Company, LineItem, Address, AuthUser } from '@/models';

export default class Review {
  public static SOURCE_GOOGLE = 247;
  public static SOURCE_FACEBOOK = 71;

  public static collectionRef() {
    return firebase.firestore().collection('reviews');
  }

  public static getById(id: string): Promise<Review> {
    return new Promise((resolve, reject) => {
      Review.collectionRef()
        .doc(id)
        .get()
        .then((snapshot) => {
          resolve(new Review(snapshot));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static getByLocationId(locationId: string): Promise<Review[]> {
    return new Promise(async (resolve, reject) => {
      Review.getByLocationIdRealtime(locationId).get().then((snapshot) => {
        resolve(snapshot.docs.map((d) => new Review(d)));
      })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static getByLocationIdRealtime(locationId: string) {
    return Review.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('date_added', 'desc');
  }

  public static getByAltId(altId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      Review.collectionRef()
        .where('alt_id', '==', altId)
        .get()
        .then((snapshot) => {
          if (snapshot.empty) {
            return reject('No matching review found for ' + altId);
          } else if (snapshot.docs.length > 1) {
            return reject('More than 1 review found for ' + altId);
          }
          return resolve(new Review(snapshot.docs[0]));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data() || {};
    } else {
      this._ref = Review.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment().utc();
    }
  }

  public save() {
    const _self = this;
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc();
      _self._ref.set(_self._data, { merge: true }).then((_) => {
        resolve(_self);
      });
    });
  }

  get id(): string {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
  }

  get deleted() {
    return this._data.deleted;
  }

  set deleted(deleted) {
    this._data.deleted = deleted;
  }

  get dirty() {
    return this._data.dirty;
  }

  set dirty(dirty) {
    this._data.dirty = dirty;
  }

  get altId() {
    return this._data.alt_id;
  }

  set altId(altId) {
    this._data.alt_id = altId;
  }

  get source(): number {
    return this._data.source;
  }

  set source(source: number) {
    this._data.source = source;
  }

  get rawData() {
    return this._data.raw_data;
  }

  set rawData(rawData) {
    this._data.raw_data = rawData;
  }

  get reviewerName() {
    return this._data.reviewer_name;
  }

  set reviewerName(reviewerName) {
    this._data.reviewer_name = reviewerName;
  }

  get reviewerId() {
    return this._data.reviewer_id;
  }

  set reviewerId(reviewerId) {
    this._data.reviewer_id = reviewerId;
  }

  get sentiment() {
    return this._data.sentiment;
  }

  set sentiment(sentiment) {
    this._data.sentiment = sentiment;
  }

  get comment() {
    return this._data.comment;
  }

  set comment(comment) {
    this._data.comment = comment;
  }

  get starRating() {
    return this._data.star_rating;
  }

  set starRating(starRating) {
    this._data.star_rating = starRating;
  }

  get replies(): Array<{}> {
    return this._data.replies;
  }

  set replies(replies: Array<{}>) {
    this._data.replies = replies;
  }
}
