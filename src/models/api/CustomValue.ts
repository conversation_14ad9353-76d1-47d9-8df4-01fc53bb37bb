import BaseModel from './base'
import LocationService from '../../services/LocationServices'

export interface ICustomValue {
  name: string
  value: string
  locationId: string
}

export default class CustomValue extends BaseModel {
  static requestBodyWhiteList = ['name', 'value']

  // Methods

  constructor(data: ICustomValue) {
    super(CustomValue.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {
          let selectedData = CustomValue.requestBodyWhiteList.length === 0
            ? this.data
            : CustomValue.requestBodyWhiteList.reduce((acc, cv) => {
                acc[cv] = this.data[cv]
                return acc
              }, {})
          response = await LocationService.CustomValues.create(
            this.locationId,
            selectedData
          )
          if (response.customValue) {
            this.data.id = response.customValue.id
            this.newRecord = false
          }
        } else {
          response = await LocationService.CustomValues.update(
            this.locationId,
            this.id,
            this.updatedData
          )
        }
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        await LocationService.CustomValues.delete(this.locationId, this.id)
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }

  static async list(locationId: string): Promise<CustomValue[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.CustomValues.list(locationId)
        resolve(response.customValues.map(n => new CustomValue(n)))
      } catch (error) {
        reject(error)
      }
    })
  }

  static async read(
    locationId: string,
    customValueId: string
  ): Promise<CustomValue> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.CustomValues.read(
          locationId,
          customValueId
        )
        resolve(new CustomValue(response.customValue))
      } catch (error) {
        reject(error)
      }
    })
  }

  // Getters and Setters
  get name(): string {
    return this.data.name
  }

  set name(value: string) {
    this.data.name = value
    this.updatedData.name = value
  }

  get value(): string {
    return this.data.value
  }

  set value(value: string) {
    this.data.value = value
    this.updatedData.value = value
  }

  get fieldKey(): string {
    return this.data.fieldKey
  }

  get locationId(): string {
    return this.data.locationId
  }
}
