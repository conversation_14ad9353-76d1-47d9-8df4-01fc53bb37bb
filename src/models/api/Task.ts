import BaseModel from './base'
import ContactService from '../../services/ContactServices'
import LocationService from '../../services/LocationServices'

export interface ITask {
  title: string
  body: string
  dueDate: string
  status?: string
  assignedTo: string
  completed?: boolean
  contactId: string
}

export default class Task extends BaseModel {
  static requestBodyWhiteList = [
    'title',
    'body',
    'dueDate',
    'completed',
    'assignedTo',
  ]

  // Methods
  constructor(data: ITask) {
    super(Task.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {
          let selectedData = Task.requestBodyWhiteList.length === 0
            ? this.data
            : Task.requestBodyWhiteList.reduce((acc, cv) => {
                acc[cv] = this.data[cv]
                return acc
              }, {})
          if(this.contactId){
            response = await ContactService.Tasks.create(
              this.contactId,
              selectedData
            )
          }else{
            response = await LocationService.Tasks.accountTaskCreate(
              this.accountId,
              selectedData

            )
          }
          if (response.task) {
            this.data.id = response.task.id
            this.newRecord = false
          }
        } else if(this.contactId){
          response = await ContactService.Tasks.update(
            this.contactId,
            this.id,
            this.updatedData
          )
        } else if(this.accountId) {
          response = await LocationService.Tasks.accountTaskUpdate(
            this.accountId,
            this.id,
            this.updatedData
          )
        }
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(locationId: string,TaskId?:string): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Tasks.delete(locationId, TaskId)
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  static async list(queryData:{ [key: string]: any }): Promise<Task[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Tasks.list(queryData)
        resolve(response.tasks.map(n => new Task(n)))
      } catch (error) {
        reject(error)
      }
    })
  }

  static async read(locationId: string, taskId: string): Promise<Task> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Tasks.read(locationId, taskId)
        resolve(new Task(response.task))
      } catch (error) {
        reject(error)
      }
    })
  }


  // Getters and Setters
  get title(): string {
    return this.data.title
  }

  set title(value: string) {
    this.data.title = value
    this.updatedData.title = value
  }

  get dueDate(): Date {
    return this.data.dueDate
  }

  set dueDate(value: Date) {
    this.data.dueDate = value
    this.updatedData.dueDate = value
  }

  get status(): string {
    return this.data.status
  }

  set status(value: string) {
    this.data.status = value
    this.updatedData.status = value
  }

  get assignedTo(): string {
    return this.data.assignedTo
  }

  set assignedTo(value: string) {
    this.data.assignedTo = value
    this.updatedData.assignedTo = value
  }

  get body(): string {
    return this.data.body
  }

  set body(value: string) {
    this.data.body = value
    this.updatedData.body = value
  }

  get completed(): boolean {
    return this.data.completed
  }

  set completed(value: boolean) {
    this.data.completed = value
    this.updatedData.completed = value
  }

  get contactId(): string {
    return this.data.contactId
  }

  get locationId(): string {
    return this.data.locationId
  }

  set locationId(value: string) {
    this.data.locationId = value
    this.updatedData.locationId = value
  }

  get accountId(): string {
    return this.data.accountId
  }

  set accountId(value: string) {
    this.data.accountId = value
    this.updatedData.accountId = value
  }

}
