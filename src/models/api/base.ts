import * as lodash from 'lodash'
export default abstract class BaseModel {
  private _id: string
  private _data: { [field: string]: any }
  protected _oldData: { [field: string]: any }
  private _updatedData: { [field: string]: any } = {}
  private _newRecord: boolean

  // Todo remove collectionRef
  constructor(requestBodyWhiteList: string[], data?: { [key: string]: any }) {
    this._data = data
    if (data.id) {
      this._id = data.id
    } else {
      this._newRecord = true
    }
    this._oldData = lodash.cloneDeep(this._data)
  }

  //   Getters and Setters
  get id(): string {
    return this._id
  }

  get data(): { [field: string]: any } {
    return this._data
  }

  get oldData(): { [key: string]: any } {
    return this._oldData
  }

  get updatedData(): { [field: string]: any } {
    return this._updatedData
  }

  get newRecord(): boolean {
    return this._newRecord
  }

  set newRecord(value: boolean) {
    this._newRecord = value
  }

  get dateAdded(): Date {
    return this._data.dateAdded
  }

  set dateAdded(value: Date) {
    this._data.dateAdded = value
  }

  get dateUpdated(): Date {
    return this._data.dateUpdated
  }

  set dateUpdated(value: Date) {
    this._data.dateUpdated = value
  }

  // Methods
  public getOldValue(key: string) {
    return this._oldData[key]
  }

  abstract save(): Promise<{ [key: string]: any }>
}
