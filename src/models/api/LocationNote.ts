import BaseModel from './base'
import LocationService from '../../services/LocationServices'

export interface INote {
  userId: string
  body: string
  contactId: string
}

export default class LocationNote extends BaseModel {
  static requestBodyWhiteList = ['userId', 'body']

  // Methods
  constructor(data: INote) {
    super(LocationNote.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {

          let selectedData = LocationNote.requestBodyWhiteList.length === 0
            ? this.data
            : LocationNote.requestBodyWhiteList.reduce((acc, cv) => {
                acc[cv] = this.data[cv]
                return acc
              }, {})


            response = await LocationService.Notes.create(
              this.accountId,
              selectedData
            )

          if (response.note) {
            this.data.id = response.note.id
            this.newRecord = false
          }
        }  else {
          response = await LocationService.Notes.update(
            this.accountId,
            this.id,
            this.updatedData
          )
        }
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Notes.delete(this.accountId, this.id)
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  static async list(locationId: string): Promise<LocationNote[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Notes.list(locationId)
        resolve(response.notes.map(n => new LocationNote(n)))
      } catch (error) {
        reject(error)
      }
    })
  }

  static async read(locationId: string, noteId: string): Promise<LocationNote> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.Notes.read(locationId, noteId)
        resolve(new LocationNote(response.note))
      } catch (error) {
        reject(error)
      }
    })
  }

  // Getters and Setters
  get body(): string {
    return this.data.body
  }

  set body(value: string) {
    this.data.body = value
    this.updatedData.body = value
  }

  get userId(): string {
    return this.data.userId
  }

  get contactId(): string {
    return this.data.contactId
  }

  get accountId(): string {
    return this.data.accountId
  }

  set accountId(value: string) {
    this.data.accountId = value
    this.updatedData.accountId = value
  }

  get locationId(): string {
    return this.data.locationId
  }

  set locationId(value: string) {
    this.data.locationId = value
    this.updatedData.locationId = value
  }
}
