import BaseModel from './base'
import LocationService from '../../services/LocationServices'

export interface ICustomField {
  name: string
  dataType: string
  placeholder: string
  options: string[]
  acceptedFormat: []
  isMultipleFile: boolean
  maxNumberOfFiles: number
  textBoxListOptions: { [key: string]: any }[]
  position: number
  documentType: string
  parentId: string
  picklistOptions: { [key: string]: any } | string[]
  picklistOptionsImage: { [key: string]: any } | string[]
  model: string
  locationId: string
}

export default class CustomField extends BaseModel {
  static requestBodyWhiteList = [
    'name',
    'dataType',
    'placeholder',
    'options',
    'acceptedFormat',
    'isMultipleFile',
    'maxNumberOfFiles',
    'textBoxListOptions',
    'position',
    'documentType',
    'parentId',
    'picklistOptions',
    'picklistOptionsImage',
    'model',
  ]

  // Methods

  constructor(data: ICustomField) {
    super(CustomField.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {
          response = await LocationService.CustomFields.create(
            this.locationId,
            this.updatedData
          )
          if (response.customField) {
            this.data.id = response.customField.id
            this.newRecord = false
          }
        } else {
          response = await LocationService.CustomFields.update(
            this.locationId,
            this.id,
            this.updatedData
          )
        }
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        await LocationService.CustomFields.delete(this.locationId, this.id)
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }

  static async list(locationId: string): Promise<CustomField[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.CustomFields.list(locationId)
        resolve(response.customFields.map(n => new CustomField(n)))
      } catch (error) {
        reject(error)
      }
    })
  }

  static async read(
    locationId: string,
    customFieldId: string
  ): Promise<CustomField> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await LocationService.CustomFields.read(
          locationId,
          customFieldId
        )
        resolve(new CustomField(response.customField))
      } catch (error) {
        reject(error)
      }
    })
  }

  // Getters and Setters
  get name(): string {
    return this.data.name
  }

  set name(value: string) {
    this.data.name = value
    this.updatedData.name = value
  }

  get fieldKey(): string {
    return this.data.fieldKey
  }

  get placeholder(): string {
    return this.data.placeholder
  }

  set placeholder(value: string) {
    this.data.placeholder = value
    this.updatedData.placeholder = value
  }

  get dataType(): string {
    return this.data.dataType
  }

  set dataType(value: string) {
    this.data.dataType = value
    this.updatedData.dataType = value
  }

  get position(): number {
    return this.data.position
  }

  set position(value: number) {
    this.data.position = value
    this.updatedData.position = value
  }

  get documentType(): string {
    return this.data.documentType
  }

  set documentType(value: string) {
    this.data.documentType = value
    this.updatedData.documentType = value
  }

  get parentId(): string {
    return this.data.parentId
  }

  set parentId(value: string) {
    this.data.parentId = value
    this.updatedData.parentId = value
  }

  get picklistOptions(): string[] {
    return this.data.picklistOptions
  }

  set picklistOptions(value: string[]) {
    this.data.picklistOptions = value
    this.updatedData.picklistOptions = value
  }

  get picklistImageOptions(): string[] {
    return this.data.picklistImageOptions
  }

  set picklistImageOptions(value: string[]) {
    this.data.picklistImageOptions = value
    this.updatedData.picklistImageOptions = value
  }

  get isAllowedCustomOption(): boolean {
    return this.data.isAllowedCustomOption
  }

  set isAllowedCustomOption(value: boolean) {
    this.data.isAllowedCustomOption = value
    this.updatedData.isAllowedCustomOption = value
  }

  get isMultiFileAllowed(): boolean {
    return this.data.isMultiFileAllowed
  }

  set isMultiFileAllowed(value: boolean) {
    this.data.isMultiFileAllowed = value
    this.updatedData.isMultiFileAllowed = value
  }

  get maxFileLimit(): number {
    return this.data.maxFileLimit
  }

  set maxFileLimit(value: number) {
    this.data.maxFileLimit = value
    this.updatedData.maxFileLimit = value
  }

  get locationId(): string {
    return this.data.locationId
  }
}
