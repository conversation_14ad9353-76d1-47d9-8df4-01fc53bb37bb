import BaseModel from './base'
import OpportunityService from '../../services/OpportunityServices'
import moment from 'moment-timezone'
import { getCountryDateFormat } from '../../models/address';

export enum Priority {
  None = 0,
  Low = 1,
  Medium = 2,
  High = 3
}

export enum Status {
  Open = 'open',
  Won = 'won',
  Lost = 'lost',
  Abandoned = 'abandoned'
}

export interface IOpportunity {
  pipelineId: string
  locationId: string
  name: string
  pipelineStageId: string
  status: Status
  contactId: string
  monetaryValue?: string
  assignedTo?: string
  source?: string
  isAttribute?: boolean
  indexVersion?: number
  details?: string
}

export default class Opportunity extends BaseModel {
  static requestBodyWhiteList = [
    'pipelineId',
    'locationId',
    'name',
    'pipelineStageId',
    'status',
    'contactId',
    'monetaryValue',
    'assignedTo',
    'source',
    'isAttribute',
    'indexVersion',
    'details'

  ]

  // Methods

  constructor(data: IOpportunity) {
    super(Opportunity.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {
          let selectedData = Opportunity.requestBodyWhiteList.length === 0
            ? this.data
            : Opportunity.requestBodyWhiteList.reduce((acc, cv) => {
                acc[cv] = this.data[cv]
                return acc
              }, {})
          response = await OpportunityService.create(
            selectedData
          )
          if (response.customValue) {
            this.data.id = response.customValue.id
            this.newRecord = false
          }
        } else {
          response = await OpportunityService.update(
            this.id,
            this.updatedData
          )
        }
        resolve(new Opportunity(response.opportunity))
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const response= await OpportunityService.delete(this.id)
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  static async getById(id: string): Promise<Opportunity> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await OpportunityService.read(id)
        resolve(new Opportunity(response.opportunity))
      } catch (error) {
        reject(error)
      }
    })
  }

  // Getters and Setters
  get name(): string {
    return this.data.name
  }

  set name(value: string) {
    this.data.name = value
    this.updatedData.name = value
  }

  get locationId(): string {
    return this.data.locationId
  }

  set locationId(value: string) {
    this.data.locationId = value
    this.updatedData.locationId = value
  }

  get assignedTo(): string {
    return this.data.assignedTo
  }

  set assignedTo(value: string) {
    this.data.assignedTo = value
    this.updatedData.assignedTo = value
  }

  get pipelineId(): string {
    return this.data.pipelineId
  }

  set pipelineId(value: string) {
    this.data.pipelineId = value
    this.updatedData.pipelineId = value
  }

  get contact(): { [key: string]: any } {
    return this.data.contact
  }

  set contact(value: { [key: string]: any }) {
    this.data.contact = value
    this.updatedData.contact = value
  }

  get contactId(): string {
    return this.data.contactId
  }

  set contactId(value: string) {
    this.data.contactId = value
    this.updatedData.contactId = value
  }

  get closeDate(): Date {
    return this.data.closeDate
  }

  set closeDate(value: Date) {
    this.data.closeDate = value
    this.updatedData.closeDate = value
  }

  get source(): string {
    return this.data.source
  }

  set source(value: string) {
    this.data.source = value
    this.updatedData.source = value
  }

  get details(): string {
    return this.data.details
  }

  set details(value: string) {
    this.data.details = value
    this.updatedData.details = value
  }

  get priority(): Priority {
    return this.data.priority
  }

  set priority(value: Priority) {
    this.data.priority = value
    this.updatedData.priority = value
  }

  get pipelineStageId(): string {
    return this.data.pipelineStageId
  }

  set pipelineStageId(value: string) {
    this.data.pipelineStageId = value
    this.updatedData.pipelineStageId = value
  }

  get status(): Status {
    return this.data.status
  }

  set status(value: Status) {
    this.data.status = value
    this.updatedData.status = value
  }

  get winProbability(): number {
    return this.data.winProbability
  }

  set winProbability(value: number) {
    this.data.winProbability = value
    this.updatedData.winProbability = value
  }

  get lastActionDate(): Date {
    return this.data.lastActionDate
  }

  set lastActionDate(value: Date) {
    this.data.lastActionDate = value
    this.updatedData.lastActionDate = value
  }

  get indexVersion(): number {
    return this.data.indexVersion;
  }

  set indexVersion(value: number) {
    this.data.indexVersion = value;
    this.updatedData.indexVersion = value
  }

  get internalSource(): { [key: string]: any } {
    return this.data.internalSource;
  }

  set internalSource(value: { [key: string]: any }) {
    this.data.internalSource = value;
    this.updatedData.internalSource = value
  }

  get monetaryValue(): number {
    return this.data.monetaryValue;
  }

  set monetaryValue(value: number) {
      this.data.monetaryValue = value
      this.updatedData.monetaryValue = value
  }

  get isAttribute(): boolean {
    return this.data.isAttribute;
  }

  set isAttribute(value: boolean) {
      this.data.isAttribute = value
      this.updatedData.isAttribute = value
  }

  get createdAt(): Date {
    return moment(this.data.createdAt,'YYYY-MM-DDTHH:mm:ssZ').format(getCountryDateFormat('extended-normal'));
  }

}
