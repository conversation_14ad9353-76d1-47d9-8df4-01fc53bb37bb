import BaseModel from './base'
import ContactService from '../../services/ContactServices'

export interface INote {
  userId: string
  body: string
  contactId: string
}

export default class Note extends BaseModel {
  static requestBodyWhiteList = ['userId', 'body']

  // Methods
  constructor(data: INote) {
    super(Note.requestBodyWhiteList, data)
  }

  async save(): Promise<{ [key: string]: any }> {
    return new Promise(async (resolve, reject) => {
      try {
        let response: { [key: string]: any }
        if (this.newRecord) {

          let selectedData = Note.requestBodyWhiteList.length === 0
            ? this.data
            : Note.requestBodyWhiteList.reduce((acc, cv) => {
                acc[cv] = this.data[cv]
                return acc
              }, {})

            response = await ContactService.Notes.create(
              this.contactId,
              selectedData
            )

          if (response.note) {
            this.data.id = response.note.id
            this.newRecord = false
          }
        } else {
          response = await ContactService.Notes.update(
            this.contactId,
            this.id,
            this.updatedData
          )
        }
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  async delete(): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await ContactService.Notes.delete(this.contactId, this.id)
        resolve(response)
      } catch (error) {
        reject(error)
      }
    })
  }

  static async list(contactId:string): Promise<Note[]> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await ContactService.Notes.list(contactId)
        resolve(response.notes.map(n => new Note(n)))
      } catch (error) {
        reject(error)
      }
    })
  }

  static async read(contactId: string, noteId: string): Promise<Note> {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await ContactService.Notes.read(contactId, noteId)
        resolve(new Note(response.note))
      } catch (error) {
        reject(error)
      }
    })
  }

  // Getters and Setters
  get body(): string {
    return this.data.body
  }

  set body(value: string) {
    this.data.body = value
    this.updatedData.body = value
  }

  get userId(): string {
    return this.data.userId
  }

  get contactId(): string {
    return this.data.contactId
  }
}
