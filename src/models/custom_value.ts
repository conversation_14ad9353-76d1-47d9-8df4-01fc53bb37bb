import firebase from 'firebase/app'
import moment from 'moment-timezone'
import handlebars from 'handlebars'
export default class CustomValue {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('custom_values')
  }

  public async save() {
    this.dateUpdated = moment().utc()
    await this._ref.set(this.data)
  }

  public static getById(id): Promise<CustomValue> {
    return new Promise((resolve, reject) => {
      CustomValue.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new CustomValue(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<CustomValue | undefined> {
    const snapshot = await CustomValue.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new CustomValue(snapshot.docs[0])
  }

  public static getByLocationIdRealtime(
    locationId: string
  ): firebase.firestore.Query {
    return CustomValue.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('name')
  }

  public static async getByLocationId(
    locationId: string
  ): Promise<CustomValue[]> {
    const snapshot = await CustomValue.getByLocationIdRealtime(locationId).get()
    return snapshot.docs.map(s => new CustomValue(s))
  }

  public static async getByFieldKey(
    locationId: string,
    fieldKey: string
  ): Promise<CustomValue[]> {
    const snapshot = await CustomValue.collectionRef()
    .where('location_id', '==', locationId)
    .where('deleted', '==', false)
    .where('field_key', '==', fieldKey)
    .get()

    return snapshot.docs.map(s => new CustomValue(s))
  }

  public static async getMapByLocationId(
    locationId: string
  ): Promise<{ [key: string]: any }> {
    try {
      const values = await CustomValue.getByLocationId(locationId)
      const templateParams = {
        custom_values: {}
      }
      values.forEach(v => {
        if (!v.value || !v.fieldKey) return
        templateParams.custom_values[v.fieldKey] = new handlebars.SafeString(
          v.value
        )
      })
      return templateParams
    } catch (err) {
      return {}
    }
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = CustomValue.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment()
      this.deleted = false
    }
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return lodash.pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get fieldKey(): string {
    return this._data.field_key
  }

  set fieldKey(fieldKey: string) {
    this._data.field_key = fieldKey
  }

  get value(): string {
    return this._data.value
  }

  set value(value: string) {
    this._data.value = value
  }
}
