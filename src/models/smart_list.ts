import firebase from 'firebase/app';
import lodash from 'lodash';
import { IColDefinition, ISortDefintion } from '../pmd/pages/smartlist/vm/interfaces';
import User from './user';
import store from '@/store'
import axios from 'axios';

const SL_SHARE_WITH_ALL_API = '/smartlist/share_with_all';
const SL_UNSHARE_WITH_ALL_API = '/smartlist/unshare_with_all';
const SL_SHARE_WITH_SELECT_API = '/smartlist/share_with_select';
const SL_DELETE_API = '/smartlist/delete';
const SL_UPDATE_SHARES_API = '/smartlist/update_shares';


export default class SmartList {

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('smart_lists');
  }

  public static getForUserLocation(userId: string, locationId: string): Promise<SmartList[]> {
    return new Promise((resolve, reject) => {
      SmartList.collectionRef()
        .where('location_id', '==', locationId)
        .where('user_id', '==', userId) // changed order to match new indexes
        .get()
        .then(snapshot => {
          if (snapshot.empty) { return resolve([]) }
          return resolve(snapshot.docs.map((d) => new SmartList(d)));
        })
        .catch(err => {
          console.error(err)
          reject(err)
        });
    });
  }

  // public static listenForLocation(locationId: string): firebase.firestore.Query  {
  //   return SmartList.collectionRef().where('location_id', '==', locationId)
  // }

  public static getGlobalLists(locationId: string): Promise<SmartList[]> {
    return new Promise((resolve, reject) => {
      SmartList.collectionRef()
        .where('location_id', '==', locationId)
        .where('global', '==', true)
        .get()
        .then(snapshot => {
          if (snapshot.empty) { return resolve([]) }
          return resolve(snapshot.docs.map((d) => new SmartList(d)));
        })
        .catch(err => {
          console.error(err)
          reject(err)
        });
    });
  }

  public static getById(documentId: string): Promise<SmartList> {
    return new Promise((resolve, reject) => {
      SmartList.collectionRef()
        .doc(documentId).get()
        .then((snapshot) => resolve(new SmartList(snapshot)))
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static getDefaultFilters() {
    return {
      filters: [],
      page: 1,
      limit: 50
    };
  }

  private _data: { [key: string]: any };
  private _id: string;
  private _ref: firebase.firestore.DocumentReference;
  private _snapshot: firebase.firestore.DocumentSnapshot;

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data() || {};
      this._snapshot = snapshot;
    } else if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot;
    } else {
      this._ref = SmartList.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.displayOrder = 1000;
    }
  }

  get id(): string {
    return this._id;
  }

  get masterListId(): string { // id of parent list from which it was shared
    return this._data.master_list_id;
  }

  set masterListId(mid: string) {
    this._data.master_list_id = mid;
  }

  get sharedWith(): { [key: string]: any }[] {
    if (!this._data.shared_with) this._data.shared_with = [];
    return this._data.shared_with;
  }

  set sharedWith(list: { [key: string]: any }[]) {
    this._data.shared_with = list;
  }

  get global(): boolean {
    return this._data.global;
  }

  set global(val: boolean) {
    this._data.global = val;
  }

  get isSharedList(): boolean {
    return this._data.is_shared_list;
  }

  set isSharedList(val: boolean) {
    this._data.is_shared_list = val;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined);
  }

  get ref() {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get listName(): string {
    return this._data.list_name;
  }

  set listName(name: string) {
    this._data.list_name = name;
  }

  get displayOrder(): number {
    return this._data.display_order;
  }

  set displayOrder(order: number) {
    this._data.display_order = order;
  }

  get userId(): string {
    return this._data.user_id;
  }

  set userId(userId: string) {
    this._data.user_id = userId;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get filterSpecs(): any {
    if (!this._data.filter_specs) {
      this._data.filter_specs = SmartList.getDefaultFilters();
    }
    return this._data.filter_specs;
  }

  set filterSpecs(specs: any) {
    this._data.filter_specs = specs;
  }

  get columns(): IColDefinition[] {
    if (!this._data.columns) {
      this._data.columns = new Array<IColDefinition>(); //SmartList.getDefaultColumns();
    }
    return this._data.columns;
  }

  set sortSpecs(specs: ISortDefintion[]) {
    this._data.sort_specs = specs;
  }

  get sortSpecs(): ISortDefintion[] {
    if (!this._data.sort_specs) {
      this._data.sort_specs = new Array<ISortDefintion>();
    }
    return this._data.sort_specs;
  }

  set columns(columns: IColDefinition[]) {
    this._data.columns = columns;
  }

  public removeFilter(filterId: string) {
    if (!filterId || filterId.trim() === '') return;
    if (!this.filterSpecs) return;
    if (!this.filterSpecs.filters) return;
    let idx = this.filterSpecs.filters.findIndex((a) => a.id === filterId);
    if (idx !== -1) {
      this.filterSpecs.filters.splice(idx, 1);
    }
  }

  public copy(lockAllFilters?: boolean) {
    let ls = new SmartList();
    ls.listName = this.listName;
    ls.userId = this.userId;
    ls.locationId = this.locationId;
    ls.displayOrder = this.displayOrder;
    ls.columns = lodash.cloneDeep(this.columns);
    ls.sortSpecs = lodash.cloneDeep(this.sortSpecs);
    if (!ls.filterSpecs) ls.filterSpecs = {};
    ls.filterSpecs.limit = this.filterSpecs.limit;
    ls.filterSpecs.filters = lodash.cloneDeep(this.filterSpecs.filters);
    console.log(ls.filterSpecs.filters)
    if (lockAllFilters === true) ls.filterSpecs.filters.forEach(f => {
      if (!f.extras) f.extras = {};
      f.extras.isLocked = true;
    });
    return ls;
  }

  public async save(syncShares: boolean = true): Promise<SmartList> {

    const _self = this;
    await this._ref.set(this.data);
    if (syncShares == true) {
      this.isProcessing = true;
      await axios.post(SL_UPDATE_SHARES_API, { smartlist_id: this.id });
      this.isProcessing = false;
    }
    return this;
  }

  public async delete(): Promise<SmartList> {
    this.isProcessing = true;
    console.log(`Deleting smart list ${this.listName}`);
    await axios.post(SL_DELETE_API, { smartlist_id: this.id });
    this.isProcessing = false;
    return this;
  }

  public async shareAll() {
    this.isProcessing = true;
    await axios.post(SL_SHARE_WITH_ALL_API, { smartlist_id: this.id });
    this.isProcessing = false;
  }

  public async shareWithSelect(shareUserIds?: string[], unShareUserIds?: string[]) {
    this.isProcessing = true;
    // const smartlist = await SmartList.getById(req.body.smartlist_id);
    const currentUser = new User(await store.dispatch('user/get'));
    await axios.post(SL_SHARE_WITH_SELECT_API, {
      smartlist_id: this.id,
      requester_user_id: currentUser ? currentUser.id : undefined,
      share_with_user_ids: shareUserIds,
      unshare_with_user_ids: unShareUserIds
    });
    this.isProcessing = false;
  }

  public canUserDelete(user: User) {
    if (!user) return false;
    if (this.global && user.isAdmin) {
      return true;
    } else if (this.masterListId) { //shared to this list by some parent list
      return true;
    } else if (this.isSharedList && this.sharedWith && this.sharedWith.length > 0) { // this list is parent list for others so cannot delete it unless there are no more shares
      return false;
    } else if (this.userId === user.id) { // the user has created this list
      return true;
    }
    return false;
  }

  public canUserShare(user: User) {

    if (this.global && user && user.isAdmin) return true;  // Global list can be only shared by admins
    if (!this.masterListId && user && user.id === this.userId) return true; // Does not have any parent list and user

    return false;
  }

  public canUserEdit(user: User) {

    if (this.global && user && user.isAdmin) return true;  // Global list can be only edited by admins
    if (!this.masterListId && user && user.id === this.userId) return true; // Does not have any parent list.

    return false;
  }

  public isProcessing = false;

}

// let expression = SmartList.collectionRef().where('location_id', '==', locationId);
// const foundUser : User = new User(await store.getters['users/getById'](userId));
// if (foundUser && foundUser.isAdmin) expression = expression.where('user_id', '==', userId);
