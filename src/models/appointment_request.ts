import firebase from 'firebase/app';
import * as moment from 'moment-timezone';
import axios from 'axios';

export default class AppointmentRequest {
    public static CHANNEL_PHONE = 'phone';
    public static CHANNEL_WEB = 'web';

    public static SOURCE_WEBSITE = 'website';
    public static SOURCE_PROFILE = 'profile';

    public static STATUS_NEW = 'new';
    public static STATUS_CONFIRMED = 'confirmed';
    public static STATUS_CANCELLED = 'cancelled';
    public static STATUS_SHOWED = 'showed';
    public static STATUS_NO_SHOW = 'noshow';
    public static STATUS_INVALID = 'invalid';
    public static collectionRef(): firebase.firestore.CollectionReference {
        return firebase.firestore().collection('appointment_requests');
    }

    public static getById(id: string): Promise<AppointmentRequest> {
        return new Promise((resolve, reject) => {
            AppointmentRequest.collectionRef()
                .doc(id)
                .get()
                .then((snapshot: firebase.firestore.DocumentSnapshot) => {
                    if (snapshot.exists) resolve(new AppointmentRequest(snapshot));
                    resolve();
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static getStreamById(id: string): firebase.firestore.DocumentReference {
        return AppointmentRequest.collectionRef().doc(id);
    }

    public static async fetchFutureAppointmentRequestForContact(
        locationId: string,
        contactId: string,
        start: moment.Moment
    ): Promise<AppointmentRequest> {
        return new Promise<AppointmentRequest>(async (resolve, reject) => {
            AppointmentRequest.collectionRef()
                .where('deleted', '==', false)
                .where('start_time', '>=', start.toDate())
                .where('location_id', '==', locationId)
                .where('contact_id', '==', contactId)
                .orderBy('start_time', 'asc')
                .limit(1)
                .get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve();
                    resolve(new AppointmentRequest(snapshot.docs[0]));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    public static fetchAllAppointmentRequests(locationId: string): firebase.firestore.Query {
        return AppointmentRequest.collectionRef()
            .where('deleted', '==', false)
            .where('location_id', '==', locationId)
            .orderBy('date_updated', 'desc');
    }

    public static fetchAllAppointmentRequestsBetween(
        locationId: string,
        start: moment.Moment,
        end: moment.Moment
    ): firebase.firestore.Query {
        return AppointmentRequest.collectionRef()
            .where('deleted', '==', false)
            .where('location_id', '==', locationId)
            .where('start_time', '>=', start.toDate())
            .where('start_time', '<', end.toDate())
            .orderBy('start_time', 'desc');
    }

    public static async fetchFrozenAppointmentRequestsBetween(
        locationId: string,
        start: moment.Moment,
        end: moment.Moment
    ): Promise<AppointmentRequest[]> {
        return new Promise<AppointmentRequest[]>(async (resolve, reject) => {
            AppointmentRequest.collectionRef()
                .where('deleted', '==', false)
                .where('start_time', '>=', start.toDate())
                .where('start_time', '<', end.toDate())
                .where('location_id', '==', locationId)
                .orderBy('start_time', 'desc')
                .get()
                .then((snapshot) => {
                    if (snapshot.empty) resolve([]);
                    resolve(snapshot.docs.map((s) => new AppointmentRequest(s)));
                })
                .catch((err) => {
                    reject(err);
                });
        });
    }

    private _id: string;
    private _data: firebase.firestore.DocumentData;
    private _ref: firebase.firestore.DocumentReference;
    private _snapshot: firebase.firestore.DocumentSnapshot;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
            this._snapshot = snapshot;
        } else {
            this._ref = AppointmentRequest.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.deleted = false;
            this.existing = false;
            this.dateAdded = moment();
            this.status = AppointmentRequest.STATUS_NEW;
        }
    }

    get id(): string {
        return this._id;
    }

    get data(): firebase.firestore.DocumentData {
        return this._data;
    }

    get ref() {
        return this._ref;
    }

    get snapshot() {
        return this._snapshot;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added.toMillis());
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated.toMillis());
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
    }

    get deleted(): boolean {
        return this._data.deleted;
    }

    set deleted(deleted: boolean) {
        this._data.deleted = deleted;
    }

    get dirty(): boolean {
        return this._data.dirty;
    }

    set dirty(dirty: boolean) {
        this._data.dirty = dirty;
    }

    get startTime(): moment.Moment | undefined {
        return this._data.start_time ? moment(this._data.start_time.toMillis()) : undefined;
    }

    set startTime(startTime: moment.Moment | undefined) {
        this._data.start_time = startTime ? firebase.firestore.Timestamp.fromMillis(startTime.valueOf()) : undefined;
    }

    get endTime(): moment.Moment | undefined {
        return this._data.end_time ? moment(this._data.end_time.toMillis()) : undefined;
    }

    set endTime(endTime: moment.Moment | undefined) {
        this._data.end_time = endTime ? firebase.firestore.Timestamp.fromMillis(endTime.valueOf()) : undefined;
    }

    get contactId(): string {
        return this._data.contact_id;
    }

    set contactId(contactId: string) {
        this._data.contact_id = contactId;
    }

    get userId(): string {
        return this._data.user_id;
    }

    set userId(userId: string) {
        this._data.user_id = userId;
    }

    get notes(): string {
        return this._data.notes;
    }

    set notes(notes: string) {
        this._data.notes = notes;
    }

    get status(): string {
        return this._data.status;
    }

    set status(status: string) {
        this._data.status = status;
    }

    get source(): string {
        return this._data.source;
    }

    set source(source: string) {
        this._data.source = source;
    }

    get channel(): string {
        return this._data.channel;
    }

    set channel(channel: string) {
        this._data.channel = channel;
    }

    get calendarId(): string {
        return this._data.calendar_id;
    }

    set calendarId(calendarId: string) {
        this._data.calendar_id = calendarId;
    }

    get callId(): string {
        return this._data.call_id;
    }

    set callId(callId: string) {
        this._data.call_id = callId;
    }

    get existing(): boolean {
        return this._data.existing;
    }

    set existing(existing: boolean) {
        this._data.existing = existing;
    }

    public async save() {
        this.dateUpdated = moment();
        await this._ref.set(this._data);
    }

    public delete() {
        return AppointmentRequest.collectionRef()
            .doc(this.id)
            .update({
                deleted: true,
            });
    }

    public async upload() {
        try {
            await axios.get('/appointment/' + this.id + '/upload');
        } catch (err) { }
    }
}
