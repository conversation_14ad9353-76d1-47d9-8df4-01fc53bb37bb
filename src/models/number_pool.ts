import firebase from 'firebase/app';
import 'firebase/database';
import * as moment from 'moment-timezone';
import * as lodash from 'lodash';

export default class NumberPool {

  private _id: string;
  private _data: { [field: string]: any };
  private _ref: firebase.firestore.DocumentReference;
  private _snapshot: firebase.firestore.DocumentSnapshot;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot | { [key: string]: any } | undefined) {
    if (snapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data();
    } else {
      this._ref = NumberPool.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.dateAdded = moment();
      this.deleted = false;
    }
  }

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('number_pool');
  }

  public save(): Promise<NumberPool> {
		const _self = this;
		return new Promise(async(resolve, reject) => {
			try {
				_self.dateUpdated = moment();
				await _self._ref.set(_self._data, { merge: true }).then((_) => {
					resolve(_self);
				}).catch((err) => {
					reject(err);
				});
			} catch (ex) {
				reject(ex);
			}
		});
	}

  public static getById(id): Promise<NumberPool> {
    return new Promise((resolve, reject) => {
      NumberPool.collectionRef()
        .doc(id)
        .get()
        .then((snapshot) => {
          resolve(new NumberPool(snapshot));
        })
        .catch((err) => {
          console.error(err);
          reject(err);
        });
    });
  }

  public static async getByLocationId(locationId: string): Promise<NumberPool[]> {
    const snapshot = await NumberPool.collectionRef()
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
      .orderBy('name', 'asc')
      .get();
    return snapshot.docs.map((d) => new NumberPool(d));
  }

  get id(): string {
    return this._id;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    );
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get count(): number {
    return this._data.count;
  }

  set count(count: number){
    this._data.count = count;
  }

  get forwarding_number(): string {
    return this._data.forwarding_number;
  }

  set forwarding_number(number: string) {
    this._data.forwarding_number = number;
  }

  get friendly_forwarding_number(): {[key: string]: string}[] {
    return this._data.friendly_forwarding_number;
  }

  set friendly_forwarding_number(number: {[key: string]: string}[]){
    this._data.friendly_forwarding_number = number;
  }

  get twilio_account_id():string {
    return this._data.twilio_account_id;
  }

  set twilio_account_id(id: string) {
    this._data.twilio_account_id = id;
  }

  get numbers(): {[key: string]: string} {
    return this._data.numbers;
  }

  set numbers(numbers: {[key: string]: string}) {
    this._data.numbers = numbers;
  }

  get isActive(): boolean {
    return this._data.isActive;
  }

  set isActive(active: boolean){
    this._data.isActive = active;
  }

  get voicemail(): boolean {
    return this._data.voicemail === undefined ? true : this._data.voicemail;
  }

  set voicemail(voicemail: boolean) {
    this._data.voicemail = voicemail
  }

  get voicemailTimeout(): number {
    return this._data.voicemail_timeout === undefined ? true : this._data.enable_voicemail;
  }

  set voicemailTimeout(voicemailTimeout: number) {
    this._data.voicemail_timeout = voicemailTimeout
  }

  get voicemailOutbound(): boolean {
    return this._data.voicemail_outbound === undefined ? true : this._data.voicemail_outbound;
  }

  set voicemailOutbound(values: boolean) {
    this._data.voicemail_outbound = values
  }

  get voicemailOutboundTimeout(): number {
    return this._data.voicemail_outbound_timeout === undefined ? true : this._data.voicemail_outbound_timeout;
  }

  set voicemailOutboundTimeout(values: number) {
    this._data.voicemail_outbound_timeout = values
  }

  get source(): string {
    return this._data.source;
  }

  set source(value: string) {
    this._data.source = value;
  }

  get whatToTrack(): string {
    return this._data.what_to_track;
  }

  set whatToTrack(value: string) {
    this._data.what_to_track = value;
  }

  get trackingType(): string {
    return this._data.tracking_type;
  }

  set trackingType(value: string) {
    this._data.tracking_type = value;
  }

  get whisper(): boolean {
    return this._data.whisper;
  }

  set whisper(value: boolean) {
    this._data.whisper = value;
  }

  get whisper_msg(): string {
    return this._data.whisper_msg;
  }

  set whisper_msg(value: string) {
    this._data.whisper_msg = value;
  }

  get incomingWhisper(): boolean {
    return this._data.incoming_whisper;
  }

  set incomingWhisper(value: boolean) {
    this._data.incoming_whisper = value;
  }

  get incomingWhisperMsg(): string {
    return this._data.incoming_whisper_msg;
  }

  set incomingWhisperMsg(value: string) {
    this._data.incoming_whisper_msg = value;
  }

  get enableCallerId(): boolean {
    return this._data.enable_callerid;
  }

  set enableCallerId(value: boolean) {
    this._data.enable_callerid = value;
  }

  get call_recording(): boolean {
    return this._data.call_recording
  }

  set call_recording(value: boolean) {
    this._data.call_recording = value
  }

  get call_recording_msg(): string {
    return this._data.call_recording_msg
  }

  set call_recording_msg(value: string) {
    this._data.call_recording_msg = value
  }

  get country_code(): string {
    return this._data.country_code
  }

  set country_code(value: string) {
    this._data.country_code = value
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get swapping_number(): string[] {
    return this._data.swapping_number;
  }

  set swapping_number(number: string[]) {
    this._data.swapping_number = number;
  }

  get swapping_checkbox(): boolean {
    return this._data.swapping_checkbox;
  }

  set swapping_checkbox(value: boolean) {
    this._data.swapping_checkbox = value;
  }

  get groupIds(): string[] {
    return this._data.group_ids
  }

  set groupIds(ids: string[]) {
    this._data.group_ids = ids
  }
}
