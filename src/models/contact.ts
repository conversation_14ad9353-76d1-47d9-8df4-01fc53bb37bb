import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { Address, User, MessageType, getCountryDateFormat, Item, CustomField } from '../models'
import Model from './model'
import { ModelType } from './model'
import config from '../config'
import axios from 'axios'
import * as libphonenumber from 'google-libphonenumber'
import store from '../store'
import vuex from '../store'
import { ModelUtils } from './model_utils'
import lodash from 'lodash'
import { Utils } from '../util/utils'
import { ContactType } from './bulk_request'
import { CustomsFieldFormatter } from '../util/customfields_formatter'
import restAgent from '../restAgent'
import { trackPendoEvent } from '../util/helper'
import ContactService from '../services/ContactServices'
const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()


export interface ContactCustomField {
  id: string;
  field_value: string;
  field_key: string;
  field_value_string: string;
  field_value_number: any;
  field_value_array: number;
  field_value_date: any;
  display: any;
}

export type Type = 'lead' | 'customer'

export interface IContactOpportunity {
  id: string;
  pipeline_id: string;
  pipeline_stage_id: string;
  monetary_value: number;
  status: string;
}

export interface IFormattedOpportunity extends IContactOpportunity {
  pipeline?: string;
  stage?: string;
}

export interface PhoneInfo {
  valid: boolean;
  sms_capable?: boolean;
  type?: string;
  carrier_name?: string;
}

export interface DialogFlowBotInfo {
  agent: string;
  projectId: string;
  calendarId: string;
  uuid?: string;
  welcomeTemplate: string;
  thankyouTemplate: string;
  timeout: Number;
  elizaSessionId: string;
  logicalElizaId?: string;
}

export default class Contact extends Model {

  private _csFieldsFormatter: CustomsFieldFormatter;

  public static TYPE_CUSTOMER = 'customer'
  public static TYPE_LEAD = 'lead'

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('contacts')
  }

  // public static getById(id: string): Promise<Contact> {
  //     return new Promise((resolve, reject) => {
  //         const unsubscribe = Contact.collectionRef()
  //             .where('__name__', '==', id)
  //             .where('deleted', '==', false)
  //             .onSnapshot((snapshot:firebase.firestore.QuerySnapshot) => {
  //                 unsubscribe();
  //                 if (snapshot.docs.length != 1) return resolve();
  //                 resolve(new Contact(snapshot.docs[0]));
  //             });
  //     });
  // }

  public static async getById(id: string): Promise<Contact> {
    trackPendoEvent('perf_contact_by_id', {
      source: 'getById'
    })

    return new Promise<Contact>((resolve, reject) => {
      Contact.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (!snapshot.exists) return resolve()
          if (snapshot.data().deleted) return resolve()
          resolve(new Contact(snapshot))
        })
        .catch((err) => { reject(err) })
    })
  }

  public static getByIds(ids: string[]): Promise<Contact[]> {
    return new Promise((resolve, reject) => {
      Contact.collectionRef()
        .where(firebase.firestore.FieldPath.documentId(), 'in', ids)
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(d => new Contact(d)))
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  public static getStreamById(id: string): firebase.firestore.DocumentReference {
    return Contact.collectionRef().doc(id);
  }

  public static fetchAllLeads(locationId: string): firebase.firestore.Query {
    return Contact.collectionRef()
      .where('deleted', '==', false)
      .where('type', '==', Contact.TYPE_LEAD)
      .where('location_id', '==', locationId)
      .orderBy('first_name_lower_case')
  }

  public static fetchAllContacts(locationId: string): firebase.firestore.Query {
    return Contact.collectionRef()
      .where('deleted', '==', false)
      .where('type', '==', Contact.TYPE_CUSTOMER)
      .where('location_id', '==', locationId)
      .orderBy('first_name_lower_case')
  }

  public static fetchAllContactsByUpdated(
    locationId: string
  ): firebase.firestore.Query {
    return Contact.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('date_updated', 'desc')
  }

  public static fetchAllContactsByUpdatedDeleted(
    locationId: string
  ): firebase.firestore.Query {
    return Contact.collectionRef()
      .where('deleted', 'in', [true, false])
      .where('location_id', '==', locationId)
      .orderBy('date_updated', 'desc')
  }

  public static fetchByTypeRealtime(
    locationId: string,
    type: string
  ): firebase.firestore.Query {
    return Contact.collectionRef()
      .where('deleted', '==', false)
      .where('type', '==', type)
      .where('location_id', '==', locationId)
      .orderBy('date_updated', 'desc')
  }

  public static async searchAll(
    locationId: string,
    type: string,
    term: string,
    token
  ): Promise<Contact[]> {
    if (!term) return Promise.resolve([])
    return new Promise<Contact[]>(async (resolve, reject) => {
      token.cancel = function () {
        reject()
      }
      let url = `${config.baseUrl
        }/search/contact/all?location_id=${locationId}&type=${type !== 'all' ? type : ''
        }&q=${term}`
      const user: User = await store.dispatch('user/get')
      if (user && user.role === 'user' && user.permissions.assigned_data_only) {
        url += `&assigned_to=${user.id}`
      }
      try {
        const response = await axios.get(url)
        if (response.status !== 200) return reject(response)
        const data: { [key: string]: any }[] = response.data.hits.hits
        return resolve(
          data.map(elasticContact => {
            const data = {
              id: elasticContact._id,
              ...elasticContact._source
            }
            return new Contact(data)
          })
        )
      } catch (err) {
        return reject(err)
      }
    })
  }

  public static async searchByName(
    locationId: string,
    type: string,
    term: string,
    token
  ): Promise<Contact[]> {
    if (!term) return Promise.resolve([])
    return new Promise<Contact[]>(async (resolve, reject) => {
      token.cancel = function () {
        reject()
      }
      let url = `${config.baseUrl
        }/search/contact/name?location_id=${locationId}&type=${type !== 'all' ? type : ''
        }&q=${term}`
      const user: User = await store.dispatch('user/get')
      if (user && user.role === 'user' && user.permissions.assigned_data_only) {
        url += `&assigned_to=${user.id}`
      }
      try {
        const response = await axios.get(url)
        if (response.status !== 200) return reject(response)
        const data: { [key: string]: any }[] = response.data.hits.hits
        return resolve(
          data.map(elasticContact => {
            const data = {
              id: elasticContact._id,
              ...elasticContact._source
            }
            return new Contact(data)
          })
        )
      } catch (err) {
        return reject(err)
      }
    })
  }

  public static async bulkImport(body: {
    tags: string[],
    conflictResolution: 'skip' | 'updateEmpty' | 'updateAll',
    locationId: string,
    importRequestId: string,
    emailValidation?: boolean
  }): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return axios.post(`${config.baseUrl}/contact/bulk_import`, body).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }

  public static async bulkUpdateTags(
    contactIds: string[],
    selectAll: boolean,
    size: number,
    tags: string[],
    filters: { [key: string]: any },
    operation: 'add' | 'remove',
    importRequestId: string
  ): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return axios.post(`${config.baseUrl}/contact/bulk_update_tags`, {
        contact_ids: contactIds,
        select_all: selectAll,
        size,
        operation,
        tags,
        filters,
        import_request_id: importRequestId
      }).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }


  public static async smartListUpdateTags(
    importRequestId: string,
    tags: string[],
    operation: 'add' | 'remove',
    smartList: any | null,
    contactIds: string[] | null,
  ): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return axios.post(`${config.baseUrl}/contact/smartlist_update_tags`, {
        import_request_id: importRequestId,
        tags,
        operation,
        smart_list: smartList,
        contact_ids: contactIds
      }).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }

  public static async bulkDelete(
    contactIds: string[],
    selectAll: boolean,
    size: number,
    filters: { [key: string]: any },
    importRequestId: string
  ): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return axios.post(`${config.baseUrl}/contact/bulk_delete`, {
        contact_ids: contactIds,
        select_all: selectAll,
        size,
        filters,
        import_request_id: importRequestId
      }).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }

  public static async smartListDeleteContacts(
    importRequestId: string,
    smartList: any | null,
    contactIds: string[] | null,
    locationId: string
  ): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return restAgent.Contact.deleteSmartlistContacts(contactIds, smartList, locationId).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }

  public static async opportunityUpdates(
    importRequestId: string,
    smartList: any | null,
    contactIds: string[] | null,
    opportunityConfig: {}
  ): Promise<Boolean> {
    return new Promise<Boolean>((resolve, reject) => {
      return axios.post(`${config.baseUrl}/contact/bulk_opportunity_updates`, {
        import_request_id: importRequestId,
        smart_list: smartList,
        contact_ids: contactIds,
        opportunity_change: opportunityConfig
      }).then(() => {
        return resolve(true);
      }).catch(err => {
        console.error(err)
        return reject(err)
      });
    });
  }



  public static getByEmail(email, locationId): Promise<Contact> {
    return new Promise((resolve, reject) => {
      Contact.collectionRef()
        .where('location_id', '==', locationId)
        .where('email_lower_case', '==', email ? email.toLowerCase() : '')
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve(null)
          }
          return resolve(new Contact(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByPhoneNumberAndLocation(
    locationId: string,
    phone: string
  ): Promise<Contact> {
    return new Promise((resolve, reject) => {
      Contact.collectionRef()
        .where('location_id', '==', locationId)
        .where('phone', '==', phone)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve(null)
          }
          return resolve(new Contact(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByLocationId(
    locationId: string,
    limit?: number
  ): Promise<Contact[]> {
    return new Promise<Contact[]>((resolve, reject) => {
      let query = Contact.collectionRef()
        .where('location_id', '==', locationId)
        .where('deleted', '==', false)
        .orderBy('date_updated')
      if (limit) {
        query = query.limit(limit)
      }
      query.get().then(snapshot => {
        resolve(snapshot.docs.map(d => new Contact(d)))
      })
    })
  }

  public static getByLocationIdForHipaa(
    locationId: string
  ): Promise<Contact[]> {
    return new Promise((resolve, reject) => {
      Contact.collectionRef()
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve([])
          }
          resolve(snapshot.docs.map(d => new Contact(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static async getContact(
    locationId: string,
    phone: string | undefined,
    email: string | undefined
  ): Promise<Contact | undefined> {
    let contact
    if (email) contact = await Contact.getByEmail(email, locationId)
    if (contact) return contact
    if (phone)
      contact = await Contact.getByPhoneNumberAndLocation(locationId, phone)
    return contact
  }

  public static last(
    fn
  ): (locationId: string, type: string, term: string) => Promise<Contact[]> {
    var lastToken = { cancel: function () { } } // start with no op
    return function () {
      lastToken.cancel()
      var args = Array.prototype.slice.call(arguments)
      args.push(lastToken)
      return fn.apply(this, args)
    }
  }

  public static getByFingerprint(
    fingerprint: string,
    locationId: string
  ): Promise<Contact> {
    return new Promise((resolve, reject) => {
      Contact.collectionRef()
        .where('location_id', '==', locationId)
        .where('fingerprint', '==', fingerprint)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve(null)
          }
          return resolve(new Contact(snapshot.docs[0]))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static async fetchOrAddContact(
    locationId: string,
    extras: {
      id?: string
      email?: string
      first_name?: string
      last_name?: string
      name?: string
      phone?: string
      date_of_birth?: moment.Moment
      lead?: boolean
      source?: string
      country?: string
      timezone?: string
      fingerprint?: string
      forceCreate?: boolean
      internalSource?: Object
    }
  ): Promise<Contact> {
    let contact: Contact
    let number
    try {
      if (extras.phone) {
        const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
        const raw = phoneUtil.parseAndKeepRawInput(
          extras.phone,
          extras.country || 'US'
        )
        number = phoneUtil.format(raw, libphonenumber.PhoneNumberFormat.E164)
      }
    } catch (e) { }
    if (!extras || !extras.forceCreate) {
      if (extras.fingerprint) {
        contact = await Contact.getByFingerprint(extras.fingerprint, locationId)
      }
      if (number) {
        contact = await Contact.getByPhoneNumberAndLocation(locationId, number)
      }
      if (!contact && extras.email) {
        contact = await Contact.getByEmail(extras.email, locationId)
      }
    }

    if (extras.id) {
      contact = await Contact.getById(extras.id)
    }

    if (!contact) {
      contact = new Contact()
      if (lodash.get(extras, 'lead')) {
        contact.type = 'lead'
      }
      contact.locationId = locationId
      if (extras.source) contact.source = extras.source.trim().toLowerCase()
      if (extras.email) contact.email = extras.email.trim()
      if (extras.name) contact.name = extras.name.trim()
      if (extras.first_name) contact.firstName = extras.first_name.trim()
      if (extras.last_name) contact.lastName = extras.last_name.trim()
      if (extras.timezone) contact.timezone = extras.timezone
      if (extras.fingerprint) contact.fingerprint = extras.fingerprint
      if (number) contact.phone = number // This should be independent of location's flag 'Allow Duplicate Contact' (extras.forceCreate)
      if (extras.date_of_birth) {
        contact.dateOfBirth = extras.date_of_birth
      }
      if (extras.internalSource) contact.internalSource = extras.internalSource
      if (extras.country) contact.country = extras.country
      if (contact.email
        || contact.phone
        || contact.name
        || contact.firstName
        || contact.lastName) {
        // Prevent saving an empty contact on the front-end
        await contact.save()
      }
    } else {
      if (extras.source && !contact.source)
        contact.source = extras.source.trim().toLowerCase()
      if (extras.email) contact.email = extras.email.trim()
      if (extras.name) contact.name = extras.name.trim()
      if (extras.first_name) contact.firstName = extras.first_name.trim()
      if (extras.last_name) contact.lastName = extras.last_name.trim()
      if (number) contact.phone = number
      if (extras.country) contact.country = extras.country
      if (extras.timezone) contact.timezone = extras.timezone
      if (extras.fingerprint && !contact.fingerprint)
        contact.fingerprint = extras.fingerprint
      if (extras.date_of_birth) {
        contact.dateOfBirth = extras.date_of_birth
        contact.birthMonth = extras.date_of_birth.month()
        contact.birthDay = extras.date_of_birth.day()
      }
      await contact.save()
    }
    return contact
  }

  // private static colours: string[] = [
  // 	'#5A8770',
  // 	'#B2B7BB',
  // 	'#6FA9AB',
  // 	'#F5AF29',
  // 	'#0088B9',
  // 	'#F18636',
  // 	'#D93A37',
  // 	'#A6B12E',
  // 	'#5C9BBC',
  // 	'#F5888D',
  // 	'#9A89B5',
  // 	'#407887',
  // 	'#9A89B5',
  // 	'#5A8770',
  // 	'#D33F33',
  // 	'#A2B01F',
  // 	'#F0B126',
  // 	'#0087BF',
  // 	'#F18636',
  // 	'#0087BF',
  // 	'#B2B7BB',
  // 	'#72ACAE',
  // 	'#9C8AB4',
  // 	'#5A8770',
  // 	'#EEB424',
  // 	'#407887',
  // ];

  private static colours: string[] = [
    '#188bf6',
    '#1976d2',
    '#e93d3d',
    '#ffbc00',
    '#37ca37',
    '#876cff',
    '#17cfbc',
    '#ff3e7f',
    '#ff7402',
    '#7be43b'
  ]

  public static standardFields: string[] = [
    'timezone',
    'location_id',
    'date_added',
    'date_updated',
    'deleted',
    'dirty',
    'type',
    'tags',
    'custom_fields',
    'state',
    'postal_code',
    'phone',
    'parent',
    'last_name',
    'name',
    'first_name',
    'email',
    'country',
    'company_name',
    'city',
    'bill_to',
    'address_geo',
    'address1',
    'source',
    'date_of_birth',
    'birth_month',
    'birth_day',
    'ssn',
    'website',
    'dnd',
    'assigned_to',
    'import_id',
    'last_conversation_id',
    'last_conversation_message_type',
    'last_note',
    'lastNote',
    'attachments',
    'fingerprint',
    'valid_email',
    'bounce_email',
    'unsubscribe_email',
    'profile_photo',
    'clio',
    'clio',
    'facebook',
    'pool_id',
    'gclid',
    'source_type',
    'keyword'
  ]

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {

    if (
      snapshot &&
      !(snapshot instanceof firebase.firestore.QueryDocumentSnapshot) &&
      !(snapshot instanceof firebase.firestore.DocumentSnapshot)
    ) {
      snapshot = lodash.transform(snapshot, (result, val, key) => {
        if (key !== 'address1') result[lodash.snakeCase(key)] = val
        else result[key] = val
      }, {})
    }

    super(snapshot)
    if (snapshot) {
      if (typeof snapshot.date_of_birth === 'number') {
        this.dateOfBirth = moment(snapshot.date_of_birth)
      }

      if (snapshot.contact_name) {
        // if (this.modelType !== ModelType.firestore) {
        //   this.fullName = snapshot.contact_name
        // }
        delete this._data.contact_name;
        delete this._oldData.contact_name;
      }

      if (snapshot.full_name) {
        this.fullName = snapshot.full_name
      }
    } else {
      this.deleted = false
      this.tags = []
      this.customFields = [] // Ask to Varun, was it required to add test custom field
      this.type = 'lead'
    }

    if (['Etc/Greenwich', 'GMT'].includes(this.timezone)) {
      this.timezone = 'UTC'
    }
  }

  get id(): string {
    return this._id
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== undefined)
  }

  get ref() {
    return this._ref
  }

  get snapshot() {
    return this._snapshot
  }

  get timezone(): string {
    return this._data.timezone
  }

  set timezone(timezone: string) {
    this._data.timezone = timezone
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get dateAdded(): moment.Moment {
    return this._data['date_added']
      ? typeof this._data['date_added'] === 'number'
        ? moment(this._data['date_added'])
        : moment(this._data['date_added'].toMillis())
      : undefined
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data['date_added'] = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return this._data['date_updated']
      ? typeof this._data['date_updated'] === 'number'
        ? moment(this._data['date_updated'])
        : moment(this._data['date_updated'].toMillis())
      : undefined
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data['date_updated'] = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get dirty(): boolean {
    return this._data.dirty
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty
  }

  get internalSource(): { [key: string]: any } {
    return this._data.internal_source;
  }

  set internalSource(internalSource: { [key: string]: any }) {
    this._data.internal_source = internalSource;
  }

  get type(): Type {
    return this._data.type
  }

  set type(type: Type) {
    this._data.type = type
  }

  get tags(): string[] {
    if (lodash.isEmpty(this._data.tags) || !(this._data.tags instanceof Array))
      return []
    return this._data.tags
  }

  set tags(tags: string[]) {
    this._data.tags = tags
  }

  public addTags(tags: string[]) {
    tags.forEach(tag => {
      this.addTag(tag)
    })
  }

  public removeTags(tags: string[]) {
    tags.forEach(tag => {
      this.removeTag(tag)
    })
  }

  public addTag(tag: string) {
    if (this.tags.indexOf(tag.toLowerCase()) === -1) {
      this.tags.push(tag.toLowerCase())
    }
  }

  public removeTag(tag: string) {
    if (this.tags.indexOf(tag) !== -1) {
      this.tags.splice(this.tags.indexOf(tag), 1)
    }
  }

  private initCustomFieldsFormatter(items: ContactCustomField[]) {
    this._csFieldsFormatter = CustomsFieldFormatter.getFormatter(items)
  }

  private syncCustomFieldsIfRequired(forceUpdate: boolean = false) {
    this._data.custom_fields = this.customFieldsFormatter.getFSFormatFields(forceUpdate);
  }

  get customFields(): ContactCustomField[] {
    this.syncCustomFieldsIfRequired(); // dont remove
    return this._data.custom_fields;
  }

  set customFields(newItems: ContactCustomField[]) {
    this._data.custom_fields = newItems;
    this.initCustomFieldsFormatter(newItems); // update formatter when custom fields get entirely refreshed
  }

  get customFieldsFormatter(): CustomsFieldFormatter {
    if (!this._csFieldsFormatter) this.initCustomFieldsFormatter(this._data && this._data.custom_fields)
    return this._csFieldsFormatter;
  }

  public addCustomFields(custom_fields: ContactCustomField[]) {
    custom_fields.forEach(field => {
      this.addCustomField(field)
    })
  }

  public removeCustomFields(custom_fields: ContactCustomField[]) {
    custom_fields.forEach(field => {
      this.removeCustomField(field)
    })
  }

  public addCustomField(custom_field: ContactCustomField) {
    const check = this.customFields.find(x => x.id === custom_field.id)
    if (!check) {
      this.customFields.push(custom_field)
    }
  }

  public removeCustomField(custom_field: ContactCustomField) {
    const index = this.customFields.findIndex(x => x.id === custom_field.id)
    if (index && index !== -1) {
      this.customFields.splice(index, 1)
    }
  }

  get state(): string | undefined {
    return this._data.state
  }

  set state(state: string | undefined) {
    this._data.state = state
  }

  get postalCode(): string | undefined {
    return this._data.postal_code
  }

  set postalCode(postalCode: string | undefined) {
    this._data.postal_code = postalCode
  }

  get phone(): string {
    return this._data.phone
  }

  set phone(phone: string) {
    this._data.phone = phone
  }

  get parent(): string {
    return this._data.parent
  }

  set parent(parent: string) {
    this._data.parent = parent
  }

  get lastName(): string {
    return this._data.last_name
  }

  set lastName(lastName: string) {
    this._data.last_name = lastName
    this._data.last_name_lower_case = lastName
      ? lastName.toLocaleLowerCase()
      : undefined
    this._data['full_name_lower_case'] = [this.firstName, this.lastName].filter(val => val).join(' ').toLowerCase();
  }

  get firstName(): string {
    return this._data.first_name
  }

  set firstName(firstName: string) {
    this._data.first_name = firstName
    this._data.first_name_lower_case = firstName
      ? firstName.toLowerCase()
      : undefined
    this._data['full_name_lower_case'] = [this.firstName, this.lastName].filter(val => val).join(' ').toLowerCase();
  }

  get name(): string {
    return [this.firstName, this.lastName].filter(val => val).join(' ')
  }

  set name(name: string) {
    if (name) {
      const parts = name.match(/\S+/g) || []
      if (parts.length >= 1) {
        this.firstName = parts[0]
      }
      if (parts.length >= 2) {
        parts.splice(0, 1)
        this.lastName = parts.join(' ')
      }
    }
  }

  get email(): string {
    return this._data.email
  }

  set email(email: string) {
    this._data.email = email
    if (email) {
      this._data.email_lower_case = email.toLowerCase()
      this._data.bounce_email = false;
      this._data.unsubscribe_email = false;
    }
  }

  get country(): string | undefined {
    return this._data.country
  }

  set country(country: string | undefined) {
    this._data.country = country
  }

  get companyName(): string {
    return this._data.company_name
  }

  set companyName(companyName: string) {
    this._data.company_name = companyName
  }

  get city(): string | undefined {
    return this._data.city
  }

  set city(city: string | undefined) {
    this._data.city = city
  }

  get billTo(): string {
    return this._data.bill_to
  }

  set billTo(billTo: string) {
    this._data.bill_to = billTo
  }

  get addressGeo(): firebase.firestore.GeoPoint | undefined {
    return this._data.address_geo
  }

  set addressGeo(addressGeo: firebase.firestore.GeoPoint | undefined) {
    this._data.address_geo = addressGeo
  }

  get address1(): string | undefined {
    return this._data.address1
  }

  set address1(address1: string | undefined) {
    this._data.address1 = address1
  }

  get source(): string {
    return this._data.source
  }

  set source(source: string) {
    this._data.source = source
  }

  get dateOfBirth(): moment.Moment | undefined {
    return this._data.date_of_birth
      ? moment(this._data.date_of_birth.toMillis())
      : null
  }

  set dateOfBirth(dateOfBirth: moment.Moment | undefined) {
    this._data.date_of_birth = dateOfBirth
      ? firebase.firestore.Timestamp.fromMillis(dateOfBirth.valueOf())
      : null
    if (dateOfBirth) {
      this._data.birth_month = dateOfBirth.month()
      this._data.birth_day = dateOfBirth.date()
    }
  }

  public removeDOB() {
    this._data.date_of_birth = firebase.firestore.FieldValue.delete()
    this._data.birth_month = firebase.firestore.FieldValue.delete()
    this._data.birth_day = firebase.firestore.FieldValue.delete()
  }

  get birthMonth(): number {
    return this._data.birth_month
  }

  set birthMonth(birthMonth: number) {
    this._data.birth_month = birthMonth
  }

  get birthDay(): number {
    return this._data.birth_day
  }

  set birthDay(birthDay: number) {
    this._data.birth_day = birthDay
  }

  get gender(): string {
    return this._data.gender
  }

  set gender(gender: string) {
    this._data.gender = gender
  }

  get googleData(): { [key: string]: any } {
    if (!this._data['google_data']) {
      this._data['google_data'] = {};
    }
    return this._data['google_data'];
  }

  set googleData(googleData: { [key: string]: any }) {
    this._data['google_data'] = googleData;
  }


  get ssn(): string {
    return this._data.ssn
  }

  set ssn(ssn: string) {
    this._data.ssn = ssn
  }

  get website(): string {
    return this._data.website
  }

  set website(website: string) {
    this._data.website = website
  }

  get dnd(): boolean {
    return this._data['dnd'] || false
  }

  set dnd(dnd: boolean) {
    this._data['dnd'] = dnd
    if (dnd) this._data['dnd_date'] = firebase.firestore.Timestamp.now();
    else this._data['dnd_date'] = null
  }

  get dndDate(): moment.Moment {
    return this._data['dnd_date']
      ? typeof this._data['dnd_date'] === 'number'
        ? moment(this._data['dnd_date'])
        : moment(this._data['dnd_date'].toMillis())
      : undefined
  }

  get assignedTo(): string {
    return this._data.assigned_to
  }

  set assignedTo(assigned_to: string) {
    this._data.assigned_to = assigned_to
  }

  get assignedToDisplay(): string {
    return Utils.getUserName(this.assignedTo);
  }

  get importId(): string {
    return this._data['import_id']
  }

  set importId(importId: string) {
    this._data['import_id'] = importId
  }

  get lastConversationId(): string {
    return this._data.last_conversation_id;
  }

  set lastConversationId(conversationId: string) {
    this._data.last_conversation_id = conversationId;
  }

  get lastConversationMessageType(): MessageType {
    return this._data.last_conversation_message_type;
  }

  set lastConversationMessageType(messageType: MessageType) {
    this._data.last_conversation_message_type = messageType;
  }

  get lastActivity(): moment.Moment {
    return ModelUtils.getDateOnRaw(this._data, 'last_activity');
  }

  set lastActivity(lastActivity: moment.Moment) {
    ModelUtils.setDateOnRaw(this._data, 'last_activity', lastActivity);
  }

  get lastAppointment(): moment.Moment {
    return ModelUtils.getDateOnRaw(this._data, 'last_appointment');
  }

  set lastAppointment(lastAppointment: moment.Moment) {
    ModelUtils.setDateOnRaw(this._data, 'last_appointment', lastAppointment);
  }

  get lastNote(): string {
    return this._data.last_note || this._data.lastNote;
  }

  set lastNote(lastNote: string) {
    this._data.last_note = lastNote;
  }

  get attachments(): string[] {
    if (!this._data.attachments) {
      this._data.attachments = []
    }
    return this._data.attachments
  }

  set attachments(attachments: string[]) {
    this._data.attachments = attachments
  }

  public remove_attachment(url) {
    lodash.remove(this._data.attachments, url)
  }

  get fingerprint(): string {
    return this._data['fingerprint']
  }
  set fingerprint(fingerprint: string) {
    this._data['fingerprint'] = fingerprint
  }

  get validEmail(): boolean {
    return this._data.valid_email;
  }

  set validEmail(validEmail: boolean) {
    this._data.valid_email = validEmail;
  }

  get bounceEmail(): boolean {
    return this._data['bounce_email'] || false;
  }

  set bounceEmail(bounceEmail: boolean) {
    this._data['bounce_email'] = bounceEmail;
  }

  get unsubscribeEmail(): boolean {
    return this._data['unsubscribe_email'] || false;
  }

  set unsubscribeEmail(bounceEmail: boolean) {
    this._data['unsubscribe_email'] = bounceEmail;
  }

  get instagramId(): string {
    return this._data.instagram_id
  }

  set instagramId(instagramId: string) {
    this._data.instagram_id = instagramId;
  }

  set address(address: Address) {
    this.address1 = address.address1
    this.city = address.city
    this.state = address.state
    this.country = address.country
    this.postalCode = address.postalCode
    this.addressGeo = address.addressGeo
  }

  get address() {
    return new Address({
      address1: this.address1,
      city: this.city,
      state: this.state,
      country: this.country,
      postalCode: this.postalCode,
      addressGeo: this.addressGeo
    })
  }

  get fullAddressLine(): string {
    let address: string = '';
    const addr1 = this.address1 || this.data.address_1; /* backward compatibility with what it is called in es index  */
    if (addr1) {
      address += addr1 + ', ';
    }
    if (this.city) {
      address += this.city + ' ';
    }
    if (this.state) {
      address += this.state + ' ';
    }
    if (this.postalCode) {
      address += this.postalCode;
    }
    return address;
  }

  get addressLine1(): string { // in elastic search it is stored as address_1 while in firestore it is stored as address1
    if (!this._data) return '';
    return this._data.address_1 || this.address1;
  }

  get fullName(): string {
    return Contact.toTitleCase(
      [this.firstName, this.lastName].filter(val => val).join(' ')
    )
  }

  set fullName(name: string) {
    if (name) {
      const parts = name.match(/\S+/g) || []
      if (parts.length >= 1) {
        this.firstName = parts[0]
      }
      if (parts.length >= 2) {
        parts.splice(0, 1)
        this.lastName = parts.join(' ')
      } else {
        this.lastName = ''
      }
    }
  }

  get displayName() {
    if (this.firstName || this.lastName)
      return Contact.toTitleCase(
        [this.firstName, this.lastName].filter(d => d).join(' ')
      )
    if (this.email) return this.email
    if (this.phone) {
      try {
        return phoneUtil.format(
          phoneUtil.parse(this.phone),
          libphonenumber.PhoneNumberFormat.NATIONAL
        )
      } catch (err) { }
    }
    if (this.companyName) return this.companyName
    return ''
  }

  public static toTitleCase(str: string) {
    if (str) {
      var words = [];
      str.split(' ').map((value) => {
        if (value.indexOf('-') > -1) {
          var chunkstr = value.split('-').map((val) => {
            return val.slice(0, 1).toUpperCase() + val.slice(1);
          });
          words.push(chunkstr.join('-'));
        } else {
          words.push(value.slice(0, 1).toUpperCase() + value.slice(1));
        }
      });
      return words.join(' ')
    }
    return str
  }

  get initials(): string {
    if (!this.displayName) return ''
    const parts = this.displayName.split(' ')
    let initials = parts[0].substring(0, 1).toUpperCase()

    if (parts.length > 1) {
      initials += parts[parts.length - 1].substring(0, 1).toUpperCase()
    }
    return initials
  }

  // get profileColor(): string {
  // 	let str = this.fullName;
  // 	if (this.phone) str += this.phone;
  // 	if (this.email) str += this.email;
  // 	var hash = 0;
  // 	if (str.length == 0) return '#afb8bc';
  // 	for (var i = 0; i < str.length; i++) {
  // 		hash = str.charCodeAt(i) + ((hash << 5) - hash);
  // 		hash = hash & hash; // Convert to 32bit integer
  // 	}
  // 	var colourIndex = Math.abs(hash) % 10;
  // 	return Contact.colours[colourIndex];
  // }

  get profileColor() {
    let str = this.fullName
    if (this.phone) str += this.phone
    if (this.email) str += this.email
    var hash = 0
    if (str.length == 0) return hash
    for (var i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash)
      hash = hash & hash // Convert to 32bit integer
    }
    var shortened = Math.abs(hash) % 360
    return 'hsl(' + shortened + ',35%, 60%)'
  }

  // get profileColor() {
  //     var hash = 0;
  //     for (var i = 0; i < this.fullName.length; i++) {
  //         hash = this.fullName.charCodeAt(i) + ((hash << 5) - hash);
  //     }
  //     var colour = '#';
  //     for (var i = 0; i < 3; i++) {
  //         var value = (hash >> (i * 8)) & 0xff;
  //         colour += ('00' + value.toString(16)).substr(-2);
  //     }
  //     return colour;
  // }

  get profilePhoto(): string {
    return this._data.profile_photo
  }

  set profilePhoto(profilePhoto: string) {
    this._data.profile_photo = profilePhoto
  }

  public async deleteHipaaContact() {
    return Contact.collectionRef()
      .doc(this.id)
      .delete()
  }

  get clio(): {} {
    return this._data.clio
  }

  set clio(clio: {}) {
    this._data.clio = clio
  }

  get clioId(): string {
    if (this._data['clio'] && this._data['clio']['id']) {
      return this._data['clio']['id']
    }
    return ''
  }

  set clioId(id: string) {
    const obj = this._data['clio'] || {}
    obj['id'] = id
    this._data['clio'] = obj
  }

  public clone() {
    const contact = new Contact()
    contact._data = this._data
    contact._id = this._id
    contact._ref = this._ref
    contact._snapshot = this._snapshot
    return contact
  }

  get facebookId(): string {
    if (this._data['facebook'] && this._data['facebook']['id']) {
      return this._data['facebook']['id']
    }
    return ''
  }

  set facebookId(id: string) {
    const obj = this._data['facebook'] || {}
    obj['id'] = id
    this._data['facebook'] = obj
  }


  get poolId(): string {
    return this._data.pool_id;
  }

  set poolId(id: string) {
    this._data.pool_id = id;
  }

  get gclid(): string {
    return this._data.gclid;
  }

  set gclid(gclid: string) {
    this._data.gclid = gclid;
  }

  get sourceType(): string {
    return this._data.source_type;
  }

  set sourceType(type: string) {
    this._data.source_type = type;
  }

  get keyword(): string {
    return this._data.keyword;
  }

  set keyword(keyword: string) {
    this._data.keyword = keyword;
  }

  get opportunities(): Array<IContactOpportunity> {
    if (!this._data.opportunities || !(this._data.opportunities instanceof Array)) this._data.opportunities = [];
    return this._data.opportunities;
  }

  set opportunities(opportunities: Array<IContactOpportunity>) {
    this._data.opportunities = opportunities;
  }

  get offers(): string[] {
    if (!this._data.offers) this._data.offers = [];
    return this._data.offers;
  }

  set offers(offers: string[]) {
    this._data.offers = offers;
  }

  get products(): string[] {
    if (!this._data.products) this._data.products = [];
    return this._data.products;
  }

  set products(products: string[]) {
    this._data.products = products;
  }

  set sessionId(sessionId: string) {
    this._data.sessionId = sessionId
  }

  get sessionId(): string {
    return this._data.sessionId
  }

  get phoneInfo(): PhoneInfo {
    return this._data.phone_info
  }

  set phoneInfo(phoneInfo: PhoneInfo) {
    this._data.phone_info = phoneInfo
  }

  get dialogflowBotInfo(): DialogFlowBotInfo {
    return this._data.dialogflow_bot_info
  }

  set dialogflowBotInfo(dialogflowBotInfo: DialogFlowBotInfo) {
    this._data.dialogflow_bot_info = dialogflowBotInfo
  }

  public incrementSkipTrigger() {
    this._data.skip_trigger = firebase.firestore.FieldValue.increment(1)
  }

  public async saveUsingAPI() {
    const contact_data = this.data;
    contact_data.id = this.id; // this is required
    return await axios.post(`${config.baseUrl}/v2/elasticsearch/addcontact`, { location_id: this.locationId, contact_data });
  }

  public save(params = {
    skipTrigger: false
  }): Promise<Contact> {
    const whiteListStandardFields = [
      'email',
      'phone',
      'firstName',
      'lastName',
      'dateOfBirth',
      'address1',
      'city',
      'state',
      'country',
      'postalCode',
      'companyName',
      'website',
      'tags',
      'source',
      'customFields',
      'type',
      'timezone',
      'dnd',
      'sessionId',
      'facebookId',
      'fingerprint',
      'assignedTo',
      'profilePhoto',
      'attributionSource',
      'dirty',
      'dialogflowBotInfo',
      'parent',
      'billTo',
      'googleData',
      'ssn',
      'importId',
      'attachments',
      'validEmail',
      'bounceEmail',
      'unsubscribeEmail',
      'instagramId',
      'poolId',
      'gclid',
      'sourceType',
      'keyword',
      'offers',
      'products',
      'phoneInfo',
      'lastAttributionSource'
    ]

    const whiteListCreateFields = ['internalSource', 'locationId']

    this.dirty = true
    const oldEmail = this._oldData.email
    this.syncCustomFieldsIfRequired()
    const data = lodash.transform(this.data, (result, val, key) => {
      const newKey = lodash.camelCase(key)
      if (
        whiteListStandardFields.includes(newKey) || (this.newRecord && whiteListCreateFields.includes(newKey))
      ) {
        if (val === "") result[newKey] = null
        else result[newKey] = val
      }

      return result

    }, {})

    data['skipTrigger'] = params.skipTrigger

    const succededProcedure = async () => {
      const sendForEmailValdiation = oldEmail !== this.email;
      if (sendForEmailValdiation) {
        axios
          .get(`/contact/validate_email/${this.id}`) // Perform email validation on lead save
          .catch(function (error) {
            console.error(error)
          })
      }

      await vuex.dispatch('oauth2/syncAll')
      if (this.type !== 'lead' && vuex.getters['oauth2/updateContact']) {
        axios
          .get('/contact/update_contact', {
            params: {
              contact_id: this.id
            }
          })
          .catch(function (error) {
            console.error(error)
          })
      }
    }

    if (data['dateOfBirth']) {
      data['dateOfBirth'] = this.dateOfBirth
    }

    if (this.newRecord) {
      return ContactService.create(data)
      .then(response => {
        this._id = response.contact.id
        this.newRecord = false
        succededProcedure()
        return this
      })
      .catch(err => {
        return err.response?.data?.message
      })
    } else {
      return ContactService.update(this.id, data)
      .then(response => {
        succededProcedure()
        return this
      })
      .catch(err => {
        return err.response?.data?.message
      })
    }

  }

  public getDisplayOpportunities() {
    return Contact.formatOpportunities(this.opportunities);
  }

  get activeWorkflows(): string[] {
    return [...(this._data.active_workflows|| []), ...(this._data.active_workflows_2||[])];
  }

  get finishedWorkflows(): string[] {
    return [...(this._data.finished_workflows|| []), ...(this._data.finished_workflows_2||[])];
  }

  public get finishedWorkflowsDisplay() {
    const items = [];
    try {
      const master = store && store.state && store.state.workflows && store.state.workflows.workflows ?
      store.state.workflows.workflows.map(a=> ({key: a.id, value: a.name})) : [];
      if (!master) return [...this.finishedWorkflows];
      this.finishedWorkflows.forEach(a => {
        let idx = master.findIndex(x => x.key === a);
        if (idx !== -1) items.push(master[idx].value);
      });
    } catch(err){
      console.log(err)
    } finally{
      return items;
    }
  }

  public get activeWorkflowsDisplay() {
    const items = [];
    try {
      const master = store && store.state && store.state.workflows && store.state.workflows.workflows ?
      store.state.workflows.workflows.map(a=> ({key: a.id, value: a.name})) : [];

      if (!master) return [...this.activeWorkflows];
      this.activeWorkflows.forEach(a => {
        let idx = master.findIndex(x => x.key === a);
        if (idx !== -1) items.push(master[idx].value);
      });
    } catch(err){
      console.log(err)
    } finally{
      return items;
    }
  }

  public get offersDisplay() {
    // console.log('getDisplayOffers called')
    if (!this.offers || this.offers.length <= 0) return [];

    let master = store.getters['membership/getOffers'];
    if (!master || master.length <= 0) return [...this.offers];
    // console.log(this.offers)
    let items = [];
    this.offers.forEach(a => {
      let idx = master.findIndex(x => x.key === a);
      if (idx !== -1) items.push(master[idx].value);
      //else items.push(a);
    });

    return items;
  }

  public get productsDisplay() {
    // console.log('getDisplayProducts called')
    if (!this.products || this.products.length <= 0) return [];

    let master = store.getters['membership/getProducts'];
    if (!master || master.length <= 0) return [...this.products];
    // console.log(this.products)
    let items = [];
    this.products.forEach(a => {
      let idx = master.findIndex(x => x.key === a);
      if (idx !== -1) items.push(master[idx].value);
      //else items.push(a);
    })

    return items;
  }

  public static createNewWithVuex(vuexData) {
    return vuexData ? new Contact(lodash.cloneDeep(vuexData)) : undefined
  }

  public static formatOpportunities(opportunities: IContactOpportunity[]): IFormattedOpportunity[] {
    if (!opportunities || !opportunities.length) return [];
    const items = [];
    opportunities.forEach(x => {
      const pipeline = store.getters['pipelines/getById'](x.pipeline_id);
      const stage = pipeline && pipeline.stages ? pipeline.stages.find(a => a.id === x.pipeline_stage_id) : null;
      if (pipeline) items.push({ ...x, pipeline: pipeline.name, stage: stage ? stage.name : null })
    })
    return items;
  }

  public get isNewRecord(): boolean {
    return this.newRecord
  }

  get attributionSource(): { [key: string]: any } {
    return this._data.attribution_source;
  }

  set attributionSource(attributionSource: { [key: string]: any }) {
    this._data.attribution_source = attributionSource;
  }

  get lastAttributionSource(): { [key: string]: any } {
    return this._data.last_attribution_source;
  }

  set lastAttributionSource(attributionSource: { [key: string]: any }) {
    this._data.last_attribution_source = attributionSource;
  }
}
