import firebase from 'firebase/app';
import moment from 'moment-timezone';
import Activity, { ActionType, ActivityType } from './activity';

export default class Note {
  public static collectionRef() {
    return firebase.firestore().collection('notes');
  }

  public static getAllConversationsRealtime(locationId: string): firebase.firestore.Query {
    return Note.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('date_added', 'desc');
  }

  public static getById(id: string): Promise<Note> {
    return new Promise((resolve, reject) => {
      Note.collectionRef()
        .doc(id)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          resolve(new Note(snapshot));
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  public static getNotesByAccountId(accountId: string): firebase.firestore.Query {
    return Note.collectionRef()
      .where('deleted', '==', false)
      .where('account_id', '==', accountId)
      .orderBy('date_added', 'desc');
  }

  public static getNotesByContactId(contactId: string, locationId: string): firebase.firestore.Query {
    return Note.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('contact_id', '==', contactId)
      .orderBy('date_added', 'desc');
  }

  public static async getByLocationId(locationId: string): Promise<Note[]> {
    return new Promise<Note[]>(async (resolve, reject) => {
      (await Note.getNotesByAccountId(locationId))
        .get()
        .then((querySnapshot) => {
          resolve(querySnapshot.docs.map((d) => new Note(d)));
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  public static async getByContactId(contactId: string, locationId: string): Promise<Note[]> {
    return new Promise<Note[]>(async (resolve, reject) => {
      (await Note.getNotesByContactId(contactId, locationId))
        .get()
        .then((querySnapshot) => {
          resolve(querySnapshot.docs.map((d) => new Note(d)));
        })
        .catch((err) => {
          reject(err);
        });
    });
  }

  public static async add(params: { companyId?: string; locationId?: string; userId: string; body: string; contactId?: string; accountId?: string }) {
    const note = new Note();
    if (params.companyId) note.companyId = params.companyId;
    if (params.locationId) note.locationId = params.locationId;
    if (params.accountId) note.accountId = params.accountId;
    if (params.contactId) note.contactId = params.contactId;
    note.userId = params.userId;
    note.body = params.body;
    note.dateUpdated = moment().utc();
    note.dateAdded = moment().utc();

    const activity = new Activity();
    if (params.companyId) activity.companyId = params.companyId;
    if (params.locationId) activity.locationId = params.locationId;
    if (params.accountId) activity.accountId = params.accountId;
    if (params.contactId) activity.contactId = params.contactId;
    activity.userId = params.userId;
    activity.type = ActivityType.NOTE;
    activity.actionType = ActionType.ADDED;
    activity.activityData = {
      body: params.body,
    };
    activity.dateUpdated = moment().utc();

    const batch = firebase.firestore().batch();
    batch.set(note.ref, note.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }

  private _data: firebase.firestore.DocumentData;
  private _id: string;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._data = snapshot.data() || {};
      this._ref = snapshot.ref;
    } else {
      this._ref = Note.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment().utc();
    }
  }

  get id(): string {
    return this._id;
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref;
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toDate());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromDate(dateAdded.toDate());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toDate());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromDate(dateUpdated.toDate());
  }

  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get locationId(): string {
    return this._data.location_id;
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId;
  }

  get accountId(): string {
    return this._data.account_id;
  }

  set accountId(accountId: string) {
    this._data.account_id = accountId;
  }

  get contactId(): string {
    return this._data.contact_id;
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId;
  }

  get userId(): string {
    return this._data.user_id;
  }

  set userId(userId: string) {
    this._data.user_id = userId;
  }

  get body(): string {
    return this._data.body;
  }

  set body(body: string) {
    this._data.body = body;
  }

  public async update(userId: string) {
    const activity = new Activity();
    if (this.companyId) activity.companyId = this.companyId;
    if (this.locationId) activity.locationId = this.locationId;
    if (this.accountId) activity.accountId = this.accountId;
    if (this.contactId) activity.contactId = this.contactId;
    activity.userId = userId;
    activity.type = ActivityType.NOTE;
    activity.actionType = ActionType.UPDATED;
    activity.activityData = {
      body: this.body,
    };
    activity.dateUpdated = moment();

    const batch = firebase.firestore().batch();
    batch.set(this.ref, this.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }

  public save(): Promise<Note> {
    const _self = this;
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc();
      _self._ref.set(this.data).then(() => {
        resolve(_self);
      });
    });
  }

  public async delete(userId: string) {
    this.deleted = true;

    const activity = new Activity();
    if (this.companyId) activity.companyId = this.companyId;
    if (this.locationId) activity.locationId = this.locationId;
    if (this.accountId) activity.accountId = this.accountId;
    if (this.contactId) activity.contactId = this.contactId;
    activity.userId = userId;
    activity.type = ActivityType.NOTE;
    activity.actionType = ActionType.DELETED;
    activity.activityData = {
      body: this.body,
    };
    activity.dateUpdated = moment();

    const batch = firebase.firestore().batch();
    batch.set(this.ref, this.data);
    batch.set(activity.ref, activity.data);
    await batch.commit();
  }
}
