import firebase from 'firebase/app'
import * as moment from 'moment';
import * as lodash from 'lodash';

export default class GMBIntegration {

  private _id: string;
  private _data: firebase.firestore.DocumentData;
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data();
    } else {
      this._ref = GMBIntegration.collectionRef().doc();
      this._id = this._ref.id;
      this._data = {};
      this.dateAdded = moment();
    }
  }

  public save() {
    const _self = this;
    return new Promise(async(resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc();
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self);
          })
          .catch(err => {
            console.error(err);
            reject(err);
          });
      } catch (ex) {
        console.error(ex);
        reject(ex);
      }
    });
  }

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('gmb_integration');
  }

  get id(): string {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get ref() {
    return this._ref;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis());
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis());
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    );
  }

  get altId(): string {
    return this._data.alt_id;
  }

  set altId(alt_id: string) {
    this._data.alt_id = alt_id;
  }

  get pageName(): string {
    return this._data.page_name;
  }

  set pageName(page_name: string) {
    this._data.page_name = page_name;
  }

  get placesId(): string {
    return this._data.places_id;
  }

  set placesId(places_id: string) {
    this._data.places_id = places_id;
  }

  get displayName(): string {
    return this._data.display_name;
  }

  set displayName(display_name: string) {
    this._data.display_name = display_name;
  }

  get contactName(): string {
    return this._data.contact_name;
  }

  set contactName(contact_name: string) {
    this._data.contact_name = contact_name;
  }

  get contactEmail(): string {
    return this._data.contact_email;
  }

  set contactEmail(contact_email: string) {
    this._data.contact_email = contact_email;
  }

  get website(): string {
    return this._data.website;
  }

  set website(website: string) {
    this._data.website = website;
  }

  get welcomeMessage(): string {
    return this._data.welcome_message;
  }

  set welcomeMessage(welcome_message: string) {
    this._data.welcome_message = welcome_message;
  }

  get logo(): string {
    return this._data.logo;
  }

  set logo(logo: string) {
    this._data.logo = logo;
  }

  get privacyPolicyUrl(): string {
    return this._data.privacy_policy_url;
  }

  set privacyPolicyUrl(privacy_policy_url: string) {
    this._data.privacy_policy_url = privacy_policy_url;
  }

  get brandName(): string {
    return this._data.brand_name;
  }

  set brandName(brand_name: string) {
    this._data.brand_name = brand_name;
  }

  get agentName(): string {
    return this._data.agent_name;
  }

  set agentName(agent_name: string) {
    this._data.agent_name = agent_name;
  }

  get locationName(): string {
    return this._data.location_name;
  }

  set locationName(location_name: string) {
    this._data.location_name = location_name;
  }

  get messagingStatus(): string {
    return this._data.messaging_status;
  }

  set messagingStatus(messaging_status: string) {
    this._data.messaging_status = messaging_status;
  }

  get callTrackingStatus(): string {
    return this._data.call_tracking_status;
  }

  set callTrackingStatus(call_tracking_status: string) {
    this._data.call_tracking_status = call_tracking_status;
  }

  get callTrackingNumber(): string {
    return this._data.call_tracking_number;
  }

  set callTrackingNumber(call_tracking_number: string) {
    this._data.call_tracking_number = call_tracking_number;
  }

  get locations(): {} {
    if (!this._data.locations) {
      this._data.locations = {};
    }
    return this._data.locations;
  }

  set locations(locations: {}) {
    this._data.locations = locations;
  }

  public removeLocation(location_id) {
    let existing = lodash.has(this.locations, location_id);
    if (existing) {
      delete this.locations[location_id];
    }
  }

  public addLocation(location_id) {
    this.locations[location_id] = true;
  }

  public static getByLocationId(locationId: string): Promise<GMBIntegration> {
    return new Promise(async (resolve, reject) => {
      try {
        await GMBIntegration.collectionRef()
          .where('locations.' + locationId, '==', true)
          .get()
          .then(snapshot => {
            if (snapshot.docs.length > 1) {
              console.log('Got multiple GMB integrations');
              return resolve();
            } else if (snapshot.docs.length === 1) {
              return resolve(new GMBIntegration(snapshot.docs[0]));
            }
            return resolve();
          })
          .catch(err => {
            console.error(err);
            reject(err);
          });
      } catch (ex) {
        console.error(ex);
        reject(ex);
      }
    });
  }

  public static getByLocationIdRealtime(locationId: string) {
    return GMBIntegration.collectionRef()
			.where('locations.' + locationId, '==', true)
			.limit(1);
  }

  public static getByPageId(pageId: string): Promise<GMBIntegration> {
    return new Promise(async (resolve, reject) => {
      try {
        await GMBIntegration.collectionRef()
          .where('alt_id', '==', pageId)
          .get()
          .then(snapshot => {
            if (snapshot.docs.length > 1) {
              console.info('Got multiple GMB integrations');
              return resolve();
            } else if (snapshot.docs.length === 1) {
              return resolve(new GMBIntegration(snapshot.docs[0]));
            }
            return resolve();
          })
          .catch(err => {
            console.error(err);
            reject(err);
          });
      } catch (ex) {
        console.error(ex);
        reject(ex);
      }
    });
  }
}
