import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { pickBy } from 'lodash'

export enum FunnelType {
  Funnel = 'funnel',
  Website = 'website',
}

export interface FunnelProduct {
  productName: string
  id: string
}

export interface SplitTestRoute {
  domain: string,
  path: string,
  lookup_id: string
}
export interface FunnelStep {
  id: string
  name: string
  pages: string[]
  sequence: number
  url: string
  type: string
  products?: FunnelProduct[],
  split: boolean,
  control_traffic: number,
  split_started_at: firebase.firestore.Timestamp,
  split_ended_at: firebase.firestore.Timestamp,
  route_all_requests: boolean,
  additional_routes: SplitTestRoute[]
}

export default class Funnel {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('funnels')
  }

  public static getById(id: string): Promise<Funnel> {
    return new Promise((resolve, reject) => {
      try {
        Funnel.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new Funnel(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }
  public static getFunnelById(
    id: string
  ): firebase.firestore.DocumentReference {
    return Funnel.collectionRef().doc(id)
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    const init_data = {
      deleted: false,
      steps: [],
      live_mode: false
    }

    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || { ...init_data }
    } else {
      this._ref = Funnel.collectionRef().doc()
      this._data = { ...init_data }
      this._id = this._ref.id
      this.dateAdded = moment().utc()
    }
  }

  public save() {
    return new Promise((resolve, reject) => {
      try {
        this.dateUpdated = moment().utc()
        this._ref
          .set(this._data, { merge: true })
          .then(_ => {
            resolve(this)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getAllByLocationRealtime(
    locationId: string
  ): firebase.firestore.Query {
    return Funnel.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('date_updated', 'desc')
  }

  public static getAllByLocation(locationId: string): Promise<Funnel[]> {
    return new Promise((resolve, reject) => {
      Funnel.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Funnel(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByDomain(
    domainId: string,
    locationId: string
  ): Promise<Funnel[]> {
    return new Promise((resolve, reject) => {
      Funnel.collectionRef()
        .where('deleted', '==', false)
        .where('domain_id', '==', domainId)
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Funnel(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByLocationAndUrl(
    locationId: string,
    url: string
  ): Promise<Funnel[]> {
    return new Promise((resolve, reject) => {
      Funnel.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .where('url', '==', url)
        .orderBy('date_updated', 'desc')
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(f => new Funnel(f)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public cloneData(funnel: Funnel) {
    this._data = funnel.data
  }

  public getStepById(stepId: string): FunnelStep {
    const step = this._data.steps.find(s => s.id === stepId)
    return step
  }

  public updateStep(step: FunnelStep) {
    const stepPos = this._data.steps.findIndex(s => s.id === step.id)
    if (stepPos > -1) {
      const updatedSteps = [
        ...this._data.steps.slice(0, stepPos),
        step,
        ...this._data.steps.slice(stepPos + 1),
      ]
      this._data.steps = updatedSteps
    }
  }

  public addNewStep(funnelStep: FunnelStep) {
    funnelStep.split = false;
    funnelStep.control_traffic = 100;
    this.steps.push(funnelStep)
  }

  public removeStep(funnelStepId: string): boolean {
    const funnelSteps = this._data.steps
    const stepIndex = funnelSteps.findIndex(s => s.id === funnelStepId)
    if (stepIndex > -1) {
      const afterRemovingStep = [
        ...funnelSteps.slice(0, stepIndex),
        ...funnelSteps.slice(stepIndex + 1),
      ]

      // Reduce the sequence of each step after the removed step
      for (let i = stepIndex; i < afterRemovingStep.length; i++) {
        const step = afterRemovingStep[i]
        step.sequence = step.sequence - 1
      }

      this._data.steps = afterRemovingStep
      return true
    }
    return false
  }

  public getFirstStep(): FunnelStep {
    if (this.noOfSteps() > 0) {
      return this._data.steps[0]
    }
    return null
  }

  public noOfSteps(): number {
    return this._data.steps.length
  }

  public getNextStepBySequence(stepSequence: number): FunnelStep {
    const nextStepSequence = stepSequence + 1
    const nextStep = this._data.steps.find(s => s.sequence === nextStepSequence)
    return nextStep
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }

  get steps(): FunnelStep[] {
    return this._data.steps
  }

  set steps(steps: FunnelStep[]) {
    this._data.steps = steps
  }

  get locationId() {
    return this._data.location_id
  }

  set locationId(locationId) {
    this._data.location_id = locationId
  }

  get favicon(): string {
    return this._data.favicon_url
  }

  set favicon(url: string) {
    this._data.favicon_url = url
  }

  get trackingCodeHead(): string {
    return this._data.tracking_code_head
  }

  set trackingCodeHead(code: string) {
    this._data.tracking_code_head = code
  }

  get trackingCodeBody(): string {
    return this._data.tracking_code_body
  }

  set trackingCodeBody(code) {
    this._data.tracking_code_body = code
  }

  get domainId(): string {
    return this._data.domain_id
  }

  set domainId(domainId: string) {
    this._data.domain_id = domainId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get processing(): boolean {
    return !!this._data.processing
  }

  set processing(_processing: boolean) {
    this._data.processing = _processing
  }

  get orderFormVersion(): number {
    return this._data.order_form_version
  }

  set orderFormVersion(type: number) {
    this._data.order_form_version = type
  }

  get type(): string {
    return this._data.type
  }

  set type(type: string) {
    this._data.type = type
  }


  get isLivePaymentMode(): boolean {
    return this._data.is_live_payment_mode
  }

  set isLivePaymentMode(mode: boolean) {
    this._data.is_live_payment_mode = mode
  }

  get ischatWidgetLive(): boolean{
    return this._data.is_chat_widget_live
  }

  set ischatWidgetLive(mode: boolean){
    this._data.is_chat_widget_live = mode
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }
}
