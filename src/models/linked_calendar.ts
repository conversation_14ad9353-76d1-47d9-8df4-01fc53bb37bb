import firebase from 'firebase/app'
import * as lodash from 'lodash'
import axios from 'axios'
import * as moment from 'moment-timezone'

export interface GoogleCalendar {
  id: string
  name: string
  watch_id: string
  resource_id: string
  expiration: number
  user_id: string
  last_sync: string
  error?: number
  error_message?: string
  oauth_id?: string
}

export interface OutlookCalendar {
  id: string
  name: string
  watch_id: string
  resource_id: string
  expiration: number
  user_id: string
  last_sync: string
  error?: number
  error_message?: string
  oauth_id?: string
}

export interface DrChronoCalendar {
  id: string
  name: string
  sync_all_doctors?: boolean
  doctor_id: string
  last_sync?: string
  exam_room?: number
}

export interface ClioCalendar {
  id: string
  name: string
}

export enum LinkedCalendarType {
  GoogleCalendar = 'Google',
  DrChronoCalendar = 'DrChrono',
  ClioCalendar = 'Clio',
  OutlookCalendar = 'Outlook'
}

export default class LinkedCalendar {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('linked_calendars_v3')
  }

  public static getById(id): Promise<LinkedCalendar> {
    return new Promise(async (resolve, reject) => {
      try {
        await LinkedCalendar.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            if (!snapshot.exists) resolve()
            resolve(new LinkedCalendar(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getByLinkedCalendarOriginId(
    linkedCalendarType: LinkedCalendarType,
    linkedCalendarOriginId: string,
    userId: string
  ): Promise<LinkedCalendar> {
    let typeText = ''
    switch (linkedCalendarType) {
      case LinkedCalendarType.GoogleCalendar:
        typeText = 'google'
        break
    case LinkedCalendarType.OutlookCalendar:
        typeText = 'outlook'
        break
      case LinkedCalendarType.DrChronoCalendar:
        typeText = 'drchrono'
        break
      case LinkedCalendarType.ClioCalendar:
        typeText = 'clio'
        break
      default:
        typeText = 'google'
        break
    }
    return new Promise(async (resolve, reject) => {
      try {
        await LinkedCalendar.collectionRef()
          .where(typeText + '.id', '==', linkedCalendarOriginId)
          // .where(typeText + '.user_id', '==', userId)
          .where('deleted', '==', false)
          .get()
          .then(snapshot => {
            if (snapshot.empty) resolve()
            return resolve(new LinkedCalendar(snapshot.docs[0]))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  public static getByOAuthId(OAuthId: string): Promise<LinkedCalendar[]> {
    return new Promise(async (resolve, reject) => {
      try {
        await LinkedCalendar.collectionRef()
          .where('google.oauth_id', '==', OAuthId)
          .where('deleted', '==', false)
          .get()
          .then(snapshot => {
            if (snapshot.empty) resolve()
            return resolve(snapshot.docs.map(d => new LinkedCalendar(d)))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }


  public static async getByUserId(
    userId: string
  ): Promise<any> {
    return new Promise<any>(async (resolve, reject) => {
      const snapshot = await LinkedCalendar.collectionRef()
        .where('google.user_id', '==', userId)
        .where('deleted', '==', false)
        .get()
      resolve(snapshot.docs.map((d) => {
        let data = d.data();
        return { id: d.id, ...data }
      }))
    })
  }

  public static getExpiredLinkedGoogleCalendars_v3(
    expEpoch: number
  ): Promise<LinkedCalendar[]> {
    return new Promise(async (resolve, reject) => {
      try {
        await LinkedCalendar.collectionRef()
          .where('google.expiration', '<=', expEpoch)
          .where('deleted', '==', false)
          .get()
          .then(snapshot => {
            resolve(snapshot.docs.map(doc => new LinkedCalendar(doc)))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = LinkedCalendar.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
      this.dateUpdated = moment()
    }
    this.clio = this.clio || <ClioCalendar>{}
    this.drchrono = this.drchrono || <DrChronoCalendar>{}
    this.google = this.google || <GoogleCalendar>{}
    this.outlook = this.outlook || <GoogleCalendar>{}

  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get name(): string {
    return this._data.name
  }

  set name(value: string) {
    this._data.name = value
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(value: boolean) {
    this._data.deleted = value
  }

  get pageToken(): string {
    return this._data.page_token
  }

  set pageToken(pageToken: string) {
    this._data.page_token = pageToken
  }

  get syncToken(): string {
    return this._data.sync_token
  }

  set syncToken(value: string) {
    this._data.sync_token = value
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(value: string) {
    this._data.origin_id = value
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added ? this._data.date_added.toMillis() : '')
  }

  set dateAdded(value: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      value.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(
      this._data.date_updated ? this._data.date_updated.toMillis() : ''
    )
  }

  set dateUpdated(value: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      value.valueOf()
    )
  }

  get linkedCalendarType(): LinkedCalendarType {
    return this._data.linked_calendar_type
  }

  set linkedCalendarType(value: LinkedCalendarType) {
    this._data.linked_calendar_type = value
  }

  get clio(): ClioCalendar {
    return this._data.clio
  }

  set clio(value: ClioCalendar) {
    this._data.clio = value
  }

  get drchrono(): DrChronoCalendar {
    return this._data.drchrono
  }

  set drchrono(value: DrChronoCalendar) {
    this._data.drchrono = value
  }

  get google(): GoogleCalendar {
    return this._data.google
  }

  set google(value: GoogleCalendar) {
    this._data.google = value
  }

  get outlook(): OutlookCalendar {
    return this._data.outlook
  }

  set outlook(value: OutlookCalendar) {
    this._data.outlook = value
  }

  get hasValidGoogleWebhookData() {
    return (
      this.linkedCalendarType === LinkedCalendarType.GoogleCalendar &&
      moment(this.google.expiration).isAfter(moment())
    )
  }

  /**
   * Set watcher to observe changes in linked calendar origin
   */
  public async addWatcher() {
    try {
      if (this.linkedCalendarType === LinkedCalendarType.GoogleCalendar) {
        await axios.get(
          '/google/linked_calendar/add_or_refresh_watcher?linked_calendar_id=' +
          this.id
        )
      }else if (this.linkedCalendarType === LinkedCalendarType.OutlookCalendar) {
        await axios.get(
          '/outlook/linked_calendar/add_or_refresh_watcher?linked_calendar_id=' +
          this.id
        )
      }
    } catch (err) { }
  }

  public async runSync() {
    try {
      if (this.linkedCalendarType === LinkedCalendarType.GoogleCalendar) {
        await axios.get(
          '/google/linked_calendar/sync?linked_calendar_id=' + this.id
        )
      }else if (this.linkedCalendarType === LinkedCalendarType.OutlookCalendar) {
        await axios.get(
          '/outlook/linked_calendar/sync?linked_calendar_id=' +
          this.id
        )
      }
    } catch (err) { }
  }

  public async save(silent?: boolean) {
    if (!silent) this.dateUpdated = moment()
    await this._ref.set(this.data)
  }
}
