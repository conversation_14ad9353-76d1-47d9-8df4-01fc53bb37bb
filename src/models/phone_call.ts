import firebase from 'firebase/app'
import 'firebase/database'
import * as moment from 'moment-timezone'

import { TwilioAccount, Contact } from '.'

export default class PhoneCall {

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('phone_calls')
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data()
    } else {
      this._ref = PhoneCall.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this.deleted = false
      this.dateAdded = moment()
    }
  }

  public save(): Promise<PhoneCall> {
    const _self = this
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment()
      _self._ref.set(_self._data, { merge: true }).then(_ => {
        resolve(_self)
      })
    })
  }

  public static getById(id) {
    return new Promise((resolve, reject) => {
      PhoneCall.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new PhoneCall(snapshot))
        })
        .catch(err => {
          console.error(err)
          reject(err)
        })
    })
  }

  public static getByCallSid(sid: string): Promise<PhoneCall> {
    return new Promise((resolve, reject) => {
      PhoneCall.collectionRef()
        .where('call_sid', '==', sid)
        .where('deleted', '==', false)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            return resolve(null)
          }
          return resolve(new PhoneCall(snapshot.docs[0]))
        })
        .catch(err => {
          console.error(JSON.stringify(err))
          reject(err)
        })
    })
  }

  public static getByCallSidRealtime(sid: string, locationId: string): firebase.firestore.Query {
    return PhoneCall.collectionRef()
      .where('call_sid', '==', sid)
      .where('location_id', '==', locationId)
      .where('deleted', '==', false)
  }

  public static fetchLatestCalls(
    locationId: string,
    dateRange: {[key: string] : string},
    assignedTo?: string,
  ): firebase.firestore.Query {
    let query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
      .orderBy('date_added', 'desc')
    if (assignedTo) query = query.where('assigned_to', '==', assignedTo)
    return query;
  }

  public static fetchAllCalls(
    locationId: string,
    option: string,
    dateRange: {[key: string] : string},
    callDirection: string,
    limit?: number
  ): firebase.firestore.Query {
    let query;
    if(option === 'all'){
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('direction', '==', callDirection)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
    }else if(option.length > 15){
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('pool_id', '==', option)
      .where('direction', '==', callDirection)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
    }else{
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
      if(callDirection == 'inbound'){
        query = query.where('to', '==', option)
      }else{
        query = query.where('from', '==', option)
      }
    }
    if (limit) {
      query = limit ? query.limit(limit): query
    }
    return query.orderBy('date_added', 'desc')
  }

  public static fetchMoreCalls(
    locationId: string,
    snapshot: any,
    option: string,
    dateRange: {[key: string] : string},
    callDirection: string,
    limit?: number
  ): firebase.firestore.Query {
    let query;
    if(option === 'all'){
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('direction', '==', callDirection)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
    }else if(option.length > 15){
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('pool_id', '==', option)
      .where('direction', '==', callDirection)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
    }else{
      query = PhoneCall.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('date_added', '>', new Date(dateRange.start))
      .where('date_added', '<', new Date(dateRange.end))
      if(callDirection == 'inbound'){
        query = query.where('to', '==', option)
      }else{
        query = query.where('from', '==', option)
      }
    }
    if (limit) {
      query = query.limit(limit)
    }
    return query.orderBy('date_added', 'desc').startAfter(snapshot)
  }

  public static deleteRecording(id: string) {
    return new Promise((resolve, reject) => {
      PhoneCall.collectionRef()
        .doc(id)
        .update({
          recording_url: ''
        })
        .then(() => resolve('SuccessFully delete.'))
        .catch(err => reject(err))
    })
  }

  public static qualifiedLead(id: string, value: boolean) {
    return new Promise((resolve, reject) => {
      PhoneCall.collectionRef()
        .doc(id)
        .set({qualified_lead: value}, { merge: true })
        .then(() => resolve('SuccessFully updated.'))
        .catch(err => reject(err))
    })
  }

  get id(): string {
    return this._id
  }

  get data(): firebase.firestore.DocumentData {
    return this._data
  }

  get companyId(): string {
    return this._data.company_id
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get contactId(): string {
    return this._data.contact_id
  }

  set contactId(contactId: string) {
    this._data.contact_id = contactId
  }

  get dateAdded(): moment.Moment {
    return moment.unix(this._data.date_added.seconds)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate()
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated)
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate()
  }

  get deleted(): boolean {
    return this._data.deleted
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get dirty(): boolean {
    return this._data.dirty
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty
  }

  get called(): string {
    return this._data.called
  }

  set called(called: string) {
    this._data.called = called
  }

  get toState(): string {
    return this._data.to_state
  }

  set toState(toState: string) {
    this._data.to_state = toState
  }

  get callerCountry(): string {
    return this._data.caller_country
  }

  set callerCountry(callerCountry: string) {
    this._data.caller_country = callerCountry
  }

  get direction(): string {
    return this._data.direction
  }

  set direction(direction: string) {
    this._data.direction = direction
  }

  get callerState(): string {
    return this._data.caller_state
  }

  set callerState(callerState: string) {
    this._data.caller_state = callerState
  }

  get toZip(): string {
    return this._data.to_zip
  }

  set toZip(toZip: string) {
    this._data.to_zip = toZip
  }

  get callSid(): string {
    return this._data.call_sid
  }

  set callSid(callSid: string) {
    this._data.call_sid = callSid
  }

  get to(): string {
    return this._data.to
  }

  set to(to: string) {
    this._data.to = to
  }

  get callerZip(): string {
    return this._data.caller_zip
  }

  set callerZip(callerZip: string) {
    this._data.caller_zip = callerZip
  }

  get toCountry(): string {
    return this._data.to_country
  }

  set toCountry(toCountry: string) {
    this._data.to_country = toCountry
  }

  get calledZip(): string {
    return this._data.called_zip
  }

  set calledZip(calledZip: string) {
    this._data.called_zip = calledZip
  }

  get calledCity(): string {
    return this._data.called_city
  }

  set calledCity(calledCity: string) {
    this._data.called_city = calledCity
  }

  get callStatus(): string {
    return this._data.call_status
  }

  set callStatus(callStatus: string) {
    this._data.call_status = callStatus
  }

  get from(): string {
    return this._data.from
  }

  set from(from: string) {
    this._data.from = from
  }

  get accountSid(): string {
    return this._data.account_sid
  }

  set accountSid(accountSid: string) {
    this._data.account_sid = accountSid
  }

  get calledCountry(): string {
    return this._data.called_country
  }

  set calledCountry(calledCountry: string) {
    this._data.called_country = calledCountry
  }

  get callerCity(): string {
    return this._data.caller_city
  }

  set callerCity(callerCity: string) {
    this._data.caller_city = callerCity
  }

  get caller(): string {
    return this._data.caller
  }

  set caller(caller: string) {
    this._data.caller = caller
  }

  get fromCountry(): string {
    return this._data.from_country
  }

  set fromCountry(fromCountry: string) {
    this._data.from_country = fromCountry
  }

  get toCity(): string {
    return this._data.to_city
  }

  set toCity(toCity: string) {
    this._data.to_city = toCity
  }

  get fromCity(): string {
    return this._data.from_city
  }

  set fromCity(fromCity: string) {
    this._data.from_city = fromCity
  }

  get calledState(): string {
    return this._data.called_state
  }

  set calledState(calledState: string) {
    this._data.called_state = calledState
  }

  get fromZip(): string {
    return this._data.from_zip
  }

  set fromZip(fromZip: string) {
    this._data.from_zip = fromZip
  }

  get fromState(): string {
    return this._data.from_state
  }

  set fromState(fromState: string) {
    this._data.from_state = fromState
  }

  get duration(): number {
    return this._data.duration
  }

  set duration(duration: number) {
    this._data.duration = duration
  }

  get recordingURL(): string {
    return this._data.recording_url
  }

  set recordingURL(recording_url: string) {
    this._data.recording_url = recording_url
  }

  get source(): string {
    return this._data.source
  }

  set source(source: string) {
    this._data.source = source
  }

  get sourceType(): string {
    return this._data.source_type
  }

  set sourceType(sourceType: string) {
    this._data.source_type = sourceType
  }

  get poolId(): string {
    return this._data.pool_id
  }

  set poolId(poolId: string) {
    this._data.pool_id = poolId
  }

  get sessionId(): string {
    return this._data.session_id
  }

  set sessionId(sessionId: string) {
    this._data.session_id = sessionId
  }

  get keyword(): string {
    return this._data.keyword
  }

  set keyword(keyword: string) {
    this._data.keyword = keyword
  }

  get isFirstTime(): boolean {
    return this._data.firstTime
  }

  set isFirstTime(firstTime: boolean) {
    this._data.firstTime = firstTime
  }

  get deviceType(): string {
    return this._data.device_type
  }

  set deviceType(type: string) {
    this._data.device_type = type
  }

  get landingPage(): string {
    return this._data.landing_page
  }

  set landingPage(url: string) {
    this._data.landing_page = url
  }

  get campaign(): string {
    return this._data.campaign;
  }

  set campaign(value: string) {
    this._data.campaign = value;
  }

  get referrer(): string {
    return this._data.referrer;
  }

  set referrer(value: string) {
    this._data.referrer = value;
  }

  static async fromJson(json, account: TwilioAccount) {
    let phoneCall = await this.getByCallSid(json['CallSid'])
    if (!phoneCall) {
      phoneCall = new PhoneCall()
      phoneCall.locationId = account.locationId
    }

    let contact = await Contact.getByPhoneNumberAndLocation(
      phoneCall.locationId,
      json['From']
    )
    if (contact) phoneCall.contactId = contact.id

    phoneCall.called = json['Called']
    phoneCall.toState = json['ToState']
    phoneCall.callerCountry = json['CallerCountry']
    phoneCall.direction = json['Direction']
    phoneCall.callerState = json['CallerState']
    phoneCall.toZip = json['ToZip']
    phoneCall.callSid = json['CallSid']
    phoneCall.to = json['To']
    phoneCall.callerZip = json['CallerZip']
    phoneCall.toCountry = json['ToCountry']
    phoneCall.calledZip = json['CalledZip']
    phoneCall.calledCity = json['CalledCity']
    phoneCall.callStatus = json['CallStatus']
    phoneCall.from = json['From']
    phoneCall.accountSid = json['AccountSid']
    phoneCall.calledCountry = json['CalledCountry']
    phoneCall.callerCity = json['CallerCity']
    phoneCall.caller = json['Caller']
    phoneCall.fromCountry = json['FromCountry']
    phoneCall.toCity = json['ToCity']
    phoneCall.fromCity = json['FromCity']
    phoneCall.calledState = json['CalledState']
    phoneCall.fromZip = json['FromZip']
    phoneCall.fromState = json['FromState']

    if (json['Duration']) {
      phoneCall.duration = Number(json['Duration'])
    }

    console.log(JSON.stringify(phoneCall.data))
    await phoneCall.save()
    return phoneCall
  }

  get groupId(): string{
    return this._data.group_id
  }

  set groupId(id: string) {
    this._data.group_id = id
  }

  get qualifiedLead(): string{
    return this._data.qualified_lead
  }

  set qualifiedLead(qualified_lead: string) {
    this._data.qualified_lead = qualified_lead
  }

  get firstCallStatus(): string {
    return this._data.first_call_status;
  }

  set firstCallStatus(value: string) {
    this._data.first_call_status = value;
  }

  get secondCallStatus(): string {
    return this._data.second_call_status;
  }

  set secondCallStatus(value: string) {
    this._data.second_call_status = value;
  }

  get agentNumber(): string {
    return this._data.agent_number;
  }

  set agentNumber(value: string) {
    this._data.agent_number = value;
  }

  get calls(): { [key: string]: any }[] {
    if (!this._data.calls || !(this._data.calls instanceof Array)) {
      this._data.calls = [];
    }
    return this._data.calls;
  }

  set assignedTo(value: string) {
    this._data.assigned_to = value;
  }

  get assignedTo(): string {
    return this._data.assigned_to;
  }

  set userId(value: string) {
    this._data.user_id = value;
  }

  get userId(): string {
    return this._data.user_id;
  }
}
