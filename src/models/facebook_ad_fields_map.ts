import firebase from 'firebase/app';

export default class FacebookAdFieldsMap {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('facebook_ad_fields_map');
	}

	public static getByLocationAndPage(locationId: string, pageId: string): Promise<FacebookAdFieldsMap[]> {
		return new Promise((resolve, reject) => {
			FacebookAdFieldsMap.collectionRef()
				.where('location_id', '==', locationId)
				.where('page_id', '==', pageId)
				.get()
				.then((snapshot) => {
					if (!snapshot) resolve();
					return resolve(snapshot.docs.map((d) => new FacebookAdFieldsMap(d)));
				})
				.catch((ex) => {
					reject(ex);
				})
		});
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;
	private _snapshot: firebase.firestore.DocumentSnapshot;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot | firebase.firestore.QueryDocumentSnapshot | { [key: string]: any }) {
		if (snapshot instanceof firebase.firestore.DocumentSnapshot || snapshot instanceof firebase.firestore.QueryDocumentSnapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data() || {};
			this._snapshot = snapshot;
		} else if (snapshot) {
			this._id = snapshot.id;
			this._ref = FacebookAdFieldsMap.collectionRef().doc(snapshot.id);
			this._data = snapshot;
		} else {
			this._ref = FacebookAdFieldsMap.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
		}
	}

	public async save() {
		await this._ref.set(this._data);
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get snapshot() {
		return this._snapshot;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get formId(): string {
		return this._data.form_id;
	}

	set formId(formId: string) {
		this._data.form_id = formId;
	}

	get pageId(): string {
		return this._data.page_id;
	}

	set pageId(pageId: string) {
		this._data.page_id = pageId;
	}

	get mappings(): { [key: string]: any } {
		return this._data.mappings;
	}

	set mappings(mappings: { [key: string]: any }) {
		this._data.mappings = mappings;
	}

	get mappingsTrackingParams(): { [key: string]: any } {
		return this._data.mappingsTrackingParams;
	}

	set mappingsTrackingParams(mappingsTrackingParams: { [key: string]: any }) {
		this._data.mappingsTrackingParams = mappingsTrackingParams;
	}

	get active(): boolean {
		return this._data.active;
	}

	set active(isActive: boolean) {
		this._data.active = isActive;
	}
}
