import firebase from 'firebase/app'
import moment from 'moment-timezone'
import { Utils } from '../util/utils'
import axios from 'axios'
import pickBy from 'lodash/pickBy'
export type CampaignStatus = 'draft' | 'published'

export type ActionCondition = 'if' | 'when' | 'wait'

export interface Window {
  condition: ActionCondition
  start: string
  end?: string
  days: number[]
}

export type MessageTemplate =
  | EmailTemplate
  | SMSTemplate
  | CallTemplate
  | VoicemailTemplate
  | IfTemplate
  | ElseTemplate

export interface CampaignData {
  lead_value?: number
  window?: Window
  users?: string[]
  templates?: CampaignTemplate<MessageTemplate>[]
  no_response?: {
    action_type?: 'campaign'
    campaign_id?: string
  }
  responded?: string[]
  from_email_address?: string
  from_name?: string
  event_time?: string
}

export interface CampaignTemplate<T> {
  id: string
  type: 'sms' | 'email' | 'call' | 'voicemail' | 'messenger'
  name: string
  window?: Window
  attributes: T
  start_after: {
    when: 'before' | 'after' | 'now'
    type: 'minutes' | 'hours' | 'days'
    value: number
    action_in?: number
  }
  next?: string
  urlAttachments: string[]
}

export interface EmailTemplate {
  attachments?: string[]
  html: string
  subject?: string
  id?: string
  urlAttachments?: string[]
  downloadUrl?: string
  name?: string
  from?: string
}

export interface SMSTemplate {
  attachments?: string[]
  body: string
  id?: string
  urlAttachments?: string[]
}

export interface CallTemplate {
  timeout?: number
  whisper_message?: string
  disable_detect_voicemail?: boolean
}

export interface VoicemailTemplate {
  file_url?: string
}
export interface IfTemplate {
  if: boolean
  segment: string // to discuss, if we goign to use segment ID or direct label here
}

export interface ElseTemplate {
  else: boolean
}

export interface WaitTemplate {
  file_url?: string
}

export default class Campaign {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('campaigns')
  }

  public static getById(id: string): Promise<Campaign> {
    return new Promise((resolve, reject) => {
      Campaign.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          if (!snapshot.exists) resolve(null)
          resolve(new Campaign(snapshot))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getQueryWithLocationId(
    locationId: string
  ): firebase.firestore.Query {
    return Campaign.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .orderBy('name')
  }

  public static getWithLocationId(locationId: string): Promise<Campaign[]> {
    return new Promise((resolve, reject) => {
      Campaign.getQueryWithLocationId(locationId)
        .get()
        .then(snapshot => {
          resolve(snapshot.docs.map(d => new Campaign(d)))
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getByLocationId(locationId: string): Promise<Campaign[]> {
    return new Promise((resolve, reject) => {
      Campaign.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', locationId)
        .orderBy('name')
        .get()
        .then(snapshot => {
          return resolve(snapshot.docs.map(d => new Campaign(d)))
        })
        .catch(err => {
          console.error(err)
          reject(err.message)
        })
    })
  }

  public static async getByOriginId(
    locationId: string,
    originId: string
  ): Promise<Campaign | undefined> {
    const snapshot = await Campaign.collectionRef()
      .where('location_id', '==', locationId)
      .where('origin_id', '==', originId)
      .where('deleted', '==', false)
      .get()
    if (!snapshot.empty) return new Campaign(snapshot.docs[0])
  }

  private _id: string
  private _data: firebase.firestore.DocumentData
  private _ref: firebase.firestore.DocumentReference

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else if (snapshot) {
      this._id = snapshot.id
      this._ref = Campaign.collectionRef().doc(snapshot.id)
      delete snapshot.id
      this._data = snapshot
    } else {
      this._ref = Campaign.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.deleted = false
      this.dateAdded = moment()
      this.status = 'draft'
      this.allowMultiple = false // Most common issue in Support is Appt Reminders firing multiple times. Set Allow Multiple to False in attempt to prevent those.
    }
  }

  get id(): string {
    return this._id
  }

  get ref() {
    return this._ref
  }

  get data(): firebase.firestore.DocumentData {
    return pickBy(this._data, (v: any) => v !== null && v !== undefined)
  }

  get folderId(): string {
    return this._data.folder_id
  }

  set folderId(folderId: string) {
    this._data.folder_id = folderId
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get originId(): string {
    return this._data.origin_id
  }

  set originId(originId: string) {
    this._data.origin_id = originId
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(
      dateAdded.valueOf()
    )
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }

  get status(): CampaignStatus {
    return this._data.status
  }

  set status(status: CampaignStatus) {
    this._data.status = status
  }

  get loopIdentified(): moment.Moment {
    if (this._data.loop_identified) {
      return moment(this._data.loop_identified.toMillis())
    } else {
      return undefined
    }
  }

  set loopIdentified(loopIdentified: moment.Moment) {
    this._data.loop_identified = firebase.firestore.Timestamp.fromMillis(
      loopIdentified.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get isWorkflow(): boolean {
    return this._data.is_workflow
  }

  set isWorkflow(isWorkflow: boolean) {
    this._data.is_workflow = isWorkflow
  }

  get campaignData(): CampaignData {
    if (!this._data.campaign_data) this._data.campaign_data = {}
    return this._data.campaign_data
  }

  set campaignData(campaignData: CampaignData) {
    this._data.campaign_data = campaignData
  }

  get tagContacts(): string[] {
    if (!this._data['tag_contacts']) return []
    return this._data['tag_contacts']
  }

  set tagContacts(tagContacts: string[]) {
    this._data['tag_contacts'] = tagContacts
  }

  get allowMultiple(): boolean {
    return this._data['allow_multiple']
  }

  set allowMultiple(allowMultiple: boolean) {
    this._data['allow_multiple'] = allowMultiple
  }

  get stopOnReply(): boolean {
    if (this._data['stop_on_reply'] === undefined)
      this._data['stop_on_reply'] = true
    return this._data['stop_on_reply']
  }

  set stopOnReply(stopOnReply: boolean) {
    this._data['stop_on_reply'] = stopOnReply
  }

  public async publish() {
    if (this.status === 'draft') {
      await axios.post(`/campaign/${this.id}/publish`)
      this.status = 'published'
      await this.save()
    }
  }

  public async draft() {
    if (this.status === 'published') {
      await axios.post(`/campaign/${this.id}/pause`)
      this.status = 'draft'
      await this.save()
    }
  }

  public save() {
    if (Utils.isEmptyStr(this.name)) {
      throw new Error('Campaign requires a name before it can be saved.')
    }
    return new Promise((resolve, reject) => {
      this.dateUpdated = moment().utc()
      this._ref.set(this.data).then(() => {
        resolve(this)
      })
    })
  }
}

export class Workflow extends Campaign {
  public static getQueryWithLocationId(
    locationId: string
  ): firebase.firestore.Query {
    return Campaign.collectionRef()
      .where('deleted', '==', false)
      .where('location_id', '==', locationId)
      .where('is_workflow', '==', true)
      .orderBy('name')
  }
}
