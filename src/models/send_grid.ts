import firebase from 'firebase/app';
import 'firebase/database';
import * as moment from 'moment-timezone';

export default class SendGridAccount {
    public static collectionRef():firebase.firestore.CollectionReference {
        return firebase.firestore().collection('sendgrid_accounts');
    }

    public save(): Promise<SendGridAccount> {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment();
            _self._ref.set(_self._data).then((_) => {
                resolve(_self);
            });
        });
    }

    public static getById(id) {
        return new Promise((resolve, reject) => {
            SendGridAccount.collectionRef()
                .doc(id)
                .get()
                .then((snapshot) => {
                    resolve(new SendGridAccount(snapshot));
                })
                .catch((err) => {
                    console.error(err);
                    reject(err);
                });
        });
    }

    public static getByLocationId(locationId: string): Promise<SendGridAccount> {
        return new Promise((resolve, reject) => {
            SendGridAccount.collectionRef()
                .where('location_id', '==', locationId)
                .get()
                .then((snapshot) => {
                    if (snapshot.docs.length === 1) {
                        resolve(new SendGridAccount(snapshot.docs[0]));
                    } else {
                        resolve();
                    }
                })
                .catch((err) => {
                    console.error(err);
                    reject(err.message);
                });
        });
    }

    public static getByCompanyId(companyId: string, locationId: string): Promise<SendGridAccount> {
        return new Promise((resolve, reject) => {
            SendGridAccount.collectionRef()
                .where('company_id', '==', companyId)
                .where('location_id', '==', locationId)
                .get()
                .then((snapshot) => {
                    if (snapshot.docs.length === 1) {
                        resolve(new SendGridAccount(snapshot.docs[0]));
                    } else {
                        resolve();
                    }
                })
                .catch((err) => {
                    console.error(err);
                    reject(err.message);
                });
        });
    }

    private _id: string;
    private _data:firebase.firestore.DocumentData;
    private _ref:firebase.firestore.DocumentReference;

    constructor(snapshot?:firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data();
        } else {
            this._ref = SendGridAccount.collectionRef().doc();
            this._data = {};
            this._id = this._ref.id;
            this.dateAdded = moment();
            this.domains = {};
            this.subdomains = {};
        }
    }

    get id(): string {
        return this._id;
    }

    get data():firebase.firestore.DocumentData {
        return this._data;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get companyId(): string {
        return this._data.company_id;
    }

    set companyId(company_id: string) {
        this._data.company_id = company_id;
    }

    get dateAdded(): moment.Moment {
        return moment(this._data.date_added);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {
        return moment(this._data.date_updated);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = dateUpdated.toDate();
    }

    get apiKey(): string {
        return this._data.api_key;
    }

    set apiKey(apiKey: string) {
        this._data.api_key = apiKey;
    }

    get domains(): { [field: string]: string } {
        if (!this._data.domains) this._data.domains = {};
        return this._data.domains;
    }

    set domains(values) {
        this._data.domains = values;
    }

    get subdomains(): { [field: string]: string } {
        if (!this._data.subdomains) this._data.subdomains = {};
        return this._data.subdomains;
    }

    set subdomains(values) {
        this._data.subdomains = values;
    }
}
