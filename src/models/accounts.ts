import firebase from 'firebase/app';
import moment from 'moment-timezone';

export default class Account {
  public static collectionRef = firebase.firestore().collection('accounts');

  public static getById(id: string) {
    return new Promise((resolve, reject) => {
      Account.collectionRef.doc(id).get().then((snapshot) => {
        resolve(new Account(snapshot));
      })
        .catch((err) => {
          reject(err);
        })
    });
  }

  private _id: string;
  private _data: { [field: string]: any };
  private _ref: firebase.firestore.DocumentReference;

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id;
      this._ref = snapshot.ref;
      this._data = snapshot.data() || {};
    } else {
      this._ref = Account.collectionRef.doc();
      this._data = {};
      this._id = this._ref.id;
      this.dateAdded = moment().utc();
    }
  }

  public save() {
    const _self = this;
    return new Promise((resolve, reject) => {
      _self.dateUpdated = moment().utc();
      _self._ref.set(_self._data, { merge: true }).then((_) => {
        resolve(_self);
      });
    });
  }

  get id(): string {
    return this._id;
  }

  get data() {
    return this._data;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added);
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate();
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated);
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate();
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
    this._data.name_lower_case = name.toLocaleLowerCase();
  }
}
