import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

export type BulkStatus = 'new' | 'queued' | 'running' | 'paused' | 'finished';
export type ContactType = 'all' | 'customers' | 'leads';

export default class BulkRequest {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('bulk_requests');
	}

	public async save() {
		this.dateUpdated = moment();
		await this._ref.set(this.data);
	}

	public static getById(id): Promise<BulkRequest> {
		return new Promise((resolve, reject) => {
			BulkRequest.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new BulkRequest(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static getWithLocationId(locationId: string): Promise<BulkRequest[]> {
		return new Promise((resolve, reject) => {
			BulkRequest
				.getWithLocationIdRealtime(locationId)
				.get()
				.then((snapshot) => {
					resolve(snapshot.docs.map((d) => new BulkRequest(d)));
				})
				.catch((err) => {
					reject(err);
				});
		});
	}

	public static getWithLocationIdRealtime(locationId: string) {
		return BulkRequest.collectionRef()
			.where('deleted', '==', false)
			.where('location_id', '==', locationId)
			.orderBy('date_updated', 'desc');
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = BulkRequest.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
			this.status = 'queued';
			this.deleted = false;
			this.tags = [];
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): { [key: string]: any } {
		return lodash.pickBy(this._data, (v) => v !== null && v !== undefined);
	}

	get ref(): firebase.firestore.DocumentReference {
		return this._ref;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get dateUpdated(): moment.Moment {
		return moment(this._data.date_updated.toMillis());
	}

	set dateUpdated(dateUpdated: moment.Moment) {
		this._data.date_updated = firebase.firestore.Timestamp.fromMillis(dateUpdated.valueOf());
	}

	get deleted() {
		return this._data.deleted;
	}

	set deleted(deleted) {
		this._data.deleted = deleted;
	}

	get campaignId(): string {
		return this._data.campaign_id;
	}

	set campaignId(campaignId: string) {
		this._data.campaign_id = campaignId;
	}

	get status(): BulkStatus {
		return this._data.status;
	}

	set status(status: BulkStatus) {
		this._data.status = status;
	}

	get name(): string {
		return this._data.name;
	}

	set name(name: string) {
		this._data.name = name;
	}

	get tags(): string[] {
		return this._data.tags;
	}

	set tags(tags: string[]) {
		this._data.tags = tags;
	}

	get contactType(): ContactType {
		return this._data.contact_type;
	}

	set contactType(contactType: ContactType) {
		this._data.contact_type = contactType;
	}

	get taskId(): string {
		return this._data.task_id;
	}

	set taskId(taskId: string) {
		this._data.task_id = taskId;
	}

	get scheduledTime(): moment.Moment | undefined {
		return this._data.scheduled_time ? moment(this._data.scheduled_time.toMillis()) : undefined;
	}

	set scheduledTime(scheduledTime: moment.Moment | undefined) {
		this._data.scheduled_time = scheduledTime ? firebase.firestore.Timestamp.fromMillis(scheduledTime.valueOf()) : undefined;
	}

	get nextExecutionTime(): moment.Moment | undefined {
		return this._data.next_execution_time ? moment(this._data.next_execution_time.toMillis()) : undefined;
	}

	set nextExecutionTime(nextExecutionTime: moment.Moment | undefined) {
		this._data.next_execution_time = nextExecutionTime ? firebase.firestore.Timestamp.fromMillis(nextExecutionTime.valueOf()) : undefined;
	}
}
