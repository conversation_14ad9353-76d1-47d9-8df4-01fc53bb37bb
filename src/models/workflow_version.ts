import firebase from 'firebase/app';
import moment from 'moment-timezone';
import { WorkflowData } from './workflow';

export default class WorkflowVersion {
	public static collectionRef(): firebase.firestore.CollectionReference {
		return firebase.firestore().collection('workflow_versions');
	}

	public async save() {
		await this._ref.set(this._data);
	}

	public static getById(id): Promise<WorkflowVersion> {
		return new Promise((resolve, reject) => {
			WorkflowVersion.collectionRef()
				.doc(id)
				.get()
				.then((snapshot) => {
					resolve(new WorkflowVersion(snapshot));
				})
				.catch((err) => {
					console.error(err);
					reject(err);
				});
		});
	}

	public static async getByWorkflowId(workflowId: string, locationId: string): Promise<WorkflowVersion> {
		const snapshot = await WorkflowVersion.collectionRef()
			.where('workflow_id', '==', workflowId)
			.where('location_id', '==', locationId)
			.orderBy('date_added', 'desc')
			.limit(1)
			.get();
		if (!snapshot.empty) return new WorkflowVersion(snapshot.docs[0]);
		return undefined;
	}

	private _id: string;
	private _data: firebase.firestore.DocumentData;
	private _ref: firebase.firestore.DocumentReference;

	constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
		if (snapshot) {
			this._id = snapshot.id;
			this._ref = snapshot.ref;
			this._data = snapshot.data();
		} else {
			this._ref = WorkflowVersion.collectionRef().doc();
			this._id = this._ref.id;
			this._data = {};
			this.dateAdded = moment();
		}
	}

	get id(): string {
		return this._id;
	}

	get data(): firebase.firestore.DocumentData {
		return this._data;
	}

	get locationId(): string {
		return this._data.location_id;
	}

	set locationId(locationId: string) {
		this._data.location_id = locationId;
	}

	get workflowId(): string {
		return this._data.workflow_id;
	}

	set workflowId(workflowId: string) {
		this._data.workflow_id = workflowId;
	}

	get dateAdded(): moment.Moment {
		return moment(this._data.date_added.toMillis());
	}

	set dateAdded(dateAdded: moment.Moment) {
		this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf());
	}

	get workflowData(): WorkflowData {
		return this._data.workflow_data;
	}

	set workflowData(workflowData: WorkflowData) {
		this._data.workflow_data = workflowData;
	}
}
