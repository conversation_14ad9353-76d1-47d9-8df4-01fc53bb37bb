import firebase from 'firebase/app';
import moment from 'moment-timezone';

export default class Item {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('items');
  }

  public static getById(itemId: string) {
    return new Promise((resolve, reject) => {
      Item.collectionRef()
        .doc(itemId)
        .get()
        .then((snapshot: firebase.firestore.DocumentSnapshot) => {
          if (snapshot.exists) resolve(new Item(snapshot));
          resolve();
        })
        .catch((err) => {
          reject(err)
        });
    });
  }

  public static getStreamById(itemId: string): firebase.firestore.DocumentReference {
    return Item.collectionRef().doc(itemId);
  }

  public static getAllItems(companyId: string): firebase.firestore.Query {
    return this.collectionRef()
      .where('deleted', '==', false)
      .where('company_id', '==', companyId)
      .orderBy('name_lower_case');
  }

  private _id?: string;
  private _data: firebase.firestore.DocumentData;
  private _ref?: firebase.firestore.DocumentReference;

  constructor(params?: firebase.firestore.DocumentSnapshot) {
    if (params) {
      this._id = params.id;
      this._ref = params.ref;
      this._data = params.data() || {};
    } else {
      this._data = {};
      this.deleted = false;
      this.dateAdded = moment();
    }
  }

  get id(): string | undefined {
    return this._id;
  }

  get data(): firebase.firestore.DocumentData {
    return this._data;
  }

  get companyId(): string {
    return this._data.company_id;
  }

  set companyId(companyId: string) {
    this._data.company_id = companyId;
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added);
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.toDate();
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated);
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = dateUpdated.toDate();
  }
  get deleted(): boolean {
    return this._data.deleted;
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted;
  }

  get dirty(): boolean {
    return this._data.dirty;
  }

  set dirty(dirty: boolean) {
    this._data.dirty = dirty;
  }

  get name(): string {
    return this._data.name;
  }

  set name(name: string) {
    this._data.name = name;
  }

  get description(): string {
    return this._data.description;
  }

  set description(description: string) {
    this._data.description = description;
  }

  get number(): string {
    return this._data.number;
  }

  set number(itemNumber: string) {
    this._data.number = itemNumber;
  }

  get unit(): string {
    return this._data.unit;
  }

  set unit(unit: string) {
    this._data.unit = unit;
  }

  get type(): string {
    return this._data.type;
  }

  set type(type: string) {
    this._data.type = type;
  }

  get upfrontAmount(): string {
    return this._data.upfront_amount;
  }

  set upfrontAmount(upfrontAmount: string) {
    this._data.upfront_amount = upfrontAmount;
  }

  get imageUrl(): string {
    return this._data.image_url;
  }

  set imageUrl(imageUrl: string) {
    this._data.image_url = imageUrl;
  }

  get price(): string {
    return this._data.price;
  }

  set price(price: string) {
    this._data.price = price;
  }

  get cost(): string {
    return this._data.cost;
  }

  set cost(cost: string) {
    this._data.cost = cost;
  }

  get taxId(): string {
    return this._data.tax_id;
  }

  set taxId(taxId: string) {
    this._data.tax_id = taxId;
  }

  get taxable(): boolean {
    return this._data.taxable;
  }

  set taxable(taxable: boolean) {
    this._data.taxable = taxable;
  }

  get bookable(): boolean {
    return this._data.bookable;
  }

  set bookable(bookable: boolean) {
    this._data.bookable = bookable;
  }

  public save() {
    this.dateUpdated = moment();
    this.dirty = true;
    if (this._ref) {
      this._ref.update(this._data);
    } else {
      this._ref = Item.collectionRef().doc();
      this._id = this._ref.id;
      this._ref.set(this._data);
    }
  }

  public delete() {
    return Item.collectionRef()
      .doc(this.id)
      .update({
        deleted: true,
      });
  }
}
