import firebase from 'firebase/app'
import moment from 'moment-timezone'

export interface meta {
  id: string
  title: string
  description: string
  author: string
  imageUrl: string
}

const FUNNEL_PAGE_TEMPLATE_LOCATION = 'default'

export default class FunnelPage {
  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('funnel_pages')
  }

  public static getById(id: string): Promise<FunnelPage> {
    return new Promise(async (resolve, reject) => {
      try {
        await FunnelPage.collectionRef()
          .doc(id)
          .get()
          .then(snapshot => {
            resolve(new FunnelPage(snapshot))
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }


  public static getByFunnelId(funnelId: string, locationId: string): Promise<FunnelPage[]> {
    // have to add a query for using 'template type' too
    return new Promise((resolve, reject) => {
      FunnelPage.collectionRef()
        .where('deleted', '==', false)
        .where('funnel_id', '==', funnelId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            resolve([])
          } else {
            resolve(snapshot.docs.map(f => new FunnelPage(f)))
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  }

  public static getAllByFunnelId(funnelId: string, locationId: string): Promise<FunnelPage[]> {
    return new Promise((resolve, reject) => {
      FunnelPage.collectionRef()
        .where('funnel_id', '==', funnelId)
        .where('location_id', '==', locationId)
        .get()
        .then(snapshot => {
          if (snapshot.empty) {
            resolve([])
          } else {
            resolve(snapshot.docs.map(f => new FunnelPage(f)))
          }
        })
        .catch(err => {
          reject(err)
        })
    })
  }


  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference

  constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
    if (snapshot) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
    } else {
      this._ref = FunnelPage.collectionRef().doc()
      this._data = {}
      this._id = this._ref.id
      this.deleted = false
      this.dateAdded = moment().utc()
    }
  }

  public cloneData(funnelPage: FunnelPage) {
    this._data = funnelPage.data
  }

  public markAsTemplate() {
    this.locationId = FUNNEL_PAGE_TEMPLATE_LOCATION
  }

  public isTemplate(): boolean {
    return this.locationId === FUNNEL_PAGE_TEMPLATE_LOCATION
  }

  public save() {
    const _self = this
    return new Promise(async (resolve, reject) => {
      try {
        _self.dateUpdated = moment().utc()
        await _self._ref
          .set(_self._data, { merge: true })
          .then(_ => {
            resolve(_self)
          })
          .catch(err => {
            reject(err)
          })
      } catch (ex) {
        reject(ex)
      }
    })
  }

  get id(): string {
    return this._id
  }

  get ref(): firebase.firestore.DocumentReference {
    return this._ref
  }

  get data() {
    return this._data
  }

  get dateAdded(): moment.Moment {
    return moment(this._data.date_added.toMillis())
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = firebase.firestore.Timestamp.fromMillis(dateAdded.valueOf())
  }

  get dateUpdated(): moment.Moment {
    return moment(this._data.date_updated.toMillis())
  }

  set dateUpdated(dateUpdated: moment.Moment) {
    this._data.date_updated = firebase.firestore.Timestamp.fromMillis(
      dateUpdated.valueOf()
    )
  }

  get name(): string {
    return this._data.name
  }

  set name(name: string) {
    this._data.name = name
  }

  get url(): string {
    return this._data.url
  }

  set url(url: string) {
    this._data.url = url
  }

  get deleted() {
    return this._data.deleted
  }

  set deleted(deleted) {
    this._data.deleted = deleted
  }

  get funnelId(): string {
    return this._data.funnel_id
  }

  set funnelId(funnelId: string) {
    this._data.funnel_id = funnelId
  }

  get stepId(): string {
    return this._data.step_id
  }

  set stepId(stepId: string) {
    this._data.step_id = stepId
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get templateType(): string {
    return this._data.template_type
  }

  set templateType(templateType: string) {
    this._data.template_type = templateType
  }

  get popups(): [] {
    return this._data.popups
  }

  set popups(popups: []) {
    this._data.popups = popups
  }

  get colors(): [] {
    return this._data.colors
  }

  set colors(colors: []) {
    this._data.colors = colors
  }

  get settings(): {} {
    return this._data.settings
  }

  set settings(settings: {}) {
    this._data.settings = settings
  }

  get general(): {} {
    return this._data.general
  }

  set general(general: {}) {
    this._data.general = general
  }

  get meta(): meta {
    return this._data.meta
  }

  set meta(meta: meta) {
    this._data.meta = meta
  }

  get trackingCode(): {} {
    return this._data.trackingCode
  }

  set trackingCode(code: {}) {
    this._data.trackingCode = code
  }

  get pageStyles(): String {
    return this._data.pageStyles
  }

  set pageStyles(pageStyles: String) {
    this._data.pageStyles = pageStyles
  }

  get pagePreview(): String {
    return this._data.pagePreview
  }

  set pagePreview(pagePreview: String) {
    this._data.pagePreview = pagePreview
  }

  get snapshotPreview(): String {
    return this._data.preview_snapshot
  }

  set firstFetch(firstFetch: boolean) {
    this._data.first_fetch = firstFetch;
  }

  get firstFetch(): boolean {
    return this._data.first_fetch;
  }

  get sectionUrl(): string {
    return this._data.section_url
  }

  set sectionUrl(url: string) {
    this._data.section_url = url
  }

  get sectionDownloadUrl(): string {
    return this._data.section_download_url
  }

  set sectionDownloadUrl(url: string) {
    this._data.section_download_url = url
  }

  get pageDataUrl(): string {
    return this._data.page_data_url
  }

  set pageDataUrl(url: string) {
    this._data.page_data_url = url
  }

  get pageDataDownloadUrl(): string {
    return this._data.page_data_download_url
  }

  set pageDataDownloadUrl(url: string) {
    this._data.page_data_download_url = url
  }

  get pageVersion(): number {
    return this._data.page_version
  }

  set pageVersion(version: number) {
    this._data.page_version = version
  }
}
