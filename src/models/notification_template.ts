import firebase from 'firebase/app';
import * as moment from 'moment-timezone';

export interface urlTypes {
    track?: string;
    display?: string;
}

export interface Settings {
    title_color?: string,
    background_color?: string
    position?: string
    allow_close?: boolean
    clickable_notification?: boolean
    notification_link?: string,
    open_in_new_tab?: boolean
    show_only_once_per_session?: boolean
    hide_on_mobile?: boolean,
    minimum_events_to_show?: number,
    display_time?: number
}

export default class NotificationTemplate {

    static TYPE_STREAM = "stream";
    static TYPE_VISITORS = "visitors";
    static TYPE_PAGE_VISITS = "page_visits";
    static TYPE_CONVERSIONS = "conversions";
    static TYPE_COMBO = "combo";
    static TYPE_REVIEW = "review";

    static COMBO_TYPE_VISITS = "visits";
    static COMBO_TYPE_CONVERSIONS = "conversions";

    static DISPLAY_TYPE_SIMPLE = "simple";
    static DISPLAY_TYPE_CONTAINS = "contains";
    static DISPLAY_TYPE_REGEX = "regex";
    static DISPLAY_TYPE_ALLPAGES = "all_pages";


    public static collectionRef() {
        return firebase.firestore().collection('notification_templates');
    }

    public static getById(id): Promise<NotificationTemplate> {
        return new Promise((resolve, reject) => {
            NotificationTemplate.collectionRef()
                .doc(id)
                .get()
                .then((snapshot) => {
                    resolve(new NotificationTemplate(snapshot));
                })
                .catch((err) => {
                    console.error(err);
                    reject(err);
                });
        });
    }


    public static getByLocationId(locationId: string): Promise<NotificationTemplate[]> {

        return new Promise((resolve, reject) => {
            NotificationTemplate.collectionRef()
                .where('location_id', '==', locationId)
                .limit(1)
                .get()
                .then((querySnapshot) => {
                    resolve(querySnapshot.docs.map((d) => new NotificationTemplate(d)));
                });
        });


    }

    public static getByLocationIdRealtime(locationId: string) {
        return NotificationTemplate.collectionRef()
            .where('location_id', '==', locationId)
            .where('deleted', '==', false)
            .orderBy('date_added', 'desc');
    }

    public static getByLocationType(locationId: string, type: string): Promise<NotificationTemplate> {
        return new Promise((resolve, reject) => {
            NotificationTemplate.collectionRef()
                .where('location_id', '==', locationId)
                .where('type', '==', type)
                .limit(1)
                .get()
                .then((querySnapshot) => {
                    !querySnapshot.empty ? resolve(new NotificationTemplate(querySnapshot.docs[0])) : reject("No scan report found for location " + locationId);
                });
        });
    }

    private _id: string;
    private _data: { [field: string]: any };
    private _ref: firebase.firestore.DocumentReference;

    constructor(snapshot?: firebase.firestore.DocumentSnapshot) {
        if (snapshot) {
            this._id = snapshot.id;
            this._ref = snapshot.ref;
            this._data = snapshot.data() || {};
        } else {
            this._ref = NotificationTemplate.collectionRef().doc();
            this._data = {};
            this.settings = {
                position: "bottom_left",
                allow_close: false,
                title_color: "#0095f7",
                background_color: "#fffff",
                clickable_notification: false,
                open_in_new_tab: false,
                show_only_once_per_session: false,
                hide_on_mobile: false,
                minimum_events_to_show: 8,
                display_time: 8
            };
            this._id = this._ref.id;
            this.dateAdded = moment().utc();
        }
    }

    public save() {
        const _self = this;
        return new Promise((resolve, reject) => {
            _self.dateUpdated = moment().utc();
            _self._ref.set(_self._data, { merge: true }).then((_) => {
                resolve(_self);
            });
        });
    }



    get id(): string {
        return this._id;
    }

    get data() {
        return this._data;
    }

    get companyId(): string {
        return this._data.company_id;
    }

    set companyId(companyId: string) {
        this._data.company_id = companyId;
    }

    get locationId(): string {
        return this._data.location_id;
    }

    set locationId(locationId: string) {
        this._data.location_id = locationId;
    }

    get dateAdded(): moment.Moment {

        return moment.unix(this._data.date_added.seconds);
    }

    set dateAdded(dateAdded: moment.Moment) {
        this._data.date_added = dateAdded.toDate();
    }

    get dateUpdated(): moment.Moment {

        return moment.unix(this._data.date_updated.seconds);
    }

    set dateUpdated(dateUpdated: moment.Moment) {
        this._data.date_updated = dateUpdated.toDate();
    }

    get deleted() {
        return this._data.deleted;
    }

    set deleted(deleted) {
        this._data.deleted = deleted;
    }

    get name(): string {
        return this._data.name;
    }

    set name(name: string) {
        this._data.name = name;
    }

    get urlTypes(): urlTypes {
        if (!this._data.url_types) this._data.url_types = {};
        return this._data.url_types;
    }

    set urlTypes(urlTypes: urlTypes) {
        this._data.url_types = urlTypes;
    }

    get type(): string {
        return this._data.type;
    }

    set type(type: string) {
        this._data.type = type;
    }

    get subtype(): string {
        return this._data.subtype;
    }

    set subtype(subtype: string) {
        this._data.subtype = subtype;
    }

    get matchType(): string {
        return this._data.match_type;
    }

    set matchType(matchType: string) {
        this._data.match_type = matchType;
    }

    get matchURLs(): string[] {
        if (!this._data.match_urls) this._data.match_urls = [];
        return this._data.match_urls;
    }

    set matchURLs(match_urls: string[]) {
        this._data.match_urls = match_urls;
    }

    get comboType(): string {
        return this._data.combo_type;
    }

    set comboType(comboType: string) {
        this._data.combo_type = comboType;
    }

    get displayType(): string {
        return this._data.display_type;
    }

    set displayType(displayType: string) {
        this._data.display_type = displayType;
    }

    get displayURL(): string {
        return this._data.display_url;
    }

    set displayURL(displayURL: string) {
        this._data.display_url = displayURL;
    }

    get settings(): Settings {
        if (!this._data.settings) this._data.settings = {};
        return this._data.settings;
    }

    set settings(settings: Settings) {
        this._data.settings = settings;
    }


}
