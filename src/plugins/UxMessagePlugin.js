import UxMessageModal from '../pmd/components/util/UxMessageModal.vue'
import * as Ux from '../util/ux_message'

/* eslint-disable prefer-promise-reject-errors */
export default {
  install(Vue, { rootSelector } = { rootSelector: '#app' }) {
    let uxMessageConstructor = Vue.extend(UxMessageModal)
    let uxMessageInstance = null

    Vue.prototype.$uxMessage = (
      type,
      message,
      callbackFunction,
      options = { isMessageInRawHTML: false }
    ) => {
      let uxMessageItem
      if (type === 'confirmation' || type === 'consent') {
        uxMessageItem = Ux.UxMessage[`${type}Type`](
          message,
          callbackFunction,
          options
        )
      } else {
        uxMessageItem = Ux.UxMessage[`${type}Type`](message)
      }

      if (uxMessageInstance) {
        uxMessageInstance.show(
          uxMessageItem,
          options.isMessageInRawHTML || false
        )
        return
      }
      uxMessageInstance = new uxMessageConstructor({
        el: document.createElement('div')
      })
      document.querySelector(rootSelector).appendChild(uxMessageInstance.$el)

      uxMessageInstance.show(uxMessageItem, options && options.isMessageInRawHTML || false)
    }
  }
}
