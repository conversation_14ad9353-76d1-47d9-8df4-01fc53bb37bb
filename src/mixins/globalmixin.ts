import Vue from 'vue';
import { Utils} from '../util/utils';
import { Moment } from 'moment-timezone';
import { MessageType } from '../models';
import {copyTextToClipboard} from '../util/helper'

Vue.mixin({
  methods: {

    clipboardCopy: (text: string) => copyTextToClipboard(text),
    isCallType:( messageType: MessageType) =>  Utils.isCall(messageType),
    isEmailType: (messageType: MessageType) => Utils.isEmail(messageType),
    isEmptyStr: (str: string) => Utils.isEmptyStr(str),
    serverReady: (str: string) => Utils.serverReady(str),
    relativeTime:(m: Moment, suffix: string = 'ago' ) => Utils.relativeTime(m,suffix),
    tzTime: (m: Moment, timeZone: string) => Utils.tzTime(m,timeZone),
    getDateParts: (m: Moment, timeZone: string) => Utils.getDateParts(m,timeZone),
    getUserName: (id:string) => Utils.getUserName(id),
    cropDisplay:(item:string,len:number) => Utils.cropDisplay(item,len)
  }
});
