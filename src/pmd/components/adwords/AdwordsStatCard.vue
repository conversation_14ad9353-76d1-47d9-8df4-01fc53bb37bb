<template>
  <div class="card campaign_stats_card">
    <h2 class="stats_card_value" v-if="stat">
      {{(name === 'Cost' || name === 'Cost per Conversion' || name === 'Average CPC') ? $options.filters.symbole(location.country) : ''}} {{ stat.value | formatNumber }} {{name === 'Conversion Rate' ? '%' : ''}}
    </h2>
    <span
      v-if="stat"
      :class="getPercentageChangeClass(stat.change_type)"
      v-html="growthStatHTML(stat.change_up_down, stat.change_percentage)"
    ></span>
    <p>{{prefix}}{{ name }}{{suffix}}</p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    stat: {
      type: Object,
      required: true
    },
    prefix: {
        type: 'String'
    },
    suffix: {
        type: 'String'
    },
    name: {
      type: String,
      required: true
    },
    location: {
      type: Object
    }
  },
  methods: {
    growthStatHTML(change_type, value) {
      if (change_type === 'up') {
        return `<span>
          <i class="icon icon-arrow-up-1"></i> ${value}%
        </span>`
      } else {
        return `<span>
          <i class="icon icon-arrow-down-1"></i> ${Math.abs(value)}%
        </span>`
      }
    },
    getPercentageChangeClass(value) {
      return value === 'increment' ? 'going-up' : 'going-down'
    }
  }
})
</script>

<style scoped>
.going-up {
    display: inline-block;
    text-align: center;
    color: #37ca37;
    border: 1px solid #37ca37;
    background-color: rgba(55,202,55,0.1);
    border-radius: 4px;
    padding: 5px 10px;
    line-height: 1;
    font-size: .6875rem;
    font-weight: bold;
    text-transform: uppercase;
}

.going-down {
  display: inline-block;
  text-align: center;
  color: #e93d3d;
  border: 1px solid #e93d3d;
  background-color: rgba(233, 61, 61, 0.1);
  border-radius: 4px;
  padding: 5px 10px;
  line-height: 1;
  font-size: 0.6875rem;
  font-weight: bold;
  text-transform: uppercase;
}

table thead tr th {
  min-width: 100px;
}

.campaign_stats_cards {
  display: grid;
  grid-gap: 10px 20px;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.campaign_stats_card {
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stats_card_value {
  font-weight: bolder;
  font-size: 1.5rem;
}
</style>
