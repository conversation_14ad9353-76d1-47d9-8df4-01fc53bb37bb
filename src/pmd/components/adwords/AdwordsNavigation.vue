<template>
  <ul class="nav nav-tabs adwords-nav" role="tablist">
    <li class="nav-item">
      <router-link
        :to="{name: 'reporting_adwords_campaigns'}"
        id="all_funnels"
        tag="a"
        class="nav-link"
        active-class="active"
        exact
        data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
      >
        Campaigns
      </router-link>
    </li>

    <li class="nav-item">
      <router-link
        :to="{name: 'reporting_adwords_adgroups'}"
        id="all_funnels"
        tag="a"
        class="nav-link"
        active-class="active"
        exact
        data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
      >
        Ad Groups
      </router-link>
    </li>

    <li class="nav-item">
      <router-link
        :to="{name: 'reporting_adwords_ads'}"
        id="all_funnels"
        tag="a"
        class="nav-link"
        active-class="active"
        exact
        data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
      >
        Ads
      </router-link>
    </li>

    <li class="nav-item">
      <router-link
        :to="{name: 'reporting_adwords_keywords'}"
        id="all_funnels"
        tag="a"
        class="nav-link"
        active-class="active"
        exact
        data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
      >
        Keywords
      </router-link>
    </li>
  </ul>
</template>


<script lang="ts">
  import Vue from 'vue'

  export default Vue.extend({
    props: {

    },
    methods: {

    }
  })
</script>

