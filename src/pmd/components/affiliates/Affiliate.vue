<template>
  <div>
    <div class="marketplace_card card">
      <div class="card-body">
        <span v-if="development" class="marketplace_card--email mr-5"><router-link :to="{ name: 'update_affiliate', params: { id }}"><i class="icon icon-edit"></i></router-link></span>
        <span v-if="development" class="marketplace_card--email mr-4">
          <i @click.prevent="deletePartner" class="icon icon-trash"></i>
        </span>
        <a v-if="!detailPage" href="#" @click.prevent="showDetails(id)" class="marketplace_card--email"
          ><i class="icon icon-mail"></i
        ></a>
        <div v-if="detailPage" class="backBtn" @click.prevent="$router.go(-1)">
            <a href="#" class="back"><i style="font-size: 1.5rem" class="icon icon-arrow-left-2"></i></a>
        </div>
        <div class="marketplace_card--top">
          <div class="marketplace_card--avatar">
            <div class="img-wrap">
              <img :src="data.profile_photo" alt="photo" />
            </div>
            <!-- <img height="26px" width="84px" :src="getRankImage(data.rank)" alt="Partner Badge"> -->
          </div>
          <div class="marketplace_card--infos">
            <h2 @click.prevent="showDetails(id)" class="gotodetail">{{ data.name }}</h2>
            <p class="title">{{ data.company || 'HighLevel Inc.'}}</p>
            <p class="location"> {{ data.city }}, {{ data.state }}, {{ data.country }}</p>
            <p class="description" v-if="!detailPage">
              {{ data.description }}
            </p>
          </div>
          <div class="marketplace_card--services">
            <h4>Services</h4>
            <ul class="list-disc mt-2 space-y-1">
              <li :key="service" v-for="service in shortServices" class="text-gray-500">{{ service }}</li>
            </ul>
            <p v-if="remainingServices > 0">+ {{ remainingServices }} more</p>
          </div>
        </div>
        <div class="mt-3 badges_style">
          <img height="44px" :src="getRankImage(data.rank)" alt="Partner Badge">
          <img v-for="badge in data.badges" :key="badge" height="44px" :src="getBadgeImage(badge)" alt="Partner Badge">
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Partner } from '../../../models'
import axios from 'axios'
import defaults from '@/config'

export default Vue.extend({
    props: {
        data: {
            type: Object
        },
        id: {
            type: String
        },
        detailPage: {
            type: Boolean,
            default: false
        },
        development: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            partner: undefined as Partner | undefined,
            baseUrl: defaults.baseUrl //'https://akash-dot-highlevel-staging.appspot.com'
        }
    },
  methods: {
    showDetails(affiliateId) {
      this.$router.push({
        name: 'affiliate_detail',
        params: { affiliate_id: affiliateId }
      })
    },
    deletePartner(){
      axios.delete(`${this.baseUrl}/partners/${this.id}`)
      .then((res) => {
        window.location.reload()
      }).catch((err) => {
        console.log("Error deleting partner : ", err);
      })
    },
    getBadgeImage: function(badge: string) {
      if(badge === 'Premier') {
        return 'https://cdn.msgsndr.com/partner/PREMIERAFFILIATE-BADGE.png'
      } else if(badge === 'Top') {
        return 'https://cdn.msgsndr.com/partner/TOPAFFILIATE-BADGE.png'
      } else {
        return 'https://cdn.msgsndr.com/partner/AFFILIATE-BADGE.png'
      }
    },
    getRankImage: function(rank: string) {
      if(rank === 'Certified Partner'){
        return 'https://cdn.msgsndr.com/partner/certifiedpartnerbadge.png'
      }else{
        return 'https://cdn.msgsndr.com/partner/PartnerBadge.png'
      }
    }
  },
  computed: {
      shortServices() {
          return this.data.services.slice(0, 5)
      },
      remainingServices() {
          return this.data.services.length - 5
      }
  },
})
</script>

<style scoped>
.backBtn{
    position: absolute;
    top: 20px;
}
.gotodetail{
    cursor: pointer;
    color: #188BF6;
}
.marketplace_card {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  padding-top: 20px;
  position: relative;
}
.marketplace_card--label {
  display: inline-block;
  line-height: 1;
  background-color: #188bf6;
  color: #fff;
  text-transform: uppercase;
  padding: 8px 12px;
  font-size: 0.6875rem;
  font-weight: bold;
  border-top-right-radius: 4px;
  position: absolute;
  top: 0;
  right: 0;
}
.marketplace_card--email {
  font-size: 1.125rem;
  color: #afb8bc;
  position: absolute;
  top: 30px;
  right: 20px;
}
.marketplace_card--email:hover {
  color: #188bf6;
}

@media (min-width: 992px) {
  .marketplace_card--top {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-left: -30px;
    margin-right: -30px;
    padding-left: 10px;
    padding-right: 10px;
  }
}
@media (min-width: 992px) {
  .marketplace_card--top > * {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0px;
    flex: 1 0 0;
    padding-left: 20px;
    padding-right: 20px;
  }
}
.marketplace_card--avatar {
  text-align: center;
}
.marketplace_card--avatar .img-wrap {
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 10px;
  width: 130px;
  height: 130px;
  border-radius: 50%;
}
.marketplace_card--avatar .img-wrap > img {
  max-width: 130px;
  height: auto;
  max-height: 130px;
  width: auto;
  border: 1px solid #e1e8ed;
}
@supports ((-o-object-fit: cover) or (object-fit: cover)) {
  .marketplace_card--avatar .img-wrap > img {
    width: 130px;
    height: 130px;
    -o-object-fit: cover;
    object-fit: cover;
    -o-object-position: center;
    object-position: center;
    border-radius: 50%;
  }
}
.marketplace_card--avatar h3 {
  font-size: 1rem;
}
.marketplace_card--avatar .important {
  text-align: left;
  margin-top: 30px;
  font-size: 0.75rem;
}
.marketplace_card--infos h2 {
  font-size: 1.375rem;
  margin-bottom: 15px;
}
.marketplace_card--infos .rating-wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 15px;
}
.marketplace_card--infos .rating-wrap .rating {
  margin-right: 10px;
}
.marketplace_card--infos > p {
  line-height: 1.5;
}
.marketplace_card--infos > p + p {
  margin-top: 10px;
}
.marketplace_card--infos > p.title {
  color: #2a3135;
  font-weight: 500;
}
.marketplace_card--infos > p.location span {
  color: #afb8bc;
  font-style: italic;
}
.marketplace_card--infos > p.description {
  font-size: 0.8125rem;
}
.marketplace_card--infos > p.phone + p.phone {
  margin-top: 0;
}
@media (min-width: 992px) {
  .marketplace_card--services {
    border-left: 2px solid #f2f7fa;
  }
}
.marketplace_card--services h4 {
  font-size: 1rem;
  color: #2a3135;
}
.marketplace_card--services ul {
  padding-left: 15px;
}
.marketplace_card--bottom {
  padding-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
@media (min-width: 768px) {
  .marketplace_card--bottom > * {
    -webkit-box-flex: 1;
    -ms-flex: 1 0 0px;
    flex: 1 0 0;
    max-width: 25%;
  }
}
.marketplace_card--badge {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 10px;
  margin-right: 30px;
}
.marketplace_card--badge img {
  margin-right: 10px;
}
.marketplace_card--badge .text h5 {
  font-size: 0.75rem;
  margin-bottom: 0;
  font-weight: normal;
  color: #607179;
}
.marketplace_card--badge .text p {
  font-size: 0.75rem;
  color: #2a3135;
}
.badges_style{
  display: grid;
  grid-template-columns: 18% 18% 32% 32%;
}
</style>
