 <template>
 <div>
	<div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="loadmodal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h2 class="modal-title">Load Account Snapshot</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner">
						<div class="form-group">
							<label>Snapshot</label>
							<vSelect
								:options="accountSnapshots"
								label="name"
								v-model="selectedSnapshot"
								:clearable="false"
								v-validate="'required'"
								name="snapshot"
							></vSelect>
							<span v-show="errors.has('snapshot')" class="error">Pick a snapshot</span>
						</div>
						<div v-if="error" class="help --red" style="text-align: center;">{{error}}</div>
					</div>
				</div>
				<div class="modal-footer">
					<div class="modal-footer--inner nav">
						<button type="button" class="btn btn-light2" data-dismiss="modal">Cancel</button>
						<div style="display: inline-block;position: relative;">
							<button
								:class="{invisible: saving}"
								type="button"
								class="btn btn-success"
								@click.prevent="loadAccount"
							>Load</button>
							<div
								style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
								v-show="saving"
							>
								<moon-loader :loading="saving" color="#37ca37" size="30px"/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
  <PushCloneAccount :values="pushCloneModal" @hidden="pushCloneModal={visible: false}" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import * as moment from 'moment-timezone';
import vSelect from "vue-select";
import { CloneAccount, AuthUser } from '@/models';
const PushCloneAccount = () =>
  import('@/pmd/components/agency/PushCloneAccount.vue')

declare var $: any;

export default Vue.extend({
	props: ['values'],
	components: {
    vSelect,
    PushCloneAccount
	},
	data() {
		return {
			authUser: {} as AuthUser,
			selectedSnapshot: undefined as { [key: string]: any } | undefined,
      accountSnapshots: [] as { [key: string]: any }[],
      pushCloneModal: { visible: false } as { [key: string]: any },
			saving: false,
			locationId: '',
			error: '',
		}
	},
	methods: {
		async fetchData() {
			this.accountSnapshots.length = 0;
			const snapshots = await CloneAccount.getByCompanyId(this.authUser.companyId).then(snapshots => {
				const shared = snapshots.filter(s => s.type === 'imported').sort((a, b) => a.name.localeCompare(b.name)).map(s => {
					return { id: s.id, name: `Imported - ${s.name}`, type: 'imported' }
				});
				const own = snapshots.filter(s => s.type === 'own').sort((a, b) => a.name.localeCompare(b.name)).map(s => {
					return { id: s.id, name: `Own - ${s.name}`, type: 'own' }
				});
				this.accountSnapshots.push.apply(this.accountSnapshots, shared);
				this.accountSnapshots.push.apply(this.accountSnapshots, own);
			});

			const verticalUrl = '/snapshot/list_templates';
			await this.$http.get(verticalUrl).then(response => {
				const verticals = response.data.sort((a, b) => a._data.name.localeCompare(b._data.name)).map(s => {
					return { id: s._id, name: `Vertical - ${s._data.name}`, type: 'vertical' }
        		})
				this.accountSnapshots.push.apply(this.accountSnapshots, verticals);
			});

			const defaultUrl = '/snapshot/templates';
			this.$http.get(defaultUrl).then(response => {
				const defaults = response.data.sort((a, b) => a.name.localeCompare(b.name)).map(s => {
					return { data: s.account_data, name: `Default - ${s.name}`, type: 'default' }
        		})
				this.accountSnapshots.push.apply(this.accountSnapshots, defaults);
			});
		},
		async loadAccount() {
			this.error = '';
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
      this.saving = true;

        $(this.$refs.loadmodal).modal('hide');

        // console.log(this.locationId, this.selectedSnapshot.id);

        //  this.pushCloneModal = {
        //   visible: true,
        //   companyId: this.authUser.companyId,
        //   locationId: this.locationId,
        //   snapshotId: this.selectedSnapshot.id
        // }

      this.saving = false;
      // try {
      //   const response = await this.$http.post(`/snapshot/${this.selectedSnapshot.id}/load/${this.locationId}`, {
      //     snapshotType: this.selectedSnapshot.type
      //   })
      //   this.$emit('hidden');
      // } catch (error) {
      //   this.error = error
      // } finally {
      //   this.saving = false;
      // }
		},
	},
	watch: {
		async values(values: { [key: string]: any }) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.loadmodal).modal('show');
			else $(this.$refs.loadmodal).modal('hide');
			if (values.visible) {
				this.authUser = await this.$store.dispatch("auth/get");
				this.locationId = values.locationId;
				this.fetchData();
			}
		},
	},
	updated() {
		if (this.values && this.values.visible) {
			$(this.$refs.loadmodal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.loadmodal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});

		if (this.values && this.values.visible) {
			$(this.$refs.loadmodal).modal('show');
		}
	},
})
</script>


