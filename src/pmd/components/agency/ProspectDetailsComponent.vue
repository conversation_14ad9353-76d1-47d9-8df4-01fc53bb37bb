<template>
  <div
    class="tab-pane fade show active"
    id="prospect"
    role="tabpanel"
    aria-labelledby="prospect-tab"
  >
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="First Name"
        class="msgsndr5"
        placeholder="First Name"
        v-model="firstName"
        v-validate="'required'"
        name="msgsndr5"
        data-lpignore="true"
        autocomplete="msgsndr5"
        data-vv-as="First name"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('msgsndr5')"
        :errorMsg="errors.first('msgsndr5')"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Last Name"
        class="msgsndr6"
        placeholder="Last Name"
        v-model="lastName"
        name="msgsndr6"
        data-lpignore="true"
        autocomplete="msgsndr6"
        data-vv-as="Last name"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('msgsndr6')"
        :errorMsg="errors.first('msgsndr6')"
      />
    </div>
    <div class="form-group">
      <UITextLabel>
        <ValidEmailIcon style="margin-right: 4px" :contact="contact" />
        Email Address
      </UITextLabel>

      <div class="mt-1 relative rounded-md shadow-sm">
        <UITextInput
          class="msgsndr3"
          placeholder="Email address"
          v-model="email"
          v-validate="'email'"
          name="msgsndr3"
          data-lpignore="true"
          autocomplete="msgsndr3"
          data-vv-as="Email"
          @input="setEdited"
          :disabled="!isAdmin"
        />
        <div v-if="errors.has('msgsndr3')" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <i class="fas fa-exclamation-circle h-5 w-5 text-red-400 text-lg mb-2 mr-1 pl-1" aria-hidden="true"></i>
        </div>
      </div>
      <p v-if="errors.has('msgsndr3')" class="mt-2 text-sm text-red-600" id="email-error">{{errors.first('msgsndr3')}}</p>
    </div>
    <div class="form-group">
      <UITextLabel
        >Phone number
        <span
          v-if="phoneInfo && (!phoneInfo.valid || !phoneInfo.sms_capable)"
          class="hl_help-sms-validation ml-1"
          v-b-tooltip.hover
          :title="
            !phoneInfo.valid
              ? 'Twilio Lookup marked this phone number as invalid.'
              : !phoneInfo.sms_capable
              ? `Twilio Lookup stated this is a ${phoneInfo.type} number, so it won't receive SMS.`
              : ''
          "
        >
          <i
            class="fa fa-exclamation-triangle text-warning"
            id="fa-question-circle"
          ></i>
        </span>
      </UITextLabel>
      <PhoneNumber
        placeholder="Phone number"
        class="msgsndr1"
        v-model="phone"
        v-validate="'phone'"
        name="msgsndr1"
        autocomplete="msgsndr1"
        data-vv-as="phone"
        :currentLocationId="currentLocationId"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('msgsndr1')"
        :errorMsg="errors.first('msgsndr1')"
      />
    </div>
    <div class="form-group" v-if="contact">
      <UITextLabel>Date of Birth</UITextLabel>
      <datepicker
        class="mt-1"
        v-model="dob"
        :bootstrap-styling="false"
        :input-class="'shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800'"
        :use-utc="true"
        :format="'M/d/yyyy'"
        :initial-view="'year'"
        :placeholder="'Date of birth'"
        name="dateOfBirth"
        @selected="setEdited"
        :clear-button="true"
        @cleared="dob = null"
      />
    </div>
    <div
      class="form-group"
      v-if="contact && gender"
      style="margin-bottom: 10px"
    >
      <UITextLabel>Gender</UITextLabel>
      <div class="tag-group mt-1">
        <div class="radio radio-div">
          <input
            type="radio"
            name="gender"
            id="gender-male"
            v-model="gender"
            value="Male"
            @change="setEdited"
          />
          <label for="gender-male">Male</label>
        </div>
        <div class="radio radio-div">
          <input
            type="radio"
            name="gender"
            id="gender-female"
            v-model="gender"
            value="Female"
            @change="setEdited"
          />
          <label for="gender-female">Female</label>
        </div>
        <div class="radio radio-div">
          <input
            type="radio"
            name="gender"
            id="gender-none"
            v-model="gender"
            value="Not Specified"
            @change="setEdited"
          />
          <label for="gender-none">Not Specified</label>
        </div>
      </div>

    </div>
    <div class="form-group" v-if="contact">
      <UITextInputGroup
        type="text"
        label="Contact Source"
        v-model="source"
        name="source"
        data-lpignore="true"
        @input="setEdited"
        :error="errors.has('source')"
        :errorMsg="errors.first('source')"
      />
    </div>
    <TagComponent v-if="contact" v-model="tags" @change="setEdited" />
    <div class="form-group" v-if="contact">
      <UITextLabel>Contact Type</UITextLabel>
      <select
        class="selectpicker"
        title="Contact Type"
        v-model="type"
        name="type"
        @change="setEdited"
      >
        <option value="customer">Customer</option>
        <option value="lead">Lead</option>
      </select>
      <span v-show="errors.has('type')" class="error">{{
        errors.first('type')
      }}</span>
    </div>
    <div class="form-group" v-if="contact">
      <UITextLabel>DND (Opt out of marketing campaigns)</UITextLabel>
      <div class="toggle mt-1">
        <UIToggle
          class="tgl tgl-light"
          id="account-buffer-tgld"
          v-model="dnd"
          @input="setEdited"
        />
      </div>
      <label class="mt-3" v-if="contact && contact.dndDate"
        >DND Date:
        {{
          contact.dndDate.format(getCountryDateFormat('extended-normal'))
        }}</label
      >
    </div>
    <div class="form-group">
      <!-- @todo need to update with tw -->
      <ContactOffers
        v-if="
          contact &&
          user &&
          (user.permissions.membership_enabled == true ||
            (user.role === 'admin' &&
              user.type === 'agency' &&
              user.permissions.membership_enabled !== false))
        "
        :contact="contact"
      />
    </div>
    <div
      class="form-group"
      v-if="contact && user && (user.automationPermission.campaigns || user.automationPermission.workflows)"
    >
      <UITextLabel>Active {{ automationString }}</UITextLabel>
      <div class="tag-group mt-1">
        <!-- <div class="tag">campaign 3</div>
        <div class="tag">campaign 5</div>-->
        <div
          class="tag"
          v-for="automation in activeAutomations"
          :key="automation.id"
          :id="automation.id"
          style="margin-right: 10px"
        >
          {{ automation.name }}
          <a
            @click="goToWorkflowLogForContact(automation.id)"
            v-if="automation.id && automation.id.includes(`workflow_`)"
          >
            <i class="fas fa-network-wired"></i>
          </a>
          <a @click="removeFromAutomation(automation)">
            <i class="icon icon-close"></i>
          </a>
        </div>
        <button
          style="display: inline-block; position: relative"
          type="button"
          class="btn btn-light2 btn-xs"
          @click="showaddToCampaignModal"
        >
          <i class="fas fa-plus"></i> Add
        </button>
      </div>
    </div>
    <div
      class="form-group"
      v-if="
        contact &&
        pastAutomations.length > 0 &&
        user &&
        (user.automationPermission.campaigns || user.automationPermission.workflows)
      "
    >
      <UITextLabel>Past {{ automationString }}</UITextLabel>
      <div class="tag-group mt-1">
        <div
          class="tag"
          v-for="automation in pastAutomations"
          :key="automation.id"
          :id="automation.id"
        >
          {{ automation.name }}
          <a
            @click="goToWorkflowLogForContact(automation.id)"
            v-if="automation.id && automation.id.includes(`workflow_`)"
          >
            <i class="fas fa-network-wired"></i>
          </a>
        </div>
      </div>
    </div>
    <div
      class="form-group"
      v-if="contact && user && user.permissions.opportunities_enabled"
    >
      <UITextLabel>Opportunities</UITextLabel>
      <div
        class="tag-group mt-1"
        v-if="opportunities.length > 0 && pipelines.length > 0"
      >
        <div
          class="tag"
          :style="opportunityColor(opportunity)"
          v-for="opportunity in opportunities"
          :key="opportunity.id"
          style="margin-right: 10px"
        >
          {{ getPipelineName(opportunity) }}
          <a @click.prevent="editOpportunity(opportunity)">
            <i class="icon icon-edit"></i>
          </a>
        </div>
      </div>
      <div class="tag-group mt-1">
        <button
          style="display: inline-block; position: relative"
          type="button"
          class="btn btn-light2 btn-xs mt-1"
          @click.prevent="moreOptions"
        >
          <i class="fas fa-plus"></i> Add
        </button>
      </div>
    </div>
    <!-- <div class="form-group" v-if="contact && (currentLocation && currentLocation.botService) && (user && user.permissions.bot_service)">
      <label>Eliza Service</label>
      <div class="tag-group">
        <div
          class="tag"
          style="margin-right: 10px;"
          v-if="agentName"
        >
         {{ agentName }}
          <a @click="removeFromBot">
            <i class="icon icon-close"></i>
          </a>
        </div>
        <button
          style="display: inline-block;position: relative;"
          type="button"
          class="btn btn-light2 btn-xs"
          @click="showaddToBotModal"
        >
          <i class="fas fa-plus"></i> Add
        </button>
      </div>
    </div> -->
    <div class="form-group" v-if="contact">
      <label v-if="contact.internalSource" class="created-text"
        >Created by: <InternalSourceLink :object="contact"
      /></label>
      <label v-if="contact.dateAdded" class="created-text"
        >Created on:
        {{
          contact.dateAdded.format(getCountryDateFormat('extended-normal'))
        }}</label
      >
    </div>
    <div class="form-footer save" v-if="edited">
      <UIButton use="outline" @click.prevent="reset">
        Cancel
      </UIButton>
      <div style="display: inline-block; position: relative; margin-left: 10px">
        <UIButton
          @click.prevent="save"
          :loading="loading"
          :disabled="!isAdmin"
        >
          Save
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'

import {
  Location,
  Contact,
  CampaignActivity,
  CampaignStatus,
  Campaign,
  Opportunity,
  Pipeline,
  getCountryDateFormat,
  User,
  LogicalEliza,
  Workflow,
} from '@/models'
import MoonLoader from '@/pmd/components/MoonLoader.vue'
import libphonenumber from 'google-libphonenumber'
import TagComponent from '../customer/TagComponent.vue'
import ContactOffers from '../customer/ContactOffers.vue'
import PhoneNumber from '../util/PhoneNumber.vue'
import ValidEmailIcon from '@/pmd/components/ValidEmailIcon.vue'
import * as moment from 'moment-timezone'
import Datepicker from 'vuejs-datepicker'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
const InternalSourceLink = () => import('../util/InternalSourceLink.vue')

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()

let cancelSubscription: () => void
let cancelPastSubscription: () => void
let cancelOpportunitySubscription: () => void

import { WorkflowStatusModel } from '@/models/workflow'
import { UxMessage } from '@/util/ux_message'
import lodash from 'lodash'

export default Vue.extend({
  components: {
    MoonLoader,
    TagComponent,
    PhoneNumber,
    Datepicker,
    ValidEmailIcon,
    ContactOffers,
    InternalSourceLink,
  },
  props: {
    location: Location,
    contact: Contact,
  },
  inject: ['uxmessage'],
  data() {
    return {
      loading: false,
      edited: false,
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      phoneInfo: undefined,
      type: '',
      stage: '',
      gender: '',
      value: 0,
      tags: [] as string[],
      dnd: false,
      activeCampaignsIds: new Set() as Set<string>,
      activeWorkflowsIds: [] as string[],
      pastCampaignsIds: new Set() as Set<string>,
      pastWorkflowsIds: [] as string[],
      currentLocationId: '',
      dob: undefined as Date | undefined,
      source: '',
      opportunities: [] as Opportunity[],
      getCountryDateFormat: getCountryDateFormat,
      currentLocation: {},
      agentName: '',
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isAdmin(): boolean {
      if (this.contact) return true
      return this.location && this.user && this.user.role === 'admin'
    },
    workflows(): Workflow[] {
      return this.$store.state.workflows.workflows.map((workflow: Workflow) => {
        return {
          id: `workflow_${workflow.id}`,
          name: `Workflow - ${workflow.name}`,
        }
      })
    },
    campaigns(): Campaign[] {
      return this.$store.state.campaigns.campaigns
    },
    pipelines(): Campaign[] {
      return this.$store.state.pipelines.pipelines
    },
    activeAutomations(): (Campaign | Workflow)[] {
      const campaigns =
        this.user?.automationPermission.campaigns && this.activeCampaignsIds.size > 0 && this.campaigns.length > 0
          ? this.campaigns.filter(c => this.activeCampaignsIds.has(c.id))
          : []
      const workflows =
        this.user?.automationPermission.workflows && this.activeWorkflowsIds.length > 0 && this.workflows.length > 0
          ? this.workflows.filter(c => this.activeWorkflowsIds.includes(c.id))
          : []
      return [...campaigns, ...workflows]
    },
    pastAutomations(): (Campaign | Workflow)[] {
      const campaigns =
        this.user?.automationPermission.campaigns && this.pastCampaignsIds.size > 0 && this.campaigns.length > 0
          ? this.campaigns.filter(c => this.pastCampaignsIds.has(c.id))
          : []
      const workflows =
        this.user?.automationPermission.workflows && this.pastWorkflowsIds.length > 0 && this.workflows.length > 0
          ? this.workflows.filter(c => this.pastWorkflowsIds.includes(c.id))
          : []
      return [...campaigns, ...workflows]
    },
    automationString() {
      return this.user.automationString() || 'Campaigns / Workflows'
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id

    if (this.currentLocationId)
      this.currentLocation = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )

    this.reset()
    await this.fetchData()
    this.getAgentName()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
    this.$root.$on('addedToBot', this.getAgentName)
    this.$root.$on('addedToWorkflow', this.fetchWorkflowData)
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    async getAgentName() {
      if (
        this.contact &&
        this.contact.dialogflowBotInfo &&
        this.contact.dialogflowBotInfo.logicalElizaId
      ) {
        const logicalEliza = await LogicalEliza.getById(
          this.contact.dialogflowBotInfo.logicalElizaId
        )
        if (logicalEliza.logicalName) this.agentName = logicalEliza.logicalName
      }
    },
    moreOptions() {
      if (!this.contact) return
      let opportunityModalValues = {
        visible: true,
        contact: this.contact,
        currentLocationId: this.contact.locationId,
        tab: 'OpportunityComponent',
      }
      this.$root.$emit('editOpportunity', opportunityModalValues)
    },
    showaddToCampaignModal() {
      const addToCampaignModal = {
        visible: true,
        contact: this.contact,
        activeAutomations: this.activeAutomations,
      }
      this.$root.$emit('showaddToCampaignModal', addToCampaignModal)
    },
    // showaddToBotModal() {
    //   const showaddToBotModal = {
    //     visible: true,
    //     contact: this.contact,
    //   }
    //   this.$root.$emit('showaddToBotModal', showaddToBotModal)
    // },
    editOpportunity(opportunity: Opportunity) {
      if (!this.contact) return
      let opportunityModalValues = {
        visible: true,
        opportunity,
        contact: this.contact,
        currentLocationId: this.contact.locationId,
        tab: 'OpportunityComponent',
      }
      this.$root.$emit('editOpportunity', opportunityModalValues)
    },
    opportunityColor(op: Opportunity) {
      const styles: { [key: string]: any } = { 'font-weight': '900' }
      switch (op.status) {
        case 'won':
          styles['background-color'] = '#37ca37'
          styles['color'] = 'white'
          break
        case 'open':
          styles['background-color'] = 'rgba(24, 139, 246, 0.1)'
          styles['color'] = '#188bf6'
          break
        case 'lost':
          styles['background-color'] = '#e93d3d'
          styles['color'] = 'white'
          break
        case 'abandoned':
          styles['background-color'] = '#607179'
          styles['color'] = 'white'
          break
      }
      return styles
    },
    getPipelineName(opportunity: Opportunity) {
      const pipeline: { [key: string]: any } =
        lodash.find(this.pipelines, { id: opportunity.pipelineId }) || {}
      const stage: { [key: string]: any } =
        lodash.find(pipeline.stages, { id: opportunity.pipelineStageId }) || {}
      if (!pipeline || !stage) return ''
      return `${stage.name} (${pipeline.name})`
    },
    async fetchData() {
      if (this.contact) {
        await Promise.all([
          this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id),
          this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id),
          this.$store.dispatch('workflows/syncAll', this.$route.params.location_id)
        ])

        if (cancelPastSubscription) {
          cancelPastSubscription()
        }
        if (this.user?.automationPermission.campaigns) this.fetchCampaignData()

        if (this.user?.automationPermission.workflows) this.fetchWorkflowData()

        if (cancelOpportunitySubscription) {
          cancelOpportunitySubscription()
        }
        cancelOpportunitySubscription = Opportunity.fetchByContact(
          this.contact.id,
          this.currentLocationId
        ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
          this.opportunities = await Promise.all(
            snapshot.docs.map(d => new Opportunity(d))
          )
        })
      }
    },
    fetchCampaignData() {
      cancelPastSubscription = CampaignStatus.fetchByContact(
        this.contact.id,
        this.currentLocationId
      ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
        let pastCampaignsIds: Set<string> = new Set(
          await Promise.all(
            snapshot.docs
              .filter(status => {
                return (
                  status.data().status !== 'running' &&
                  status.data().status !== 'paused'
                )
              })
              .map(async status => {
                return status.data().campaign_id
              })
          )
        )

        let activeCampaignsIds: Set<string> = new Set(
          await Promise.all(
            snapshot.docs
              .filter(status => {
                return (
                  status.data().status === 'running' ||
                  status.data().status === 'paused'
                )
              })
              .map(async status => {
                return status.data().campaign_id
              })
          )
        )

        this.activeCampaignsIds = activeCampaignsIds
        this.pastCampaignsIds = pastCampaignsIds
      })
    },
    async fetchWorkflowData() {
      if (
        this.$store.state.workflows.workflows &&
        this.$store.state.workflows.workflows.length > 0
      ) {
        this.activeWorkflowsIds = []
        this.pastWorkflowsIds = []

        const workflowStatuses: WorkflowStatusModel[] = await Workflow.getWorkflowStatusesByContactId(
          this.contact.id,
          this.currentLocationId
        )

        workflowStatuses.forEach(workflowStatus => {
          const statusId = `workflow_${workflowStatus.workflowId}`
          if (workflowStatus.status === 'finished')
            this.pastWorkflowsIds.push(statusId)
          else this.activeWorkflowsIds.push(statusId)
        })
      }
    },
    setEdited() {
      this.edited = true

      if (this.type == Contact.TYPE_LEAD && !this.stage) {
        this.stage = Contact.STAGE_LEAD
      }
    },
    reset() {
      this.errors.clear()
      this.loading = false
      this.edited = false
      if (this.location) {
        this.firstName = this.location.prospectInfo.first_name || ''
        this.lastName = this.location.prospectInfo.last_name || ''
        this.email = this.location.prospectInfo.email || ''
        this.phone = this.location.phone
      } else if (this.contact) {
        this.firstName = this.contact.firstName
        this.lastName = this.contact.lastName
        this.email = this.contact.email
        this.phone = this.contact.phone
        this.phoneInfo = this.contact.phoneInfo
        this.type = this.contact.type
        this.stage = this.contact.stage
        this.value = this.contact.value
        this.tags = this.contact.tags
        this.dnd = this.contact.dnd === true
        this.dob = this.contact.dateOfBirth
          ? this.contact.dateOfBirth.toDate()
          : null
        this.gender = this.contact.gender
        this.source = this.contact.source
      }
    },
    async save() {
      if (!this.isAdmin)
        alert(
          "You don't have permission to upload file. Check your permissions with your agency admin."
        )
      if (this.loading) return

      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }

      this.loading = true
      let response
      if (this.location) {
        this.location.prospectInfo.first_name = this.firstName
        this.location.prospectInfo.last_name = this.lastName
        this.location.prospectInfo.email = this.email
        //if (this.phone)
        this.location.phone = this.phone
        await this.location.save()
      } else if (this.contact) {
        this.contact.firstName = this.firstName
        this.contact.lastName = this.lastName
        this.contact.email = this.email
        this.contact.type = this.type
        this.contact.stage = this.stage
        this.contact.tags = this.tags
        this.contact.value = this.value
        this.contact.dnd = this.dnd
        this.contact.dateOfBirth = this.dob ? moment(this.dob) : null
        this.contact.gender = this.gender
        //if (this.phone)
        this.contact.phone = this.phone
        this.contact.source = this.source
        response = await this.contact.save()
      }
      this.loading = false
      if (response instanceof Contact || response === undefined) {
        this.edited = false
        this.$root.$emit('updateAudit');
      } else {
        this.uxmessage(UxMessage.errorType(lodash.capitalize(response)), true)
      }
    },
    // async removeFromBot() {
    //   if (!this.contact) return
    //   this.$uxMessage('confirmation', `Are you sure you want to remove ${this.contact.name || 'this contact'} from ${this.contact.dialogflowBotInfo.agent} Bot?`, async (res) => {
    //     if (res === 'ok') {
    //       this.agentName = '';
    //       this.contact.dialogflowBotInfo = {};
    //       await this.contact.ref.update({
    //         'dialogflow_bot_info': firebase.firestore.FieldValue.delete(),
    //         'date_updated': firebase.firestore.FieldValue.serverTimestamp()
    //       })

    //     }
    //   })
    // },
    async removeFromAutomation(automation: Campaign | Workflow) {
      if (!this.contact) return
      this.$uxMessage(
        'confirmation',
        `Are you sure you want to remove ${
          this.contact.name || 'this contact'
        } from ${automation.name}?`,
        async res => {
          if (res === 'ok') {
            if (automation.id.includes('workflow_')) {
              let workflowId = automation.id.replace('workflow_', '')
              let response = await Workflow.stopWorkflowStatusExecution(
                this.contact.id,
                this.currentLocationId,
                workflowId,
                this.user.id,
                'contact_detail_page'
              )
              this.fetchWorkflowData()
            } else {
              let response = await this.$http.post(
                `/contact/${this.contact.id}/campaign/${automation.id}/stop?user_id=${this.user.id}`
              )
            }
          }
        }
      )
    },
    goToWorkflowLogForContact(workflowId: string) {
      if (workflowId && this.currentLocationId && this.contact) {
        workflowId = workflowId.replace('workflow_', '')
        const routeRef = this.$router.resolve({
          path: `/location/${this.currentLocationId}/workflow/${workflowId}/logs?contactId=${this.contact.id}`,
        })
        window.open(routeRef.href, '_blank')
      }
    },
  },
  watch: {
    '$route.params.contact_id': function (id) {
      this.fetchData()
    },
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id
      await Promise.all([
        this.$store.dispatch('pipelines/syncAll', id),
        this.$store.dispatch('campaigns/syncAll', id),
        this.$store.dispatch('workflows/syncAll', id)
      ])
    },
  },
  beforeDestroy(): void {
    this.$root.$off('addedToBot')
    if (cancelSubscription) {
      cancelSubscription()
    }
    if (cancelPastSubscription) {
      cancelPastSubscription()
    }
    if (cancelOpportunitySubscription) {
      cancelOpportunitySubscription()
    }
  },
})
</script>

<style>
.radio-div {
  position: relative;
  display: inline-flex;
  margin-right: 15px;
}
.vdp-datepicker__clear-button {
  position: absolute;
  top: 11px;
  right: 12px;
}
</style>
