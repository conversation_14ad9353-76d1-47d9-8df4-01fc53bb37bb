<template>
  <tr class="twilio-row" :class="{
    '--pending': locationTwilioAccount.migration_status === 'pending',
    '--completed': locationTwilioAccount.under_ghl_account
  }" >
    <td>{{location.name}}</td>
    <td>
      <span v-if="locationTwilioAccount.under_ghl_account">
        ***************** *****************
      </span>
      <span v-else>{{accountSID}}</span>
      <span></span>

      <!-- <input
        type="text"
        class="form-control"
        placeholder="Account SID"
        v-model="accountSID"
        v-validate="'required'"
        name="accountSID"
		@input="handleChangeSID"
      >
      <span
        v-show="errors.has('accountSID')"
        class="--red"
      >Account SID Required</span> -->
    </td>
    <!-- <td>
      {{accountToken}} -->
      <!-- {{this.locationTwilioAccount.token}} -->
      <!-- <input
        type="text"
        class="form-control"
        placeholder="Account Token"
        v-model="accountToken"
        v-validate="'required'"
        name="token"
        @input="handleChangeToken"
      >
      <span
        v-show="errors.has('token')"
        class="--red"
      >Auth Token Required</span> -->
    <!-- </td> -->
    <td>
      <div v-if="toggleLoading" style="display:flex; justify-items: start; justify-content: start;">
        <moon-loader :loading="toggleLoading" color="#188bf6" size="16px" />
      </div>
      <div v-else-if="!isEmpty && locationTwilioAccount.migration_status !== 'pending' && !locationTwilioAccount.under_ghl_account" class="toggle">
        <UIToggle
          :id="`update_sms_validation${locationTwilioAccount.id}`"
          v-model="validate_sms"
          v-on:change="editValidateSms"
        />
      </div>
    </td>
    <td>

      <div class="twilio-action">
        <div v-if="!processing && !locationTwilioAccount.under_ghl_account" class="dropdown dropleft bootstrap-select more-select-left">
          <button type="button" class="btn dropdown-toggle more-select" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          </button>
          <div class="dropdown-menu">
            <a
                class="mx-1 my-1 pointer dropdown-item" href="javascript:void(0);"
                @click.prevent="edit" >Update Credentials</a>
            <!-- <a
                class="mx-1 my-1 pointer dropdown-item" href="javascript:void(0);"
                v-if="isEmpty && !isAgencyEmpty"
                @click.prevent="createSubAccount">
                <span v-if="!inIsvMode">Create Sub-Account</span>
                <span v-else>Use leadconnector phone service</span>
                </a> -->
          </div>
        </div>
        <!-- <button
          :class="{invisible: processing}"
          type="button"
          class="btn btn-success"
          @click.prevent="saveAccount"
		      v-if="edited && !processing"
        >Save</button> -->
        <div
          style="padding-left:30px;"
          v-show="processing"
        >
          <moon-loader
            :loading="processing"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </td>
    <td>
      <div v-if="isEmpty && (!isAgencyEmpty || inIsvMode)">
        <UIButton
          type="button"
          @click.prevent="createSubAccount"
          use="outline"
          style="display: block; margin: auto;"
        >
          <span v-if="inIsvMode" >Use LeadConnector Phone Service</span>
          <span v-else>Create Sub-Account</span>
        </UIButton>
      </div>
      <div v-if="inIsvMode && !isEmpty && !locationTwilioAccount.under_ghl_account">
        <UIButton
          type="button"
          @click="$emit('optIsv', {
            migration_status: locationTwilioAccount.migration_status || ''
          })"
          style="display: block; margin: auto;"
        >
          Switch to LeadConnector
        </UIButton>
        <div class="text-center switch-btn-status --warning" v-if="locationTwilioAccount.migration_status === 'pending'"
          v-b-tooltip.hover
          title="Twilio is working on your request. Please follow up on the email you had sent if it is taking longer than 2-3 days"
        >
          Processing !!
          <i class="fas fa-question-circle"></i>
        </div>
      </div>
      <div v-else-if="locationTwilioAccount.under_ghl_account" class="text-center switch-btn-status --success">
        Managed by LeadConnector Phone System
      </div>
    </td>
  </tr>
</template>

<script lang="ts">
import Vue from 'vue'
import { TwilioAccount } from '@/models';

export default Vue.extend({
  props: ['location', 'locationTwilioAccount','isAgencyEmpty', 'inIsvMode', 'isvBetaEnabled', 'companyId'],
  data() {
    return {
      twilioAccount: {} as TwilioAccount,
      processing: false,
      accountSID: '',
      accountToken: '',
      edited: false,
      toggleLoading: false,
      validate_sms: false,
    }
  },
  async created() {
    if (this.locationTwilioAccount) {
      this.accountSID = this.locationTwilioAccount.account_sid;
      // this.accountToken = this.locationTwilioAccount.token;
      this.validate_sms = this.locationTwilioAccount.validate_sms
      // if(!this.accountSID || !this.accountToken){
      //   this.isEmpty = true;
      // }
    }
  },
  computed: {
    isEmpty(): boolean {
      return !this.accountSID
    }
  },
  watch: {
    locationTwilioAccount() {
      if(this.locationTwilioAccount) {
        this.accountSID = this.locationTwilioAccount.account_sid;
        this.accountToken = this.locationTwilioAccount.token;
        this.validate_sms = this.locationTwilioAccount.validate_sms
        // if(!this.accountSID || !this.accountToken){
        //   this.isEmpty = true;
        // } else {
        //   this.isEmpty = false;
        // }
      }
    }
  },
  methods: {
    edit() {
      this.$emit('edit');
    },
	  // handleChangeSID(e){
		//   if(e.target.value && e.target.value !== this.locationTwilioAccount.account_sid){
		// 	  this.edited = true;
		//   } else{
		// 	  this.edited = false;
		//   }
	  // },
	  // handleChangeToken(e){
		//   if(e.target.value && e.target.value !== this.locationTwilioAccount.token){
		// 	  this.edited = true;
		//   } else{
		// 	  this.edited = false;
		//   }
	  // },
	  async createSubAccount(){
		  this.processing = true;
		  try{
        let subAccount = await this.$http.post(`/twilio/set_up_sub_account/${this.location.id}?company_id=${this.companyId}`);
        // console.log("Created new sub-account", subAccount);
        this.accountSID = subAccount.data.account_sid;
        // this.accountToken = subAccount.data.token;
        this.validate_sms = subAccount.data.validate_sms
        this.$store.commit('agencyTwilio/setLocationTwilioEmpty', false);
        // this.isEmpty = false;
        this.processing = false;
        this.$emit('updated',{ twilioAccount: subAccount, locationId: this.location.id});
        // let response = await this.$http.get('/twilio/create_application/' + this.location.id, {});
		  } catch (ex) {
        console.error("Unable to create twilio sub-account:", ex);
        if (ex && ex.response && ex.response.data && ex.response.data.msg) this.$uxMessage('error', ex.response.data.msg)
        this.processing = false;
      }
    },
    editValidateSms() {
      this.toggleLoading = true
				let body = {
					// accountSID: this.accountSID && this.accountSID.trim(),
					// accountToken: this.accountToken,
          company_id: this.location.company_id,
          validateSms: this.validate_sms
				};
      this.$http.post('/twilio/edit?location_id=' + this.location.id, body).then(async res => {
        if (res && res.status == 200) this.locationTwilioAccount.validate_sms = this.validate_sms // Update the array's value (not DB)
      }).catch(err => {
        this.$uxMessage('warning', 'An error has ocurred, please try again.')
      }).finally(() => {
        this.toggleLoading = false
      })
    }
    // async saveAccount() {
    //   const result = await this.$validator.validateAll();
    //   if (!result) {
    //     return false;
    //   }
    //   this.processing = true;
    //   try {
    //       let body = {
    //         accountSID: this.accountSID && this.accountSID.trim(),
    //         accountToken: this.accountToken,
    //         company_id: this.location.company_id
    //       };
    //       let res = await this.$http.post('/twilio/edit?location_id=' + this.location.id, body)
    //       if (res.data === 'invalid') {
    //         alert("Invalid accountSID and access token");
    //         this.edited = false;
    //         this.processing = false;
    //         return;
    //       }
    //       let response = await this.$http.get('/twilio/create_application/' + this.location.id, {});
    //       this.edited = false;
    //       this.processing = false;
    //     } catch (ex) {
    //       console.error("Couldn't create twilio app:", ex);
    //       this.edited = false;
    //       this.processing = false;
    //     }
    // }
  }
})
</script>

<style scoped lang="scss">
.twilio-action{
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.dropleft .dropdown-toggle::before {
    border-top: none;
    border-right: none;
    border-bottom: none;
    background: none;
}
.more-select-left> .btn.dropdown-toggle:hover {
    background: none;
}
.dropdown-toggle {
    background: none !important;
}
.switch-btn-status {
  margin-top: 4px;
  font-size: 12px;
  font-weight: 700;
  &.--warning{
    color: #c1910c;
  }
  &.--success {
    color: #2dae2e;
  }
}
.twilio-row {
  &.--pending {
    background-color: #ffffc4;
  }
  &.--completed {
    background-color: #e8f5e9;
  }
}
</style>
