<template>
  <div
    class="modal fade hl_sms-template--modal hl-snapshot-flow-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Account Snapshot</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="modal-body--inner">
              <ol
                class="hl_import-customers--steps-heading"
                style="padding-top: 15px"
              >
                <li :class="{ active: currentStep === 1 }">1</li>
                <li :class="{ active: currentStep === 2 }">2</li>
                <li :class="{ active: currentStep === 3 }">3</li>
                <li :class="{ active: currentStep === 4 }">4</li>
              </ol>

              <div class="hl_import-customers--heading steps-heading">
                <h3>
                  <strong>Step {{ currentStep }}:</strong>
                  {{ heading }}
                </h3>
                <!-- <p>{{ subHeading }}</p> -->
              </div>
              <div class="form-group">
                <div class="hl_import-customers--steps2">
                  <div class="hl_imported-file">
                    <h3 class="text-button-row" v-show="isLoaded && headTitle">
                      {{ headTitle }}
                    </h3>
                    <template v-if="!(singleLocation && currentStep == 1)">
                      <div
                        class="pull-right text-button-row"
                        style="margin-right: 15px"
                        v-show="currentStep !== 4 && isLoaded"
                      >
                        <div class="radio">
                          <input
                            type="radio"
                            class="bulk-skip"
                            @change="globalBulkSelect(0)"
                            v-model="globalSelect"
                            id="global-skip-radio"
                            name="global-radio"
                            value="0"
                          />
                          <label for="global-skip-radio">{{
                            currentStep === 3 ? 'Overwrite All' : 'Skip'
                          }}</label>
                        </div>
                        <div class="radio right-radio">
                          <input
                            type="radio"
                            class="bulk-select"
                            @change="globalBulkSelect(1)"
                            v-model="globalSelect"
                            id="global-select-radio"
                            name="global-radio"
                            value="1"
                          />
                          <label for="global-select-radio">{{
                            currentStep === 3 ? 'Skip All' : 'Select All'
                          }}</label>
                        </div>
                      </div>
                    </template>
                    <div
                      class="text-center"
                      v-show="!isLoaded"
                      style="margin-top: 35px"
                    >
                      <!-- <h3 style="font-size: 16px;"> Loading results...</h3> -->
                      <div style="display: inline-block; position: relative">
                        <div
                          style="
                            position: absolute;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            top: 50%;
                          "
                          v-show="processing"
                        >
                          <moon-loader
                            :loading="processing"
                            color="#37ca37"
                            size="30px"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- step 1 stars-->
                  <div class="hl_imported-file-map" v-if="currentStep == 1">
                    <div v-if="singleLocation">
                      <div class="container">
                        <div class="form-group">
                          <label>Snapshot</label>
                          <vSelect
                            :options="accountSnapshots"
                            label="name"
                            v-model="selectedSnapshot"
                            :clearable="false"
                            v-validate="'required'"
                            name="snapshot"
                            placeholder="Select a snapshot"
                          ></vSelect>
                          <span v-show="errors.has('snapshot')" class="error"
                            >Pick a snapshot</span
                          >
                        </div>
                        <div
                          v-if="error"
                          class="help --red"
                          style="text-align: center"
                        >
                          {{ error }}
                        </div>
                      </div>
                    </div>
                    <!-- <h3>Map your fields to companyName's Field</h3> -->
                    <div class="card" v-if="!singleLocation">
                      <div class="container">
                        <div class="table-wrap">
                          <table class="table">
                            <thead>
                              <tr>
                                <th>Linked Locations</th>
                                <th class="pull-right"></th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr
                                v-for="(location, i) in locationsList"
                                :key="i"
                              >
                                <td style="width: 70%">{{ location.name }}</td>
                                <td class="table-radio">
                                  <div class="radio">
                                    <input
                                      @change="singleRadioChange"
                                      type="radio"
                                      :name="'location-select' + i"
                                      :id="'skip-location' + i"
                                      v-model="location.isSelected"
                                      :value="0"
                                    />
                                    <label :for="'skip-location' + i"
                                      >Skip</label
                                    >
                                  </div>
                                  <div class="radio right-radio">
                                    <input
                                      @change="singleRadioChange"
                                      type="radio"
                                      :name="'location-select' + i"
                                      :id="'select-location' + i"
                                      v-model="location.isSelected"
                                      :value="1"
                                    />
                                    <label :for="'select-location' + i"
                                      >Select</label
                                    >
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- step 1 ends -->

                  <!-- step 2 stars-->
                  <div>
                    <div v-if="error">{{ error }}</div>
                    <div
                      class="accordion accordion-wrapper"
                      v-if="currentStep == 2"
                    >
                      <div
                        class="card mb-0"
                        v-for="key in assetKeys"
                        :key="key"
                      >
                        <div
                          class="hl_imported-file-map"
                          v-if="allAssets[key].length"
                        >
                          <div class="card">
                            <div
                              class="card-header collapsed"
                              data-toggle="collapse"
                              :href="`#collapse-${key}`"
                            >
                              <a class="card-title asset-title"
                                >{{ key | capitalize }} ({{
                                  countSelectedAssets(allAssets[key])
                                }})</a
                              >
                              <template
                                style="margin-right: 15px"
                              >
                                <div class="text-button-row asset-radio">
                                  <div class="radio" v-on:click.stop>
                                    <input
                                      type="radio"
                                      @change="assetBulkSelect(0, key)"
                                      v-model="assetSelect[key]"
                                      :id="`asset-skip-radio-${key}`"
                                      :name="`resource-radio-${key}`"
                                      :value="0"
                                    />
                                    <label :for="`asset-skip-radio-${key}`"
                                      >Skip</label
                                    >
                                  </div>
                                  <div class="radio right-radio" v-on:click.stop>
                                    <input
                                      type="radio"
                                      @change="assetBulkSelect(1, key)"
                                      v-model="assetSelect[key]"
                                      :id="`asset-select-radio-${key}`"
                                      :name="`resource-radio-${key}`"
                                      :value="1"
                                    />
                                    <label :for="`asset-select-radio-${key}`"
                                      >Select All</label
                                    >
                                  </div>
                                </div>
                              </template>
                            </div>
                            <div
                              :id="`collapse-${key}`"
                              class="card-body collapse"
                            >
                              <div class="table-wrap">
                                <table class="table">
                                  <!-- <thead>
                                                        <tr style="background-color: #cccccc;">
                                                          <th>Campaigns</th>
                                                          <th>
                                                          </th>
                                                        </tr>
                                  </thead>-->
                                  <tbody>
                                    <tr
                                      v-for="item in allAssets[key]"
                                      :key="item.id"
                                    >
                                      <td style="width: 70%">
                                        <span
                                          v-if="item.type === 'directory'"
                                          class="workflow-folder"
                                        >
                                          <i class="far fa-folder"></i>
                                        </span>
                                        {{ item.name }}
                                      </td>
                                      <td class="table-radio">
                                        <div class="radio">
                                          <input
                                            @change="changeDependentRadio(key)"
                                            type="radio"
                                            :name="`item-select-${item.id}`"
                                            :id="`skip-item-${item.id}`"
                                            v-model="item.isSelected"
                                            :value="0"
                                          />
                                          <label :for="`skip-item-${item.id}`"
                                            >Skip</label
                                          >
                                        </div>
                                        <div class="radio right-radio">
                                          <input
                                            @change="changeDependentRadio(key)"
                                            type="radio"
                                            :name="`item-select-${item.id}`"
                                            :id="`select-item-${item.id}`"
                                            v-model="item.isSelected"
                                            :value="1"
                                          />
                                          <label :for="`select-item-${item.id}`"
                                            >Select</label
                                          >
                                        </div>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <!-- step 2 ends-->

                  <!-- step 3 stars-->
                  <div v-if="currentStep == 3 && isLoaded">
                    <DetectedConflicts
                      :data="compiledConflictsData"
                      :error="error"
                      :resourcesToSkip="resourcesToSkip"
                      :conflictAssetSelect="conflictAssetSelect"
                      :conflictBulkSelect="conflictBulkSelect"
                      :skipAllConflictsForLocation="skipAllConflictsForLocation"
                      :skipDependendConflicts="skipDependendConflicts"
                    />
                    <ConfirmBulkOverwrite
                      v-if="askBulkOverwriteConfirmation"
                      @confirmed="bulkOverwriteConfirmation"
                      @cancel="askBulkOverwriteConfirmation = false"
                    />
                  </div>
                  <!-- step 3 ends-->

                  <!-- step 4 stars-->
                  <div
                    class="text-center"
                    style="margin-top: 0px"
                    v-if="currentStep == 4 && isLoaded"
                  >
                    <h3 v-if="!error" style="font-size: 16px; font-weight: 500">
                      Copying the data, we will send a notification once it's
                      completed.
                    </h3>
                    <p v-else>{{ error }}</p>
                  </div>
                  <!-- step 4 ends-->
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <div class="modal-footer--inner nav" v-if="isLoaded">
              <div v-if="currentStep !== 4" :style="{ marginRight: 'auto' }">
                <button
                  type="button"
                  class="btn btn-primary"
                  data-dismiss="modal"
                >
                  Cancel
                </button>
              </div>
              <div v-if="currentStep < 4">
                <button
                  type="button"
                  class="btn btn-primary"
                  v-if="currentStep > 1"
                  @click="goToPreviousStep"
                >
                  Back
                </button>
                <button
                  type="button"
                  class="btn btn-success"
                  :class="{ disabled: disableButton }"
                  :disabled="disableButton"
                  @click="proceedSteps()"
                >
                  Proceed
                </button>
              </div>
              <div v-if="currentStep === 4">
                <button
                  type="button"
                  class="btn btn-primary"
                  data-dismiss="modal"
                >
                  Okay
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as lodash from 'lodash'
import vSelect from 'vue-select'
import { mapState } from 'vuex'
import firebase from 'firebase/app'
import { LocationState } from '@/store/state_models'
import { Location, CloneAccount, AuthUser } from '@/models'
import DetectedConflicts from './snapshots/DetectedConflicts.vue'
import ConfirmBulkOverwrite from './snapshots/ConfirmBulkOverwrite.vue'
import depreciatedFeatures from '@/util/depreciated_features';

declare var $: any
let foldersListener: () => void

export default Vue.extend({
  props: ['values'],
  components: {
    vSelect,
    DetectedConflicts,
    ConfirmBulkOverwrite,
  },
  data() {
    return {
      processing: false,
      isLoaded: true,
      locationsList: [] as any,
      currentStep: 1,
      allAssets: {} as any,
      globalSelect: 1,
      selectedLocation: [] as any,
      singleLocation: false,
      authUser: {} as AuthUser,
      selectedSnapshot: undefined as { [key: string]: any } | undefined,
      accountSnapshots: [] as { [key: string]: any }[],
      error: '',
      selectedSnapshotAssets: {} as { [key: string]: any },
      compiledConflictsData: {} as { [key: string]: any },
      foundConflicts: false,
      resourcesToSkip: [],
      bulkOverwriteConfirmed: false,
      askBulkOverwriteConfirmation: false,
      assetSelect: {} as any,
      conflictAssetSelect: {} as any
    }
  },
  methods: {
    async fetchData() {
      this.accountSnapshots.length = 0
      const locationId = this.values?.locationId
      const snapshots = await CloneAccount.getByCompanyId(
        this.authUser.companyId
      ).then(snapshots => {
        const shared = snapshots
          .filter(s => s.type === 'imported')
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(s => {
            return { id: s.id, name: `Imported - ${s.name}`, type: 'imported' }
          })
        const own = snapshots
          .filter(s => s.type === 'own' && s.locationId !== locationId)
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(s => {
            return { id: s.id, name: `Own - ${s.name}`, type: 'own' }
          })
        this.accountSnapshots.push.apply(this.accountSnapshots, shared)
        this.accountSnapshots.push.apply(this.accountSnapshots, own)
      })

      const verticalUrl = '/snapshot/list_templates'
      await this.$http.get(verticalUrl).then(response => {
        const verticals = response.data
          .sort((a, b) => a._data.name.localeCompare(b._data.name))
          .map(s => {
            return {
              id: s._id,
              name: `Vertical - ${s._data.name}`,
              type: 'vertical',
            }
          })
        this.accountSnapshots.push.apply(this.accountSnapshots, verticals)
      })

      const defaultUrl = '/snapshot/templates'
      this.$http.get(defaultUrl).then(response => {
        const defaults = response.data
          .sort((a, b) => a.name.localeCompare(b.name))
          .map(s => {
            return {
              data: s.account_data,
              name: `Default - ${s.name}`,
              type: 'default',
            }
          })
        this.accountSnapshots.push.apply(this.accountSnapshots, defaults)
      })

      if (this.values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
    },
    countSelectedAssets(asset: { [key: string]: any }) {
      let count = 0

      asset.forEach((list: any) => {
        if (list.isSelected == 1) {
          ++count
        }
      })
      return count
    },
    async fetchLocation() {
      if (!this.selectedSnapshot?.id) {
        this.error = 'Snapshot not selected'
        return
      }
      this.locationsList = []
      this.currentStep = 1
      this.processing = true
      this.isLoaded = false

      const listOfLocations = await Location.fetchLocationBySnapshotId(
        this.selectedSnapshot.id
      )
      const cloneAccount = await CloneAccount.getById(this.selectedSnapshot.id)
      const originalLocationId = cloneAccount?.locationId
      this.locationsList = originalLocationId ? listOfLocations.filter(l => l.id !== originalLocationId) : listOfLocations
      this.bulkSelect(1, this.locationsList)
      this.globalSelect = 1

      this.processing = false
      this.isLoaded = true
      if (this.values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
    },

    async getAllAssets() {
      this.allAssets = {}

      if (!this.selectedSnapshot?.id) {
        this.error = 'Snapshot not selected'
        return
      }

      this.processing = true
      this.isLoaded = false

      try {
        const { data } = await this.$http.get(
          `/snapshot/${this.selectedSnapshot.id}/get_assets?type=${this.selectedSnapshot.type}`
        )

        const resources = Object.keys(data)
        let sortedData: any = {}
        this.globalSelect = 1
        for (let resource of resources) {
          if (resource === 'workflow') {
            sortedData[resource] = data[resource]
          } else {
            sortedData[resource] = data[resource].sort((a: any, b: any) =>
              a.name.localeCompare(b.name)
            )
          }
          this.assetSelect[resource] = this.globalSelect
        }
        this.allAssets = sortedData

        this.globalBulkSelect(1)
        this.isLoaded = true
        this.processing = false
      } catch (e) {
        console.error(e)
        this.isLoaded = true
        this.processing = false
        this.error =
          'Error while fetching contents of snapshot, please try again!'
      }
    },
    async compareAssets() {
      this.selectedLocation = []

      this.compiledConflictsData = {}
      this.foundConflicts = false

      const resources = Object.keys(this.allAssets)
      // prepare a map of assets/resources that were selected to push out
      this.selectedSnapshotAssets = resources.reduce(
        (agg: any, resourceKey: string) => {
          const resourceData = this.allAssets[resourceKey]
          if (Array.isArray(resourceData)) {
            const selectedAssetIds = resourceData.reduce(
              (_selectedData: Array<string>, data: any) => {
                if (data.isSelected) {
                  _selectedData.push(data.id)
                }
                return _selectedData
              },
              []
            )

            agg[resourceKey] = selectedAssetIds
          }

          return agg
        },
        {}
      )

      const selectedLocationIds: Array<string> = this.locationsList.reduce(
        (agg: Array<string>, location: any) => {
          if (location.isSelected) {
            this.selectedLocation.push({
              id: location.id,
              name: location.name,
            })
            agg.push(location.id)
          }

          return agg
        },
        []
      )
      this.processing = true

      const conflictPayload = {
        selectedSnapshotAssets: this.selectedSnapshotAssets,
        selectedLocationIds,
      }
      this.isLoaded = false
      try {
        if(!this.selectedSnapshot?.id) {
          this.error = 'Snapshot not selected'
          return
        }

        const { data: detectedConflicts } = await this.$http.post(
          `/snapshot/${this.selectedSnapshot.id}/conflicts`,
          conflictPayload
        )

        this.globalSelect = 1
        this.compiledConflictsData = detectedConflicts.reduce(
          (aggConflicts: any, conflictsForLocation: any) => {
            const {
              id: locationId,
              conflicts: conflictsInLocation,
            } = conflictsForLocation
            const location = this.selectedLocation.find(
              (l: any) => l.id === locationId
            )
            let noOfConflictsForLocation = 0

            const conflictDataForLocation = Object.keys(
              conflictsInLocation
            ).reduce((agg: any, conflictingResource: string) => {
              const resourceAssets = this.allAssets[conflictingResource]

              const conflicts = conflictsInLocation[conflictingResource]

              const addedNameToConflictData = conflicts.map(
                (conflictId: string) => {
                  const asset = resourceAssets.find(
                    (r: any) => r.id === conflictId
                  )
                  if (
                    conflictingResource === 'workflow' &&
                    asset.type === 'directory'
                  ) {
                    return {
                      id: conflictId,
                      name: asset.name,
                      type: asset.type,
                      skipOverwrite: 1
                    }
                  }
                  return { id: conflictId, name: asset.name, skipOverwrite: 1 }
                }
              )

              noOfConflictsForLocation += conflicts.length

              agg[conflictingResource] = addedNameToConflictData
              if(!this.conflictAssetSelect[locationId]) {
                this.conflictAssetSelect[locationId] = {}
              }
              this.conflictAssetSelect[locationId][conflictingResource] = this.globalSelect

              return agg
            }, {})

            aggConflicts[locationId] = {
              ...location,
              conflicts: conflictDataForLocation,
              noOfConflicts: noOfConflictsForLocation,
              skipAllConflicts: 1,
            }

            if (noOfConflictsForLocation > 0) {
              this.foundConflicts = true
            }

            return aggConflicts
          },
          {}
        )

        this.isLoaded = true
        this.processing = false
      } catch (e) {
        this.isLoaded = true
        this.processing = false
        this.error =
          'Something went wrong while finding conflicts, please try again later!'
      }
    },
    skipConflictsForAllLocations(skipAll: boolean) {
      const allLocations = Object.keys(this.compiledConflictsData)
      allLocations.forEach(locationId =>
        this.skipAllConflictsForLocation(locationId, skipAll)
      )
    },
    skipAllConflictsForLocation(locationId: string, skipAll: boolean) {
      const conflictDataForLocation = this.compiledConflictsData[locationId]
      if (conflictDataForLocation) {
        const { conflicts } = conflictDataForLocation
        const conflictingResources = Object.keys(conflicts)

        conflictingResources.forEach((resource: string) => {
          this.conflictAssetSelect[locationId][resource] = skipAll ? 1 : 0
          if (!this.resourcesToSkip.includes(resource)) {
            const assets = conflicts[resource]
            assets.forEach((asset: any) => {
              asset.skipOverwrite = skipAll ? 1 : 0
            })
          }
        })

        conflictDataForLocation.skipAllConflicts = skipAll ? 1 : 0
      }
    },
    bulkOverwriteConfirmation() {
      this.bulkOverwriteConfirmed = true
      this.askBulkOverwriteConfirmation = false
      this.proceedSteps()
    },
    globalBulkSelect(isSelected: number) {
      for (let asset of this.assetKeys) {
        this.assetSelect[asset] = isSelected
      }
      switch (this.currentStep) {
        case 1: {
          this.bulkSelect(isSelected, this.locationsList)
          break
        }
        case 2: {
          for (var key in this.allAssets) {
            this.bulkSelect(isSelected, this.allAssets[key])
          }
          break
        }
        case 3: {
          this.skipConflictsForAllLocations(isSelected ? true : false)
          break
        }
        default: {
          console.log(
            'global bulk select not handled for step --> ',
            this.currentStep
          )
        }
      }
    },
    conflictBulkSelect(isSelected: number, locationId: string, asset: string) {
      if(isSelected) {
        this.compiledConflictsData[locationId].skipAllConflicts = isSelected
      }
      const conflictDataForLocation = this.compiledConflictsData[locationId]
      if (conflictDataForLocation) {
        const { conflicts } = conflictDataForLocation
        const assets = conflicts[asset]
        assets.forEach((asset: any) => {
          asset.skipOverwrite = isSelected
        })
      }
    },
    assetBulkSelect(isSelected: number, asset: string) {
      this.globalSelect = 0
      this.bulkSelect(isSelected, this.allAssets[asset])
    },
    skipDependendConflicts(isSelected: number, locationId: string, asset: string) {
      this.compiledConflictsData[locationId].skipAllConflicts = isSelected
      this.conflictAssetSelect[locationId][asset] = isSelected
    },
    async pushFilteredData() {
      if (!this.selectedSnapshot) {
        console.error('snapshot not selected yet --> ', this.selectedSnapshot)
        return
      }

      this.isLoaded = false
      this.processing = true

      // prepare a map of assets that won't be overwritten

      const selectedData = Object.keys(this.compiledConflictsData).reduce(
        (agg: any, locationId: string) => {
          const locationData = this.compiledConflictsData[locationId]

          const { conflicts } = locationData

          const dataToKeep = Object.keys(this.selectedSnapshotAssets).reduce(
            (chosenAssets: any, resource: string) => {
              const assetIds = this.selectedSnapshotAssets[resource]

              let filteredAssets = new Set(assetIds)

              let assetIdsToSkip = new Set()

              const conflicts =
                this.compiledConflictsData[locationId] &&
                this.compiledConflictsData[locationId].conflicts[resource]

              if (conflicts) {
                conflicts.forEach((conflict: any) => {
                  if (
                    conflict.skipOverwrite ||
                    this.resourcesToSkip.indexOf(resource) > -1
                  ) {
                    filteredAssets.delete(conflict.id)
                  }
                })
              }

              chosenAssets[resource] = [...filteredAssets]
              return chosenAssets
            },
            {}
          )

          agg[locationId] = dataToKeep

          return agg
        },
        {}
      )

      try {
        await this.$http.post(
          `/snapshot/${this.selectedSnapshot.id}/set_assets_to_locations`,
          { selectedData, snapshotType: this.selectedSnapshot.type || 'own' }
        )
        this.isLoaded = true
        this.processing = false
      } catch (e) {
        this.error = 'Something went wrong, please try again later'
        this.isLoaded = true
        this.processing = false
        console.error(e)
      }

      try {
        if (this.allAssets.campaigns.length > 0 || this.allAssets.triggers.length > 0) {
          let features = lodash.cloneDeep(depreciatedFeatures)
          features = features.map(d => {
              return {
                ...d,
                enable: true
              };
          }) as []
          await Location.collectionRef().doc(this.values.locationId).update({
            depreciated_features: features
          })
        }
      } catch (e) {}
    },
    singleRadioChange() {
      this.globalSelect = 0
    },
    changeDependentRadio(asset: string) {
      this.globalSelect = 0
      this.assetSelect[asset] = 0
    },
    async proceedSteps() {
      this.error = ''
      if (this.currentStep === 1) {
        if (this.singleLocation) {
          const result = await this.$validator.validateAll()

          if (!result) {
            return false
          }

          const location = await Location.getById(this.values.locationId)
          this.locationsList = [location]
          this.locationsList[0]['isSelected'] = 1
        }
        this.currentStep = 2
        this.getAllAssets()
      } else if (this.currentStep === 2) {
        this.currentStep = 3
        this.compareAssets()
      } else if (this.currentStep === 3) {
        if (this.bulkOverwritingConflicts && !this.bulkOverwriteConfirmed) {
          this.askBulkOverwriteConfirmation = true
        } else if (
          !this.bulkOverwritingConflicts ||
          this.bulkOverwriteConfirmed
        ) {
          this.currentStep = 4
          this.pushFilteredData()
        }
      }
    },
    goToPreviousStep() {
      this.error = ''

      if (this.currentStep >= 2 && this.currentStep <= 4) {
        this.currentStep -= 1
      }
    },
    bulkSelect(value: number, list: Array<any>) {
      list.map((e, index: number) => {
        this.$set(list[index], 'isSelected', value)
      })
    },
    resetData() {
      this.selectedSnapshot = undefined
      this.locationsList = []
      this.error = ''
      this.processing = false
      this.isLoaded = true
      this.selectedSnapshotAssets = {}
      this.bulkOverwriteConfirmed = false
      this.askBulkOverwriteConfirmation = false
    },
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    async values(values: { [key: string]: any }) {
      this.resetData()
      if (values.snapshotId) {
        this.singleLocation = false
        this.selectedSnapshot = {
          id: values.snapshotId,
        }
        this.fetchLocation()
      } else if (values.locationId) {
        this.currentStep = 1
        this.singleLocation = true
        this.authUser = await this.$store.dispatch('auth/get')
        this.fetchData()
      }
    },
  },
  computed: {
    heading(): string {
      switch (this.currentStep) {
        case 1: {
          if (this.singleLocation) {
            return 'Choose a snapshot'
          }
          return 'Select the locations you want to update'
        }
        case 2: {
          return 'Select all the assets you want to push'
        }
        case 3: {
          return 'Checking for conflicts'
        }
        case 4: {
          return 'Response'
        }
      }
      return 'Heading title'
    },
    headTitle(): string {
      switch (this.currentStep) {
        case 1: {
          if (this.singleLocation) {
            return 'Pick a snapshot'
          }
          return `Found ${this.locationsList.length} Location${
            this.locationsList.length > 1 ? 's' : ''
          } linked`
        }
        case 2: {
          return 'All Assets found linked to this snapshot'
        }
        case 3: {
          if (this.foundConflicts) {
            return 'There are few conflicts - Please choose which assets to overwrite'
          } else {
            return 'No Conflicts found - Click on Proceed to Continue'
          }
        }
        case 4: {
          return ''
        }
      }

      return ''
    },
    disableButton(): boolean {
      let disable = true

      switch (this.currentStep) {
        case 1: {
          if (this.singleLocation) {
            disable = false
          } else {
            for (let location of this.locationsList) {
              if (location.isSelected) {
                disable = false
                break
              }
            }
          }
          break
        }
        case 2: {
          for (var key in this.allAssets) {
            if (Array.isArray(this.allAssets[key])) {
              for (let asset of this.allAssets[key]) {
                if (asset.isSelected === 1) {
                  disable = false
                  break
                }
              }
            }
          }
          break
        }
        case 3: {
          disable = false
          // if (!this.bulkOverwritingConflicts || this.bulkOverwriteConfirmed) {
          // }
          break
        }
        default: {
          disable = true
        }
      }
      return disable
    },
    bulkOverwritingConflicts(): boolean {
      for (const locationId in this.compiledConflictsData) {
        const conflictData = this.compiledConflictsData[locationId]
        if (!conflictData.skipAllConflicts) {
          return true
        }
      }

      return false
    },
    assetKeys(): string[] {
      const assets = Object.keys(this.allAssets)

      return assets.sort()
    },
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  filters: {
    capitalize: function (value) {
      if (!value) return ''
      value = value.toString()
      return value.split('_').join(' ')
    },
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function () {
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
})
</script>
<style>
.dropdown-toggle::after {
  content: none;
}
.bulk-select.btn.btn-primary:active {
  background-color: #37ca37 !important;
  color: #ffffff !important;
}
.accordion .card-header:after {
  font-family: 'FontAwesome';
  content: '\f068';
  float: right;
}
.accordion .card-header.collapsed:after {
  /* symbol for "collapsed" panels */
  content: '\f067';
}
.hl-snapshot-flow-modal .text-button-row,
.radio,
.hl-snapshot-flow-modal .modal-footer--inner button,
.hl-snapshot-flow-modal .modal-footer--inner .nav {
  display: inline-block;
}
.hl-snapshot-flow-modal .modal-footer--inner button {
  margin-left: 15px;
}
.right-radio {
  margin-left: 15px;
}
.table-radio {
  margin-left: auto;
  float: right;
}

.hl-snapshot-flow-modal .radio > input:disabled + label:before {
  border-color: lightgray;
}
.hl-snapshot-flow-modal .radio > input:disabled + label:after {
  background-color: lightgray;
}

.hl-snapshot-flow-modal .table-radio label {
  line-height: 1.8;
  margin: 5px 0px;
}
.hl-snapshot-flow-modal .table tbody tr {
  border-top: 2px solid #ffffff;
}
.hl-snapshot-flow-modal .table tbody tr td,
.table tbody tr td {
  border: 0px;
}
.hl-snapshot-flow-modal .card .container {
  padding-left: 0px;
  padding-right: 0px;
}
.hl-snapshot-flow-modal .table thead {
  background: #e8f3fe;
}
.hl-snapshot-flow-modal .table thead tr th {
  font-weight: 700;
}
.hl-snapshot-flow-modal .hl_imported-file {
  margin-bottom: 10px;
}
.hl-snapshot-flow-modal .accordion-wrapper .card-title {
  text-transform: capitalize;
}
.hl-snapshot-flow-modal .accordion-wrapper .card-header {
  background: #e8f3fe;
  padding: 5px 10px;
  border: 1px solid #d7dcde;
  cursor: pointer;
}
.hl-snapshot-flow-modal .accordion-wrapper a.card-title {
  margin: 0px;
}
.hl-snapshot-flow-modal .accordion-wrapper .hl_imported-file-map .card {
  border: 1px solid #f2f7fa;
  margin-bottom: 5px;
}
.hl-snapshot-flow-modal .accordion-wrapper .card .card-body {
  padding: 0px !important;
}
.hl-snapshot-flow-modal .accordion-wrapper .table tbody tr td {
  padding: 5px 15px;
}
.hl-snapshot-flow-modal .accordion-wrapper .table tbody tr td:first-child {
  padding-left: 20px;
}
.hl-snapshot-flow-modal table {
  background: #f2f7fa;
}
.hl_import-customers--heading.steps-heading {
  margin-left: 25px;
}
.workflow-folder {
  padding-right: 2px;
}
.asset-title {
  width: 32%;
}
.asset-radio {
  width: 60%;
  height: 30px;
  padding-right: 5px;
  text-align: right;
  padding-top: 2px;
}
</style>
