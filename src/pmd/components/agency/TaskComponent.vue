<template>
  <div class="tab-content">
    <div
      class="tab-pane fade show active"
      id="email"
      role="tabpanel"
      aria-labelledby="email-tab"
    >
      <div class="form-group">
        <UITextInputGroup
          label="Title*"
          type="text"
          placeholder="Task Title"
          v-model="title"
          @change="edited = true"
          v-validate="'required'"
          name="title"
          :error="errors.has('title')"
          :errorMsg="errors.first('title')"
        />
      </div>
      <div class="form-group">
        <UITextAreaGroup
          label="Description"
          rows="7"
          placeholder="Task Description"
          v-model="taskBody"
          @change="edited = true"
          name="taskBody"
        />
        <span v-show="errors.has('taskBody')" class="error"
          >Task description cannot be empty</span
        >
      </div>
      <div class="form-group">
        <UITextLabel>Assign to</UITextLabel>
        <UISelect
          placeholder="Not assigned"
          :options="twUsers"
          :value="assignTo"
          name="assignTo"
          :class="{
            input: true,
          }"
          @change="
            val => {
              assignTo = val
              edited = true
            }
          "
        />
        <span v-show="errors.has('assignTo')" class="error"
          >Task needs to be assigned to a user</span
        >
      </div>
      <div class="form-group">
        <UITextLabel>Due date*</UITextLabel>
        <vue-ctk-date-time-picker
          :locale="getCountryInfo('locale')"
          v-model="dueDate"
          :noClearButton="true"
          :minute-interval="5"
          color="#188bf6"
          enable-button-validate
          class="mt-1"
        />
        <span v-show="errors.has('dueDate')" class="error"
          >Due date is required</span
        >
      </div>
      <div class="form-footer save space-x-2" v-if="enableButtons || edited">
        <UIButton use="outline" @click.prevent="cancel"> Cancel </UIButton>

        <UIButton :loading="loading" @click.prevent="save">
          <span v-if="!loading">Save</span>
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User, getCountryDateFormat, getCountryInfo } from '@/models'
import Task from '@/models/api/Task'
import Datepicker from 'vuejs-datepicker'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import moment from 'moment-timezone'
import lodash from 'lodash'
import { UxMessage } from '@/util/ux_message'

declare var $: any
let unsubscribeUsers: () => void
export default Vue.extend({
  props: ['enableButtons'],
  inject: ['uxmessage'],
  components: {
    Datepicker,
  },
  data() {
    return {
      loading: false,
      edited: false,
      currentTask: undefined as Task | undefined,
      taskBody: '',
      dueDate: moment()
        .add(1, 'days')
        .set({ hour: 8, minute: 0, seconds: 0 })
        .format(),
      assignTo: '',
      currentLocationId: '',
      currentAccountId: '',
      title: '',
      users: [] as User[],
      contactId: undefined as undefined | string,
      getCountryDateFormat: getCountryDateFormat,
      getCountryInfo: getCountryInfo,
    }
  },
  watch: {
    '$route.params.account_id': function (id) {
      this.currentAccountId = id
    },
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
    },
    '$route.query.task_id': async function (id) {
      if (id) {
        this.reset()
        this.setCurrentTask(id)
      }
    },
  },
  computed: {
    twUsers() {
      return this.users.map((user: User) => {
        return {
          value: user.id,
          label: user.fullName,
        }
      })
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  async created() {
    if (this.$router.currentRoute.params.account_id)
      this.currentAccountId = this.$router.currentRoute.params.account_id
    if (this.$router.currentRoute.params.location_id)
      this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.$router.currentRoute.params.contact_id)
      this.contactId = this.$router.currentRoute.params.contact_id
    if (this.$route.query.task_id) {
      this.setCurrentTask(<string>this.$router.currentRoute.query.task_id)
    }
    if (this.currentLocationId) {
      this.users = this.$store.state.users.users.map(u => new User(u))
    }

    if (this.currentAccountId) {
      unsubscribeUsers = (await User.fetchAllAgencyUsers()).onSnapshot(
        snapshot => {
          this.users = snapshot.docs.map(d => new User(d))
        }
      )
    }
  },
  beforeDestroy() {
    if (unsubscribeUsers) unsubscribeUsers()
  },
  methods: {
    async setCurrentTask(id: string) {
      try {
        let locationId = this.currentAccountId||this.currentLocationId
        this.currentTask = await Task.read(locationId, id)
        this.taskBody = this.currentTask.body || ''
        ;(this.dueDate = this.currentTask.dueDate
          ? moment(this.currentTask.dueDate).format()
          : moment()
              .add(1, 'days')
              .set({ hour: 8, minute: 0, seconds: 0 })
              .format()),
          (this.assignTo = this.currentTask.assignedTo || '')
        this.title = this.currentTask.title
        this.edited = true
      } catch (error) {
        this.uxmessage(UxMessage.errorType(error), true)
      }
    },
    reset() {
      this.assignTo = ''
      this.currentTask = undefined
      this.edited = false
      this.loading = false
      this.taskBody = ''
      this.title = ''
      this.dueDate = moment()
        .add(1, 'days')
        .set({ hour: 8, minute: 0, seconds: 0 })
        .format()
      this.errors.clear()
    },
    cancel() {
      this.reset()
      this.$emit('cancelButton')
      const query = Object.assign({}, this.$route.query)
      delete query['task_id']
      if (!lodash.isEqual(query, this.$route.query)) {
        //is_equal avoids navigation error printed in console
        this.$router.replace({ query: query, params: this.$route.params })
      }
    },
    async save() {
      if (this.loading) return

      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }

      this.loading = true
      if (!this.currentTask) {
        if(this.currentAccountId){
          this.currentTask = new Task({
            title: this.title,
            body: this.taskBody,
            assignedTo: this.assignTo,
            dueDate: moment(this.dueDate,['YYYY-MM-DDTHH:mm:ssZ','YYYY-MM-DD hh:mm a']).format(),
            accountId: this.currentAccountId,
            completed: false
          })
        }else{
          this.currentTask = new Task({
            title: this.title,
            body: this.taskBody,
            assignedTo: this.assignTo,
            dueDate: moment(this.dueDate,['YYYY-MM-DDTHH:mm:ssZ','YYYY-MM-DD hh:mm a']).format(),
            contactId: this.contactId,
            completed: false
          })
        }

      } else {
        this.currentTask.title = this.title
        this.currentTask.dueDate = moment(this.dueDate,['YYYY-MM-DDTHH:mm:ssZ','YYYY-MM-DD hh:mm a']).format()
        this.currentTask.assignedTo = this.assignTo
        this.currentTask.body = this.taskBody
      }
      try {
        await this.currentTask.save()
        this.cancel()
        this.$emit('task_saved')
      } catch (error) {
        this.uxmessage(UxMessage.errorType(error), true)
      }

      this.loading = false
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style scoped>
.spinner {
  margin: 10px 0px;
}
</style>
