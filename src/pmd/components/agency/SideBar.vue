<template>
  <div>
    <nav class="hl_navbar --agency" id="navbar">
      <div class="hl_navbar--inner">
        <a class="hl_navbar--logo">
          <img v-if="company && company.logoURL" :src="company.logoURL" />
          <svg
            v-else-if="company"
            xmlns="http://www.w3.org/2000/svg"
            width="28"
            height="26"
          >
            <image
              width="28"
              height="26"
              xlink:href="data:img/png;base64,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"
            />
          </svg>
        </a>
        <button
          @click.prevent="toggleCollapse"
          class="hl_navbar--toggler"
          type="button"
          id="navbar-toggler"
        >
          <span class="navbar-toggler-bar"></span>
          <span class="navbar-toggler-bar"></span>
          <span class="navbar-toggler-bar"></span>
        </button>
        <div
          class="hl_navbar--collapse"
          :style="`display: ${collapse ? 'none' : 'block'}`"
          id="navbar-collapse"
        >
          <ul class="hl_navbar--links list-unstyled" id="nav-links">
            <!-- <router-link :to="{name: 'agency_dashboard'}" id="nav-accounts_list" tag="li" active-class="active">
                            <a>
                                <i class="icon-duplicate"></i>
                                <span>Dashboard</span>
                            </a>
            </router-link>-->

            <router-link
              :to="{ name: 'agency_dashboard' }"
              id="nav-accounts_list"
              v-if="
                company.saasSettings &&
                company.saasSettings.agency_dashboard_visible_to ===
                  'individual'
                  ? user.saasSettings &&
                    user.saasSettings.agency_dashboard_visible
                  : user.role === 'admin'
              "
              tag="li"
              active-class="active"
            >
              <a>
                <i class="fas fa-th-large"></i>
                <span>Agency Dashboard</span>
              </a>
            </router-link>
            <router-link
              :to="{ name: 'saas_dashboard' }"
              id="nav-accounts_list"
              v-if="agencyInSaasPlan"
              tag="li"
              active-class="active"
            >
              <a>
                <i class="far fa-chart-bar"></i>
                <span>SaaS Configurator</span>
              </a>
            </router-link>
            <router-link
              :to="{ name: 'accounts_list' }"
              id="nav-accounts_list"
              tag="li"
              active-class="active"
            >
              <a>
                <i class="icon-pin"></i>
                <span>Accounts</span>
              </a>
            </router-link>
            <router-link
              :to="{ name: 'account_add_template' }"
              id="nav-agency_vertical-snapshots"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="fas fa-draw-polygon"></i>
                <span>Account Snapshots</span>
              </a>
            </router-link>
            <li
              v-if="canManageTemplates"
              id="manage-website-funnel-templates"
              :class="{ active: subIsActive('/website-template-category') }"
            >
              <a
                data-toggle="collapse"
                href="#nav-templates-collapse"
                role="button"
                aria-expanded="false"
                aria-controls="nav-templates-collapse"
              >
                <i class="fas fa-filter"></i>
                <span>Manage Templates</span>
                <i class="caret icon-arrow-down-1"></i>
              </a>
              <div
                class="collapse nav-marketing-links"
                id="nav-templates-collapse"
              >
                <ul>
                  <router-link
                    :to="{
                      name: 'website_template_category_list',
                      params: { type: websiteTemplateCategory },
                    }"
                    tag="li"
                    active-class="active"
                  >
                    <a>Websites</a>
                  </router-link>
                  <router-link
                    :to="{
                      name: 'website_template_category_list',
                      params: { type: funnelTemplateCategory },
                    }"
                    tag="li"
                    active-class="active"
                  >
                    <a>Funnels</a>
                  </router-link>
                </ul>
              </div>
            </li>
            <!-- <router-link
              v-if="canManageTemplates"
              :to="{name: 'website_template_category_list'}"
              id="nav-agency_website-template-category"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="fas fa-globe"></i>
                <span>Manage Funnel Templates</span>
              </a>
            </router-link>-->
            <template v-if="company && company.yextReseller">
              <router-link
                :to="{ name: 'reselling' }"
                id="nav-agency_reselling"
                tag="li"
                active-class="active"
              >
                <a href>
                  <ResellingIcon class="sidebar-icon" />
                  <span>Reselling</span>
                  <span class="hl_new_badge">new</span>
                </a>
              </router-link>
            </template>
            <router-link
              :to="{ name: 'marketplace-frame' }"
              id="nav-agency_services"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="far fa-paper-plane"></i>
                <span>Marketplace</span>
              </a>
            </router-link>
            <router-link  v-if="user.role === 'admin'"
              :to="{ name: 'affiliate-dashboard-frame' }"
              id="nav-agency_services"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="fas fa-users"></i>
                <span>Affiliate Portal</span>
              </a>
              </router-link>
            <router-link
              :to="{ name: 'affiliates' }"
              id="nav-affiliates"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="far fa-handshake"></i>
                <span>Partners</span>
              </a>
            </router-link>
            <li
              id="nav-university"
              active-class="active"
            >
              <a :href="universityUrl" target="_blank">
                <i class="fas fa-graduation-cap"></i>
                <span>University</span>
              </a>
            </li>
            <router-link
              :to="{ name: 'saas_education' }"
              id="nav-saas-education"
              tag="li"
              active-class="active"
            >
              <a href>
                <i class="fas fa-book-reader"></i>
                <span>SaaS Education</span>
              </a>
            </router-link>
            <router-link
              :to="{ name: 'saas_fasttrack' }"
              id="nav-saas-education"
              tag="li"
              v-if="agencyInSaasPlan"
              active-class="active"
            >
              <a href>
                <!-- <i class="fas fa-book-reader"></i> -->
                <i class="fas fa-fast-forward"></i>
                <span>SaaS FastTrack</span>
              </a>
            </router-link>
            <router-link
              :to="{ name: 'agency_ideas' }"
              id="nav-settings"
              tag="li"
              active-class="active"
            >
              <a>
                <i class="icon-ok-circle"></i>

                <span>Ideas</span>
              </a>
            </router-link>
            <!-- No need for v-if since this is already a Agency-only SideBar -->
            <!--<router-link
              :to="{ name: 'help' }"
              id="nav-settings"
              tag="li"
              active-class="active hl_help-article">
             <a>
                <i class="fas fa-question"></i>
                 <span>Help</span>
              </a>
            </router-link> -->
            <!-- <router-link
              :to="{ name: 'agency_resources' }"
              id="nav-agency_sales-resources"
              tag="li"
              active-class="active"
            >
              <a>
                <i class="icon-document-text"></i>
                <span>Sales Resources</span>
              </a>
            </router-link> -->

            <template v-if="company">
              <li
                v-for="(link, index) in company.customMenuLinks"
                :key="index"
                v-show="link.show_on_company"
                @click="customMenuLinkClicked(link)"
                :id="`nav-custom-menu-link-${link.id}`"
                tag="li"
                :class="$route.params.id === link.id ? 'active' : null"
              >
                <a>
                  <i
                    v-if="link.icon.value"
                    class="sm-button"
                    :style="`--fa:'\\${link.icon.value.unicode}';--ff:'${link.icon.value.fontFamily}'`"
                  />
                  <span>{{ link.title }}</span>
                </a>
              </li>
            </template>

            <router-link
              :to="{ name: 'agency_profile_settings' }"
              id="nav-settings"
              tag="li"
              active-class="active"
            >
              <a>
                <i class="icon-settings-1"></i>
                <span>Settings</span>
              </a>
            </router-link>
          </ul>
        </div>
      </div>
    </nav>
    <AnnouncementModal v-if="showWorkflowAnnouncement" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { CompanyState, UserState } from '../../../store/state_models'
import { Company, User } from '../../../models'
import config from '@/config'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'
import AnnouncementModal from '../../components/workflow/modals/AnnouncementModal.vue'
import ResellingIcon from '@/assets/pmd/img/icon-reselling.svg'


export default Vue.extend({
  components: {
    AnnouncementModal,
    ResellingIcon
  },
  data: () => {
    return {
      collapse: false,
      websiteTemplateCategory: WebsiteTemplateCategoryType.Websites,
      funnelTemplateCategory: WebsiteTemplateCategoryType.Funnels,
      universityUrl: 'https://university.gohighlevel.com?loginCode=UP$D6e',
    }
  },
  computed: {
    showWorkflowAnnouncement() {
      return false
    },
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    canManageTemplates(): boolean {
      if (!this.company) return false

      const permissionForManagingTemplates =
        config.companyIdForManagingTemplates === this.company.id
      return permissionForManagingTemplates
    },
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
  },
  mounted() {
    if (this.user) {
      this.universityUrl = `${this.universityUrl}&email=${this.user.email}`
    }
  },
  methods: {
    toggleCollapse() {
      this.collapse = !this.collapse
    },
    subIsActive(input: string): boolean {
      const paths: string[] = Array.isArray(input) ? input : [input]

      return paths.some(path => {
        return this.$route.path.indexOf(path) !== -1
      })
    },
    customMenuLinkClicked(link: any) {
      if (link.url.includes('{{')) {
        this.$uxMessage(
          'error',
          `There was an error compiling the provided Custom Values. Please make sure they exist inside the Location and that you're not on the Agency sidebar.`
        )
      } else {
        switch (link.open_mode) {
          case 'iframe':
            this.$router.push({
              name: 'custom_menu_link',
              params: { id: link.id },
            })
            break
          case 'new_tab':
            window.open(link.url, '_blank')
            break
        }
      }
    },
  },
})
</script>
<style scoped>
.hl_navbar--links > li span {
  white-space: nowrap;
}
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}

.sidebar-icon {
  margin-right: 20px;
  opacity: 0.5;
}
.active .sidebar-icon {
  opacity: 1;
}
</style>
