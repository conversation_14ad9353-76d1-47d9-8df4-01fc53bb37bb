<template>
  <div
    class="modal fade hl_website-add-new-website-template-category--modal"
    id="new-website-template-category"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Create New {{ templateCategoryTitle }} Template</h2>
            <button
              @click="hideModal()"
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                :placeholder="`${templateCategoryTitle} Template Name`"
                v-model="templateName"
                v-validate="'required'"
                name="templatename"
                label="Template name"
                :error="errors.has('templatename')"
                :errorMsg="'You need to give the template a name.'"
              />
            </div>
            <div class="form-group">
              <UITextAreaGroup
                type="text"
                placeholder="Description for Template"
                label="Description"
                v-model="description"
                name="description"
              ></UITextAreaGroup>
            </div>
            <div class="form-group">
              <UITextInputGroup
                type="url"
                placeholder="URL for live preview of template"
                v-model="livePreview"
                name="livePreview"
                label="Live Preview"
              />
            </div>
            <div class="form-group">
              <UITextInputGroup
                type="url"
                placeholder="Image URL for preview"
                v-model="image"
                name="image"
                label="Image Preview"
              />
            </div>
            <div class="form-group">
              <UITextLabel>Account</UITextLabel>
              <vSelect
                :options="locations"
                label="name"
                v-model="locationPicked"
                :clearable="false"
                placeholder="Select account"
                v-validate="'required'"
                name="location"
                data-vv-as="Location"
                :loading="loading.locations"
                class="mt-1"
              >
                <template #spinner="{ loading }">
                  <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                </template>
              </vSelect>
              <span v-show="errors.has('location')" class="--red">You need to pick an account.</span>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <UIButton type="button" use="outline" data-dismiss="modal">Cancel</UIButton>
            <div style="display: inline-block;position: relative;">
              <UIButton
                type="submit"
                :disabled="submitDisabled"
                @click="createTemplate()"
                :loading="processing"
              >Create New Template</UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import vSelect from 'vue-select'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    templateCategoryId: {
      type: String,
      required: true
    },
    templateCategoryTitle: {
      type: String
    }
  },
  components: {
    vSelect
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  data() {
    return {
      locationPicked: undefined as { [key: string]: any } | undefined,
      templateName: '',
      description: '',
      livePreview: '',
      image: '',
      processing: false,
      locations: [],
      loading: {
        locations: false
      }
    }
  },
  computed: {
    submitDisabled: function(): boolean {
      return !this.templateName || this.processing
    }
  },
  methods: {
    hideModal() {
      this.$emit('hide')
    },
    async createTemplate() {
      const result = await this.$validator.validateAll()
      if (!result || !this.locationPicked) {
        return false
      }

      this.processing = true
      try {
        const {
          data: { id: websiteTemplateId }
        } = await this.$http.post('/website-template/create', {
          name: this.templateName,
          description: this.description,
          live_preview: this.livePreview,
          image: this.image,
          location_id: this.locationPicked.id,
          category_id: this.templateCategoryId
        })

        console.log('got new website template id --> ', websiteTemplateId)
        this.$emit('newTemplateId', websiteTemplateId)
        this.hideModal()
      } catch (error) {
        console.error('error while creating new template --> ', error)
        alert(
          'Error while creating new template! Make sure to have only one funnel/website in the selected location.'
        )
      } finally {
        this.processing = false
      }
    }
  },
  watch: {
    async showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.processing = false
        this.templateName = ''
        this.description = ''
        this.livePreview = ''
        this.image = ''
        this.locationPicked = undefined

        this.loading.locations = true
        this.locations = await this.$store.dispatch('locations/getAll')
        this.loading.locations = false

        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
