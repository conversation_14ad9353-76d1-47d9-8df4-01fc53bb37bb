<template>
  <div>
    <HLWarning
      :showAlert="showAlert && !isDismissed"
      heading="Your agency is over limit!"
      v-bind:title="
        `You have  ${locationCount} active locations while your plan only allows for ${
          allowedLocations === 2 ? '1' : allowedLocations
        } location.`
      "
      description="Please take a moment to upgrade your account."
      @closeWarning="closeAlert"
    >
      <template v-slot:action>
        <span v-if="************* >= +new Date()" style="font-weight: 600;">
          You will be auto-upgraded on September 15<sup>th</sup> 2020.</span
        >
        <div class="alert-actions">
          <button
            type="button"
            class="btn btn-success upgrade-btn"
            @click="handlePlanUpgrade()"
          >
            Upgrade Now
          </button>
          <button type="button" class="btn btn-primary" @click="closeAlert">
            Dismiss
          </button>
        </div>
        <div class="info-message">
          Only Agency Admins can see this warning. For more info
          <contact-support />
        </div>
      </template>
    </HLWarning>

    <upgrade-modal
      :company="company"
      v-if="showUpgradeModal"
      :show="showUpgradeModal"
      source="special-alert"
      @close="showUpgradeModal = false"
    />
  </div>
</template>

<script>
import { Company, User } from '@/models'
import UpgradeModal from '@/pmd/components/agency/billing/UpgradeModalv2.vue'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import { trackGaEvent } from '@/util/helper'
import HLWarning from '@/pmd/components/common/HLWarning.vue'

export default {
  data() {
    return {
      showAlert: false,
      locationCount: '',
      allowedLocations: 2,
      // company: Company,
      showUpgradeModal: false,
    }
  },
  components: {
    ContactSupport,
    UpgradeModal,
    HLWarning,
  },
  mounted() {
    setTimeout(async () => {
      if (
        this.company &&
        this.company.status &&
        this.company.status === 'active_overused' &&
        this.user &&
        this.user.type === 'agency' &&
        this.user.role === 'admin'
      ) {
        await this.fetchLocationsCount()
        if (this.locationCount > this.allowedLocations) {
          this.showAlert = true
          trackGaEvent(
            'BillingUpgradeInitiate',
            this.company.id,
            'Upgrade Alert: Shown',
            1
          )
        }
      }
    }, 5000)
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    company() {
      return this.$store.state.company.company
    },
    isDismissed() {
      if (
        this.company &&
        this.company.upgrade_alert_dismissed &&
        this.$route.path
      ) {
        if (
          (this.company.upgrade_alert_dismissed.seconds + 3600) * 1000 >
          +new Date()
        ) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    async closeAlert() {
      // this.showAlert = false;
      let d = new Date()
      await Company.collectionRef()
        .doc(this.company.id)
        .update({
          upgrade_alert_dismissed: d,
        })
      trackGaEvent(
        'BillingUpgradeInitiate',
        this.company.id,
        'Upgrade Alert: Dismissed',
        1
      )
      this.showAlert = false
    },
    handlePlanUpgrade() {
      this.showAlert = false
      this.showUpgradeModal = true
      trackGaEvent(
        'BillingUpgradeInitiate',
        this.company.id,
        'Upgrade Alert: Upgrading',
        1
      )
    },
    async fetchLocationsCount() {
      let result = await this.$http.post('/stripe/sync_locations', {
        company_id: this.company ? this.company.id : '',
      })
      this.locationCount = result.data.totalActiveLocations
      this.allowedLocations = result.data.allowedLocations
    },
  },
}
</script>

<style scoped>
.alert-actions {
  margin: 20px 0px;
}
.upgrade-btn {
  margin-right: 20px;
}
.info-message {
  border-left: 4px solid #198bf5;
  padding: 8px;
  background-color: #eaf3fe;
}
</style>
