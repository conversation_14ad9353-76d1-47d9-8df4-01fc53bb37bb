<template>
  <div>
    <modal v-if="showPricingModal && show" @close="$emit('close')" maxWidth="880" :showCloseIcon="true">
      <div>
        <div class="upgrade-modal__back-btn"
            v-if="showSalesCalendar"
            @click="showSalesCalendar = false"
          >
            <i class="fas fa-arrow-left"/>
          </div>
        <div class="upgrade-modal__header" v-if="activePlanLevel === 0">{{type === 'reactivate'? 'Reactivate' : 'Upgrade'}} your account</div>
        <div class="upgrade-modal__shimmer" v-if="activePlanLevel === 0">
          <div class="upgrade-modal__alert--danger" v-if="errorMsg">{{errorMsg}}
              <span class="open-support-widget" @click="contactSupport">contact support</span> !!
          </div>
            <upgrade-modal-shimmer v-else/>
        </div>
        <contact-sales v-else-if="showSalesCalendar"/>
        <pricing-table v-else :current-level="activePlanLevel" @upgrade="handleUpgrade" :company="company" :isActivePlanLVRG="isActivePlanLVRG" :type="type"/>
      </div>
    </modal>
    <modal v-if="showConfirmUpgradeModal && show" @close="$emit('close')" maxWidth="585" :showCloseIcon="showCloseModalBtn || type=== 'annual-upgrade'" :noBackdropClose="true">
      <div >
        <div class="upgrade-modal__header" v-if="type=== 'annual-upgrade'">
          Upgrade from <span class="plan-name--upgrade-from">Monthly</span> to <span class="plan-name--upgrade-to">Annual</span> Subscription
        </div>
        <div class="upgrade-modal__header" v-else>
          <span v-if="type=== 'reactivate' && activePlanLevel === -1">Reactivate Billing for {{company.name}}</span>
          <span v-else>
            Upgrade from <span class="plan-name--upgrade-from">{{isActivePlanLVRG? 'LVRG': $options.agencyPlans[activePlanLevel -1].label}}</span> to <span class="plan-name--upgrade-to">{{$options.agencyPlans[upgradePlanLevel -1].label}}</span>
          </span>
          <div class="upgrade-modal__back-btn"
            v-if="activeModalBodyType === 'purchase-summary'"
            @click="handleBack"
          >
            <i class="fas fa-arrow-left"/>
          </div>
        </div>
        <div class="upgrade-modal__body" :class="{'--expanded': activeModalBodyType === 'tshirt-form'}">
          <purchase-summary v-if="activeModalBodyType === 'purchase-summary'"
            :upgradeLevel="upgradePlanLevel"
            :planDuration="upgradePlanDuration"
            :company="company"
            :activePlan="activePlan"
            :type="type"
            @success="handleOnSuccess"
            @failed="handleOnFailed"
            @failedSCA="handleOnFailedSCA"
          />
          <transaction-successful v-else-if="activeModalBodyType === 'transaction-successful'"
            :showCloseBtn="showCloseModalBtn"
            :type="type"
            @close="$emit('close')"
            />
          <transaction-failed v-else-if="activeModalBodyType === 'transaction-failed'"
            @retry="handleOnRetry"
            :error="failedErrorMsg"
          />
          <transactionFailedSCA v-else-if="activeModalBodyType === 'transaction-failed-sca'"
            @retry="handleOnRetry"
            :error="failedErrorMsg"
          />
          <tshirt-form v-else-if="activeModalBodyType === 'tshirt-form'" :company="company"
            @submit="handlleSubmitShirt"/>
          <thank-you v-else-if="activeModalBodyType === 'thank-you'"
            @close="$emit('close')"/>
        </div>
      </div>
    </modal >
  </div>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

import { trackGaEvent } from "@/util/helper";

import PricingTable from "./upgrade_modal/PricingTable";
import ContactSales from "./upgrade_modal/ContactSales";
import PurchaseSummary from "./upgrade_modal/PurchaseSummary";
import TransactionSuccessful from "./upgrade_modal/TransactionSuccessful";
import TransactionFailed from "./upgrade_modal/TransactionFailed";
import TransactionFailedSCA from "./upgrade_modal/TransactionFailedSCA";
import TshirtForm from "./upgrade_modal/TshirtForm";
import ThankYou from "./upgrade_modal/ThankYou";
import { agencyPlans } from "./upgrade_modal/agencyPlans";

export default {
  props: ['show','company','source', 'type'], // type:[annual-upgrade' || 'reactivate']
  components: {
    Modal,
    PricingTable,
    ContactSales,
    PurchaseSummary,
    TransactionSuccessful,
    TransactionFailed,
    TransactionFailedSCA,
    TshirtForm,
    ThankYou,
    UpgradeModalShimmer
  },
  agencyPlans: agencyPlans,
  data(){
    return{
        activePlan: '',
        activePlanName: '', // '97', '297', '597', '237', (actual)
        // activePlanStatus: '', // 'past_due' ...
        isActivePlanLVRG: false,
        activePlanLevel: 0,
        upgradePlanLevel: 0,
        upgradePlanDuration: 'monthly', // 'monthly', 'annual'
        processing: false,
        upgraded: false,
        isAdmin: true,

        errorMsg: '',
        failedErrorMsg: '',
        showPricingModal: true,
        showConfirmUpgradeModal: false,
        activeModalBodyType: 'purchase-summary',
        showCloseModalBtn: false,
        showSalesCalendar: false
    }
  },
  created(){
    this.getCurrentPlan();
    trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Step 1', 1);
  },
  methods:{
      async getCurrentPlan(){
        this.errorMsg = '';
        try {
            let activePlan = await this.$http.get(`/api/stripe/agency_plan?company_id=${this.company.id}`);
            if(activePlan.data && activePlan.data.plan){
                // if (activePlan.data.plan.status === 'past_due') {
                //   this.activePlanStatus = 'past_due';
                // }
                this.activePlan = activePlan.data.plan;
                this.activePlanName = activePlan.data.plan.name.split('_')[2];
                switch(this.activePlanName) {
                  case '237':
                    this.isActivePlanLVRG = true;
                  case '97':
                    this.activePlanLevel = 1;
                    break;
                  case '297':
                    this.activePlanLevel = 2;
                    break;
                  case '497':
                    this.activePlanLevel = 3;
                    break;
                  default:
                    // this.notUpgradable();
                    throw new Error('No valid active plan found !!');
                }
            } else {
                // this.notUpgradable();
                throw new Error('No valid active plan found !!');
            }
        } catch (e) {
            if(this.type === 'reactivate'){
              this.activePlanLevel = -1;
            } else{
              this.notUpgradable();
            }
        }
        if(this.type === 'annual-upgrade') {
          if(this.isActivePlanLVRG)
            this.notUpgradable();
          this.handleUpgrade(this.activePlanLevel,'annual');
        }
    },
    notUpgradable(){
      this.upgradePlan = 'NA';
      this.activePlanLevel = 0;
      this.errorMsg = 'We are unable to upgrade your account. Please ';
      trackGaEvent('LevelUp Upgrade', this.company.id, 'Not Upgradable', 1);
    },
    contactSupport(){
        this.$emit('close');
        window.fcWidget.open();
    },
    handleUpgrade(newPlanLevel, selectedDuration){
      this.upgradePlanLevel = newPlanLevel;
      this.upgradePlanDuration = selectedDuration;
      trackGaEvent('LevelUp Upgrade', this.company.id, `Requested Upgrade to Level: ${newPlanLevel}`, 1);
      if(newPlanLevel === 4){
        this.showSalesCalendar = true;
        // window.open('https://speakwith.us/michael','_blank');
      } else {
        setTimeout( ()=>{
          this.showPricingModal = false;
          this.activeModalBodyType = 'purchase-summary';
          this.showConfirmUpgradeModal = true;
        }, 100)
      }
    },
    handleOnSuccess(newPlan){
      this.activeModalBodyType = 'transaction-successful';
      this.$emit('success',newPlan);
      trackGaEvent('LevelUp Upgrade', this.company.id, `Successful Upgraded to Level: ${this.upgradePlanLevel}`, 1);
      if(this.upgradePlanLevel !== 3){
        setTimeout( ()=>{
          this.showCloseModalBtn = true;
        },2000)
      } else {
        // show T-shirt modal
        setTimeout( ()=>{
          this.activeModalBodyType = 'tshirt-form';
        },4000)
      }
    },
    handleOnFailed(err){
      this.failedErrorMsg = err;
      this.activeModalBodyType = 'transaction-failed';
      trackGaEvent('LevelUp Upgrade', this.company.id, `FAILED Upgrade to Level: ${this.upgradePlanLevel} | ${agencyPlans[this.upgradePlanLevel - 1].label} [error: ${err}]`, 1);
    },
    handleOnFailedSCA(err){
      this.failedErrorMsg = err;
      this.activeModalBodyType = 'transaction-failed-sca';
      trackGaEvent('LevelUp Upgrade', this.company.id, `FAILED Upgrade to Level: ${this.upgradePlanLevel} | ${agencyPlans[this.upgradePlanLevel - 1].label} [error: ${err}]`, 1);
      trackGaEvent('LevelUp Upgrade', this.company.id, `FAILED [SCA] Upgrade to Level: ${this.upgradePlanLevel} | ${agencyPlans[this.upgradePlanLevel - 1].label} [error: ${err}]`, 1);
    },
    handleOnRetry(){
      this.activeModalBodyType = 'purchase-summary';
      this.showCloseModalBtn = false;
      trackGaEvent('LevelUp Upgrade', this.company.id, `Retrying Upgrade to Level: ${this.upgradePlanLevel} | ${agencyPlans[this.upgradePlanLevel - 1].label}`, 1);
    },
    handlleSubmitShirt(){
      this.activeModalBodyType = 'thank-you';
      this.showCloseModalBtn = true;
    },
    handleBack(){
      this.showConfirmUpgradeModal = false;
      setTimeout( ()=>{
        this.showPricingModal = true;
      }, 300)
    }
  }

}
</script>

<style>
.upgrade-modal__shimmer{
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
}
.upgrade-modal__alert--danger{
    border-left: 2px solid #e93d3d;
    padding: 6px 12px;
    margin: 16px 0px 8px;
    background-color: #fff3f3;
}
.open-support-widget{
    cursor: pointer;
    color: #188bf6;
    font-weight: 600;
}

.upgrade-modal__header{
  position: relative;
  text-align: center;
  padding: 32px;
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  color: #373737;
  border-bottom: 1px solid #e0e0e0;
}
.upgrade-modal__header .plan-name--upgrade-from{
  color: #828282;
}
.upgrade-modal__header .plan-name--upgrade-to{
  color: #2F80ED;
}
.upgrade-modal__back-btn{
  position: absolute;
  top: 24px;
  left: 24px;

  height: 32px;
  width: 32px;

  background-color: #F2F2F2;
  border-radius: 50%;
  cursor: pointer;

  font-size: 16px;
  line-height: 16px;
  padding: 8px;
  transition: all 0.3s ease-in-out;
}
.upgrade-modal__back-btn:hover{
  background-color: #d2d2d2;
}
.upgrade-modal__body{
  height: 460px;
}
.upgrade-modal__body.--expanded{
  height: unset;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}
</style>
