<template>
  <div class="transaction-status--sca">
    <billing-method :show-heading="false" :show-cancel="true" :show-sca-warning="true" save-button-text="Update" @cardChangedTimeout="$emit('retry')" @cancel="$emit('retry')"/>
  </div>
</template>

<script>
import BillingMethod from "@/pmd/pages/agency/billing/BillingMethod.vue";
export default {
  props: ['error'],
  components: {
    BillingMethod
  },
}
</script>

<style scoped>
.transaction-status--sca{
  text-align: center;
  margin: auto;
  width: 100%;
}
</style>
