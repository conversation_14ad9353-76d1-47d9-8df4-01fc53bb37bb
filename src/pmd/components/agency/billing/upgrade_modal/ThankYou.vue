<template>
  <div class="transaction-status">
    <SuccessIcon
      class="transaction-status__icon"
    />
    <div class="transaction-status__label">Preference Saved</div>
    <div class="transaction-status__heading">Thank you!</div>
    <div
      class="transaction-status__action-btn btn btn-success"
      @click="$emit('close')"
    >
      Close
    </div>
  </div>
</template>

<script>
import SuccessIcon from '@/assets/pmd/img/billing/upgrade-modal/success-icon.svg'
export default {
  props: ['showCloseBtn'],
  components: {
    SuccessIcon
  }
}
</script>

<style>
.transaction-status {
  padding: 50px;
  text-align: center;
  margin: auto;
  width: fit-content;
}
.transaction-status__icon {
  /* font-size: 100px; */
  width: 80px;
  height: 80px;
  margin: auto;
}
.transaction-status__label {
  line-height: 20px;
  font-size: 16px;
  color: #4f4f4f;
  margin-top: 20px;
  font-weight: 700;
}
.transaction-status__heading {
  margin-top: 80px;
  font-size: 48px;
  line-height: 56px;
  text-align: center;

  /* Gray 2 */

  color: #4f4f4f;
}
.transaction-status__action-btn {
  width: 140px;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  margin: 50px auto 0px;

  background-color: #27ae60;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.transaction-status__action-btn:hover {
  background-color: #249151;
}
</style>
