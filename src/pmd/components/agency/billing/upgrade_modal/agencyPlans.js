const agencyPlans = [
  {
    level: 1,
    label: 'Starter',
    amount: '97',
    amount_annual: '81',
    highlights: [
      'Unlimited Users',
      '1 Business Account'
    ],
    features: [
      {
        label: 'Workflow Builder',
        upcoming: false,
      },
      {
        label: 'Campaign Builder',
        upcoming: false
      },
      {
        label: '2 way SMS',
        upcoming: false
      },
      {
        label: '2 Way Email',
        upcoming: false
      },
      {
        label: 'Pipeline Management',
        upcoming: false
      },
      {
        label: 'Landing Page Builder',
        upcoming: false
      },
      {
        label: 'Website Builder',
        upcoming: false
      },
      {
        label: 'Standard Integrations',
        upcoming: false
      },
      {
        label: 'Calendar',
        upcoming: false
      },
      {
        label: 'Email Builder',
        upcoming: false
      },
      {
        label: 'Power Dialer',
        upcoming: false
      },
      {
        label: 'Call Tracking',
        upcoming: false
      },
      {
        label: 'Google Reporting',
        upcoming: false
      },
      {
        label: 'Facebook Reporting',
        upcoming: false
      },
      {
        label: 'Attribution Reporting',
        upcoming: false
      },
      {
        label: 'Reputation management',
        upcoming: false
      },
      {
        label: 'Basic CRM Functionality',
        upcoming: false
      },
      {
        label: 'Form Builder',
        upcoming: false
      },
      {
        label: 'Survey Builder',
        upcoming: false
      },
      {
        label: 'Call Recording',
        upcoming: false
      },
      {
        label: 'Website Widget',
        upcoming: false
      },
      {
        label: 'Memberships',
        upcoming: false
      },
      {
        label: 'Intent Trigger',
        upcoming: false
      },
      {
        label: 'Email Support',
        upcoming: false
      },
    ]
  },
  {
    level: 2,
    label: 'Freelancer',
    amount: '297',
    amount_annual: '248',
    highlights: [
      'Unlimited Users',
      'Unlimited Business Account'
    ],
    features: [
      {
        label: 'Whitelabel Desktop',
        upcoming: false
      },
      {
        label: 'Memberships',
        upcoming: false
      },
      {
        label: 'Basic API Access',
        upcoming: false
      },
      {
        label: 'Chat Support',
        upcoming: false
      },
      {
        label: 'Phone Support',
        upcoming: false
      },
      {
        label: 'Partner Program',
        upcoming: false
      },
    ]
  },
  {
    level: 3,
    label: 'Agency Pro',
    amount: '497',
    amount_annual: '414',
    highlights: [
      'Unlimited Users',
      'Unlimited Business Account',
      'Unlimited SaaS'
    ],
    features: [
      {
        label: 'SaaS Mode',
        upcoming: false
      },
      {
        label: 'Email / Phone / Text Rebilling',
        upcoming: false
      },
      {
        label: 'Split Testing',
        upcoming: false
      },
      // {
      //   label: 'Whitelabel Support',
      //   upcoming: false
      // },
      {
        label: 'Advanced API Access',
        upcoming: false
      },
      {
        label: 'Agent Reporting',
        upcoming: false,
        // new: true
      },
      {
        label: 'AI Conversational Bot',
        upcoming: false,
      },
      // {
      //   label: 'Advanced CDN',
      //   upcoming: true
      // },

      // {
      //   label: 'Advanced CRM Functionality',
      //   upcoming: false
      // },
      // {
      //   label: 'Tracking Pixels',
      //   upcoming: false
      // },
      // {
      //   label: 'Advanced Integrations',
      //   upcoming: false
      // },
      // {
      //   label: 'Inbound Calling',
      //   upcoming: false
      // },
      // {
      //   label: 'Lead Distrubution',
      //   upcoming: false
      // },
      // {
      //   label: 'Blogging',
      //   upcoming: false
      // },
    ]
  },
  // {
  //   level: 4,
  //   label: 'Enterprise',
  //   amount: 'Custom',
  //   amount_annual: 'Custom',
  //   highlights: [
  //     'Unlimited Users',
  //     'Unlimited Business Account',
  //     'Unlimited SaaS',
  //     'Scalable Pricing'
  //   ],
  //   features: [
  //     {
  //       label: 'Custom Development',
  //       upcoming: false
  //     },
  //     {
  //       label: 'Dedicated Support',
  //       upcoming: false
  //     },
  //     {
  //       label: 'First In Line Support',
  //       upcoming: false
  //     },
  //     {
  //       label: 'Dedicated Account Manager',
  //       upcoming: false
  //     },
  //     {
  //       label: 'HIPAA Compliant',
  //       upcoming: false
  //     },
  //     {
  //       label: 'Custom Mobile App',
  //       upcoming: false
  //     },
  //     {
  //       label: 'Custom Setup and Consultation',
  //       upcoming: false
  //     },
  //   ]
  // }
]

export { agencyPlans }
