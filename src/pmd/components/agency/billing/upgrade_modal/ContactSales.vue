<template>
        <div class="contact-sales">
            <div class="modal-heading-wrap">
                <div class="modal-heading-content">
                    <h4>Schedule a call with Highlevel Representative</h4>
                    <p>Get customized solution as per the requirement of your bussiness.</p>
                </div>
            </div>
            <iframe :src="`https://link.gohighlevel.com/widget/appointment/service/paulson?group=marketplacesales${userQuery}`"
                style="width:100%" :height="calendarHeight" frameborder="0" scrolling="auto"
                @load="iframeLoaded"
                />
        </div>
</template>

<script>
import config from '@/config';
import { trackGaEvent } from "@/util/helper";


export default {
    data(){
      return{
        user: {},
        calendarHeight: 800,
      }
    },
    computed:{
        userQuery(){
            let userData = {
                first_name: this.user.first_name || '',
                last_name: this.user.last_name || '',
                email: this.user.email || '',
                phone: this.user.phone || ''
            }
            return this.jsonToQueryString(userData);
        }
    },
    mounted(){
      this.user = this.$store.state.user.user;
    },
    methods: {
        jsonToQueryString(json) {
            return '&' +
                Object.keys(json).map(function(key) {
                    return encodeURIComponent(key) + '=' +
                        encodeURIComponent(json[key]);
                }).join('&');
        },
        iframeLoaded(){
            window.addEventListener('message', this.handleIframeMessage)
        },
        handleIframeMessage(e){
            if(e.data[0] === 'msgsndr-booking-complete'){
                console.log('Call booked !!');
            }
            if(e.data[0] === 'highlevel.setHeight'){
                this.calendarHeight = e.data[1].height;
            }
        },
    }
}
</script>

<style scoped>
.contact-sales {
  max-height: calc(100vh - 64px);
  overflow-y: auto;
}
.modal-heading-wrap {
    padding: 30px 30px 16px;
}
.modal-heading-content {
	text-align: center;
	/* padding-bottom: 20px;
	border-bottom: 2px solid #f2f7fa; */
}
</style>
