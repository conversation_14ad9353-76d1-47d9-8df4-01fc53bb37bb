<template>
    <modal v-if="show" @close="$emit('close')" maxWidth="700" :showCloseIcon="true">
        <div >
            <div class="modal-heading-wrap">
                <div class="modal-heading-content">
                    <h4>Upgrade your account</h4>
                    <!-- <p>Please upgrade your account to unlock more features</p> -->
                    <p v-if="upgradePlan === '297' && this.source==='help-drawer'">To use live chat or to create more locations, please upgrade to a higher plan</p>
                    <p v-else-if="upgradePlan === '297'">To create more locations
                      <!-- or to use live chat,  -->
                      please upgrade to a higher plan</p>
                    <p v-else-if="upgradePlan === '597'">To get quick priority support, please upgrade to a higher plan</p>
                </div>
            </div>
            <div class="modal-body" v-if="upgradePlan === ''">
                <upgrade-modal-shimmer style="width:100%"/>
            </div>
            <div class="modal-body" v-else-if="upgradePlan === 'NA'">
                <div class="section-info danger" v-if="errorMsg">{{errorMsg}}
                    <span class="open-support-widget" @click="contactSupport">contact support</span> !!
                </div>
            </div>
            <div class="modal-body" v-else>

                <div class="marketplace-info" v-if="upgradePlan === '597' && durationSelected && !upgraded">
                    <div class="form-group">
                        <label>Agency Name*</label>
                        <input
                            type="text"
                            class="form-control"
                            placeholder="Agency name"
                            v-validate="'required'"
                            name="organizationName"
                            v-model="organizationName"
                        >
                        <span v-show="errors.has('organizationName')" class="error">Agency name required</span>
                    </div>

                    <div class="form-group">
                        <label>Full Address*</label>
                        <input
                            type="text"
                            class="form-control"
                            placeholder="Agency address"
                            v-validate="'required'"
                            name="address"
                            v-model="address"
                        >
                        <span v-show="errors.has('address')" class="error">Address required</span>
                    </div>
                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>Contact Person Name*</label>
                            <input
                                type="text"
                                class="form-control"
                                placeholder="Conatct Person name"
                                v-validate="'required'"
                                name="contactName"
                                v-model="userName"
                            >
                            <span v-show="errors.has('contactName')" class="error">Contact name required</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>Role/Title*</label>
                            <input
                                type="text"
                                class="form-control"
                                placeholder="Designation"
                                v-validate="'required'"
                                name="designation"
                                v-model="designation"
                            >
                            <span v-show="errors.has('designation')" class="error">Role/Title is required</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>Phone*</label>
                            <PhoneNumber
                                class="form-control"
                                placeholder="Phone"
                                v-validate="'required|phone'"
                                name="phone"
                                v-model="phone"
                            />
                            <span v-show="errors.has('phone')" class="error">{{ errors.first('phone') }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>Email*</label>
                            <input
                                type="text"
                                class="form-control"
                                placeholder="Email"
                                v-validate="'required|email'"
                                name="email"
                                v-model="email"
                            >
                            <span v-show="errors.has('email')" class="error">{{ errors.first('email') }}</span>
                        </div>
                    </div>
                </div>
                <div class="plan-container" v-else>
                    <div class="plan-label" v-if="upgradePlan === '297'">Agency Plan</div>
                    <div class="plan-label" v-else-if="upgradePlan === '597'">Premium Agency Plan</div>
                    <ul class="price-card--features--list">
                        <li class="price-card--features--item">
                            More Locations/ Subaccounts
                        </li>
                        <li class="price-card--features--item">
                            Twilio integration
                        </li>
                        <li class="price-card--features--item ">
                            Mailgun Integration
                        </li>
                        <li class="price-card--features--item">
                            24/7 Live Support Chat
                        </li>
                        <li class="price-card--features--item">
                            Branded Desktop App
                        </li>
                        <li class="price-card--features--item" v-if="upgradePlan === '597'">
                            Priority Support
                        </li>
                        <li class="price-card--features--item" v-if="upgradePlan === '597'">
                            1 on 1 Support
                        </li>
                    </ul>
                </div>
                <div class="upgraded-message" v-if="upgraded">
                    <p >Your account is now upgraded to the ${{upgradePlanDuration==='monthly'?`${price.monthlyPrice} monthly`:`${price.discountedPrice} annual`}} plan.</p>
                </div>
                <div class="payment-duration" v-else-if="!durationSelected">
                    <div class="section-label">Select Duration</div>
                    <div class="radio">
                        <input
                        type="radio"
                        v-model="upgradePlanDuration"
                        value="monthly"
                        id="monthly"
                        />
                        <label for="monthly"> Pay Monthly - ${{price.monthlyPrice}}/month </label>
                    </div>
                    <div class="radio">
                        <input
                        type="radio"
                        v-model="upgradePlanDuration"
                        value="annually"
                        id="annually"
                        />
                        <label for="annually">Pay Annually - <span class="original-price">${{price.originalPrice}}/year </span> ${{price.discountedPrice}}/year </label>
                        <div class="annual-offer"> Save two months, when you pay annually &nbsp;
                            <i
                            class="fa fa-info-circle"
                            v-b-tooltip.hover
                            title="With annual subscription, you will get two months free to use..."
                        ></i></div>
                    </div>
                </div>

                <div class="payment-confirmation" v-else-if="durationSelected">
                    <div class="section-label">Confirm your upgrade</div>
                    <div class="section-info">Your subscription will be upgraded to ${{upgradePlanDuration==='monthly'?`${price.monthlyPrice} monthly`:`${price.discountedPrice} annual`}} plan  <span class="plan-change-link" @click="durationSelected = false">Change Duration</span></div>
                    <div class="option" >
                        <input type="checkbox" id="accept-upgrade" name="accept-upgrade" v-model="accepted" />
                        <label for="accept-upgrade" style="line-height:30px" v-if="isAdmin">
                            I agree to pay ${{upgradePlanDuration==='monthly'?`${price.monthlyPrice}/month`:`${price.discountedPrice}/year`}} to upgrade my plan.
                            <i
                                class="fa fa-info-circle"
                                v-b-tooltip.hover
                                title="Your current payment will be adjusted in the upgraded plan"
                            ></i>
                        </label>
                    </div>
                </div>
                <div class="section-info danger" v-if="errorMsg">{{errorMsg}}</div>
            </div>
            <div class="modal-footer" v-if="upgradePlan !== ''">
                <div class="btn btn-light2" @click="$emit('close')"> Cancel </div>
                <div class="btn btn-blue" v-if="!durationSelected" @click="nextStep()">Proceed to upgrade</div>
                <div class="btn btn-blue" v-else-if="!upgraded" @click="upgradeNow" :disabled="!accepted || processing">
                    <span v-if="!processing">Confirm upgrade</span>
                    <moon-loader v-else color="#ffffff" size="16px"/>
                </div>
                <div class="btn btn-blue" v-else @click="$emit('close')">Close</div>
            </div>
        </div>
    </modal>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'
const PhoneNumber = () => import('@/pmd/components/util/PhoneNumber.vue');

import config from '@/config';
import { trackGaEvent } from "@/util/helper";

export default {
    props: ['show','company','source'],
    data(){
        return{
            activePlan: '',
            activePlanName: '', // '97', '297', '597', '237', (actual)
            upgradePlan: '', // '297', '597', 'NA' (computed)
            upgradePlanDuration: 'monthly', // 'monthly', 'annually'
            // annualPay: false,
            accepted: false,
            durationSelected: false,
            processing: false,
            upgraded: false,
            isAdmin: true,
            designation: '',
            organizationName: '',
            address: '',
            userName: '',
            phone: '',
            email: '',
            errorMsg: '',
        }
    },
    components: { Modal, UpgradeModalShimmer, PhoneNumber },
    created(){
        this.getCurrentPlan();
        trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Step 1', 1);
    },
    computed:{
        price() {
            let originalPrice = 'NA';
            let discountedPrice = 'NA';
            let monthlyPrice = 'NA';
            switch(this.upgradePlan) {
                case '97': // never come true
                    originalPrice = '1,164';
                    discountedPrice = '970';
                    monthlyPrice = '97';
                    break;
                case '297':
                    originalPrice = '3,564';
                    discountedPrice = '2,970';
                    monthlyPrice = '297';
                    break;
                case '237':
                    originalPrice = '2,844';
                    discountedPrice = '2,370';
                    monthlyPrice = '237';
                    break;
                case '537':
                    originalPrice = '6,444';
                    discountedPrice = '5,370';
                    monthlyPrice = '537';
                    break;
                case '597':
                    originalPrice = '7,164';
                    discountedPrice = '5,970';
                    monthlyPrice = '597';
                    break;
                default:
                    originalPrice = 'NA';
                    discountedPrice = 'NA';
                    monthlyPrice = this.upgradePlan
            }
            return {originalPrice, discountedPrice, monthlyPrice};
        }
    },

    methods: {
        async getCurrentPlan(){
            try {
                let activePlan = await this.$http.get(`/api/stripe/agency_plan?company_id=${this.company.id}`);
                if(activePlan.data && activePlan.data.plan){
                    this.activePlan = activePlan.data.plan;
                    this.activePlanName = activePlan.data.plan.name.split('_')[2];
                    switch(this.activePlanName) {
                        case '97':
                            this.upgradePlan = '297'
                            break;
                        // case '237': // not a good practice to create base + 300$ plan for every case.
                        //     this.upgradePlan = '537'
                        //     break;
                        case '297':
                            this.upgradePlan = '597'
                            this.prefillForm();
                            break;
                        case '597':
                            console.log('Your account is already upgraded !!');
                        default:
                            this.notUpgradable();
                    }
                } else {
                    this.notUpgradable();
                }
            } catch (e) {
                this.notUpgradable();
            }
        },
        prefillForm() {
            // Prefilling form
            if (this.company) {
                this.organizationName = this.company.name || '';
                this.address = this.company.address || '';
                this.phone = this.company.phone || '';
                this.email = this.company.email || '';
            }
            const user = this.$store.state.user.user;
            if(user) {
                this.userName = user.first_name + ' ' + user.last_name;
                if (!this.phone) this.phone = user.phone || '';
                if (!this.email) this.email = user.email || '';
            }
        },
        notUpgradable(){
            this.upgradePlan = 'NA';
            this.errorMsg = 'We are unable to upgrade your account. Please ';
            // Just to handle footer cases:
            this.durationSelected = true;
            this.upgraded = true;
        },
        async nextStep(){
            // fetch user-details
            const user = this.$store.state.user.user;
            if (user.role !== 'admin') {
                this.isAdmin = false;
                this.errorMsg = "You are not authorized to perform this action. Please contact your admin.";
            }
            this.durationSelected= true;
            trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Step 2', 1);
        },
        async upgradeNow(){
            const result = await this.$validator.validateAll();
            if (!this.accepted || !result) {
                return;
            }
            this.processing = true;
            let new_plan_label = `agency_${this.upgradePlanDuration === 'monthly'?'monthly':'annual'}_${this.upgradePlan}`
            const authUser = await this.$store.dispatch("auth/get");
            trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Upgrading', 1);
            try {
                let upgradeResponse = await this.$http.post('/stripe/agency_upgrade',{
                    company_id: this.company.id,
                    plan: this.activePlan,
                    current_plan_label: this.activePlan.name,
                    current_plan_status: this.activePlan.status,
                    new_plan_label,
                    user_id: authUser.userId
                })
                if (this.upgradePlan === '597') { // generating ticket for automation
                    const serviceId = config.mode === 'production' ? 'XqIAeLI5nm1QUN15JpvH' : 'mir9m8WqY3FfYICp6ETa';
                    let marketplaceResponse = await this.$http.post(`/agency_service/load/${serviceId}`,{
                        planLevel: 1,
                        upgrade: false,
                        agency: {
                            companyId: this.company.id,
                            name: this.organizationName,
                            address: this.address,
                        },
                        user: {
                            name: this.userName,
                            email: this.email,
                            phone: this.phone,
                            designation: this.designation,
                            userId: authUser.userId
                        },
                        subs_id: upgradeResponse.data.subscription? upgradeResponse.data.subscription.id : ''
                    });
                }
                trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Completed', 1);
                // if(upgradeResponse.data && response.data.plan){
                    this.upgraded = true;
                    this.processing = false;
                    // this.$emit('success',response.data.plan);
                    // this.submitted = true;
                // }
            } catch (e) {
                console.log(e);
                this.showError('Something went wrong !! Please try again later.', 5000);
                this.processing = false;
            }
        },
        async showError(newError, duration = 2000) {
            this.errorMsg = newError;
            setTimeout( ()=>{
                this.errorMsg = '';
            },duration);
        },
        contactSupport(){
            this.$emit('close');
            window.fcWidget.open();
        }
    }
}
</script>

<style scoped>
.modal-heading-wrap {
    padding: 30px 30px 0px;
}
.modal-heading-content {
	text-align: center;
	padding-bottom: 20px;
	border-bottom: 2px solid #f2f7fa;
}
.modal-body{
    max-height: 80vh;
    overflow: auto;
    padding: 2rem;
}


.plan-container{
    border: 2px solid #188bf6;
    border-radius: 8px;
    background-color: #f5faff;
}
.plan-label{
    background-color: #188bf6;
    color: #ffffff;
    padding: 8px 32px;
    display: inline-block;
    border-radius: 0px 0px 8px 0px;
    text-transform: uppercase;
    font-weight: 500;
}

.price-card--features--list{
    display: flex;
    flex-wrap: wrap;
    padding: 20px 20px 0px;
    list-style: none;
}
.price-card--features--item{
    flex-basis: 40%;
    white-space: nowrap;
    margin-left: 30px;
    position: relative;
}
.price-card--features--item:before {
	content: "✓";
	color: green;
	display: block;
	position: absolute;
	left: -20px;
}


/**
    Radio buttons:
**/
.payment-duration {
    padding-left: 20px;
}
.payment-duration .section-label{
    margin-left: -20px;
    margin-top: 20px;
    font-size: 16px;
}
.payment-duration .radio {
    display: block;
}
.payment-duration .radio > label{
    font-size: 16px;
}
.payment-duration .radio > label:before{
    top: 5px;
    left: -4px;
}
.payment-duration .radio > input:checked + label:before{
    border-color: #188bf6;
}
.payment-duration .radio > label:after{
    top: 9px;
    left: 0px;
    background-color: #188bf6;
}
.annual-offer{
    margin: -12px 20px;
    font-style: italic;
    opacity: 0.8;
}
.original-price{
    text-decoration: line-through;
    margin-right: 10px;
}

/***
    payment-confirmation
***/
.payment-confirmation .section-label{
    margin-top: 20px;
    font-size: 16px;
}
.payment-confirmation .section-info{
    border-left: 2px solid #188bf6;
    padding: 6px 12px;
    margin: 8px 0px;
    background-color: #f5fafe;
}

.payment-confirmation .plan-change-link{
    color: #188bf6;
    font-weight: 500;
    cursor: pointer;
}
.payment-confirmation .option > label:after{
    border: 1px solid #188bf6;
    background-color: #188bf6;
}
.payment-confirmation .option > label{
    font-size: 16px;
}
.btn:disabled,
.btn[disabled]{
    opacity: 0.6;
    pointer-events: none;
}


.section-info.danger{
    border-left: 2px solid #e93d3d;
    padding: 6px 12px;
    margin: 16px 0px 8px;
    background-color: #fff3f3;
}
/**
upgraded-message
 **/

.upgraded-message{
    border-left: 3px solid #35ca38;
    background-color: #eeffef;
    padding: 12px 20px;
    margin: 20px 0px;
    color: #373737;
}

.open-support-widget{
    cursor: pointer;
    color: #188bf6;
    font-weight: 600;
}
</style>
