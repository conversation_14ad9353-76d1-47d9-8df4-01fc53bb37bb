<template>
  <tr>
    <td>{{ account.name }}</td>
    <td v-if="type === 'own'">
      {{
      account.dateUpdated.format(
      getCountryDateFormat('extended-normal')
      )
      }}
    </td>
    <td v-if="type === 'own'">{{ accountName }}</td>
    <td v-if="type === 'own'">
      <i
        class="fas fa-share-alt pointer --light"
        v-b-tooltip.hover
        title="Share"
        @click.prevent="$emit('shareSnapshot')"
      ></i>
    </td>
    <td v-if="type === 'own' && isAdmin">
      <i
        class="fas fa-redo pointer --light"
        v-b-tooltip.hover
        title="Refresh snapshot"
        v-if="!isProcessingDehydrate"
        @click.prevent="$emit('dehydrateAccount')"
      ></i>
      <moon-loader
        v-if="isProcessingDehydrate"
        :loading="isProcessingDehydrate"
        color="#188bf6"
        size="15px"
        style="display: inline-block;"
      />
    </td>
    <td v-if="type === 'own' && isAdmin">
      <i
        class="fas fa-upload pointer --light"
        v-b-tooltip.hover
        title="Push update to linked accounts"
        v-if="!isProcessingPush"
        @click.prevent="$emit('pushToAllAccounts')"
      ></i>
      <moon-loader
        v-if="isProcessingPush"
        :loading="isProcessingPush"
        color="#188bf6"
        size="15px"
        style="display: inline-block;"
      />
    </td>
    <td v-if="type !== 'default' && isAdmin">
      <i
        class="icon icon-pencil pointer --light"
        v-b-tooltip.hover
        title="Edit snapshot"
        @click.prevent="$emit('editAccountSnapshot')"
      ></i>
    </td>
    <td v-if="type !== 'default' && isAdmin">
      <i
        v-b-tooltip.hover
        title="Delete snapshot"
        v-if="!isProcessingDelete"
        class="icon icon-trash pointer --light"
        @click.prevent="$emit('deleteAccountSnapshot')"
      ></i>
      <moon-loader
        v-if="isProcessingDelete"
        :loading="isProcessingDelete"
        color="#188bf6"
        size="15px"
        style="display: inline-block;"
      />
    </td>
  </tr>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location, getCountryDateFormat } from '@/models'
export default Vue.extend({
  props: {
    account: {
      type: Object,
      required: true
    },
    isProcessingPush: {
      type: Boolean,
      default: false
    },
    isProcessingDehydrate: {
      type: Boolean,
      default: false
    },
    isProcessingDelete: {
      type: Boolean,
      default: false
    },
    type: {
      type: String
    },
    isAdmin: {
      type: Boolean,
      default: false
    }
  },
  components: {},
  data() {
    return {
      getCountryDateFormat,
      accountName: ''
    }
  },
  mounted() {
    this.getLocationName(this.account?.locationId)
  },
  methods: {
    async getByLocationId(locationId: string): Promise<Location | undefined> {
      try {
        return await this.$store.dispatch('locations/getById', locationId)
      } catch(error) {
        console.error(error)
      }
    },
    async getLocationName(locationId: string) {
      const location = await this.getByLocationId(locationId)
      if (location) this.accountName = location.name
    }
  }
})
</script>
