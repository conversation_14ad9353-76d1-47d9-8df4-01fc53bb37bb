<template>
  <div>
    <div v-if="error">{{ error }}</div>
    <div class="accordion accordion-wrapper">
      <div
        class="card mb-0"
        v-for="(conflictDataForLocation, locationId) in data"
        :key="locationId"
      >
        <div class="hl_imported-file-map">
          <div class="card">
            <div
              class="card-header collapsed"
              data-toggle="collapse"
              :href="`#collapse3-${locationId}`"
            >
              <a class="card-title">{{ conflictDataForLocation.name }}</a>
              <div
                class="location-skip-overwrites table-radio"
                :class="{'all-skipped': allConflictsSkipped(locationId), 'all-overwritten': allConflictsOverwritten(locationId)}"
                v-on:click.stop
              >
                <div class="radio overwrite-all">
                  <input
                    type="radio"
                    :name="`overwrite-select-${locationId}`"
                    :id="`overwrite-item-${locationId}`"
                    @change="skipAllConflictsForLocation(locationId, false)"
                    v-model="conflictDataForLocation.skipAllConflicts"
                    :value="0"
                  />
                  <label :for="`overwrite-item-${locationId}`">Overwrite</label>
                </div>
                <div class="radio right-radio skip-all">
                  <input
                    type="radio"
                    :name="`skip-select-${locationId}`"
                    :id="`skip-item-${locationId}`"
                    @change="skipAllConflictsForLocation(locationId, true)"
                    v-model="conflictDataForLocation.skipAllConflicts"
                    :value="1"
                  />
                  <label :for="`skip-item-${locationId}`">Skip</label>
                </div>
              </div>
            </div>
            <h3
              class="text-center"
              style="margin-top: 15px;"
              v-if="conflictDataForLocation.noOfConflicts === 0"
            >No Conflicts Detected</h3>
            <div :id="`collapse3-${locationId}`" class="card-body collapse">
              <div
                class="accordion accordion-wrapper"
                style="margin-left: 25px; padding: 15px 0px;"
              >
                <template v-for="(conflicts, resource) in conflictDataForLocation.conflicts">
                  <div class="card mb-0" v-if="conflicts.length" :key="resource">
                    <div class="hl_imported-file-map">
                      <div class="card">
                        <div
                          class="card-header collapse in"
                          data-toggle="collapse"
                          :href="`#collapse3-${resource}-${locationId}`"
                          style="border-left: 4px solid #188bf6;"
                        >
                          <a class="card-title asset-title">{{ resource | capitalize }} ({{ conflicts.length }})</a>
                          <template>
                          <div
                            class="text-button-row asset-radio"
                          >
                            <div class="radio" v-on:click.stop>
                              <input
                                type="radio"
                                @change="conflictBulkSelect(0, locationId, resource)"
                                v-model="conflictAssetSelect[locationId][resource]"
                                :id="`asset-overwrite-radio-${resource}-${locationId}`"
                                :name="`resource-radio-${resource}-${locationId}`"
                                :value="0"
                              />
                              <label :for="`asset-overwrite-radio-${resource}-${locationId}`">Overwrite</label>
                            </div>
                            <div class="radio right-radio" v-on:click.stop>
                              <input
                                type="radio"
                                @change="conflictBulkSelect(1, locationId, resource)"
                                v-model="conflictAssetSelect[locationId][resource]"
                                :id="`asset-skip-radio-${resource}-${locationId}`"
                                :name="`resource-radio-${resource}-${locationId}`"
                                :value="1"
                              />
                              <label :for="`asset-skip-radio-${resource}-${locationId}`">Skip</label>
                            </div>
                          </div>
                        </template>
                        </div>
                        <div
                          :id="`collapse3-${resource}-${locationId}`"
                          class="card-body collapse show"
                        >
                          <div class="table-wrap">
                            <table class="table">
                              <tbody>
                                <tr v-for="conflict in conflicts" :key="conflict.id">
                                  <td style="width: 70%">
                                    <span
                                      v-if="conflict.type === 'directory'"
                                      class="workflow-folder"
                                    >
                                      <i class="far fa-folder"></i>
                                    </span>
                                    {{ conflict.name }}
                                  </td>
                                  <td class="table-radio">
                                    <div class="radio">
                                      <input
                                        type="radio"
                                        :name="`overwrite-select-${locationId}-${conflict.id}`"
                                        :id="`overwrite-item-${locationId}-${conflict.id}`"
                                        v-model="conflict.skipOverwrite"
                                        :value="0"
                                        :disabled="skipResource(resource)"
                                      />
                                      <label
                                        :for="`overwrite-item-${locationId}-${conflict.id}`"
                                      >Overwrite</label>
                                    </div>
                                    <div class="radio right-radio">
                                      <input
                                        type="radio"
                                        :name="`skip-select-${locationId}-${conflict.id}`"
                                        :id="`skip-item-${locationId}-${conflict.id}`"
                                        v-model="conflict.skipOverwrite"
                                        @change="skipDependendConflicts(1, locationId, resource)"
                                        :value="1"
                                        :disabled="skipResource(resource)"
                                      />
                                      <label :for="`skip-item-${locationId}-${conflict.id}`">Skip</label>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    data: {
      type: Object
    },
    error: {
      type: String
    },
    resourcesToSkip: {
      type: Array
    },
    skipAllConflictsForLocation: {
      type: Function
    },
    conflictAssetSelect: {
      type: Object
    },
    conflictBulkSelect: {
      type: Function
    },
    skipDependendConflicts: {
      type: Function
    }
  },
  methods: {
    skipResource(resource: string) {
      return this.resourcesToSkip.indexOf(resource) > -1
    },
    allConflictsOverwritten(locationId: string) {
      const conflicts = this.data[locationId].conflicts

      let overwritten = true

      for (let resource in conflicts) {
        if (this.skipResource(resource)) {
          continue
        }

        for (let conflict of conflicts[resource]) {
          if (conflict.skipOverwrite) {
            overwritten = false
            return overwritten
          }
        }
      }

      return overwritten
    },
    allConflictsSkipped(locationId: string) {
      const conflicts = this.data[locationId].conflicts

      let skipped = true

      for (let resource in conflicts) {
        if (this.skipResource(resource)) {
          continue
        }

        for (let conflict of conflicts[resource]) {
          if (!conflict.skipOverwrite) {
            skipped = false
            return skipped
          }
        }
      }

      return skipped
    }
  },
  filters: {
    capitalize: function(value: string) {
      if (!value) return ''
      value = value.toString()
      return value.split('_').join(' ')
    }
  }
})
</script>
<style scoped>
.location-skip-overwrites {
  margin-right: 20px;
}

.location-skip-overwrites:not(.all-skipped)
  .radio.skip-all
  > input:checked
  + label:before,
.location-skip-overwrites:not(.all-skipped)
  .radio.skip-all
  > input:checked
  + label:after,
.location-skip-overwrites:not(.all-overwritten)
  .radio.overwrite-all
  > input:checked
  + label:before,
.location-skip-overwrites:not(.all-overwritten)
  .radio.overwrite-all
  > input:checked
  + label:after {
  opacity: 0.5;
}
.workflow-folder {
  padding-right: 2px;
}
.asset-radio {
  padding-right: 0px;
  width: 61.5%;
}
</style>
