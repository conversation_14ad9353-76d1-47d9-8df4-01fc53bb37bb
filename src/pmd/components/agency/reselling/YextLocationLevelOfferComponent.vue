<template>
  <div
    id="yext_reselling_location_level"
    v-if="company && location"
  >
    <!--   <div :class="{ backdrop: !company.stripeConnectId}"></div> -->
    <template
      v-if="
        (location.yextReseller &&
          location.yextReseller.payment_status === 'COMPLETE') ||
        yextId
      "
    >
      <!--Yext already sold-->
      <div class="card">
        <div class="card-header">
          <div class="toggle header-toggle">
            <UIToggle
                id="yext_location_offer_toggle"
                :value="true"
                :disabled="true"
              />

            <label for="yext_show_customer_tgl" class="card-header__label"
              >Offer Yext</label
            >
          </div>
        </div>
        <div class="card-body card-body-sold">
          <div class="plan-details">
            <div class="title">YEXT</div>
            <div class="price">
              <template
                v-if="
                  location.yextReseller.meta &&
                  location.yextReseller.meta.location_sell_amount >= 0
                "
              >
                <span
                  >
                  {{ location.yextReseller.meta.location_sell_amount }}
                  {{
                    location.yextReseller.meta.sell_currency ? location.yextReseller.meta.sell_currency : 'USD'
                  }}
                </span
                >&nbsp;/&nbsp; Month
              </template>
              <template v-else>SOLD</template>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <!-- Yext unsold -->
      <template v-if="!company.stripeConnectId">
        <div class="backdrop">
          <div
            class="stripe-connect__setup-btn btn"
            @click="gotoStripeSettings()"
          >
            Connect Stripe
            <i class="fas fa-exclamation-circle"></i>
          </div>
        </div>
      </template>

      <template v-else-if="company.country != 'US' || location.country != 'US'">
        <div class="backdrop">
          <div class="country_restriction">
            <i class="fas fa-exclamation-circle"></i> Yext is only available in
            US for now. We are working to support this in other countries.
          </div>
        </div>
      </template>
      <div class="card">
        <div class="card-header">
          <div class="toggle header-toggle">
            <template
              v-if="
                company.stripeConnectId &&
                company.country == 'US' &&
                location.country == 'US'
              "
            >
              <UIToggle
                id="yext_location_offer_toggle"
                @change="save"
                v-model="yextReseller.location_enabled"
              />
             <!--  <input
                type="checkbox"
                id="yext_show_customer_tgl"
                class="tgl tgl-light"
                @change="save"
                v-model="yextReseller.location_enabled"
              /><label
                for="yext_show_customer_tgl"
                id="toggle_sms_settings"
                class="tgl-btn"
              ></label> -->
            </template>

            <label for="yext_show_customer_tgl" class="card-header__label"
              >Offer Yext</label
            >
          </div>
        </div>
        <div class="card-body">
          <template v-if="!yextReseller.location_enabled">
            <div class="backdrop only-body-backdrop"></div>
          </template>
          <div class="logo">
            <img class="sidebar-icon" src="/pmd/img/icon-yext-logo.svg" />
          </div>
          <div class="details">
            <div class="yext_offer_table">
              <form action="#">
                <label for="hl_price">HighLevel Price</label>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    disabled
                    type="text"
                    id="hl_price"
                    :value="company.yextReseller.hl_price"
                  />
                </div>

                <label for="agency_price">Your Price</label>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    type="text"
                    id="location_price"
                    v-model.number="location_price"
                  />
                  <span class="field_error">
                    <template
                      v-if="location_price < company.yextReseller.hl_price"
                    >
                      Price must be more or equal to HighLevel price
                    </template>
                  </span>
                </div>
                <label for="agency_profit">Your Profit</label>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    disabled
                    type="text"
                    id="agency_profit"
                    :value="
                      location_price >= company.yextReseller.hl_price
                        ? getFriendlyNumber(location_price - company.yextReseller.hl_price)
                        : 0
                    "
                  />
                </div>
              </form>
            </div>
            <div class="place-eater"></div>
            <div class="footer-controls">
              <button
                :disabled="dirtyCheckForLocationPrice == location_price"
                class="btn btn-success reselling-btn"
                id="yext_resell_location_level"
                @click="save"
              >
                <template v-if="saving">
                  <i
                    class="icon"
                    :class="saving ? 'icon-clock' : 'icon-ok'"
                  ></i>
                  Saving
                </template>
                <template v-else> Save </template>
              </button>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
<script lang="ts">
// @ts-nocheck

import Vue from 'vue'
import { UserState, CompanyState, LocationState } from '@/store/state_models'
import { mapState } from 'vuex'
import { User, Company, Location } from '@/models'
import firebase from 'firebase/app'
import { getFriendlyNumber } from '@/util/helper'

export default Vue.extend({
  props: {
    location: {
      type: Object as Location
    }
  },
  components: {},
  data: function () {
    return {
      yextId: '',
      saving: false,
      location_price: 0,
      yextReseller: {},
      dirtyCheckForLocationPrice: 0,
      getFriendlyNumber
    }
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },
  created: async function () {
    this.locationId = this.$router.currentRoute.params.account_id
    this.initOffer()
  },
  watch: {
  },
  methods: {
    initOffer() {
      if (this.company && this.location) {
        if (this.location.yextReseller.location_price != undefined) {
          this.location_price = this.location.yextReseller.location_price
        } else {
          this.location_price = this.company.yextReseller.agency_price
        }
        this.dirtyCheckForLocationPrice = this.location_price
        this.yextReseller = {
          ...this.location.yextReseller,
        }

        if (this.location.yextReseller.location_id) {
          this.yextId = this.location.yextReseller.location_id
        } else if (this.location.yextId) {
          this.yextId = this.location.yextId
        } else {
          this.yextId = null
        }
      }
    },
    async save() {
      if (this.company.stripeConnectId && !this.yextId) {
        // this check is required, user might remove the backdrop by inspecting and start tinkering
        this.saving = true
        if (this.location_price < this.company.yextReseller.hl_price) {
          this.location_price = this.company.yextReseller.hl_price
        }
        this.location.yextReseller.location_price = this.location_price
        this.location.yextReseller.location_enabled =
          this.yextReseller.location_enabled
        this.dirtyCheckForLocationPrice = this.location_price
        await this.location.ref.update({
          'reseller.yext':  this.location.yextReseller,
           date_updated: firebase.firestore.FieldValue.serverTimestamp(),
        })
        this.saving = false
      }
    },
    gotoStripeSettings() {
      this.$router.push({ name: 'stripe_settings' })
    }
  },
})
</script>
<style lang="scss">
#yext_reselling_location_level {
  position: relative;
  height: 100%;
  .backdrop {
    background: #ffffffb5;
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 3;
    border-radius: 3px;
    display: grid;
    justify-content: center;
    align-items: center;

    .stripe-connect__setup-btn {
      background: #feebc8;
      border-radius: 3px;
      font-weight: 500;
      font-size: 1rem;
      /* line-height: 14px; */
      text-align: center;
      margin-bottom: 14px;
      color: #f6ad55;
      padding: 10px 20px;
      box-shadow: 0 0 2px 0px #00000038;
      i {
        margin-left: 6px;
      }
      &:hover {
        background: #f6ad55;
        color: #ffffff;
      }
    }
    .country_restriction {
      font-size: 1rem;
      background: #feebc8;
      padding: 10px 20px;
      max-width: 60%;
      margin: auto;
      display: grid;
      grid-template-columns: auto auto;
      grid-column-gap: 10px;
      color: #ed8936;
      border-radius: 2px;
      .fa-exclamation-circle {
        margin-top: 5px;
        font-size: 2rem;
      }
    }
  }
  .only-body-backdrop {
    /* border: 1px solid; */
    right: 0;
    top: 51px;
    height: calc(100% - 56px);
    /* filter: blur(12px); */
  }
  .card {
    height: 100%;
    .card-header {
      padding: 10px 20px;

      .header-toggle {
        display: grid;
        grid-template-columns: auto auto;
        grid-gap: 10px;
        align-items: center;
        input {
          display: none;
        }
        .disabled-on + label {
          /* border: 1px solid; */
          filter: opacity(0.6);
        }
      }

      .card-header__label {
        margin: 0;
        font-size: 1.129rem;
        font-weight: 400;
        color: #2a3844;
      }
    }
    .card-body {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-column-gap: 40px;
      .logo {
        max-height: 100px;
        max-width: 100px;
        overflow: hidden;
        img {
          height: 100%;
        }
      }
      .details {
        display: grid;
        grid-template-rows: auto auto auto 1fr auto;
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #4f4f4f;
        }
        .sub-heading {
          font-size: 1rem;
          margin: 10px 0;
          font-weight: 600;
          color: #4f4f4f;
        }
      }

      .yext_offer_table {
        form {
          display: grid;
          grid-template-columns: auto 1fr;
          grid-gap: 10px;
          label {
            font-size: 1rem;
            color: #4f4f4f;
          }

          .prefix_input {
            position: relative;
            .prefix {
              position: absolute;
              top: 2px;
              left: 3px;
              background: white;
              padding: 0 10px;
              display: grid;
              align-items: center;
            }
            input {
              padding: 0 0 0 30px;
              width: 90px;
            }
          }
          .field_error {
            display: block;
            font-size: 0.8rem;
            color: #e93d3d;
            min-height: 14px;
          }
        }
        [for='agency_profit'] {
          color: #27ae60;
        }
      }

      .reselling-btn {
        background: #27ae60;
        padding: 6px 10px;
        justify-self: right;
      }
      .footer-controls {
        display: grid;
        justify-content: right;
        grid-gap: 15px;
      }
    }
    .card-body-sold {
      display: unset;
      .plan-details {
        display: flex;
        padding: 24px 0;
        justify-content: center;
        align-items: center;
        background: linear-gradient(90.69deg, #cee5f1 0%, #d3daf9 100%);
        border-radius: 3px;
        margin-bottom: 24px;
      }

      .plan-details .title {
        font-size: 15px;
        margin-right: 10px;
        color: #4a5568;
      }

      .plan-details .price {
        background: rgba(236, 244, 255, 0.35);
        border: 1px solid rgba(163, 186, 222, 0.27);
        box-sizing: border-box;
        border-radius: 9px;
        padding: 8.5px 12.5px;
        color: gray;
        margin-left: 10px;
      }

      .plan-details .price span {
        font-weight: bold;
        font-size: 16px;
        color: black;
      }
    }
  }
}
</style>