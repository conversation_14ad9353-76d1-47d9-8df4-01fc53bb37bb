<template>
  <div class="modal fade hl_sms-template--modal" role="dialog" ref="modal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner" style="display:flex;align-items:center;">
						<img
							src="/pmd/img/logo_medallions/twilio-medallion.png"
							style="height:50px; margin-right:20px;"
						>
						<h5 class="modal-title">
							Twilio Account <span v-if="values.location" style="font-size:12px;"> ( {{values.location.name}} )</span >
						</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body" v-if="values.visible">
					<div class="modal-body--inner" >
						<div class="row">
							<div class="col-sm-12">
								<div class="form-group">
									<UITextInputGroup
										type="text"
										placeholder="Account SID"
										v-model="accountSID"
										v-validate="'required'"
										name="accountSID"
										@change="edited=true"
										label="Account SID"
										:error="errors.has('accountSID')"
										:errorMsg="'Account SID Required'"
									/>
								</div>
							</div>
							<div class="col-sm-12">
								<div class="form-group">
									<UITextInputGroup
										type="text"
										placeholder="Auth Token"
										v-model="accountToken"
										v-validate="'required'"
										name="token"
										@change="edited=true"
										label="Auth Token"
										:error="errors.has('token')"
										:errorMsg="'Auth Token Required'"
									/>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<div class="modal-footer--inner nav">
						<UIButton style="text-align: left;" use="danger" :loading="deleteProcessing" v-if="values && values.twilioAccount && values.twilioAccount.account_sid" type="button" @click.prevent="removeAccount">
							<span>Delete connection</span>
						</UIButton>
						<UIButton
							type="button"
							data-dismiss="modal"
							v-if="!isLoading"
							use="outline"
						>
							Cancel
						</UIButton>
						<UIButton
							type="button"
							@click="saveAccount"
							:disabled="isLoading || !accountSID || !accountToken"
							:loading="isLoading"
						>
							<span>Save</span>
						</UIButton>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Company, User
} from '@/models'

declare var $: any
export default Vue.extend({
	components: { },
	props: ['values'],
	data() {
		return {
			accountSID: '',
			accountToken: '',
			isLocation: false,
			isEditMode: false,
			isLoading: false,
			edited: false,
			deleteProcessing: false
		}
	},
	watch: {
		values(values : { [key: string]: any }) {
			if (values.visible)
				$(this.$refs.modal).modal('show');
			else
				$(this.$refs.modal).modal('hide');
			if (values.visible) {
				if (values.twilioAccount && values.twilioAccount.account_sid) {
					this.accountSID = values.twilioAccount.account_sid || '';
					this.accountToken = values.twilioAccount.token || '';
					this.isEditMode = true;
				} else {
					this.accountSID = '';
					this.accountToken = '';
					this.isEditMode = false;
				}
				this.isLocation = values.location ? true : false;
			}
		}
	},
	computed:{

	},
  	updated() {
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
			$(this.$refs.modal).on('hidden.bs.modal', function () {
				_self.$emit('hidden');
			});
			if (this.values && this.values.visible) {
				$(this.$refs.modal).modal('show');
			}
	},
	methods: {
		async removeAccount() {
			if(this.values.twilioAccount.id) {

				this.$uxMessage('consent', `Are you sure you want to remove this Twilio Subaccount from this location?`, async (res) => {
					if (res === 'ok') {
						const closeMessage = `Do you wish to close the Twilio subaccount too? "When you close a subaccount, Twilio will release all phone numbers assigned to it and shut it down completely.
								You can't ever use a closed account to make and receive phone calls or send and receive SMS messages.
								It's closed, gone, kaput – you cannot reopen a closed account." - Twilio`
						this.$nextTick(() => {
							this.$uxMessage('consent', closeMessage, async (close) => {
								this.deleteProcessing = true
								try {
									let reponse = await this.$http.delete(`/twilio/account?location_id=${this.values.location.id}${close === 'cancel' ? '&dont_close_twilio=true' : ''}`)
                  this.$emit('success',{ twilioAccount: {data: { account_sid: '', token: ''}}, locationId: this.values.location.id});
                  if(reponse.status===202 && reponse.data){
                    let message=`${reponse.data.message}`;
                    let webUrl = reponse.data.webUrl || '';
                    if(webUrl && webUrl.trim().length >6){
                    message+=` <a target="_blank" href="${webUrl}"> more on this error</a>`
                    }
                    this.$uxMessage('info', message,null,{ isMessageInRawHTML: true});
                  }
								} catch (err) {
									console.error(err)
									this.$uxMessage('error', 'An error has ocurred, please try again.')
									this.edited = false
								} finally {
									this.deleteProcessing = false
								}
							}, { consentString: 'Close', cancelButton: 'No' })
						})
					}
				})
			}
		},
		async saveAccount() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.isLoading = true;
			try {
        const newSID  = this.accountSID && this.accountSID.trim().length ? this.accountSID.trim() : undefined
        const newToken = this.accountToken && this.accountToken.trim().length ? this.accountToken.trim() : undefined

				let body = {
					accountSID: newSID ,
					accountToken: newToken,
					company_id: this.values.location.company_id
				};
				let newTwilioAccount = await this.$http.post('/twilio/edit?location_id=' + this.values.location.id, body)
				if (newTwilioAccount.data === 'invalid') {
					alert("Invalid accountSID and access token");
					this.isLoading = false;
					return;
				} else if (newTwilioAccount.data === 'linked_for_rebilling') {
          alert(
            "An account with same SID is linked for twilio rebilling, can't use same for another account!"
          )
          this.isLoading = false
          return
        }
				let response = await this.$http.get('/twilio/create_application/' + this.values.location.id, {});
				this.$store.commit('agencyTwilio/setLocationTwilioEmpty', false);
				this.$emit('success',{ twilioAccount: newTwilioAccount, locationId: this.values.location.id});
				this.isLoading = false;
			} catch (ex) {
				console.error("Couldn't create twilio app:", ex);
				this.isLoading = false;
			}
		}
	}
})
</script>

<style scoped>
.btn-blue[disabled='disabled'] {
	cursor: not-allowed;
	/* pointer-events: none; */
}
</style>
