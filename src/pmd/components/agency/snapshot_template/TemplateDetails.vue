<template>
    <div class="hl_template_info">
        <div class="row">
            <div class="hl_template_info--header col-sm-9">
                  <div @click="$emit('back')" class="back">
                      <i class="icon icon-arrow-left-2"></i>
                  </div>
                <div class="hl_template_info--overview">
                    <h2>{{selectedTemplate._data.name}}</h2>
                    <h5>{{selectedTemplate._data.overview}}</h5>
                </div>
            </div>
            <div class="col-sm-3">
                <img :src="selectedTemplate._data.image_url" class="hl_template_info--image">
            </div>
        </div>
        <div class="hl_new-variation--header">
            <ul class="filter">
            <li :class="activeTab === 'overview'? 'active':''" @click.prevent="activeTab = 'overview'">
                <a href="#">Overview</a>
            </li>
            <li :class="activeTab === 'exfb'? 'active':''" @click.prevent="activeTab = 'exfb'">
                <a href="#">Example Facebook Ads</a>
            </li>
            <li :class="activeTab === 'guide'? 'active':''" @click.prevent="activeTab = 'guide'">
                <a href="#">Setup Guide</a>
            </li>
            <!-- <li>
                <a href="#"><i class="icon icon-download"></i> Download Snapshot</a>
            </li> -->
            </ul>
        </div>
        <div class="hl_template_info--body" >
            <div v-if="activeTab === 'overview'">
                <div class="hl_template_info--video">
                    <iframe width="560" height="315"  frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen
                        :src="`https://www.youtube.com/embed/${getVideoId(selectedTemplate._data.video_url)}`" >
                    </iframe>
                </div>
            </div>
            <div v-if="activeTab === 'exfb'">
                <img v-for="img in selectedTemplate._data.fb_ad_image_url" :src="img" :key="img" class="hl_template_info--exfb">
            </div>
            <div v-if="activeTab === 'guide'">
                <iframe class="guide_doc" frameborder="0" :src="`${selectedTemplate._data.checklist_url}?embedded=true`"></iframe>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { getVideoId } from "@/util/video_utils";
export default Vue.extend({
    props: ['selectedTemplate'],
    data() {
		return {
            activeTab: 'overview',
            getVideoId: getVideoId
		}
    },
})
</script>
<style scoped>

.hl_template_info--header{
	display: flex;
	align-items: flex-start;
}
.hl_template_info--header .back{
	cursor: pointer;
	font-size: 20px;
    color: #188bf6;
    padding: 10px 20px 10px 0px;
}
.hl_template_info--image{
	max-height: 150px;
    width: 100%;
    object-fit: cover;
}
.hl_template_info--body{
    overflow-x: hidden;
    overflow-y: auto;
}
.in-modal .hl_template_info--body{
	max-height: calc(100vh - 460px);
}
.hl_template_info--video{
	margin: auto;
    max-width: 560px;
    display: block;
}
.hl_template_info--exfb{
	margin: 0px auto 20px;
    max-width: 500px;
    display: block;
}
.guide_doc{
	width: 100%;
    height: calc(100vh - 470px);
}
.in-modal .guide_doc{
	height:calc(100vh - 500px);
}
</style>
