<template>
  <div
    class="modal fade hl_funnel-add-new-variation--modal"
    id="add-new-variation"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --large" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner flex--align-center">
            <h2 class="modal-title" v-if="source === 'sidebar'">
              Available Vertical Snapshots
            </h2>
            <h2 class="modal-title" v-else>Select Snapshot Template</h2>
            <div class="account-type__tabs" v-if="agencyInSaasPlan">
              <div
                class="account-type__tabs-btn"
                :class="{ active: accountType === 'saas' }"
                @click="accountType = 'saas'"
              >
                SaaS Account
              </div>
              <div
                class="account-type__tabs-btn"
                :class="{ active: accountType === 'regular' }"
                @click="accountType = 'regular'"
              >
                Regular Account
              </div>
            </div>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <iframe
              v-show="isDFYIframe"
              src="https://www.gohighlevel.com/dfy"
              frameborder="0"
              style="
                height: 100%;
                width: 100%;
                top: 0px;
                left: 0px;
                right: 0px;
                bottom: 0px;
                min-height: 650px;
              "
              height="100%"
              width="100%"
            ></iframe>

            <template-list
              v-if="values.visible && !isDFYIframe"
              :source="source"
              @open="handleOpenTemplate"
              @select="onSelectTemplate"
            />
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <button type="button" class="btn btn-light3" data-dismiss="modal">
              {{ isDFYIframe ? 'Close' : 'Cancel' }}
            </button>
            <button
              type="button"
              class="btn btn-success"
              v-if="selectedTemplate._id"
              @click="$emit('select', selectedTemplate._id)"
            >
              {{ source === 'sidebar' ? 'Create New Account' : 'Continue' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { SnapshotTemplate, AuthUser } from '@/models'
import TemplateList from '@/pmd/components/agency/snapshot_template/TemplateList.vue'
import { trackGaEvent } from '@/util/helper'
// const TemplateList = () => import('@/pmd/components/agency/snapshot_template/TemplateList.vue');

declare var $: any

export default Vue.extend({
  props: ['values', 'source'],
  components: { TemplateList },
  data() {
    return {
      authUser: {} as AuthUser,
      locationId: '',
      selectedTemplate: {} as SnapshotTemplate,
      isDFYIframe: false,
      accountType: 'saas', // 'regular'
    }
  },
  computed: {
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
  },
  methods: {
    handleOpenTemplate(snapshot) {
      this.selectedTemplate = snapshot
    },
    onSelectTemplate(snapshot) {
      if (snapshot.type === 'dfy') {
        trackGaEvent('DFY Button', 'Button Clicked', this.authUser.companyId)
        this.isDFYIframe = true
      } else {
        this.$emit(
          'select',
          snapshot._id,
          snapshot.type ? snapshot.type : 'vertical',
          this.agencyInSaasPlan ? this.accountType : 'regular'
        )
      }
    },
  },
  watch: {
    async values(values: { [key: string]: any }) {
      // const data: () => object = <() => object>this.$options.data
      // if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      if (values.visible) {
        this.authUser = await this.$store.dispatch('auth/get')
        this.locationId = values.locationId
      }
    },
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    // const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
})
</script>
<style scoped>
/* Pricing Table Duration ===================> */
.flex--align-center {
  display: flex;
  align-items: center;
}
.account-type__tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #ebf8ff;
  color: #3182ce;
  border-radius: 30px;
  background-color: #ebf8ff;
  margin: auto;
  /* margin-top: 20px;
  margin-bottom: 16px; */
  width: fit-content;
}
.account-type__tabs-btn {
  font-size: 14px;
  font-weight: 500;
  width: 160px;
  padding: 8px 0px;
  border-radius: 30px;
  /* transition: all 0.3s; */
  cursor: pointer;
  user-select: none;
  text-align: center;
}
.account-type__tabs-btn.active {
  background-color: #3182ce;
  color: #ffffff;
}
</style>
