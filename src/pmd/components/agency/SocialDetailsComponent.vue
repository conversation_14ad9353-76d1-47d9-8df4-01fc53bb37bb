<template>
    <div class="tab-pane fade show active" id="prospect" role="tabpanel" aria-labelledby="prospect-tab">
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Facebook URL" placeholder="Facebook URL" v-model="facebook" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Google+" placeholder="Google+ URL" v-model="googlePlus" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="LinkedIn" placeholder="LinkedIn URL" v-model="linkedIn" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Foursquare" placeholder="Foursquare URL" v-model="foursquare" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Twitter" placeholder="Twitter URL" v-model="twitter" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Yelp" placeholder="Yelp URL" v-model="yelp" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Instagram" placeholder="Instagram URL" v-model="instagram" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="YouTube" placeholder="YouTube URL" v-model="youtube" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Pinterest" placeholder="Pintrest URL" v-model="pinterest" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Blog RSS" placeholder="Blog RSS" v-model="blogRss" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-group">
            <UITextInputGroup type="text" data-lpignore="true" label="Google Places ID" placeholder="Google Places ID" v-model="placesId" @keyup.native="setEdited" :disabled="!isAdmin"/>
        </div>
        <div class="form-footer save" v-if="edited">
            <div class="btns space-x-2" v-if="!loading">
                <UIButton type="button" use="outline" @click.prevent="reset">Cancel</UIButton>
                <UIButton type="button" :loading="loading" @click.prevent="save" :disabled="!isAdmin">Save</UIButton>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Location, User } from '@/models';
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'

export default Vue.extend({
    props: {
        location: Location,
    },
    data() {
        return {
            loading: false,
            edited: false,
            facebook: '',
            googlePlus: '',
            linkedIn: '',
            foursquare: '',
            twitter: '',
            yelp: '',
            instagram: '',
            youtube: '',
            pinterest: '',
            blogRss: '',
            placesId: ''
        };
    },
    created() {
        this.reset();
    },
    computed: {
      ...mapState('user', {
        user: (s: UserState) => {
          return s.user ? new User(s.user) : undefined
        }
      }),
      isAdmin(): boolean {
        return this.user && this.user.role === 'admin'
      },
    },
    methods: {
        setEdited() {
            this.edited = true;
        },
        reset() {
            this.errors.clear();
            this.loading = false;
            this.edited = false;
            this.facebook = this.location.facebookUrl;
            this.googlePlus = this.location.googlePlus;
            this.linkedIn = this.location.linkedIn;
            this.foursquare = this.location.foursquare;
            this.twitter = this.location.twitter;
            this.yelp = this.location.yelp;
            this.instagram = this.location.instagram;
            this.youtube = this.location.youtube;
            this.pinterest = this.location.pinterest;
            this.blogRss = this.location.blogRss;
            this.placesId = this.location.googlePlacesId
        },
        async save() {
            if (!this.isAdmin) alert('You don\'t have permission to upload file. Check your permissions with your agency admin.');
            if (this.loading) return;

            await this.$validator.validateAll();
            if (this.errors.any()) {
                return Promise.resolve(true);
            }

            this.loading = true;
            this.location.facebookUrl = this.facebook || '';
            this.location.googlePlus = this.googlePlus || '';
            this.location.linkedIn = this.linkedIn || '';
            this.location.foursquare = this.foursquare || '';
            this.location.twitter = this.twitter || '';
            this.location.yelp = this.yelp || '';
            this.location.instagram = this.instagram || '';
            this.location.youtube = this.youtube || '';
            this.location.pinterest = this.pinterest || '';
            this.location.blogRss = this.blogRss || '';
            this.location.googlePlacesId = this.placesId || '';
            await this.location.save();

            this.loading = false;
            this.edited = false;
        }
    }
});
</script>

