<template>
	<tr>
		<td>{{location.name}}</td>
		<td>
			<input
				type="text"
				class="form-control"
				placeholder="API Key"
				v-model="apiKey"
				v-validate="'required'"
				name="api<PERSON>ey"
				@keydown="edited=true"
			>
			<span v-show="errors.has('apiKey')" class="--red">API Key Required</span>
		</td>
		<td>
			<div style="display: inline-block;position: relative;">
				<button
					:class="{invisible: processing}"
					type="button"
					class="btn btn-success"
					@click.prevent="saveAccount"
				>Save</button>
				<div
					style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
					v-show="processing"
				>
					<moon-loader :loading="processing" color="#37ca37" size="30px"/>
				</div>
			</div>
		</td>
	</tr>
</template>

<script lang="ts">
import Vue from 'vue'
import { SendGridAccount } from '@/models';

export default Vue.extend({
	props: ['location'],
	data() {
		return {
			sendGridAccount: {} as SendGridAccount,
			processing: false,
			apiKey: '',
			edited: false,
		}
	},
	async created() {
		this.sendGridAccount = await SendGridAccount.getByLocationId(this.location.id);
		if (!this.sendGridAccount) {
			this.sendGridAccount = new SendGridAccount();
			this.sendGridAccount.locationId = this.location.id;
		} else {
			this.apiKey = this.sendGridAccount.apiKey
		}
	},
	methods: {
		async saveAccount() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.processing = true;
			this.sendGridAccount.apiKey = this.apiKey;

			await this.sendGridAccount.save();
			this.edited = false;
			this.processing = false;
		}
	}
})
</script>

