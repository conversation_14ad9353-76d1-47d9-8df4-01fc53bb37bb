<template>
  <div
    class="tab-pane fade show active"
    id="prospect"
    role="tabpanel"
    aria-labelledby="prospect-tab"
  >
    <div class="form-group">
      <div class="d-inline-flex justify-content-between" style="width: 100%">
        <UITextLabel>Business name </UITextLabel>
        <div
          v-if="allowRelatedContactDisplay === true && relatedCount > 1"
          class="tag pointer"
          style="margin-right: 10px"
          @click.prevent="showCompanyContacts(name)"
        >
          Related Contacts ({{ relatedCount }})<a
            ><i class="icon icon-edit"></i
          ></a>
        </div>
      </div>
      <UITextInput
        type="text"
        placeholder="Business name"
        v-model="name"
        name="businessName"
        data-vv-as="Business name"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('businessName')"
        :errorMsg="errors.first('businessName')"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Street address"
        placeholder="Street address"
        v-model="address"
        name="msgsndr4"
        data-lpignore="true"
        autocomplete="msgsndr4"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('msgsndr4')"
        :errorMsg="errors.first('msgsndr4')"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="City"
        placeholder="City"
        v-model="city"
        name="city"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('city')"
        :errorMsg="errors.first('city')"
      />
    </div>
    <div class="form-group">
      <UITextLabel>Country</UITextLabel>
      <select
        class="selectpicker mt-1"
        title="Country"
        v-model="country"
        name="country"
        @change="setEdited"
        :disabled="!isAdmin"
      >
        <option
          :value="country.iso"
          v-for="country in countriesArray"
          :key="country.iso"
        >
          {{ country.name }}
        </option>
      </select>
      <span v-show="errors.has('country')" class="error">{{
        errors.first('country')
      }}</span>
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="State / Prov / Region"
        placeholder="State"
        v-model="state"
        name="state"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('state')"
        :errorMsg="errors.first('state')"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Zip / Postal code"
        class="msgsndr7"
        placeholder="Zip / Postal code"
        v-model="postalCode"
        name="msgsndr7"
        data-lpignore="true"
        autocomplete="msgsndr7"
        data-vv-as="Postal Code"
        @input="setEdited"
        :disabled="!isAdmin"
        :error="errors.has('msgsndr7')"
        :errorMsg="errors.first('msgsndr7')"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Website"
        placeholder="Website"
        name="website"
        v-model="website"
        @input="setEdited"
        :disabled="!isAdmin"
      />
    </div>
    <div class="form-group" v-if="showTimezone">
      <UITextLabel>Time Zone</UITextLabel>
      <select
        class="selectpicker mt-1"
        v-model="timezone"
        name="timezone"
        data-size="5"
        @change="setEdited"
        :disabled="!isAdmin"
      >
        <option value disabled>Choose one..</option>
        <option
          v-if="!hasTimezoneInList && timezone"
          :value="timezone"
          v-text="timezone"
        ></option>
        <option
          v-for="timezone in timezones"
          :key="timezone.value"
          :value="timezone.value"
          v-text="timezone.label"
          :disabled="!isAdmin"
        ></option>
      </select>
      <span v-show="errors.has('timezone')" class="error">{{
        errors.first('timezone')
      }}</span>
    </div>
    <div class="form-footer save" v-if="edited">
      <UIButton use="outline" @click.prevent="reset">
        Cancel
      </UIButton>
      <div style="display: inline-block; position: relative; margin-left: 10px">
        <UIButton
          @click.prevent="save"
          :loading="loading"
        >
          Save
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, Contact, User } from '@/models'
import libphonenumber from 'google-libphonenumber'
import { isoToState } from '@/util/state_helper'
import countries from '@/util/countries'
import timezones from '@/util/timezones.ts'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import { getCommonBusinessName } from '../../pages/smartlist/vm/helpers'
import { UxMessage } from '@/util/ux_message'
import lodash from 'lodash'

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
declare var $: any

export default Vue.extend({
  props: {
    location: Location,
    contact: Contact,
  },
  inject: ['uxmessage'],
  data() {
    return {
      loading: false,
      edited: false,
      name: '',
      address: '',
      city: '',
      country: '',
      state: '',
      postalCode: '',
      website: '',
      timezone: '',
      isoToState,
      timezones: timezones,
      showTimezone: false,
      relatedCount: 0,
      allowRelatedContactDisplay: false,
      intialBusinessName: '',
      countriesArray: [] as any,
    }
  },
  async mounted() {
    await this.reset()
    this.countriesArray = Object.keys(countries).map(countryCode => {
      return { iso: countryCode, name: countries[countryCode] }
    })
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  watch: {
    name(newVal: string) {
      if (
        newVal &&
        this.intialBusinessName &&
        newVal === this.intialBusinessName
      ) {
        this.allowRelatedContactDisplay = true
      } else this.allowRelatedContactDisplay = false
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isAdmin(): boolean {
      if (this.contact) return true
      return this.location && this.user && this.user.role === 'admin'
    },
    hasTimezoneInList() {
      return this.timezones.findIndex(x => x.value === this.timezone) > -1
    },
  },
  methods: {
    setEdited() {
      this.edited = true
    },
    showCompanyContacts(bizName: string) {
      const r = { name: 'business_name', params: { business_name: bizName } }
      this.$router.push(r)
    },
    clearRelatedContacts() {
      this.allowRelatedContactDisplay = false
      this.relatedCount = 0
      this.intialBusinessName = ''
    },
    async updatedRelatedContacts() {
      try {
        if (this.name) {
          const locationId =
            this.$router.currentRoute && this.$router.currentRoute.params
              ? this.$router.currentRoute.params.location_id
              : ''
          if (locationId) {
            const contacts = await getCommonBusinessName(
              this.name,
              locationId,
              this.user ? this.user.id : null
            )
            this.intialBusinessName = this.name
            this.relatedCount = contacts.length
            this.allowRelatedContactDisplay = true
          }
        }
      } catch(err) {
        console.log(err)
      }
    },
    async reset() {
      this.clearRelatedContacts()
      this.errors.clear()
      this.loading = false
      this.edited = false
      if (this.location) {
        this.name = this.location.name
        this.address = this.location.address
        this.city = this.location.city
        this.country = this.location.country || ''
        this.state = this.location.state
        this.postalCode = this.location.postalCode
        this.website = this.location.website
        this.timezone = this.location.timezone
        this.showTimezone = true
      } else if (this.contact) {
        this.name = this.contact.companyName
        this.address = this.contact.address1 || ''
        this.city = this.contact.city || ''
        this.country = this.contact.country || ''
        this.state = this.contact.state || ''
        this.postalCode = this.contact.postalCode || ''
        this.website = this.contact.website
        const location = new Location(
          await this.$store.dispatch(
            'locations/getById',
            this.contact.locationId
          )
        )
        this.showTimezone = location.disableContactTimezone === false
        this.timezone = this.contact.timezone || location.timezone // Show location timezone if empty because that is how the field is treated on the backend. When the field is empty we consider the Location's timezone.
      }
      await this.updatedRelatedContacts()
    },
    async save() {
      if (!this.isAdmin)
        alert(
          "You don't have permission to upload file. Check your permissions with your agency admin."
        )
      if (this.loading) return
      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }
      this.loading = true
      let response
      if (this.location) {
        this.location.name = this.name
        this.location.address = this.address
        this.location.city = this.city
        this.location.country = this.country
        this.location.state = this.state
        this.location.postalCode = this.postalCode
        this.location.website = this.website
        this.location.timezone = this.timezone
        console.log(this.location)
        await this.location.save()
      } else if (this.contact) {
        this.contact.companyName = this.name
        this.contact.address1 = this.address
        this.contact.city = this.city
        this.contact.country = this.country
        this.contact.state = this.state
        this.contact.postalCode = this.postalCode
        this.contact.website = this.website
        this.contact.timezone = this.timezone
        console.log(this.contact)
        response = await this.contact.save()
      }
      if (response instanceof Contact || response === undefined) {
        this.edited = false
        this.$root.$emit('updateAudit');
      } else {
        this.uxmessage(UxMessage.errorType(lodash.capitalize(response)), true)
      }
      this.clearRelatedContacts()
      this.loading = false
      await this.updatedRelatedContacts()
    },
  },
})
</script>
