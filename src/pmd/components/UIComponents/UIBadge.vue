<template>
  <span
    class="hl-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
    :class="[
      {
        'bg-gray-100 text-gray-800': use === 'gray',
      },
      {
        'bg-red-100 text-red-800': use === 'red',
      },
      {
        'bg-yellow-100 text-yellow-800': use === 'yellow',
      },
      {
        'bg-green-100 text-green-800': use === 'green',
      },
      {
        'bg-blue-100 text-blue-800': use === 'blue',
      },
      {
        'bg-indigo-100 text-indigo-800': use === 'indigo',
      },
      {
        'bg-purple-100 text-purple-800': use === 'purple',
      },
      {
        'bg-pink-100 text-pink-800': use === 'pink',
      },
    ]"
  >
    <slot></slot>
  </span>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    use: {
      type: String,
      default: 'gray',
    },
  },
})
</script>

<style scoped></style>
