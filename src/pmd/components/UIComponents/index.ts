import UITextInput from './UITextInput.vue'
import UITextInputGroup from './UITextInputGroup.vue'
import UITextArea from './UITextArea.vue'
import UISelect from './UISelect.vue'
import UIToggle from './UIToggle.vue'
import UITextLabel from './UITextLabel.vue'
import UIButton from './UIButton.vue'
import UICheckbox from './UICheckbox.vue'
import UITextAreaGroup from './UITextAreaGroup.vue'
import UIIcon from './UIIcon.vue'
import UIJsonPrettyView from './UIJsonPrettyView.vue'
import UIMultiSelect from './UIMultiSelect.vue'
import UISelectSearch from './UISelectSearch.vue'
import UISearchInput from './UISearchInput.vue'
import UILocationPicker from './UILocationPicker.vue'
import UIRadioButton from './UIRadioButton.vue'
import UIBadge from './UIBadge.vue'
import UIAvatar from './UIAvatar.vue'
import UIAlert from './UIAlert.vue'

export const registerComponents = {
  install(app) {
    app.component('UITextInput', UITextInput)
    app.component('UITextInputGroup', UITextInputGroup)
    app.component('UITextArea', UITextArea)
    app.component('UISelect', UISelect)
    app.component('UIToggle', UIToggle)
    app.component('UITextLabel', UITextLabel)
    app.component('UIButton', UIButton)
    app.component('UICheckbox', UICheckbox)
    app.component('UITextAreaGroup', UITextAreaGroup)
    app.component('UIIcon', UIIcon)
    app.component('UIJsonPrettyView', UIJsonPrettyView)
    app.component('UIMultiSelect', UIMultiSelect)
    app.component('UISelectSearch', UISelectSearch)
    app.component('UISearchInput', UISearchInput)
    app.component('UILocationPicker', UILocationPicker)
    app.component('UIRadioButton', UIRadioButton)
    app.component('UIBadge', UIBadge)
    app.component('UIAvatar', UIAvatar)
    app.component('UIAlert', UIAlert)
  }
}
