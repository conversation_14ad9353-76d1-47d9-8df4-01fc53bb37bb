<template>
  <input
    v-model="checkboxValue"
    :checked="checked"
    type="checkbox"
    class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50"
    :disabled="disabled"
  />
</template>

<script lang="ts">
import { computed, defineComponent } from '@vue/composition-api'

export default defineComponent({
  props: {
    checked: {
      type: [Boolean, Array, String],
    },
    value: {
      type: [Boolean, Array, String],
      default: false,
    },
    disabled: {
      type: [Boolean, Array, String],
      default: false,
    },
  },
  setup(props, { emit }) {
    const checkboxValue = computed({
      get() {
        return props.value
      },
      set(val: boolean) {
        emit('input', val)
        emit('change', val)
      },
    })
    return {
      checkboxValue
    }
  },
})
</script>

<style scoped></style>
