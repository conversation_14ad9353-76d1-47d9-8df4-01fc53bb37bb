<template>
  <span
    class="hl-avatar flex justify-center  h-6 w-6 overflow-hidden bg-gray-100"
    :class="[squaredGroup || square ? 'rounded' : 'rounded-full']"
    :style="{
      'background-color': color || '',
      width: computedSize,
      height: computedSize,
      'line-height': computedSize,
    }"
  >
    <img v-if="src" :src="src" />
    <span v-else-if="text" class="text-white ">{{ avatarText }}</span>
    <svg
      v-else
      class="h-full w-full text-gray-200"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z"
      />
    </svg>
  </span>
</template>

<script lang="ts">
import { defineComponent, computed, inject } from '@vue/composition-api'
export default defineComponent({
  name: 'UIAvatar',
  props: {
    src: { type: String },
    text: { type: String },
    color: { type: String },
    size: { type: String, default: '40px' },
    square: { type: Boolean, default: false },
  },
  setup(props) {
    const squaredGroup = inject('square', null)
    const groupSize = inject('size', null)
    const computedSize = computed(() => groupSize || props.size)
    const avatarText = computed(() => {
      if (!props.text) {
        return
      }
      const splited = props.text.split(' ')
      return splited.length > 1
        ? splited[0][0].toUpperCase() + splited[1][0].toUpperCase()
        : props.text[0].toUpperCase()
    })
    return { squaredGroup, computedSize, avatarText }
  },
})
</script>
