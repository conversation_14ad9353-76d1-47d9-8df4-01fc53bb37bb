<template>
  <div class="hl-text-input-container">
    <div class="flex space-x-3">
      <span v-if="label" :for="name" class="hl-text-input-label block text-sm font-medium text-gray-700 mb-1">{{
        label
      }}</span>
      <span
        v-if="tooltip"
        class="input-group-addon"
        v-b-tooltip.hover
        :title="tooltip"
      >
        <i class="fas fa-question-circle"></i>
      </span>
    </div>
    <div class="relative rounded-md shadow-sm" :class="{'hl-text-input-wrapper': adjustAddOns}">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <UIIcon v-if="icon" :icon="icon" />
      </div>
      <div class="absolute inset-y-0 left-0 mx-2 flex items-center pointer-events-none hl-text-input-addon">
        <span v-if="preAddOn" class="text-gray-500 sm:text-sm">
          {{ preAddOn }}
        </span>
      </div>
      <UITextInput
        ref="inputRef"
        :type="type"
        :name="name"
        :id="id"
        :placeholder="placeholder"
        @input="val => $emit('input', val)"
        @blur="val => $emit('blur', val)"
        @change="(e) => $emit('change', e)"
        :value="value"
        :min="min"
        :disabled="disabled"
        :autocomplete="autocomplete"
        v-bind:class="{'pl-10': icon, 'pl-4': preAddOn, 'arrow-disable': disableArrow, 'border-red-300 focus:ring-red-400 focus:border-red-400 pr-10' : error}"
        @keyup="val => $emit('keyup', val)"
        @enter="val => $emit('enter', val)"
        :readonly="readonly"
        :maxlength="maxlength"
        :ignoreLastPass="ignoreLastPass"
      />
      <div v-if="error" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
        <i class="fas fa-exclamation-circle h-5 w-5 text-red-400 text-lg mb-2 mr-1 pl-1" aria-hidden="true"></i>
     </div>
     <div class="absolute inset-y-0 right-0 flex items-center pointer-events-none hl-text-input-addon" :class="error ? 'pr-10' : 'pr-3'">
      <span v-if="postAddOn" class="text-gray-500 sm:text-sm">
        {{ postAddOn }}
      </span>
     </div>
    </div>
   <p v-if="description" class="hl-input-description mt-2 text-xs text-gray-500">{{description}}</p>
   <p v-if="errorMsg && error" class="hl-input-error mt-2 text-sm text-red-600" id="email-error">{{errorMsg}}</p>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/composition-api';
import UITextInput from './UITextInput.vue'

export default defineComponent({
  components: { UITextInput },
  $_veeValidate: {
    value() {
      return this.value;
    },
    name() {
      return this.name;
    }
  },
  props: {
    value: {
      type:[String, Number],
      default:''
    },
    label: {
      type:String
    },
    id: {
      type:String,
    },
    placeholder: {
      type:String,
      default:''
    },
    disabled: {
      type: [Boolean, String],
      default: false
    },
    name: {
      type:String,
      default:''
    },
    type: {
      type:[String, Number],
      default:'text'
    },
    error: {
      type: Boolean,
      default: false
    },
    errorMsg: {
      type: String,
      default: ''
    },
    icon: {
      type: String
    },
    description:{
      type: String,
      default: ''
    },
    min: {
      type: [Number, String]
    },
    autocomplete: {
      type: String,
      default: ''
    },
    readonly: {
      type: [Boolean, String]
    },
    maxlength: {
      type: String,
      default: ''
    },
    ignoreLastPass:{
      type: Boolean,
      default: true
    },
    tooltip: {
      type: String,
    },
    postAddOn: {
      type: String,
      default: ''
    },
    preAddOn: {
      type: String,
      default: ''
    },
    disableArrow: {
      type: Boolean,
      default: false
    },
    adjustAddOns: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style scoped>
  .arrow-disable::-webkit-inner-spin-button,
  .arrow-disable::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .hl-text-input-wrapper {
    border: 1px solid rgb(209, 213, 219);
    border-radius: 0.25rem;
    display: flex;
  }
  .hl-text-input-wrapper .hl-text-input {
    border: none;
    padding-left: 0 !important;
    flex: 1;
  }
  .hl-text-input-wrapper .hl-text-input:focus {
    outline: none;
    border: none;
    box-shadow: none;
  }
  .hl-text-input-wrapper .hl-text-input-addon {
    position: relative;
  }
</style>
