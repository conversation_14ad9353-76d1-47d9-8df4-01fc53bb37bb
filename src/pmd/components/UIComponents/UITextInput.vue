<template>
  <input
    :type="type"
    :data-lpignore="ignoreLastPass"
    class="hl-text-input shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded disabled:opacity-50 text-gray-800 "
    :value="value"
    :disabled="disabled"
    @input="$emit('input',$event.target.value)"
    @keyup="$emit('keyup', $event.target.value)"
    @change="$emit('change', $event.target.value)"
    @blur="$emit('blur',  $event.target.value)"
    @focus="$emit('focus',  $event.target.value)"
    @keyup.enter="$emit('enter',  $event.target.value)"
    :min="min"
    :autocomplete="autocomplete"
  />
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
   $_veeValidate: {
    value() {
      return this.value;
    }
  },
  props:{
    value:[String, <PERSON>, <PERSON>olean],
    type: {
      type:String,
      default: 'text'
    },
    disabled: {
      type: [Boolean, String],
      default: false
    },
    min: {
      type: [Number, String]
    },
    autocomplete: {
      type: String,
      default: ''
    },
    ignoreLastPass:{
      type: Boolean,
      default: true
    }
  }
})
</script>

<style >
.hl-text-area-input::placeholder,
.hl-text-input::placeholder {
  color: rgb(175, 184, 188);
  opacity: 1;
}
</style>
