// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here, other Firebase libraries
// are not available in the service worker.
importScripts('https://www.gstatic.com/firebasejs/8.7.0/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.7.0/firebase-messaging.js');

// Initialize the Firebase app in the service worker by passing in the
// messagingSenderId.
firebase.initializeApp({
  apiKey: 'AIzaSyAbVVAyzRuw0Mx18DYczCehMsjWFCYX1Lo',
  authDomain: 'highlevel-staging.firebaseapp.com',
  databaseURL: 'https://highlevel-staging.firebaseio.com',
  projectId: 'highlevel-staging',
  storageBucket: 'highlevel-staging.appspot.com',
  messagingSenderId: '85350210461',
  appId: '1:85350210461:web:d3728d34bf8bd6b5',
})

self.addEventListener('install', event => event.waitUntil(self.skipWaiting()))

self.addEventListener('activate', event =>
  event.waitUntil(self.clients.claim())
)

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging()
let _data

self.addEventListener('notificationclick', function(event) {
  event.notification.close()
  event.waitUntil(
    clients
      .matchAll({
        type: 'window'
      })
      .then(windows => {
        if (windows.length > 0) {
          const window = windows[0]
          window.postMessage({
            customPushMessage: _data
          })
          if ('focus' in window) {
            window.focus()
          }
        } else {
          return clients.openWindow(this.origin).then(window => {
            // https://stackoverflow.com/questions/49954977/service-worker-wait-for-clients-openwindow-to-complete-before-postmessage
            return new Promise((resolve, reject) => {
              setTimeout(() => {
                window.postMessage({
                  customPushMessage: _data
                })
                resolve()
              }, 2000)
            })
          })
        }
      })
  )
})

messaging.onBackgroundMessage(function(message) {
  const data = (_data = message.data)
  let title
  switch (data.type) {
    case 'fb_messenger':
      title = 'New facebook message from ' + (data.fromName || 'unknown')
      return self.registration.showNotification(title, {
        icon: data.logo,
        body: data.body
      })
      break
    case 'sms':
      title = 'New message from ' + (data.fromName || data.fromNumber)
      return self.registration.showNotification(title, {
        icon: data.logo,
        body: data.body
      })
      break
    case 'email':
      title = 'New message from ' + (data.fromName || data.fromEmail)
      return self.registration.showNotification(title, {
        icon: data.logo,
        body: data.body
      })
      break
    case 'call':
      title = 'Call from ' + data.fromNumber
      return self.registration.showNotification(title, {
        icon: data.logo
      })
      break
    case 'review':
    case 'reviewReply':
      if (data.type === 'review') {
        title = 'New review from ' + data.reviewerName
      } else if (data.type === 'reviewReply') {
        title = data.reviewerName + ' replied to the review'
      }
      return self.registration.showNotification(title, {
        icon: data.logo,
        body: data.body
      })
      break
    case 'appointment':
      title = 'New appointment booked by ' + data.name
      return self.registration.showNotification(title, {
        icon: data.logo
      })
      break
    case 'snapshot':
      title = `${data.snapshotName} ${
        data.loadStatus && data.loadStatus === 'success' ? 'Success' : 'Failed'
      }!`
      return self.registration.showNotification(title, {
        body: `Snapshot ${
          data.loadStatus && data.loadStatus === 'success'
            ? 'load completed'
            : 'failed'
        }!`
      })
      break
    case 'voicemail':
      title = 'New voicemail from ' + (data.fromName || data.fromNumber)
      return self.registration.showNotification(title, {
        icon: data.logo
      })
      break
  }
})
