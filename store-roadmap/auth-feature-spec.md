# Authentication System - Complete Feature Specification

## Overview
The authentication system handles user authentication, token management, location-based access control, and session persistence. This document provides complete specifications for implementing equivalent functionality with RTK Query.

## Core Data Models

### AuthUser Object
```typescript
interface AuthUser {
  userId: string;
  companyId: string;
  email: string;
  role: 'admin' | 'agency' | 'user' | 'sub-account';
  permissions: string[];
  locationPermissions?: { [locationId: string]: string[] };
  firstName?: string;
  lastName?: string;
  phone?: string;
  timezone?: string;
  lastLoginTime?: Date;
  isActive: boolean;
}
```

### Authentication State
```typescript
interface AuthState {
  user: AuthUser | null;
  currentLocationId: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  tokenExpiresAt: Date | null;
  refreshTokenExpiresAt: Date | null;
}
```

### Location Access Data
```typescript
interface LocationAccess {
  locationId: string;
  permissions: string[];
  role: string;
  isActive: boolean;
}
```

## API Endpoints Required

### 1. Token Refresh (Primary Authentication Endpoint)
```
GET /signin/refresh
Query Parameters:
- version: number (always 2)
- location_id: string (comma-separated list of location IDs)
- refresh?: boolean (force refresh)

Response:
{
  token: string, // Firebase custom token
  user: AuthUser,
  expires_at: string,
  refresh_expires_at: string
}

Headers:
- Authorization: Bearer {current_token}
```

### 2. User Authentication Check
```
GET /auth/me

Response: AuthUser object

Headers:
- Authorization: Bearer {firebase_token}
```

### 3. Logout
```
POST /signout

Response: 204 No Content
```

### 4. Location Access Validation
```
GET /auth/locations/{locationId}/access

Response:
{
  hasAccess: boolean,
  permissions: string[],
  role: string
}
```

## Business Logic Requirements

### 1. Token Management Strategy
```typescript
interface TokenManager {
  // Store tokens securely
  setTokens(tokens: {
    firebaseToken: string;
    expiresAt: Date;
    refreshExpiresAt: Date;
  }): void;
  
  // Get current valid token
  getValidToken(): Promise<string | null>;
  
  // Check if token needs refresh
  needsRefresh(): boolean;
  
  // Refresh token if needed
  refreshIfNeeded(): Promise<string | null>;
  
  // Clear all tokens
  clearTokens(): void;
}

class TokenManager {
  private readonly TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes
  
  needsRefresh(): boolean {
    const expiresAt = this.getTokenExpiration();
    if (!expiresAt) return true;
    
    const now = Date.now();
    const timeUntilExpiry = expiresAt.getTime() - now;
    
    return timeUntilExpiry <= this.TOKEN_REFRESH_THRESHOLD;
  }
  
  async refreshIfNeeded(): Promise<string | null> {
    if (!this.needsRefresh()) {
      return this.getCurrentToken();
    }
    
    try {
      const response = await this.refreshToken();
      this.setTokens(response);
      return response.token;
    } catch (error) {
      this.clearTokens();
      throw error;
    }
  }
}
```

### 2. Location-based Authentication
```typescript
// Location switching logic
async function switchLocation(locationId: string, forceRefresh: boolean = false): Promise<void> {
  const currentLocationId = getCurrentLocationId();
  
  // Check if location change is needed
  if (!forceRefresh && currentLocationId === locationId) {
    return;
  }
  
  // Get active locations list
  const activeLocations = await addToActiveLocations([locationId]);
  
  // Check if refresh is needed
  const isChanged = activeLocations.includes(locationId) && 
                   !activeLocations.includes(currentLocationId);
  
  if (!forceRefresh && !isChanged) {
    return;
  }
  
  try {
    // Refresh token with new location access
    const response = await refreshTokenForLocation(activeLocations.join(','));
    
    // Update Firebase authentication
    await firebase.auth().signInWithCustomToken(response.token);
    
    // Store refreshed token
    localStorage.set('refreshedToken', response.token);
    
    // Update API client token
    setApiAuthToken(response.token);
    
    // Update current location
    setCurrentLocationId(locationId);
    
  } catch (error) {
    console.error('Failed to switch location:', error);
    throw error;
  }
}
```

### 3. Session Persistence Strategy
```typescript
// Multi-layer session storage
class SessionManager {
  // Store in localStorage (primary)
  setUserSession(user: AuthUser): void {
    const encoded = btoa(JSON.stringify(user));
    localStorage.set('a', encoded);
    localStorage.set('loginDate', new Date().toISOString());
  }
  
  // Store in cookies (backup/cross-domain)
  setUserCookie(user: AuthUser): void {
    const encoded = btoa(JSON.stringify(user));
    const domain = this.getDomainForCookie();
    const maxAge = 365 * 24 * 60 * 60; // 1 year
    
    document.cookie = `a=${encoded};domain=${domain};path=/;max-Age=${maxAge};`;
  }
  
  // Get user from storage
  getUserSession(): AuthUser | null {
    try {
      const encoded = localStorage.get('a');
      if (!encoded) return null;
      
      return JSON.parse(atob(encoded));
    } catch (error) {
      console.error('Failed to parse user session:', error);
      return null;
    }
  }
  
  // Clear all session data
  clearSession(): void {
    localStorage.remove('a');
    localStorage.remove('loginDate');
    localStorage.remove('refreshedToken');
    
    // Clear cookies
    const domain = this.getDomainForCookie();
    document.cookie = `a=; Max-Age=-99999999;domain=${domain};path=/;`;
  }
  
  private getDomainForCookie(): string {
    const hostname = window.location.hostname;
    if (hostname === 'localhost') return 'localhost';
    
    const parts = hostname.split('.');
    return `.${parts[parts.length - 2]}.${parts[parts.length - 1]}`;
  }
}
```

### 4. Firebase Integration
```typescript
// Firebase authentication setup
class FirebaseAuthManager {
  async initializeAuth(): Promise<void> {
    // Set up auth state listener
    firebase.auth().onAuthStateChanged(async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Get user data from our API
          const userData = await this.fetchUserData();
          this.setAuthenticatedUser(userData);
        } catch (error) {
          console.error('Failed to fetch user data:', error);
          await this.handleAuthError(error);
        }
      } else {
        this.clearAuthenticatedUser();
      }
    });
  }
  
  async signInWithCustomToken(token: string): Promise<void> {
    try {
      await firebase.auth().signInWithCustomToken(token);
    } catch (error) {
      console.error('Firebase sign-in failed:', error);
      throw new Error('Authentication failed');
    }
  }
  
  async signOut(): Promise<void> {
    try {
      await firebase.auth().signOut();
    } catch (error) {
      console.error('Firebase sign-out failed:', error);
    }
  }
  
  private async handleAuthError(error: any): Promise<void> {
    if (error.message === 'Missing or insufficient permissions.') {
      // Force logout and redirect
      await this.forceLogout();
    }
  }
  
  private async forceLogout(): Promise<void> {
    // Clear all authentication data
    this.sessionManager.clearSession();
    
    // Sign out from Firebase
    await this.signOut();
    
    // Clear browser data
    this.clearBrowserData();
    
    // Redirect to login
    window.location.href = '/login';
  }
  
  private clearBrowserData(): void {
    // Clear all cookies
    document.cookie.split(';').forEach((cookie) => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    });
    
    // Clear localStorage
    window.localStorage.clear();
    
    // Clear IndexedDB
    if (window.indexedDB) {
      const databases = [
        'firebase-installations-database',
        'firebaseLocalStorageDb',
        'firebase-messaging-database',
      ];
      
      databases.forEach(dbName => {
        window.indexedDB.deleteDatabase(dbName);
      });
    }
  }
}
```

## State Management Requirements

### 1. RTK Query Configuration
```typescript
// Auth API slice
const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/',
    prepareHeaders: async (headers, { getState }) => {
      const token = await tokenManager.getValidToken();
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Auth', 'User'],
  endpoints: (builder) => ({
    refreshToken: builder.mutation<TokenResponse, RefreshTokenRequest>({
      query: ({ locationId, refresh }) => ({
        url: `/signin/refresh?version=2&location_id=${locationId}${refresh ? '&refresh=true' : ''}`,
        method: 'GET',
      }),
      invalidatesTags: ['Auth', 'User'],
    }),
    
    getCurrentUser: builder.query<AuthUser, void>({
      query: () => '/auth/me',
      providesTags: ['User'],
    }),
    
    logout: builder.mutation<void, void>({
      query: () => ({
        url: '/signout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth', 'User'],
    }),
  }),
});
```

### 2. Auth Slice
```typescript
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<AuthUser>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    },
    
    setCurrentLocation: (state, action: PayloadAction<string>) => {
      state.currentLocationId = action.payload;
    },
    
    clearAuth: (state) => {
      state.user = null;
      state.currentLocationId = null;
      state.isAuthenticated = false;
      state.tokenExpiresAt = null;
      state.refreshTokenExpiresAt = null;
      state.error = null;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    setTokenExpiration: (state, action: PayloadAction<{ expiresAt: Date; refreshExpiresAt: Date }>) => {
      state.tokenExpiresAt = action.payload.expiresAt;
      state.refreshTokenExpiresAt = action.payload.refreshExpiresAt;
    },
  },
});
```

## Advanced Features

### 1. Automatic Token Refresh
```typescript
// Middleware for automatic token refresh
const tokenRefreshMiddleware: Middleware = (store) => (next) => async (action) => {
  // Check if token needs refresh before API calls
  if (action.type.endsWith('/pending') && action.meta?.baseQueryMeta) {
    try {
      await tokenManager.refreshIfNeeded();
    } catch (error) {
      // Handle refresh failure
      store.dispatch(authSlice.actions.clearAuth());
      return;
    }
  }
  
  return next(action);
};
```

### 2. Permission Management
```typescript
// Permission checking utilities
class PermissionManager {
  hasPermission(permission: string, locationId?: string): boolean {
    const user = getCurrentUser();
    if (!user) return false;
    
    // Check location-specific permissions first
    if (locationId && user.locationPermissions?.[locationId]) {
      return user.locationPermissions[locationId].includes(permission);
    }
    
    // Fall back to global permissions
    return user.permissions.includes(permission);
  }
  
  hasRole(role: string): boolean {
    const user = getCurrentUser();
    return user?.role === role;
  }
  
  hasAnyRole(roles: string[]): boolean {
    const user = getCurrentUser();
    return user ? roles.includes(user.role) : false;
  }
  
  canAccessLocation(locationId: string): boolean {
    const user = getCurrentUser();
    if (!user) return false;
    
    // Admin and agency users can access all locations
    if (['admin', 'agency'].includes(user.role)) {
      return true;
    }
    
    // Check location-specific permissions
    return !!user.locationPermissions?.[locationId];
  }
}
```

### 3. Route Protection
```typescript
// Route guard hook
export function useAuthGuard(requiredPermission?: string, requiredRole?: string) {
  const { isAuthenticated, user, isLoading } = useSelector(selectAuth);
  const navigate = useNavigate();
  const permissionManager = new PermissionManager();
  
  useEffect(() => {
    if (isLoading) return;
    
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }
    
    if (requiredPermission && !permissionManager.hasPermission(requiredPermission)) {
      navigate('/unauthorized');
      return;
    }
    
    if (requiredRole && !permissionManager.hasRole(requiredRole)) {
      navigate('/unauthorized');
      return;
    }
  }, [isAuthenticated, user, isLoading, requiredPermission, requiredRole, navigate]);
  
  return { isAuthenticated, user, isLoading };
}
```

## Implementation Priorities

### Phase 1: Core Authentication
1. Basic login/logout functionality
2. Token management and refresh
3. Session persistence
4. Firebase integration

### Phase 2: Location Management
1. Location-based authentication
2. Location switching
3. Permission validation
4. Route protection

### Phase 3: Advanced Features
1. Automatic token refresh
2. Error handling and recovery
3. Security enhancements
4. Performance optimizations

## Security Considerations

### 1. Token Security
- Store tokens securely (httpOnly cookies preferred)
- Implement token rotation
- Use short-lived access tokens
- Validate token integrity

### 2. Session Management
- Implement session timeout
- Clear sensitive data on logout
- Handle concurrent sessions
- Monitor for suspicious activity

### 3. Permission Validation
- Validate permissions on both client and server
- Implement principle of least privilege
- Regular permission audits
- Secure permission updates

## Error Handling

### 1. Authentication Errors
- Invalid credentials
- Expired tokens
- Permission denied
- Network failures

### 2. Recovery Strategies
- Automatic retry with exponential backoff
- Graceful degradation
- Clear error messages
- Fallback authentication methods
