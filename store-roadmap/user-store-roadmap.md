# User Store Migration Roadmap

## Current Implementation Analysis

### File: `src/store/user.ts`

**Current State Shape:**
```typescript
interface UserState {
  user?: { [key: string]: any };
  passwordUpdatedInOnboarding: boolean;
  internalUser: boolean;
}
```

**Key Features:**
- Firebase real-time user data synchronization
- Password change detection and logout logic
- Internal user detection based on referrer
- Complex authentication error handling
- User permissions and role management
- Integration with Firebase auth state changes

**Current Actions:**
- `get` - Fetch user data with error handling
- `syncAll` - Set up real-time Firebase listener
- `deleteUserState` - Clear user state
- `logout` - Clean up user data and listeners

**Current Mutations:**
- `set` - Set user data
- `updatePasswordUpdatedInOnboarding` - Track password updates
- `setInternalUser` - Mark user as internal

**Complex Logic:**
- Password change detection with logout
- Firebase authentication error handling
- Internal user detection
- Real-time user data synchronization

## RTK Migration Plan

### 1. RTK Query API Structure

```typescript
// src/store/api/userApi.ts
export const userApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCurrentUser: builder.query<User, void>({
      query: () => '/users/me',
      providesTags: ['User'],
    }),
    
    getUserById: builder.query<User, string>({
      query: (id) => `/users/${id}`,
      providesTags: (result, error, id) => [{ type: 'User', id }],
    }),
    
    updateUser: builder.mutation<User, { id: string; data: Partial<User> }>({
      query: ({ id, data }) => ({
        url: `/users/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'User', id },
        'User',
      ],
    }),
    
    updatePassword: builder.mutation<void, { currentPassword: string; newPassword: string }>({
      query: (data) => ({
        url: '/users/password',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),
  }),
});
```

### 2. RTK Slice for User State

```typescript
// src/store/slices/userSlice.ts
interface UserState {
  currentUser: User | null;
  isLoading: boolean;
  error: string | null;
  passwordUpdatedInOnboarding: boolean;
  internalUser: boolean;
  lastPasswordChange: Date | null;
  loginDate: Date | null;
  realTimeEnabled: boolean;
}

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.error = null;
    },
    
    clearCurrentUser: (state) => {
      state.currentUser = null;
      state.passwordUpdatedInOnboarding = false;
      state.lastPasswordChange = null;
      state.loginDate = null;
    },
    
    setPasswordUpdatedInOnboarding: (state, action: PayloadAction<boolean>) => {
      state.passwordUpdatedInOnboarding = action.payload;
    },
    
    setInternalUser: (state, action: PayloadAction<boolean>) => {
      state.internalUser = action.payload;
    },
    
    setLastPasswordChange: (state, action: PayloadAction<Date>) => {
      state.lastPasswordChange = action.payload;
    },
    
    setLoginDate: (state, action: PayloadAction<Date>) => {
      state.loginDate = action.payload;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    toggleRealTime: (state, action: PayloadAction<boolean>) => {
      state.realTimeEnabled = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(userApi.endpoints.getCurrentUser.matchPending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addMatcher(userApi.endpoints.getCurrentUser.matchFulfilled, (state, action) => {
        state.isLoading = false;
        state.currentUser = action.payload;
        state.error = null;
      })
      .addMatcher(userApi.endpoints.getCurrentUser.matchRejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch user';
      });
  },
});
```

### 3. Complex User Management Hooks

```typescript
// src/hooks/useUser.ts
export const useUser = () => {
  const dispatch = useAppDispatch();
  const userState = useAppSelector(selectUser);
  
  const {
    data: user,
    isLoading,
    error,
    refetch,
  } = userApi.useGetCurrentUserQuery();
  
  // Handle user data updates
  useEffect(() => {
    if (user && user !== userState.currentUser) {
      dispatch(userSlice.actions.setCurrentUser(user));
    }
  }, [user, userState.currentUser, dispatch]);
  
  // Check for internal user
  useEffect(() => {
    const isInternal = localStorage.get('app_referer') === 'https://support.leadconnectorhq.com/';
    if (isInternal !== userState.internalUser) {
      dispatch(userSlice.actions.setInternalUser(isInternal));
    }
  }, [userState.internalUser, dispatch]);
  
  return {
    user: userState.currentUser,
    isLoading: isLoading || userState.isLoading,
    error: error || userState.error,
    isInternalUser: userState.internalUser,
    passwordUpdatedInOnboarding: userState.passwordUpdatedInOnboarding,
    refetch,
  };
};

export const useUserRealTime = (userId: string) => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { realTimeEnabled, loginDate, passwordUpdatedInOnboarding } = useAppSelector(selectUser);
  
  useEffect(() => {
    if (!realTimeEnabled || !userId) return;
    
    // Set up WebSocket for real-time user updates
    const ws = new WebSocket(`/ws/users/${userId}`);
    
    ws.onmessage = async (event) => {
      const { type, data: userData } = JSON.parse(event.data);
      
      if (type === 'user_updated') {
        // Check for critical changes that require logout
        const shouldLogout = await checkForLogoutConditions(userData, {
          loginDate,
          passwordUpdatedInOnboarding,
        });
        
        if (shouldLogout) {
          await handleForceLogout(dispatch, navigate);
        } else {
          // Update user data
          dispatch(userSlice.actions.setCurrentUser(userData));
          
          // Update password change tracking
          if (userData.last_password_change) {
            dispatch(userSlice.actions.setLastPasswordChange(new Date(userData.last_password_change)));
          }
        }
      }
      
      if (type === 'user_deleted' || type === 'user_deactivated') {
        await handleForceLogout(dispatch, navigate);
      }
    };
    
    ws.onerror = (error) => {
      console.error('User WebSocket error:', error);
      dispatch(userSlice.actions.setError('Real-time connection failed'));
    };
    
    return () => {
      ws.close();
    };
  }, [realTimeEnabled, userId, loginDate, passwordUpdatedInOnboarding, dispatch, navigate]);
};
```

### 4. Password Change Detection Logic

```typescript
// src/utils/userSecurity.ts
export const checkForLogoutConditions = async (
  userData: User,
  context: {
    loginDate: Date | null;
    passwordUpdatedInOnboarding: boolean;
  }
): Promise<boolean> => {
  // Check if user is deleted or inactive
  if (userData.deleted === true || userData.is_active === false) {
    return true;
  }
  
  // Check for password change
  if (userData.last_password_change && context.loginDate) {
    const lastPasswordChange = new Date(userData.last_password_change);
    const loginDate = new Date(context.loginDate);
    
    if (loginDate < lastPasswordChange && !context.passwordUpdatedInOnboarding) {
      console.log('Logging out due to password change', loginDate, lastPasswordChange);
      return true;
    }
  }
  
  // Check for first-time login requirement
  const isInternalUser = localStorage.get('app_referer') === 'https://support.leadconnectorhq.com/';
  if (!isInternalUser && !userData.last_login_time) {
    return true;
  }
  
  return false;
};

export const handleForceLogout = async (dispatch: AppDispatch, navigate: NavigateFunction) => {
  try {
    // Sign out from server
    await axios.post('/signout');
    
    // Clear Redux state
    dispatch(userSlice.actions.clearCurrentUser());
    dispatch(authSlice.actions.clearAuth());
    
    // Clear Firebase auth
    await firebase.auth().signOut();
    
    // Clear browser storage
    clearAllBrowserData();
    
    // Navigate to login
    navigate('/login');
  } catch (error) {
    console.error('Error during force logout:', error);
  }
};

const clearAllBrowserData = () => {
  // Clear cookies
  document.cookie.split(';').forEach((cookie) => {
    const eqPos = cookie.indexOf('=');
    const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
  });
  
  // Clear localStorage
  window.localStorage.clear();
  
  // Clear IndexedDB
  if (window.indexedDB) {
    const dbs = [
      'firebase-installations-database',
      'firebaseLocalStorageDb',
      'firebase-messaging-database',
    ];
    dbs.forEach(db => {
      window.indexedDB.deleteDatabase(db);
    });
  }
};
```

### 5. Firebase Integration

```typescript
// src/utils/firebaseUserSync.ts
export const setupFirebaseUserSync = (dispatch: AppDispatch) => {
  return firebase.auth().onAuthStateChanged(async (firebaseUser) => {
    if (firebaseUser) {
      try {
        // Get user data from our API
        const userData = await dispatch(userApi.endpoints.getCurrentUser.initiate()).unwrap();
        
        // Set login date
        const loginDate = localStorage.get('loginDate') || new Date();
        dispatch(userSlice.actions.setLoginDate(new Date(loginDate)));
        
        // Enable real-time updates
        dispatch(userSlice.actions.toggleRealTime(true));
        
      } catch (error) {
        console.error('Error syncing user data:', error);
        
        // Handle authentication errors
        if (error.message === 'Missing or insufficient permissions.') {
          await handleForceLogout(dispatch, navigate);
        }
      }
    } else {
      // User signed out
      dispatch(userSlice.actions.clearCurrentUser());
      dispatch(userSlice.actions.toggleRealTime(false));
    }
  });
};
```

### 6. Migration Strategy

#### Phase 1: Basic User API (Week 1)
1. Create userApi with getCurrentUser endpoint
2. Implement basic userSlice
3. Create useUser hook

#### Phase 2: Real-time Sync (Week 2)
1. Replace Firebase listener with WebSocket
2. Implement password change detection
3. Add force logout logic

#### Phase 3: Security Features (Week 3)
1. Implement internal user detection
2. Add comprehensive logout handling
3. Create browser data clearing utilities

#### Phase 4: Integration (Week 4)
1. Integrate with Firebase auth state
2. Update existing components
3. Add comprehensive error handling

### 7. Testing Strategy

```typescript
// src/store/slices/__tests__/userSlice.test.ts
describe('userSlice', () => {
  it('should set current user', () => {
    const user = createMockUser();
    const state = userSlice.reducer(initialState, userSlice.actions.setCurrentUser(user));
    expect(state.currentUser).toEqual(user);
  });
  
  it('should clear user on logout', () => {
    const state = userSlice.reducer(
      { ...initialState, currentUser: createMockUser() },
      userSlice.actions.clearCurrentUser()
    );
    expect(state.currentUser).toBeNull();
  });
});

// src/utils/__tests__/userSecurity.test.ts
describe('checkForLogoutConditions', () => {
  it('should return true for deleted user', async () => {
    const userData = { ...createMockUser(), deleted: true };
    const result = await checkForLogoutConditions(userData, {});
    expect(result).toBe(true);
  });
  
  it('should return true for password change', async () => {
    const userData = createMockUser();
    const context = {
      loginDate: new Date('2023-01-01'),
      passwordUpdatedInOnboarding: false,
    };
    userData.last_password_change = new Date('2023-01-02');
    
    const result = await checkForLogoutConditions(userData, context);
    expect(result).toBe(true);
  });
});
```

### 8. Migration Checklist

- [ ] Create userApi with CRUD endpoints
- [ ] Implement userSlice with security features
- [ ] Create useUser and useUserRealTime hooks
- [ ] Implement password change detection
- [ ] Add force logout functionality
- [ ] Set up Firebase auth integration
- [ ] Create browser data clearing utilities
- [ ] Add internal user detection
- [ ] Write comprehensive tests
- [ ] Update existing components
- [ ] Handle error states
- [ ] Implement loading states

### 9. Potential Challenges

1. **Firebase Integration**: Maintaining Firebase auth while using RTK
2. **Security Logic**: Complex password change detection
3. **Real-time Updates**: Handling user state changes efficiently
4. **Browser Data**: Comprehensive data clearing on logout

### 10. Success Criteria

- [ ] User data fetching works correctly
- [ ] Real-time updates function properly
- [ ] Password change detection triggers logout
- [ ] Internal user detection works
- [ ] Force logout clears all data
- [ ] Firebase integration is maintained
- [ ] Error handling provides clear feedback
- [ ] Performance is maintained or improved
