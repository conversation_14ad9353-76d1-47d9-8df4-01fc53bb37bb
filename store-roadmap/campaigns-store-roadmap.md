# Campaigns Store Migration Roadmap

## Current Implementation Analysis

### File: `src/store/campaigns.ts`

**Current State Shape:**
```typescript
interface CampaignState {
  campaigns: { [key: string]: any };
  locationId: undefined | string;
}
```

**Key Features:**
- Firebase real-time listener for campaign data
- Location-based campaign filtering
- Simple CRUD operations
- Real-time synchronization with Firestore

**Current Actions:**
- `syncAll` - Set up Firebase listener for location campaigns
- `logout` - Clear campaign data on logout
- `clearAll` - Clear all campaigns and listeners

**Current Mutations:**
- `addUpdate` - Update campaigns array and location
- `clearAll` - Clear campaigns and location data

**Current Getters:**
- `getById` - Find campaign by ID
- `getAll` - Get all campaigns

## RTK Migration Plan

### 1. RTK Query API Structure

```typescript
// src/store/api/campaignsApi.ts
export const campaignsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getCampaigns: builder.query<Campaign[], { locationId: string }>({
      query: ({ locationId }) => `/campaigns?locationId=${locationId}`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Campaign' as const, id })),
              { type: 'Campaign', id: 'LIST' },
            ]
          : [{ type: 'Campaign', id: 'LIST' }],
    }),
    
    getCampaign: builder.query<Campaign, string>({
      query: (id) => `/campaigns/${id}`,
      providesTags: (result, error, id) => [{ type: 'Campaign', id }],
    }),
    
    createCampaign: builder.mutation<Campaign, CreateCampaignRequest>({
      query: (data) => ({
        url: '/campaigns',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Campaign', id: 'LIST' }],
    }),
    
    updateCampaign: builder.mutation<Campaign, { id: string; data: Partial<Campaign> }>({
      query: ({ id, data }) => ({
        url: `/campaigns/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Campaign', id },
        { type: 'Campaign', id: 'LIST' },
      ],
    }),
    
    deleteCampaign: builder.mutation<void, string>({
      query: (id) => ({
        url: `/campaigns/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Campaign', id },
        { type: 'Campaign', id: 'LIST' },
      ],
    }),
    
    duplicateCampaign: builder.mutation<Campaign, { id: string; name: string }>({
      query: ({ id, name }) => ({
        url: `/campaigns/${id}/duplicate`,
        method: 'POST',
        body: { name },
      }),
      invalidatesTags: [{ type: 'Campaign', id: 'LIST' }],
    }),
    
    getCampaignStats: builder.query<CampaignStats, string>({
      query: (id) => `/campaigns/${id}/stats`,
      providesTags: (result, error, id) => [{ type: 'CampaignStats', id }],
    }),
  }),
});
```

### 2. RTK Slice for Local State

```typescript
// src/store/slices/campaignsSlice.ts
interface CampaignsState {
  currentLocationId: string | null;
  selectedCampaignId: string | null;
  realTimeEnabled: boolean;
  filters: CampaignFilters;
  sortBy: 'name' | 'created_at' | 'updated_at';
  sortOrder: 'asc' | 'desc';
}

const initialState: CampaignsState = {
  currentLocationId: null,
  selectedCampaignId: null,
  realTimeEnabled: false,
  filters: {
    status: 'all',
    type: 'all',
    search: '',
  },
  sortBy: 'updated_at',
  sortOrder: 'desc',
};

const campaignsSlice = createSlice({
  name: 'campaigns',
  initialState,
  reducers: {
    setCurrentLocation: (state, action: PayloadAction<string>) => {
      if (state.currentLocationId !== action.payload) {
        state.currentLocationId = action.payload;
        state.selectedCampaignId = null;
      }
    },
    
    setSelectedCampaign: (state, action: PayloadAction<string | null>) => {
      state.selectedCampaignId = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<CampaignFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy as any;
      state.sortOrder = action.payload.sortOrder;
    },
    
    toggleRealTime: (state, action: PayloadAction<boolean>) => {
      state.realTimeEnabled = action.payload;
    },
    
    clearState: (state) => {
      state.currentLocationId = null;
      state.selectedCampaignId = null;
      state.filters = initialState.filters;
    },
  },
});
```

### 3. Custom Hooks for Campaign Management

```typescript
// src/hooks/useCampaigns.ts
export const useCampaigns = (locationId: string) => {
  const dispatch = useAppDispatch();
  const { currentLocationId, filters, sortBy, sortOrder } = useAppSelector(selectCampaigns);
  
  const {
    data: campaigns,
    isLoading,
    error,
    refetch,
  } = campaignsApi.useGetCampaignsQuery(
    { locationId },
    { skip: !locationId }
  );
  
  // Update location when it changes
  useEffect(() => {
    if (locationId !== currentLocationId) {
      dispatch(campaignsSlice.actions.setCurrentLocation(locationId));
    }
  }, [locationId, currentLocationId, dispatch]);
  
  // Filter and sort campaigns
  const filteredCampaigns = useMemo(() => {
    if (!campaigns) return [];
    
    let filtered = campaigns.filter(campaign => {
      // Status filter
      if (filters.status !== 'all' && campaign.status !== filters.status) {
        return false;
      }
      
      // Type filter
      if (filters.type !== 'all' && campaign.type !== filters.type) {
        return false;
      }
      
      // Search filter
      if (filters.search && !campaign.name.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      return true;
    });
    
    // Sort campaigns
    filtered.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    return filtered;
  }, [campaigns, filters, sortBy, sortOrder]);
  
  return {
    campaigns: filteredCampaigns,
    allCampaigns: campaigns || [],
    isLoading,
    error,
    refetch,
  };
};

export const useCampaign = (id: string) => {
  const {
    data: campaign,
    isLoading,
    error,
    refetch,
  } = campaignsApi.useGetCampaignQuery(id, { skip: !id });
  
  return { campaign, isLoading, error, refetch };
};

export const useCampaignStats = (id: string) => {
  const {
    data: stats,
    isLoading,
    error,
    refetch,
  } = campaignsApi.useGetCampaignStatsQuery(id, { skip: !id });
  
  return { stats, isLoading, error, refetch };
};
```

### 4. Campaign Management Hooks

```typescript
// src/hooks/useCampaignActions.ts
export const useCampaignActions = () => {
  const dispatch = useAppDispatch();
  
  const [createCampaign, { isLoading: isCreating }] = campaignsApi.useCreateCampaignMutation();
  const [updateCampaign, { isLoading: isUpdating }] = campaignsApi.useUpdateCampaignMutation();
  const [deleteCampaign, { isLoading: isDeleting }] = campaignsApi.useDeleteCampaignMutation();
  const [duplicateCampaign, { isLoading: isDuplicating }] = campaignsApi.useDuplicateCampaignMutation();
  
  const handleCreateCampaign = useCallback(async (data: CreateCampaignRequest) => {
    try {
      const result = await createCampaign(data).unwrap();
      dispatch(campaignsSlice.actions.setSelectedCampaign(result.id));
      return result;
    } catch (error) {
      console.error('Failed to create campaign:', error);
      throw error;
    }
  }, [createCampaign, dispatch]);
  
  const handleUpdateCampaign = useCallback(async (id: string, data: Partial<Campaign>) => {
    try {
      const result = await updateCampaign({ id, data }).unwrap();
      return result;
    } catch (error) {
      console.error('Failed to update campaign:', error);
      throw error;
    }
  }, [updateCampaign]);
  
  const handleDeleteCampaign = useCallback(async (id: string) => {
    try {
      await deleteCampaign(id).unwrap();
      dispatch(campaignsSlice.actions.setSelectedCampaign(null));
    } catch (error) {
      console.error('Failed to delete campaign:', error);
      throw error;
    }
  }, [deleteCampaign, dispatch]);
  
  const handleDuplicateCampaign = useCallback(async (id: string, name: string) => {
    try {
      const result = await duplicateCampaign({ id, name }).unwrap();
      dispatch(campaignsSlice.actions.setSelectedCampaign(result.id));
      return result;
    } catch (error) {
      console.error('Failed to duplicate campaign:', error);
      throw error;
    }
  }, [duplicateCampaign, dispatch]);
  
  return {
    createCampaign: handleCreateCampaign,
    updateCampaign: handleUpdateCampaign,
    deleteCampaign: handleDeleteCampaign,
    duplicateCampaign: handleDuplicateCampaign,
    isCreating,
    isUpdating,
    isDeleting,
    isDuplicating,
  };
};
```

### 5. Real-time Updates Integration

```typescript
// src/hooks/useCampaignRealTime.ts
export const useCampaignRealTime = (locationId: string) => {
  const dispatch = useAppDispatch();
  const { realTimeEnabled } = useAppSelector(selectCampaigns);
  
  useEffect(() => {
    if (!realTimeEnabled || !locationId) return;
    
    // Set up WebSocket for real-time campaign updates
    const ws = new WebSocket(`/ws/campaigns?locationId=${locationId}`);
    
    ws.onmessage = (event) => {
      const { type, data: campaign } = JSON.parse(event.data);
      
      switch (type) {
        case 'campaign_created':
        case 'campaign_updated':
          // Update campaign in cache
          dispatch(
            campaignsApi.util.updateQueryData('getCampaigns', { locationId }, (draft) => {
              const index = draft.findIndex(c => c.id === campaign.id);
              if (index !== -1) {
                draft[index] = campaign;
              } else {
                draft.push(campaign);
              }
            })
          );
          
          // Update individual campaign cache if it exists
          dispatch(
            campaignsApi.util.updateQueryData('getCampaign', campaign.id, () => campaign)
          );
          break;
          
        case 'campaign_deleted':
          // Remove campaign from cache
          dispatch(
            campaignsApi.util.updateQueryData('getCampaigns', { locationId }, (draft) => {
              return draft.filter(c => c.id !== campaign.id);
            })
          );
          
          // Invalidate individual campaign cache
          dispatch(
            campaignsApi.util.invalidateTags([{ type: 'Campaign', id: campaign.id }])
          );
          break;
      }
    };
    
    ws.onerror = (error) => {
      console.error('Campaign WebSocket error:', error);
    };
    
    return () => {
      ws.close();
    };
  }, [realTimeEnabled, locationId, dispatch]);
};
```

### 6. Migration Strategy

#### Phase 1: Basic API Setup (Week 1)
1. Create campaignsApi with CRUD endpoints
2. Implement basic campaignsSlice
3. Create useCampaigns hook

#### Phase 2: Advanced Features (Week 2)
1. Add filtering and sorting functionality
2. Implement campaign actions hooks
3. Add campaign statistics endpoint

#### Phase 3: Real-time Updates (Week 3)
1. Replace Firebase listener with WebSocket
2. Implement real-time cache updates
3. Add error handling for real-time connections

#### Phase 4: Integration & Testing (Week 4)
1. Update existing components
2. Add comprehensive tests
3. Optimize performance

### 7. Testing Strategy

```typescript
// src/store/api/__tests__/campaignsApi.test.ts
describe('campaignsApi', () => {
  it('should fetch campaigns for location', async () => {
    const mockCampaigns = [createMockCampaign()];
    // Mock API and test
  });
  
  it('should create campaign successfully', async () => {
    const campaignData = createMockCampaignData();
    // Test creation
  });
  
  it('should update campaign cache on real-time updates', () => {
    // Test real-time cache updates
  });
});

// src/hooks/__tests__/useCampaigns.test.ts
describe('useCampaigns', () => {
  it('should filter campaigns by status', () => {
    // Test filtering logic
  });
  
  it('should sort campaigns correctly', () => {
    // Test sorting logic
  });
});
```

### 8. Migration Checklist

- [ ] Create campaignsApi with CRUD endpoints
- [ ] Implement campaignsSlice for local state
- [ ] Create useCampaigns hook with filtering
- [ ] Implement useCampaignActions hook
- [ ] Add campaign statistics endpoint
- [ ] Set up real-time WebSocket updates
- [ ] Implement filtering and sorting
- [ ] Add error handling
- [ ] Write comprehensive tests
- [ ] Update existing components
- [ ] Optimize performance
- [ ] Handle loading states

### 9. Potential Challenges

1. **Real-time Sync**: Maintaining data consistency with WebSocket updates
2. **Filtering Performance**: Efficient client-side filtering for large datasets
3. **Cache Management**: Proper cache invalidation strategies
4. **Error Handling**: Graceful handling of network failures

### 10. Success Criteria

- [ ] Campaign CRUD operations work correctly
- [ ] Real-time updates maintain data consistency
- [ ] Filtering and sorting perform efficiently
- [ ] Location switching clears appropriate data
- [ ] Error handling provides clear feedback
- [ ] Performance is maintained or improved
- [ ] Memory usage is optimized
