# Contacts Management - Complete Feature Specification

## Overview
The contacts management system handles contact data with local caching, watch lists, real-time synchronization, and soft delete tracking. This document provides complete specifications for implementing equivalent functionality with RTK Query.

## Core Data Models

### Contact Object
```typescript
interface Contact {
  id: string;
  location_id: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  company_name?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  website?: string;
  tags?: string[];
  custom_fields?: { [key: string]: any };
  date_added: Date;
  date_updated: Date;
  last_activity?: Date;
  source?: string;
  deleted?: boolean;
  
  // Computed properties
  display_name: string; // first_name + last_name or email
  full_name: string; // first_name + last_name
}
```

### Local Cache Structure
```typescript
interface CachedContact {
  data: Contact;
  timestamp: number; // when cached
  expires: number; // timestamp + 3000ms
}

interface ContactCache {
  [contactId: string]: CachedContact;
}
```

## API Endpoints Required

### 1. Get Individual Contact
```
GET /contacts/{id}

Response: Contact object or 404 if not found/deleted
```

### 2. Get Contacts List
```
GET /contacts
Query Parameters:
- location_id: string (required)
- limit?: number (default: 50)
- offset?: number
- search?: string
- tags?: string[] (comma-separated)
- date_from?: string
- date_to?: string

Response:
{
  contacts: Contact[],
  total: number,
  has_more: boolean
}
```

### 3. Contact CRUD Operations
```
POST /contacts - Create contact
PUT /contacts/{id} - Update contact
DELETE /contacts/{id} - Soft delete contact
```

### 4. Bulk Operations
```
GET /contacts/bulk?ids=id1,id2,id3 - Get multiple contacts
PUT /contacts/bulk - Update multiple contacts
DELETE /contacts/bulk - Delete multiple contacts
```

## Business Logic Requirements

### 1. Local Caching Strategy
The system implements a sophisticated local caching mechanism:

```typescript
class ContactCache {
  private cache: ContactCache = {};
  private readonly CACHE_TIMEOUT = 3000; // 3 seconds
  
  exists(contactId: string): boolean {
    const cached = this.cache[contactId];
    if (!cached) return false;
    
    const isExpired = Date.now() > cached.expires;
    if (isExpired) {
      this.remove(contactId);
      return false;
    }
    
    return true;
  }
  
  get(contactId: string): Contact | null {
    if (!this.exists(contactId)) return null;
    return this.cache[contactId].data;
  }
  
  set(contact: Contact): void {
    this.cache[contact.id] = {
      data: contact,
      timestamp: Date.now(),
      expires: Date.now() + this.CACHE_TIMEOUT
    };
  }
  
  remove(contactId: string): void {
    delete this.cache[contactId];
  }
  
  clear(): void {
    this.cache = {};
  }
}
```

### 2. Watch List Management
Track which contacts are being actively viewed/used:

```typescript
interface WatchListState {
  watchedContacts: Set<string>; // contact IDs being watched
  deletedContacts: Set<string>; // known deleted contact IDs
}

// Add contacts to watch list when:
// - Displayed in UI components
// - Referenced by opportunities
// - Used in conversations
// - Accessed via direct API calls
```

### 3. Contact Data Fetching Strategy
```typescript
// Priority order for contact data:
// 1. Check local cache first (if not expired)
// 2. If cached and valid, return immediately
// 3. If not cached or expired, fetch from API
// 4. Update cache with fresh data
// 5. Add to watch list for real-time updates

async function getContact(id: string, useCache: boolean = true): Promise<Contact | null> {
  // Check cache first
  if (useCache && contactCache.exists(id)) {
    return contactCache.get(id);
  }
  
  // Check if known to be deleted
  if (deletedContacts.has(id)) {
    return null;
  }
  
  try {
    const contact = await fetchContactFromAPI(id);
    if (contact) {
      contactCache.set(contact);
      addToWatchList(id);
      return contact;
    } else {
      addToDeletedContacts(id);
      return null;
    }
  } catch (error) {
    console.error('Failed to fetch contact:', error);
    return null;
  }
}
```

### 4. Real-time Synchronization
Handle real-time contact updates:

```typescript
// WebSocket event types:
interface ContactUpdateEvent {
  type: 'contact_updated' | 'contact_created' | 'contact_deleted';
  data: Contact;
  location_id: string;
}

// Real-time update logic:
function handleContactUpdate(event: ContactUpdateEvent) {
  const { type, data: contact } = event;
  
  switch (type) {
    case 'contact_updated':
    case 'contact_created':
      if (!contact.deleted) {
        // Update cache
        contactCache.set(contact);
        // Update RTK Query cache
        updateContactInCache(contact);
        // Add to watch list if being viewed
        if (isContactBeingViewed(contact.id)) {
          addToWatchList(contact.id);
        }
      } else {
        // Handle soft delete
        contactCache.remove(contact.id);
        addToDeletedContacts(contact.id);
        removeFromWatchList(contact.id);
      }
      break;
      
    case 'contact_deleted':
      contactCache.remove(contact.id);
      addToDeletedContacts(contact.id);
      removeFromWatchList(contact.id);
      invalidateContactInCache(contact.id);
      break;
  }
}
```

## State Management Requirements

### 1. Local State Structure
```typescript
interface ContactsState {
  // Current context
  currentLocationId: string | null;
  
  // Watch list management
  watchedContacts: Set<string>;
  deletedContacts: Set<string>;
  
  // Local cache
  localCache: ContactCache;
  
  // Real-time features
  realTimeEnabled: boolean;
  
  // UI state
  selectedContactId: string | null;
  
  // Performance tracking
  cacheHitRate: number;
  lastCacheCleanup: number;
}
```

### 2. Cache Management Strategy
```typescript
// RTK Query cache configuration
const contactsApi = createApi({
  // ... base config
  keepUnusedDataFor: 300, // 5 minutes
  refetchOnMountOrArgChange: 30, // 30 seconds
  
  endpoints: (builder) => ({
    getContact: builder.query<Contact, string>({
      query: (id) => `/contacts/${id}`,
      providesTags: (result, error, id) => [{ type: 'Contact', id }],
      
      // Custom cache behavior
      serializeQueryArgs: ({ queryArgs }) => queryArgs,
      merge: (currentCache, newItem) => newItem,
      forceRefetch({ currentArg, previousArg }) {
        // Force refetch if not in local cache or expired
        return !contactCache.exists(currentArg);
      },
    }),
  }),
});
```

### 3. Location-based Data Management
```typescript
// Clear data when location changes
function handleLocationChange(newLocationId: string) {
  if (currentLocationId !== newLocationId) {
    // Clear local cache
    contactCache.clear();
    
    // Clear watch lists
    watchedContacts.clear();
    deletedContacts.clear();
    
    // Invalidate RTK Query cache for old location
    dispatch(contactsApi.util.invalidateTags(['Contact']));
    
    // Update current location
    currentLocationId = newLocationId;
  }
}
```

## Advanced Features

### 1. Bulk Contact Operations
```typescript
// Efficient bulk contact fetching
async function getBulkContacts(ids: string[]): Promise<Contact[]> {
  const results: Contact[] = [];
  const uncachedIds: string[] = [];
  
  // Check cache first
  for (const id of ids) {
    const cached = contactCache.get(id);
    if (cached) {
      results.push(cached);
    } else if (!deletedContacts.has(id)) {
      uncachedIds.push(id);
    }
  }
  
  // Fetch uncached contacts
  if (uncachedIds.length > 0) {
    const freshContacts = await fetchBulkContactsFromAPI(uncachedIds);
    for (const contact of freshContacts) {
      contactCache.set(contact);
      results.push(contact);
    }
    
    // Add to watch list
    addToWatchList(uncachedIds);
  }
  
  return results;
}
```

### 2. Contact Search & Filtering
```typescript
interface ContactSearchParams {
  locationId: string;
  query?: string; // search across name, email, phone, company
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
  source?: string;
  limit?: number;
  offset?: number;
}

// Search implementation with caching
async function searchContacts(params: ContactSearchParams): Promise<Contact[]> {
  const cacheKey = generateSearchCacheKey(params);
  
  // Check if we have cached search results
  const cached = searchCache.get(cacheKey);
  if (cached && !cached.isExpired()) {
    return cached.results;
  }
  
  // Fetch from API
  const results = await fetchContactsFromAPI(params);
  
  // Cache individual contacts
  for (const contact of results) {
    contactCache.set(contact);
  }
  
  // Cache search results
  searchCache.set(cacheKey, results);
  
  return results;
}
```

### 3. Contact Validation & Normalization
```typescript
function normalizeContact(contact: Partial<Contact>): Contact {
  return {
    ...contact,
    id: contact.id || generateId(),
    display_name: contact.display_name || 
      `${contact.first_name || ''} ${contact.last_name || ''}`.trim() || 
      contact.email || 
      'Unknown Contact',
    full_name: `${contact.first_name || ''} ${contact.last_name || ''}`.trim(),
    email: contact.email?.toLowerCase(),
    phone: normalizePhoneNumber(contact.phone),
    tags: contact.tags || [],
    custom_fields: contact.custom_fields || {},
    date_added: contact.date_added || new Date(),
    date_updated: new Date(),
  } as Contact;
}
```

## Performance Optimizations

### 1. Memory Management
```typescript
// Periodic cache cleanup
setInterval(() => {
  const now = Date.now();
  const expiredKeys: string[] = [];
  
  for (const [id, cached] of Object.entries(contactCache.cache)) {
    if (now > cached.expires) {
      expiredKeys.push(id);
    }
  }
  
  // Remove expired entries
  for (const key of expiredKeys) {
    contactCache.remove(key);
  }
  
  // Clean up deleted contacts set if too large
  if (deletedContacts.size > 1000) {
    deletedContacts.clear();
  }
}, 60000); // Every minute
```

### 2. Selective Real-time Subscriptions
```typescript
// Only subscribe to contacts in watch list
function setupRealTimeSubscription() {
  const ws = new WebSocket('/ws/contacts');
  
  ws.onmessage = (event) => {
    const update: ContactUpdateEvent = JSON.parse(event.data);
    
    // Only process updates for watched contacts or current location
    if (watchedContacts.has(update.data.id) || 
        update.data.location_id === currentLocationId) {
      handleContactUpdate(update);
    }
  };
}
```

## Implementation Priorities

### Phase 1: Core Functionality
1. Basic contact CRUD operations
2. Local caching with expiration
3. Watch list management
4. Location-based filtering

### Phase 2: Advanced Caching
1. Bulk contact operations
2. Search result caching
3. Cache cleanup and optimization
4. Memory management

### Phase 3: Real-time Features
1. WebSocket integration
2. Real-time cache updates
3. Selective subscriptions
4. Connection management

### Phase 4: Performance & UX
1. Optimistic updates
2. Background sync
3. Error handling and retry logic
4. Loading states and skeleton screens

## Testing Requirements

### 1. Cache Behavior Tests
- Cache hit/miss scenarios
- Expiration handling
- Memory cleanup
- Bulk operations

### 2. Real-time Update Tests
- WebSocket connection handling
- Update processing
- Cache synchronization
- Error recovery

### 3. Performance Tests
- Large contact list handling
- Cache efficiency
- Memory usage optimization
- Search performance

## Error Handling

### 1. Network Errors
- Graceful degradation when API unavailable
- Retry logic with exponential backoff
- Offline capability using cached data

### 2. Data Consistency
- Handle stale cache data
- Resolve conflicts between cache and server
- Validate contact data integrity

### 3. Real-time Connection Issues
- Reconnect on WebSocket disconnection
- Sync data after reconnection
- Handle partial update failures
