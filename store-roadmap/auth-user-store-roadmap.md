# Authentication Store Migration Roadmap

## Current Implementation Analysis

### File: `src/store/auth_user.ts`

**Current State Shape:**
```typescript
interface AuthState {
  user?: AuthUser,
  locationId: undefined | string
}
```

**Key Features:**
- Firebase authentication integration
- Token refresh mechanism
- Location-based authentication
- Local storage management
- Cookie-based session persistence

**Current Actions:**
- `refreshFirebaseToken` - Refreshes Firebase token for location access
- `get` - Retrieves user from localStorage
- `set` - Stores user data in localStorage and cookies
- `logout` - Clears authentication data

**Current Mutations:**
- `setLocationId` - Updates current location
- `set` - Sets user data
- `delete` - Clears user data

## RTK Migration Plan

### 1. RTK Slice Structure

```typescript
// src/store/slices/authSlice.ts
interface AuthState {
  user: AuthUser | null;
  locationId: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  locationId: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};
```

### 2. RTK Query API Endpoints

```typescript
// src/store/api/authApi.ts
export const authApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    refreshToken: builder.mutation<TokenResponse, RefreshTokenRequest>({
      query: ({ locationId, refresh }) => ({
        url: `/signin/refresh?version=2&location_id=${locationId}`,
        method: 'GET',
      }),
      invalidatesTags: ['User'],
    }),
    signOut: builder.mutation<void, void>({
      query: () => ({
        url: '/signout',
        method: 'POST',
      }),
    }),
  }),
});
```

### 3. Migration Steps

#### Step 1: Create Base Auth Slice
```typescript
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<AuthUser>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
      state.error = null;
    },
    setLocationId: (state, action: PayloadAction<string>) => {
      state.locationId = action.payload;
    },
    clearAuth: (state) => {
      state.user = null;
      state.locationId = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
  },
});
```

#### Step 2: Create Auth Thunks
```typescript
// Complex async logic that doesn't fit in RTK Query
export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { dispatch, getState }) => {
    const b64String = localStorage.get('a');
    if (!b64String) throw new Error('Not logged in');
    
    const user = JSON.parse(atob(b64String));
    dispatch(authSlice.actions.setUser(user));
    return user;
  }
);

export const refreshFirebaseToken = createAsyncThunk(
  'auth/refreshFirebaseToken',
  async (params: { locationId: string; refresh?: boolean }, { dispatch, getState }) => {
    // Implementation logic
  }
);
```

#### Step 3: Local Storage Middleware
```typescript
// src/store/middleware/authMiddleware.ts
export const authMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);
  
  if (authSlice.actions.setUser.match(action)) {
    // Handle localStorage and cookie updates
    const user = action.payload;
    localStorage.set('a', btoa(JSON.stringify(user)));
    // Set cookie logic
  }
  
  if (authSlice.actions.clearAuth.match(action)) {
    // Clear localStorage and cookies
    localStorage.remove('a');
    // Clear cookie logic
  }
  
  return result;
};
```

### 4. Integration Points

#### Firebase Integration
```typescript
// src/utils/firebaseAuth.ts
export const setupFirebaseAuth = (dispatch: AppDispatch) => {
  firebase.auth().onAuthStateChanged((user) => {
    if (user) {
      // Handle authenticated user
    } else {
      dispatch(authSlice.actions.clearAuth());
    }
  });
};
```

#### Router Integration
```typescript
// src/hooks/useAuthGuard.ts
export const useAuthGuard = () => {
  const { isAuthenticated, isLoading } = useSelector(selectAuth);
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, isLoading, navigate]);
};
```

### 5. Testing Strategy

#### Unit Tests
```typescript
// src/store/slices/__tests__/authSlice.test.ts
describe('authSlice', () => {
  it('should set user on setUser action', () => {
    const user = { id: '1', email: '<EMAIL>' };
    const state = authSlice.reducer(initialState, authSlice.actions.setUser(user));
    expect(state.user).toEqual(user);
    expect(state.isAuthenticated).toBe(true);
  });
});
```

#### Integration Tests
```typescript
// src/store/api/__tests__/authApi.test.ts
describe('authApi', () => {
  it('should refresh token successfully', async () => {
    // Mock API response and test endpoint
  });
});
```

### 6. Migration Checklist

- [ ] Create auth slice with proper TypeScript types
- [ ] Implement RTK Query endpoints for auth operations
- [ ] Create async thunks for complex auth logic
- [ ] Set up localStorage/cookie middleware
- [ ] Integrate with Firebase authentication
- [ ] Update router guards to use new auth state
- [ ] Write comprehensive tests
- [ ] Update components to use new selectors
- [ ] Handle error states and loading states
- [ ] Implement logout functionality
- [ ] Test token refresh mechanism
- [ ] Verify location-based authentication works

### 7. Potential Challenges

1. **Firebase Integration**: Maintaining Firebase auth while using RTK
2. **Token Refresh**: Complex token refresh logic with location switching
3. **Cookie Management**: Browser cookie handling across domains
4. **Backward Compatibility**: Ensuring existing components continue to work

### 8. Success Criteria

- [ ] All authentication flows work correctly
- [ ] Token refresh mechanism functions properly
- [ ] Location switching maintains authentication
- [ ] Logout clears all authentication data
- [ ] Error handling provides clear feedback
- [ ] Performance is maintained or improved
- [ ] Type safety is enforced throughout
