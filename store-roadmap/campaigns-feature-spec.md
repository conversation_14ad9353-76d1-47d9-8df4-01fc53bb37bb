# Campaigns Management - Complete Feature Specification

## Overview
The campaigns management system handles marketing campaign data with real-time synchronization, location-based filtering, and comprehensive CRUD operations. This document provides complete specifications for implementing equivalent functionality with RTK Query.

## Core Data Models

### Campaign Object
```typescript
interface Campaign {
  id: string;
  location_id: string;
  name: string;
  description?: string;
  type: 'email' | 'sms' | 'voicemail' | 'facebook' | 'gmb' | 'webhook' | 'workflow';
  status: 'active' | 'inactive' | 'draft' | 'completed' | 'paused';
  
  // Campaign configuration
  trigger_type?: 'manual' | 'form_submission' | 'appointment_booked' | 'tag_added' | 'date_based';
  trigger_config?: { [key: string]: any };
  
  // Content and settings
  content?: {
    subject?: string;
    body?: string;
    template_id?: string;
    attachments?: string[];
  };
  
  // Scheduling
  schedule?: {
    start_date?: Date;
    end_date?: Date;
    timezone?: string;
    recurring?: boolean;
    frequency?: 'daily' | 'weekly' | 'monthly';
  };
  
  // Targeting
  target_audience?: {
    tags?: string[];
    custom_fields?: { [key: string]: any };
    location_ids?: string[];
  };
  
  // Tracking and analytics
  stats?: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    replied: number;
    bounced: number;
    unsubscribed: number;
  };
  
  // Metadata
  created_by: string;
  created_at: Date;
  updated_at: Date;
  last_run?: Date;
  next_run?: Date;
  
  // Flags
  is_template: boolean;
  is_archived: boolean;
  deleted?: boolean;
}
```

### Campaign Statistics
```typescript
interface CampaignStats {
  campaign_id: string;
  total_sent: number;
  total_delivered: number;
  total_opened: number;
  total_clicked: number;
  total_replied: number;
  total_bounced: number;
  total_unsubscribed: number;
  
  // Rates
  delivery_rate: number; // delivered / sent
  open_rate: number; // opened / delivered
  click_rate: number; // clicked / delivered
  reply_rate: number; // replied / delivered
  bounce_rate: number; // bounced / sent
  unsubscribe_rate: number; // unsubscribed / delivered
  
  // Time-based data
  daily_stats: Array<{
    date: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
  
  last_updated: Date;
}
```

### Campaign Filters
```typescript
interface CampaignFilters {
  location_id: string;
  status?: 'all' | 'active' | 'inactive' | 'draft' | 'completed' | 'paused';
  type?: 'all' | 'email' | 'sms' | 'voicemail' | 'facebook' | 'gmb' | 'webhook' | 'workflow';
  search?: string; // search in name and description
  created_by?: string;
  date_from?: string;
  date_to?: string;
  is_template?: boolean;
  is_archived?: boolean;
  tags?: string[];
}
```

## API Endpoints Required

### 1. Get Campaigns List
```
GET /campaigns
Query Parameters:
- location_id: string (required)
- status?: string
- type?: string
- search?: string
- created_by?: string
- date_from?: string (ISO date)
- date_to?: string (ISO date)
- is_template?: boolean
- is_archived?: boolean
- limit?: number (default: 50)
- offset?: number
- sort_by?: 'name' | 'created_at' | 'updated_at' | 'last_run'
- sort_order?: 'asc' | 'desc'

Response:
{
  campaigns: Campaign[],
  total: number,
  has_more: boolean
}
```

### 2. Individual Campaign Operations
```
GET /campaigns/{id}
Response: Campaign object

POST /campaigns
Body: CreateCampaignRequest
Response: Campaign object

PUT /campaigns/{id}
Body: UpdateCampaignRequest
Response: Campaign object

DELETE /campaigns/{id}
Response: 204 No Content
```

### 3. Campaign Actions
```
POST /campaigns/{id}/start
POST /campaigns/{id}/pause
POST /campaigns/{id}/stop
POST /campaigns/{id}/duplicate
Body: { name: string }
Response: Campaign object

POST /campaigns/{id}/test
Body: { contact_ids: string[] }
Response: { success: boolean, message: string }
```

### 4. Campaign Statistics
```
GET /campaigns/{id}/stats
Query Parameters:
- date_from?: string
- date_to?: string
- granularity?: 'hour' | 'day' | 'week' | 'month'

Response: CampaignStats object
```

### 5. Campaign Templates
```
GET /campaigns/templates
Query Parameters:
- location_id: string
- type?: string

Response:
{
  templates: Campaign[],
  total: number
}

POST /campaigns/{id}/save-as-template
Body: { name: string, description?: string }
Response: Campaign object
```

## Business Logic Requirements

### 1. Real-time Synchronization
```typescript
// WebSocket event types for campaigns
interface CampaignUpdateEvent {
  type: 'campaign_created' | 'campaign_updated' | 'campaign_deleted' | 'campaign_stats_updated';
  data: Campaign | CampaignStats;
  location_id: string;
  user_id: string;
}

// Real-time update handling
function handleCampaignUpdate(event: CampaignUpdateEvent) {
  const { type, data, location_id } = event;
  
  // Only process updates for current location
  if (location_id !== getCurrentLocationId()) {
    return;
  }
  
  switch (type) {
    case 'campaign_created':
    case 'campaign_updated':
      const campaign = data as Campaign;
      if (!campaign.deleted) {
        updateCampaignInCache(campaign);
        
        // Update lists that might include this campaign
        invalidateCampaignLists();
      } else {
        removeCampaignFromCache(campaign.id);
      }
      break;
      
    case 'campaign_deleted':
      const deletedCampaign = data as Campaign;
      removeCampaignFromCache(deletedCampaign.id);
      break;
      
    case 'campaign_stats_updated':
      const stats = data as CampaignStats;
      updateCampaignStatsInCache(stats);
      break;
  }
}
```

### 2. Campaign Filtering & Search
```typescript
// Client-side filtering logic
function filterCampaigns(campaigns: Campaign[], filters: CampaignFilters): Campaign[] {
  return campaigns.filter(campaign => {
    // Status filter
    if (filters.status && filters.status !== 'all' && campaign.status !== filters.status) {
      return false;
    }
    
    // Type filter
    if (filters.type && filters.type !== 'all' && campaign.type !== filters.type) {
      return false;
    }
    
    // Search filter (name and description)
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      const searchableText = `${campaign.name} ${campaign.description || ''}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) {
        return false;
      }
    }
    
    // Created by filter
    if (filters.created_by && campaign.created_by !== filters.created_by) {
      return false;
    }
    
    // Date range filter
    if (filters.date_from || filters.date_to) {
      const createdDate = new Date(campaign.created_at);
      if (filters.date_from && createdDate < new Date(filters.date_from)) {
        return false;
      }
      if (filters.date_to && createdDate > new Date(filters.date_to)) {
        return false;
      }
    }
    
    // Template filter
    if (filters.is_template !== undefined && campaign.is_template !== filters.is_template) {
      return false;
    }
    
    // Archived filter
    if (filters.is_archived !== undefined && campaign.is_archived !== filters.is_archived) {
      return false;
    }
    
    return true;
  });
}

// Sorting logic
function sortCampaigns(campaigns: Campaign[], sortBy: string, sortOrder: 'asc' | 'desc'): Campaign[] {
  return [...campaigns].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'created_at':
        aValue = new Date(a.created_at);
        bValue = new Date(b.created_at);
        break;
      case 'updated_at':
        aValue = new Date(a.updated_at);
        bValue = new Date(b.updated_at);
        break;
      case 'last_run':
        aValue = a.last_run ? new Date(a.last_run) : new Date(0);
        bValue = b.last_run ? new Date(b.last_run) : new Date(0);
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
}
```

### 3. Campaign State Management
```typescript
// Campaign status transitions
const VALID_STATUS_TRANSITIONS = {
  'draft': ['active', 'inactive'],
  'inactive': ['active', 'draft'],
  'active': ['paused', 'completed', 'inactive'],
  'paused': ['active', 'completed', 'inactive'],
  'completed': ['inactive'], // Can only reactivate as inactive first
};

function canTransitionStatus(currentStatus: string, newStatus: string): boolean {
  return VALID_STATUS_TRANSITIONS[currentStatus]?.includes(newStatus) || false;
}

// Campaign validation
function validateCampaign(campaign: Partial<Campaign>): string[] {
  const errors: string[] = [];
  
  if (!campaign.name?.trim()) {
    errors.push('Campaign name is required');
  }
  
  if (!campaign.type) {
    errors.push('Campaign type is required');
  }
  
  if (!campaign.location_id) {
    errors.push('Location ID is required');
  }
  
  // Type-specific validation
  if (campaign.type === 'email') {
    if (!campaign.content?.subject) {
      errors.push('Email subject is required');
    }
    if (!campaign.content?.body && !campaign.content?.template_id) {
      errors.push('Email body or template is required');
    }
  }
  
  if (campaign.type === 'sms') {
    if (!campaign.content?.body) {
      errors.push('SMS message is required');
    }
    if (campaign.content?.body && campaign.content.body.length > 160) {
      errors.push('SMS message must be 160 characters or less');
    }
  }
  
  // Schedule validation
  if (campaign.schedule?.start_date && campaign.schedule?.end_date) {
    if (new Date(campaign.schedule.start_date) >= new Date(campaign.schedule.end_date)) {
      errors.push('End date must be after start date');
    }
  }
  
  return errors;
}
```

## State Management Requirements

### 1. RTK Query Configuration
```typescript
const campaignsApi = createApi({
  reducerPath: 'campaignsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/',
    prepareHeaders: (headers, { getState }) => {
      const token = selectAuthToken(getState());
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Campaign', 'CampaignStats', 'CampaignTemplate'],
  endpoints: (builder) => ({
    getCampaigns: builder.query<CampaignsResponse, CampaignFilters>({
      query: (filters) => ({
        url: '/campaigns',
        params: filters,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.campaigns.map(({ id }) => ({ type: 'Campaign' as const, id })),
              { type: 'Campaign', id: 'LIST' },
            ]
          : [{ type: 'Campaign', id: 'LIST' }],
    }),
    
    getCampaign: builder.query<Campaign, string>({
      query: (id) => `/campaigns/${id}`,
      providesTags: (result, error, id) => [{ type: 'Campaign', id }],
    }),
    
    createCampaign: builder.mutation<Campaign, CreateCampaignRequest>({
      query: (data) => ({
        url: '/campaigns',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Campaign', id: 'LIST' }],
    }),
    
    updateCampaign: builder.mutation<Campaign, { id: string; data: UpdateCampaignRequest }>({
      query: ({ id, data }) => ({
        url: `/campaigns/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Campaign', id },
        { type: 'Campaign', id: 'LIST' },
      ],
    }),
    
    deleteCampaign: builder.mutation<void, string>({
      query: (id) => ({
        url: `/campaigns/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Campaign', id },
        { type: 'Campaign', id: 'LIST' },
      ],
    }),
    
    getCampaignStats: builder.query<CampaignStats, { id: string; dateFrom?: string; dateTo?: string }>({
      query: ({ id, dateFrom, dateTo }) => ({
        url: `/campaigns/${id}/stats`,
        params: { date_from: dateFrom, date_to: dateTo },
      }),
      providesTags: (result, error, { id }) => [{ type: 'CampaignStats', id }],
    }),
  }),
});
```

### 2. Local State Slice
```typescript
interface CampaignsState {
  currentLocationId: string | null;
  selectedCampaignId: string | null;
  activeFilters: CampaignFilters;
  sortBy: 'name' | 'created_at' | 'updated_at' | 'last_run';
  sortOrder: 'asc' | 'desc';
  realTimeEnabled: boolean;
  bulkSelection: Set<string>;
}

const campaignsSlice = createSlice({
  name: 'campaigns',
  initialState,
  reducers: {
    setCurrentLocation: (state, action: PayloadAction<string>) => {
      if (state.currentLocationId !== action.payload) {
        state.currentLocationId = action.payload;
        state.selectedCampaignId = null;
        state.bulkSelection.clear();
        state.activeFilters = { ...state.activeFilters, location_id: action.payload };
      }
    },
    
    setSelectedCampaign: (state, action: PayloadAction<string | null>) => {
      state.selectedCampaignId = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<CampaignFilters>>) => {
      state.activeFilters = { ...state.activeFilters, ...action.payload };
    },
    
    setSorting: (state, action: PayloadAction<{ sortBy: string; sortOrder: 'asc' | 'desc' }>) => {
      state.sortBy = action.payload.sortBy as any;
      state.sortOrder = action.payload.sortOrder;
    },
    
    toggleBulkSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      if (state.bulkSelection.has(id)) {
        state.bulkSelection.delete(id);
      } else {
        state.bulkSelection.add(id);
      }
    },
    
    clearBulkSelection: (state) => {
      state.bulkSelection.clear();
    },
    
    toggleRealTime: (state, action: PayloadAction<boolean>) => {
      state.realTimeEnabled = action.payload;
    },
  },
});
```

## Implementation Priorities

### Phase 1: Core CRUD Operations
1. Basic campaign listing with filters
2. Create, read, update, delete campaigns
3. Campaign validation and error handling
4. Location-based filtering

### Phase 2: Advanced Features
1. Campaign statistics and analytics
2. Campaign templates
3. Bulk operations
4. Campaign actions (start, pause, stop)

### Phase 3: Real-time & Performance
1. Real-time updates via WebSocket
2. Optimistic updates
3. Advanced caching strategies
4. Performance optimizations

## Testing Requirements

### 1. API Integration Tests
- CRUD operations for all campaign types
- Filter combinations and edge cases
- Statistics calculation accuracy
- Real-time update handling

### 2. Business Logic Tests
- Campaign validation rules
- Status transition logic
- Filter and search functionality
- Sorting algorithms

### 3. Performance Tests
- Large campaign list handling
- Real-time update performance
- Memory usage optimization
- Cache efficiency
