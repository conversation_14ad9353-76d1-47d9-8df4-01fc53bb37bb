# Master Store Feature Documentation & RTK Implementation Roadmap

## Overview
This document provides comprehensive feature documentation for the existing Vuex store architecture to enable implementation of equivalent functionality using Redux Toolkit (RTK) with RTK Query in a separate Next.js project. The current codebase contains 39 store modules with complex patterns including Firebase real-time listeners, REST API calls, and location-based data management.

## Purpose
These roadmaps serve as complete feature specifications that can be used to implement equivalent functionality in RTK Query without requiring access to the original Vuex store code.

## Current Architecture Analysis

### Store Structure
- **Main Store**: `src/store/index.ts` - Central store configuration with 39 modules
- **State Models**: `src/store/state_models.ts` - TypeScript interfaces for all state shapes
- **Module Pattern**: Namespaced Vuex modules with actions, mutations, getters, and state

### Key Patterns Identified
1. **Firebase Real-time Listeners**: Real-time data synchronization using Firebase Firestore
2. **REST API Integration**: Axios-based API calls for CRUD operations
3. **Location-based Filtering**: Most modules filter data by locationId
4. **Local Caching**: Custom caching mechanisms for performance optimization
5. **Authentication Flow**: Complex auth patterns with token refresh and session management
6. **Aggregate Data Management**: Complex calculations and data aggregations

## Migration Strategy

### Phase 1: Foundation Setup (Week 1-2)
1. **RTK Setup**
   - Install Redux Toolkit and RTK Query
   - Configure store with RTK
   - Set up TypeScript types
   - Create base API slice configuration

2. **Core Infrastructure**
   - Authentication slice with RTK Query
   - Base API configuration with interceptors
   - Error handling middleware
   - DevTools configuration

### Phase 2: Core Modules Migration (Week 3-6)
**Priority Order:**
1. `auth_user.ts` - Authentication foundation
2. `user.ts` - User management
3. `company.ts` - Company data
4. `location.ts` - Location management
5. `contacts.ts` - Contact management

### Phase 3: Business Logic Modules (Week 7-12)
**Priority Order:**
1. `opportunities.ts` - Complex business logic
2. `campaigns.ts` - Marketing campaigns
3. `workflows.ts` - Workflow management
4. `pipelines.ts` - Sales pipelines
5. `conversation.ts` - Communication management

### Phase 4: Supporting Modules (Week 13-16)
**Remaining modules in dependency order**

### Phase 5: Testing & Optimization (Week 17-18)
- Comprehensive testing
- Performance optimization
- Documentation updates

## Technical Considerations

### RTK Query Configuration
```typescript
// Base API configuration
export const baseApi = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/',
    prepareHeaders: (headers, { getState }) => {
      // Add auth token
      return headers;
    },
  }),
  tagTypes: ['User', 'Contact', 'Opportunity', 'Campaign'],
  endpoints: () => ({}),
});
```

### State Shape Migration
- Convert Vuex state to RTK slices
- Maintain backward compatibility during transition
- Use RTK Query for server state
- Keep local UI state in RTK slices

### Real-time Data Handling
- Replace Firebase listeners with RTK Query polling
- Implement WebSocket integration for real-time updates
- Use RTK Query cache invalidation for data freshness

## Dependencies and Setup

### Required Packages
```json
{
  "@reduxjs/toolkit": "^1.9.0",
  "react-redux": "^8.0.0",
  "redux": "^4.2.0"
}
```

### TypeScript Configuration
- Strict typing for all slices
- Proper API response typing
- State shape interfaces

## Risk Mitigation

### Gradual Migration
- Maintain both stores during transition
- Module-by-module migration
- Feature flags for new implementation

### Testing Strategy
- Unit tests for each slice
- Integration tests for API endpoints
- E2E tests for critical user flows

### Rollback Plan
- Keep Vuex implementation as fallback
- Feature toggles for easy rollback
- Monitoring and alerting for issues

## Success Metrics

### Performance
- Reduced bundle size
- Improved data fetching efficiency
- Better caching strategies

### Developer Experience
- Type safety improvements
- Better debugging with Redux DevTools
- Simplified state management patterns

### Maintainability
- Cleaner code organization
- Better separation of concerns
- Improved testability

## Next Steps
1. Review individual module roadmaps
2. Set up development environment
3. Begin Phase 1 implementation
4. Establish testing framework
5. Create migration timeline with team
