# Complete Store Modules Summary & Implementation Priority

## Overview
This document provides a comprehensive overview of all 39 store modules in the Vuex codebase, categorized by complexity and implementation priority for RTK Query migration.

## Module Categories by Complexity

### 🔴 High Complexity Modules (Weeks 3-8)
These modules have complex business logic, real-time features, and advanced data management.

#### 1. **opportunities.ts** - Sales Pipeline Management
- **Complexity**: Very High
- **Features**: Complex Elasticsearch search, real-time updates, aggregate calculations, pagination
- **API Endpoints**: 5+ endpoints with complex filtering
- **Business Logic**: Opportunity positioning, filter validation, contact integration
- **Real-time**: Firebase listeners + WebSocket integration needed
- **Priority**: High (Core business functionality)

#### 2. **contacts.ts** - Contact Management  
- **Complexity**: High
- **Features**: Local caching (3s timeout), watch lists, soft delete tracking
- **API Endpoints**: 4+ endpoints with bulk operations
- **Business Logic**: Cache management, real-time sync, location filtering
- **Real-time**: Contact updates, deletion tracking
- **Priority**: High (Referenced by many other modules)

#### 3. **conversation.ts** - Communication Management
- **Complexity**: High  
- **Features**: Message threading, real-time chat, file attachments, search
- **API Endpoints**: 6+ endpoints for messages, attachments, search
- **Business Logic**: Message ordering, thread management, typing indicators
- **Real-time**: Live chat updates, presence indicators
- **Priority**: High (Core communication feature)

#### 4. **auth_user.ts** - Authentication System
- **Complexity**: High
- **Features**: Firebase integration, token refresh, location switching, session management
- **API Endpoints**: 3+ auth endpoints
- **Business Logic**: Token management, location-based auth, session persistence
- **Real-time**: Auth state changes
- **Priority**: Critical (Foundation for all other modules)

#### 5. **user.ts** - User Management
- **Complexity**: High
- **Features**: Real-time user sync, password change detection, force logout logic
- **API Endpoints**: 3+ user endpoints
- **Business Logic**: Security validation, session management, internal user detection
- **Real-time**: User profile updates, security events
- **Priority**: High (User profile and security)

### 🟡 Medium Complexity Modules (Weeks 9-14)

#### 6. **campaigns.ts** - Marketing Campaigns
- **Complexity**: Medium-High
- **Features**: Campaign management, real-time sync, location filtering
- **API Endpoints**: 4+ endpoints
- **Business Logic**: Campaign validation, status management
- **Real-time**: Campaign updates
- **Priority**: Medium-High

#### 7. **workflows.ts** - Automation Workflows  
- **Complexity**: Medium-High
- **Features**: Workflow builder, trigger management, step execution
- **API Endpoints**: 5+ endpoints
- **Business Logic**: Workflow validation, execution logic
- **Real-time**: Workflow status updates
- **Priority**: Medium-High

#### 8. **pipelines.ts** - Sales Pipelines
- **Complexity**: Medium
- **Features**: Pipeline management, stage configuration
- **API Endpoints**: 3+ endpoints  
- **Business Logic**: Pipeline validation, stage ordering
- **Real-time**: Pipeline updates
- **Priority**: Medium

#### 9. **calendars.ts** - Calendar Integration
- **Complexity**: Medium
- **Features**: Calendar sync, appointment management
- **API Endpoints**: 4+ endpoints
- **Business Logic**: Calendar validation, sync logic
- **Real-time**: Calendar updates
- **Priority**: Medium

#### 10. **locations.ts** - Location Management
- **Complexity**: Medium
- **Features**: Multi-location support, active location tracking
- **API Endpoints**: 3+ endpoints
- **Business Logic**: Location switching, permission validation
- **Real-time**: Location updates
- **Priority**: High (Foundation for multi-tenancy)

### 🟢 Low-Medium Complexity Modules (Weeks 15-18)

#### 11. **company.ts** - Company Management
- **Complexity**: Medium
- **Features**: Company data, white-label domain handling
- **API Endpoints**: 2+ endpoints
- **Business Logic**: Company validation, domain management
- **Real-time**: Company updates
- **Priority**: Medium

#### 12. **users.ts** - Users List Management
- **Complexity**: Medium
- **Features**: User listing, role management
- **API Endpoints**: 3+ endpoints
- **Business Logic**: User filtering, role validation
- **Real-time**: User list updates
- **Priority**: Medium

#### 13. **teams.ts** - Team Management
- **Complexity**: Medium
- **Features**: Team organization, member management
- **API Endpoints**: 3+ endpoints
- **Business Logic**: Team validation, member permissions
- **Real-time**: Team updates
- **Priority**: Medium

#### 14. **integrations.ts** - Third-party Integrations
- **Complexity**: Medium
- **Features**: Integration management, OAuth handling
- **API Endpoints**: 4+ endpoints
- **Business Logic**: Integration validation, OAuth flow
- **Real-time**: Integration status updates
- **Priority**: Medium

### 🔵 Simple Modules (Weeks 19-20)

#### 15-39. **Simple State Management Modules**
These modules have basic CRUD operations and minimal business logic:

- **filters.ts** - Filter state management
- **products.ts** - Product catalog
- **affiliate.ts** - Affiliate tracking  
- **agency_users.ts** - Agency user management
- **agency_twilio.ts** - Twilio integration
- **calendar_providers.ts** - Calendar provider configs
- **campaign_folder.ts** - Campaign organization
- **contact.ts** - Single contact state
- **custom_fields.ts** - Custom field definitions
- **default_email_service.ts** - Email service config
- **default_number.ts** - Phone number management
- **funnel.ts** - Sales funnel management
- **iframe.ts** - Iframe communication
- **image_preview.ts** - Image preview state
- **incoming_call.ts** - Call management
- **levelup_day.ts** - Feature flags
- **linked_calendars.ts** - Calendar connections
- **mailgun_services.ts** - Mailgun integration
- **manual_call_status.ts** - Call status tracking
- **membership.ts** - Membership management
- **oauth2.ts** - OAuth state management
- **phone_call.ts** - Phone call handling
- **review_aggregate.ts** - Review analytics
- **review_request_aggregate.ts** - Review request analytics
- **sidebarv2.ts** - Sidebar state
- **smtp_services.ts** - SMTP configuration
- **stripe_connect.ts** - Stripe integration
- **trigger_folder.ts** - Trigger organization
- **user_calendars.ts** - User calendar preferences

## Implementation Strategy

### Phase 1: Foundation (Weeks 1-2)
1. Set up RTK Query infrastructure
2. Implement auth system (auth_user.ts)
3. Basic user management (user.ts)
4. Location management (locations.ts)

### Phase 2: Core Business Logic (Weeks 3-8)  
1. Contacts management (contacts.ts)
2. Opportunities management (opportunities.ts)
3. Conversation system (conversation.ts)
4. Company management (company.ts)

### Phase 3: Business Features (Weeks 9-14)
1. Campaigns (campaigns.ts)
2. Workflows (workflows.ts)  
3. Pipelines (pipelines.ts)
4. Calendar integration (calendars.ts)
5. Teams and users (teams.ts, users.ts)
6. Integrations (integrations.ts)

### Phase 4: Supporting Features (Weeks 15-20)
1. All remaining simple modules
2. Performance optimization
3. Testing and bug fixes
4. Documentation

## Common Patterns Across Modules

### 1. Location-based Filtering
Almost all modules filter data by `location_id`:
```typescript
interface BaseState {
  currentLocationId: string | null;
  // Clear data when location changes
}
```

### 2. Real-time Updates
Most modules use Firebase listeners or need WebSocket integration:
```typescript
// WebSocket event pattern
interface UpdateEvent {
  type: 'created' | 'updated' | 'deleted';
  data: EntityType;
  location_id: string;
}
```

### 3. CRUD Operations
Standard CRUD pattern for most entities:
```typescript
// API endpoints pattern
GET /entities?location_id={id}
GET /entities/{id}  
POST /entities
PUT /entities/{id}
DELETE /entities/{id}
```

### 4. Cache Management
Many modules implement caching strategies:
```typescript
// Cache invalidation pattern
invalidatesTags: (result, error, { id }) => [
  { type: 'Entity', id },
  { type: 'Entity', id: 'LIST' },
]
```

## Shared Utilities Needed

### 1. Location Management
- Location switching logic
- Permission validation
- Data cleanup on location change

### 2. Real-time Infrastructure  
- WebSocket connection management
- Event handling and routing
- Reconnection logic

### 3. Cache Management
- Cache invalidation strategies
- Memory management
- Performance optimization

### 4. Error Handling
- Network error recovery
- User-friendly error messages
- Retry logic with exponential backoff

### 5. Authentication Integration
- Token management
- Permission checking
- Route protection

## Success Metrics

### Performance
- Reduced bundle size (target: 20% reduction)
- Improved data fetching (target: 30% faster)
- Better caching (target: 50% cache hit rate)

### Developer Experience  
- Type safety (100% TypeScript coverage)
- Better debugging (Redux DevTools)
- Simplified state management

### Maintainability
- Cleaner code organization
- Better separation of concerns
- Improved testability (target: 80% test coverage)

## Risk Mitigation

### Technical Risks
- Complex business logic migration
- Real-time feature parity
- Performance regression

### Mitigation Strategies
- Gradual module-by-module migration
- Comprehensive testing at each phase
- Performance monitoring and optimization
- Rollback plan for each module
