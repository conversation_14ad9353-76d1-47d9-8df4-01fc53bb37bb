# Opportunities Management - Complete Feature Specification

## Overview
The opportunities management system handles complex sales pipeline data with advanced search, filtering, real-time updates, and aggregate calculations. This document provides complete specifications for implementing equivalent functionality with RTK Query.

## Core Data Models

### Opportunity Object
```typescript
interface Opportunity {
  id: string;
  contact_id: string;
  location_id: string;
  pipeline_id: string;
  pipeline_stage_id: string;
  name: string;
  monetary_value: number;
  status: 'open' | 'won' | 'lost' | 'abandoned';
  assigned_to?: string;
  campaign_id?: string;
  date_added: Date;
  date_updated: Date;
  last_action_date?: Date;
  source?: string;
  tags?: string[];
  custom_fields?: { [key: string]: any };
  deleted?: boolean;
  
  // Contact data (populated from contacts store)
  contact_name?: string;
  email?: string;
  phone?: string;
  full_name?: string;
  company_name?: string;
}
```

### Opportunity Aggregate Data
```typescript
interface OpportunityStageData {
  id: string; // pipeline_stage_id
  totalCount: number;
  revenues: number;
  isLoaded?: boolean;
  isLoading?: boolean;
}

interface OpportunityAggregates {
  [stageId: string]: OpportunityStageData;
}
```

### Search & Filter Parameters
```typescript
interface OpportunityFilters {
  locationId: string;
  pipelineId: string;
  pipelineStageId?: string;
  query?: string; // text search
  assignedTo?: string; // user ID
  campaignId?: string;
  status?: 'all' | 'open' | 'won' | 'lost' | 'abandoned';
  startDate?: string;
  endDate?: string;
  orderBy?: 'date_desc' | 'date_asc' | 'added_desc' | 'added_asc' | 'value_desc' | 'value_asc';
  limit?: number;
  startAfter?: string; // for pagination
}
```

## API Endpoints Required

### 1. Search Opportunities (Primary Endpoint)
```
GET /search/opportunity/v2
Query Parameters:
- location_id: string (required)
- pipeline_id: string (required)
- pipeline_stage_id?: string
- q?: string (URL encoded search query, + becomes %2B)
- assigned_to?: string
- campaign_id?: string
- status?: string (exclude if 'all')
- date?: string (start date)
- endDate?: string
- order?: string (default: 'date_desc')
- limit?: number (default: 15)
- startAfter?: string (for pagination)

Response:
{
  opportunities: Opportunity[],
  aggregations: {
    pipelines: {
      buckets: Array<{
        key: string, // pipeline_stage_id
        doc_count: number,
        revenues: { value: number },
        pipelines: {
          hits: {
            hits: Array<{
              _id: string,
              _source: Opportunity
            }>
          }
        }
      }>
    }
  }
}
```

### 2. Opportunity Aggregates
```
GET /aggregate/opportunity
Query Parameters: (same as search endpoint)

Response:
{
  aggregations: {
    pipelines: {
      buckets: Array<{
        key: string, // pipeline_stage_id
        doc_count: number,
        status: {
          buckets: Array<{
            doc_count: number,
            revenues: { value: number }
          }>
        }
      }>
    }
  }
}
```

### 3. Individual Opportunity Operations
```
GET /opportunities/{id}
PUT /opportunities/{id}
DELETE /opportunities/{id}
POST /opportunities
```

## Business Logic Requirements

### 1. Search Query Processing
- Handle special characters in search (+ becomes %2B)
- Support text search across opportunity name, contact data
- Filter by multiple criteria simultaneously
- Support date range filtering

### 2. Opportunity Positioning & Ordering
- **Date Descending**: Most recent first
- **Date Ascending**: Oldest first  
- **Added Descending**: Recently added first
- **Added Ascending**: Oldest added first
- **Value Descending**: Highest value first
- **Value Ascending**: Lowest value first

### 3. Real-time Update Logic
When an opportunity is updated via real-time connection:

1. **Filter Validation**: Check if updated opportunity still matches current filters
2. **Position Calculation**: Determine correct position based on sort order
3. **Aggregate Updates**: Recalculate stage totals and revenues
4. **Contact Data Integration**: Merge contact information if available

### 4. Pagination Logic
- Load 15 opportunities per request by default
- Use `startAfter` parameter for cursor-based pagination
- Track loading/loaded state per pipeline stage
- Merge new results with existing data

### 5. Aggregate Calculations
- **Total Count**: Number of opportunities per stage
- **Total Revenue**: Sum of monetary_value per stage
- **Real-time Updates**: Increment/decrement on opportunity changes
- **Stage Transitions**: Handle revenue movement between stages

## State Management Requirements

### 1. Local State Structure
```typescript
interface OpportunitiesState {
  // Current context
  currentLocationId: string | null;
  activeFilters: OpportunityFilters;
  
  // UI state
  selectedOpportunityId: string | null;
  
  // Real-time features
  realTimeEnabled: boolean;
  watchedOpportunities: Set<string>;
  
  // Pagination state
  stageLoadingStates: {
    [stageId: string]: {
      isLoading: boolean;
      isLoaded: boolean;
    }
  };
  
  // Performance tracking
  aggregateLoadingStates: {
    [pipelineId: string]: boolean;
  };
}
```

### 2. Cache Management Strategy
- **Query Key Structure**: Include all filter parameters
- **Cache Invalidation**: On opportunity CRUD operations
- **Selective Updates**: Update specific opportunities in cache
- **Memory Management**: Clear cache on location/pipeline changes

### 3. Real-time Integration Points
- **WebSocket Events**: opportunity_updated, opportunity_added, opportunity_deleted
- **Filter Validation**: Check if real-time updates match current filters
- **Cache Updates**: Optimistically update RTK Query cache
- **Contact Integration**: Fetch contact data for new opportunities

## Advanced Features

### 1. Contact Data Integration
- Fetch contact data for each opportunity
- Cache contact information locally
- Include contact fields in search results
- Handle contact updates affecting opportunities

### 2. Opportunity Validation
```typescript
// Validate if opportunity matches current filters
async function validateOpportunityFilter(
  opportunity: Opportunity,
  contact: Contact | null,
  filters: OpportunityFilters
): Promise<boolean> {
  // Status filter
  if (filters.status !== 'all' && opportunity.status !== filters.status) {
    return false;
  }
  
  // User assignment filter
  if (filters.assignedTo && opportunity.assigned_to !== filters.assignedTo) {
    return false;
  }
  
  // Pipeline filter
  if (filters.pipelineId && opportunity.pipeline_id !== filters.pipelineId) {
    return false;
  }
  
  // Text search across opportunity and contact data
  if (filters.query) {
    const searchText = filters.query.toLowerCase();
    const searchableText = [
      opportunity.name,
      contact?.contact_name,
      contact?.email,
      contact?.phone,
      contact?.company_name,
    ].filter(Boolean).join(' ').toLowerCase();
    
    if (!searchableText.includes(searchText)) {
      return false;
    }
  }
  
  // Date range filter
  if (filters.startDate || filters.endDate) {
    const oppDate = new Date(opportunity.date_added);
    if (filters.startDate && oppDate < new Date(filters.startDate)) {
      return false;
    }
    if (filters.endDate && oppDate > new Date(filters.endDate)) {
      return false;
    }
  }
  
  return true;
}
```

### 3. Performance Optimizations
- **Selective Subscriptions**: Only watch opportunities in current view
- **Debounced Search**: Delay search requests during typing
- **Virtual Scrolling**: Handle large opportunity lists
- **Background Sync**: Refresh data periodically
- **Optimistic Updates**: Update UI before server confirmation

## Implementation Priorities

### Phase 1: Core Functionality
1. Basic opportunity search with filters
2. CRUD operations for opportunities
3. Aggregate data calculations
4. Pagination support

### Phase 2: Advanced Features
1. Real-time updates via WebSocket
2. Contact data integration
3. Complex filter validation
4. Opportunity positioning logic

### Phase 3: Performance & UX
1. Optimistic updates
2. Advanced caching strategies
3. Error handling and retry logic
4. Loading states and skeleton screens

## Testing Requirements

### 1. API Integration Tests
- Search with various filter combinations
- Pagination edge cases
- Real-time update handling
- Error scenarios (network failures, invalid data)

### 2. Business Logic Tests
- Filter validation accuracy
- Opportunity positioning algorithms
- Aggregate calculation correctness
- Contact data integration

### 3. Performance Tests
- Large dataset handling (1000+ opportunities)
- Real-time update performance
- Memory usage optimization
- Cache efficiency

## Error Handling

### 1. Network Errors
- Retry failed requests with exponential backoff
- Show user-friendly error messages
- Maintain offline capability where possible

### 2. Data Validation Errors
- Validate opportunity data before display
- Handle missing or corrupted data gracefully
- Provide fallback values for required fields

### 3. Real-time Connection Issues
- Reconnect WebSocket on disconnection
- Sync data after reconnection
- Show connection status to users
