# Contacts Store Migration Roadmap

## Current Implementation Analysis

### File: `src/store/contacts.ts`

**Current State Shape:**
```typescript
interface ContactsState {
  contacts: { [key: string]: any };
  deletedContacts: { [key: string]: any };
  ids: Set<string>;
  locationId: undefined | string;
}
```

**Key Features:**
- Local caching mechanism with timeout (3 seconds)
- Watch list for contact IDs
- Real-time Firebase listeners (commented out)
- Soft delete tracking
- Location-based contact management

**Current Actions:**
- `syncGet` - Get contact with local caching
- `update` - Update contact and add to watch list
- `getAndWatch` - Get contact and start watching for changes
- `syncAll` - Sync all contacts for location (Firebase listener)

**Current Mutations:**
- `addToWatchList` - Add contact IDs to watch list
- `clearAll` - Clear all contacts and listeners
- `addUpdate` - Add or update contact
- `addIfNotExists` - Add contact if not exists
- `addDeletedContact` - Mark contact as deleted

**Local Caching Class:**
- `LocalCachedContacts` - Custom caching with 3-second timeout

## RTK Migration Plan

### 1. RTK Query API Structure

```typescript
// src/store/api/contactsApi.ts
export const contactsApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getContact: builder.query<Contact, string>({
      query: (id) => `/contacts/${id}`,
      providesTags: (result, error, id) => [{ type: 'Contact', id }],
    }),
    
    getContacts: builder.query<Contact[], { locationId: string; limit?: number }>({
      query: ({ locationId, limit = 50 }) => 
        `/contacts?locationId=${locationId}&limit=${limit}`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Contact' as const, id })),
              { type: 'Contact', id: 'LIST' },
            ]
          : [{ type: 'Contact', id: 'LIST' }],
    }),
    
    updateContact: builder.mutation<Contact, { id: string; data: Partial<Contact> }>({
      query: ({ id, data }) => ({
        url: `/contacts/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Contact', id }],
    }),
    
    deleteContact: builder.mutation<void, string>({
      query: (id) => ({
        url: `/contacts/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [{ type: 'Contact', id }],
    }),
  }),
});
```

### 2. RTK Slice for Local State

```typescript
// src/store/slices/contactsSlice.ts
interface ContactsState {
  watchList: Set<string>;
  deletedContacts: Set<string>;
  currentLocationId: string | null;
  localCache: { [key: string]: { data: Contact; timestamp: number } };
  isRealTimeEnabled: boolean;
}

const contactsSlice = createSlice({
  name: 'contacts',
  initialState,
  reducers: {
    addToWatchList: (state, action: PayloadAction<string | string[]>) => {
      const ids = Array.isArray(action.payload) ? action.payload : [action.payload];
      ids.forEach(id => state.watchList.add(id));
    },
    
    removeFromWatchList: (state, action: PayloadAction<string>) => {
      state.watchList.delete(action.payload);
    },
    
    addToDeletedContacts: (state, action: PayloadAction<string>) => {
      state.deletedContacts.add(action.payload);
    },
    
    setCurrentLocation: (state, action: PayloadAction<string>) => {
      if (state.currentLocationId !== action.payload) {
        state.currentLocationId = action.payload;
        state.watchList.clear();
        state.deletedContacts.clear();
        state.localCache = {};
      }
    },
    
    updateLocalCache: (state, action: PayloadAction<{ id: string; data: Contact }>) => {
      state.localCache[action.payload.id] = {
        data: action.payload.data,
        timestamp: Date.now(),
      };
    },
    
    clearLocalCache: (state) => {
      state.localCache = {};
    },
    
    toggleRealTime: (state, action: PayloadAction<boolean>) => {
      state.isRealTimeEnabled = action.payload;
    },
  },
});
```

### 3. Custom Hooks for Contact Management

```typescript
// src/hooks/useContacts.ts
export const useContacts = (locationId: string) => {
  const dispatch = useAppDispatch();
  const { watchList, currentLocationId } = useAppSelector(selectContacts);
  
  const {
    data: contacts,
    isLoading,
    error,
  } = contactsApi.useGetContactsQuery(
    { locationId },
    { skip: !locationId }
  );
  
  useEffect(() => {
    if (locationId !== currentLocationId) {
      dispatch(contactsSlice.actions.setCurrentLocation(locationId));
    }
  }, [locationId, currentLocationId, dispatch]);
  
  return { contacts, isLoading, error };
};

export const useContact = (id: string, options?: { enableCache?: boolean }) => {
  const dispatch = useAppDispatch();
  const { localCache } = useAppSelector(selectContacts);
  
  // Check local cache first
  const cachedContact = useMemo(() => {
    if (options?.enableCache && localCache[id]) {
      const cached = localCache[id];
      const isExpired = Date.now() - cached.timestamp > 3000; // 3 second timeout
      return isExpired ? null : cached.data;
    }
    return null;
  }, [id, localCache, options?.enableCache]);
  
  const {
    data: contact,
    isLoading,
    error,
  } = contactsApi.useGetContactQuery(id, {
    skip: !!cachedContact,
  });
  
  const finalContact = cachedContact || contact;
  
  useEffect(() => {
    if (finalContact) {
      dispatch(contactsSlice.actions.updateLocalCache({ id, data: finalContact }));
      dispatch(contactsSlice.actions.addToWatchList(id));
    }
  }, [finalContact, id, dispatch]);
  
  return { contact: finalContact, isLoading, error };
};
```

### 4. Real-time Integration

```typescript
// src/hooks/useContactRealTime.ts
export const useContactRealTime = (locationId: string) => {
  const dispatch = useAppDispatch();
  const { isRealTimeEnabled, watchList } = useAppSelector(selectContacts);
  
  useEffect(() => {
    if (!isRealTimeEnabled || !locationId) return;
    
    // Set up WebSocket or Server-Sent Events for real-time updates
    const eventSource = new EventSource(`/api/contacts/stream?locationId=${locationId}`);
    
    eventSource.onmessage = (event) => {
      const { type, data } = JSON.parse(event.data);
      
      switch (type) {
        case 'contact_updated':
          if (watchList.has(data.id)) {
            dispatch(contactsApi.util.updateQueryData('getContact', data.id, () => data));
          }
          break;
        case 'contact_deleted':
          dispatch(contactsSlice.actions.addToDeletedContacts(data.id));
          dispatch(contactsApi.util.invalidateTags([{ type: 'Contact', id: data.id }]));
          break;
      }
    };
    
    return () => {
      eventSource.close();
    };
  }, [isRealTimeEnabled, locationId, watchList, dispatch]);
};
```

### 5. Migration Strategy

#### Phase 1: Basic RTK Query Setup
1. Create contactsApi with basic CRUD operations
2. Implement RTK slice for local state management
3. Create basic hooks for contact fetching

#### Phase 2: Caching Implementation
1. Implement local caching mechanism in RTK slice
2. Create custom hooks with cache-first strategy
3. Add cache invalidation logic

#### Phase 3: Real-time Features
1. Replace Firebase listeners with WebSocket/SSE
2. Implement real-time update handling
3. Add watch list functionality

#### Phase 4: Advanced Features
1. Implement soft delete tracking
2. Add location-based filtering
3. Optimize performance with selective updates

### 6. Testing Strategy

```typescript
// src/store/api/__tests__/contactsApi.test.ts
describe('contactsApi', () => {
  it('should fetch contact by id', async () => {
    const mockContact = { id: '1', name: 'John Doe' };
    // Mock API and test
  });
  
  it('should handle contact updates', async () => {
    // Test update mutation
  });
  
  it('should invalidate cache on delete', async () => {
    // Test cache invalidation
  });
});

// src/hooks/__tests__/useContacts.test.ts
describe('useContacts', () => {
  it('should return contacts for location', () => {
    // Test hook behavior
  });
  
  it('should use cached data when available', () => {
    // Test caching behavior
  });
});
```

### 7. Performance Optimizations

1. **Selective Subscriptions**: Only subscribe to contacts in watch list
2. **Cache Management**: Implement LRU cache for contact data
3. **Batch Updates**: Group multiple contact updates
4. **Lazy Loading**: Load contacts on demand
5. **Memory Management**: Clear cache when location changes

### 8. Migration Checklist

- [ ] Create contactsApi with all CRUD endpoints
- [ ] Implement contactsSlice for local state
- [ ] Create useContacts and useContact hooks
- [ ] Implement local caching mechanism
- [ ] Set up real-time update handling
- [ ] Add watch list functionality
- [ ] Implement soft delete tracking
- [ ] Add location-based filtering
- [ ] Write comprehensive tests
- [ ] Optimize performance
- [ ] Update existing components
- [ ] Handle error states
- [ ] Implement loading states

### 9. Potential Challenges

1. **Cache Synchronization**: Keeping local cache in sync with server
2. **Real-time Complexity**: Managing real-time updates efficiently
3. **Memory Management**: Preventing memory leaks with large contact lists
4. **Location Switching**: Handling data cleanup when switching locations

### 10. Success Criteria

- [ ] Contact fetching works with caching
- [ ] Real-time updates function correctly
- [ ] Watch list tracks viewed contacts
- [ ] Location switching clears appropriate data
- [ ] Performance is maintained or improved
- [ ] Memory usage is optimized
- [ ] Error handling provides clear feedback
