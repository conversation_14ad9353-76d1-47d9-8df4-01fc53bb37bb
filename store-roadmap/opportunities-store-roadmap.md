# Opportunities Store Migration Roadmap

## Current Implementation Analysis

### File: `src/store/opportunities.ts`

**Current State Shape:**
```typescript
interface OpportunityState {
  opportunities: any[];
  locationId: undefined | string;
  filters: any;
  opportunitiesStageData: any;
  loadingAggregates: boolean;
  isReady: boolean;
}
```

**Key Features:**
- Complex Elasticsearch-based search and filtering
- Real-time Firebase listeners for opportunity updates
- Aggregate data calculations (revenue, counts by stage)
- Advanced filtering (text search, user assignment, pipeline, date ranges)
- Pagination with load more functionality
- Complex opportunity positioning and ordering
- Integration with contacts store for contact data

**Current Actions:**
- `syncAll` - Complex search with filters and aggregations
- `addRealtimeListner` - Firebase real-time updates
- `updateOppsInState` - Complex opportunity update logic
- `updateStatus` - Update opportunity status with positioning
- `updateAggregates` - Refresh aggregate calculations
- `loadMoreData` - Pagination support

**Complex Business Logic:**
- Opportunity positioning based on date and stage
- Revenue calculations per pipeline stage
- Filter validation and search optimization
- Contact data integration for search

## RTK Migration Plan

### 1. RTK Query API Structure

```typescript
// src/store/api/opportunitiesApi.ts
export const opportunitiesApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    searchOpportunities: builder.query<OpportunitySearchResponse, OpportunitySearchParams>({
      query: (params) => ({
        url: '/search/opportunity/v2',
        params: {
          location_id: params.locationId,
          q: params.query,
          assigned_to: params.assignedTo,
          pipeline_id: params.pipelineId,
          pipeline_stage_id: params.pipelineStageId,
          campaign_id: params.campaignId,
          date: params.startDate,
          endDate: params.endDate,
          order: params.orderBy || 'date_desc',
          limit: params.limit || 15,
          startAfter: params.startAfter,
          status: params.status !== 'all' ? params.status : undefined,
        },
      }),
      providesTags: (result, error, params) => [
        { type: 'Opportunity', id: 'LIST' },
        { type: 'OpportunityAggregate', id: params.pipelineId },
      ],
      serializeQueryArgs: ({ queryArgs }) => {
        // Custom serialization to handle complex filter objects
        const { startAfter, ...baseArgs } = queryArgs;
        return baseArgs;
      },
      merge: (currentCache, newItems, { arg }) => {
        if (arg.startAfter) {
          // Pagination: merge with existing data
          return {
            ...newItems,
            opportunities: [...currentCache.opportunities, ...newItems.opportunities],
          };
        }
        return newItems;
      },
      forceRefetch({ currentArg, previousArg }) {
        return !isEqual(currentArg, previousArg);
      },
    }),

    getOpportunityAggregates: builder.query<OpportunityAggregates, OpportunityAggregateParams>({
      query: (params) => ({
        url: '/aggregate/opportunity',
        params,
      }),
      providesTags: (result, error, params) => [
        { type: 'OpportunityAggregate', id: params.pipelineId },
      ],
    }),

    updateOpportunity: builder.mutation<Opportunity, UpdateOpportunityParams>({
      query: ({ id, data, nextOpportunityId }) => ({
        url: `/opportunities/${id}`,
        method: 'PUT',
        body: { ...data, nextOpportunityId },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Opportunity', id },
        { type: 'Opportunity', id: 'LIST' },
        { type: 'OpportunityAggregate', id: 'PARTIAL-LIST' },
      ],
      onQueryStarted: async ({ id, data, nextOpportunityId }, { dispatch, queryFulfilled }) => {
        // Optimistic update
        const patchResult = dispatch(
          opportunitiesApi.util.updateQueryData('searchOpportunities', {}, (draft) => {
            const index = draft.opportunities.findIndex(opp => opp.id === id);
            if (index !== -1) {
              draft.opportunities[index] = { ...draft.opportunities[index], ...data };
              
              // Handle repositioning if nextOpportunityId is provided
              if (nextOpportunityId) {
                const [opportunity] = draft.opportunities.splice(index, 1);
                const nextIndex = draft.opportunities.findIndex(opp => opp.id === nextOpportunityId);
                if (nextIndex !== -1) {
                  draft.opportunities.splice(nextIndex, 0, opportunity);
                } else {
                  draft.opportunities.push(opportunity);
                }
              }
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),
  }),
});
```

### 2. RTK Slice for Complex State Management

```typescript
// src/store/slices/opportunitiesSlice.ts
interface OpportunitiesState {
  currentLocationId: string | null;
  activeFilters: OpportunityFilters;
  realTimeEnabled: boolean;
  watchedOpportunities: Set<string>;
  stageLoadingStates: { [stageId: string]: { isLoading: boolean; isLoaded: boolean } };
  aggregateLoadingStates: { [pipelineId: string]: boolean };
}

const opportunitiesSlice = createSlice({
  name: 'opportunities',
  initialState,
  reducers: {
    setActiveFilters: (state, action: PayloadAction<OpportunityFilters>) => {
      state.activeFilters = action.payload;
    },
    
    setCurrentLocation: (state, action: PayloadAction<string>) => {
      if (state.currentLocationId !== action.payload) {
        state.currentLocationId = action.payload;
        state.watchedOpportunities.clear();
        state.stageLoadingStates = {};
      }
    },
    
    addToWatchList: (state, action: PayloadAction<string[]>) => {
      action.payload.forEach(id => state.watchedOpportunities.add(id));
    },
    
    setStageLoading: (state, action: PayloadAction<{ stageId: string; isLoading: boolean }>) => {
      const { stageId, isLoading } = action.payload;
      if (!state.stageLoadingStates[stageId]) {
        state.stageLoadingStates[stageId] = { isLoading: false, isLoaded: false };
      }
      state.stageLoadingStates[stageId].isLoading = isLoading;
    },
    
    setStageLoaded: (state, action: PayloadAction<{ stageId: string; isLoaded: boolean }>) => {
      const { stageId, isLoaded } = action.payload;
      if (!state.stageLoadingStates[stageId]) {
        state.stageLoadingStates[stageId] = { isLoading: false, isLoaded: false };
      }
      state.stageLoadingStates[stageId].isLoaded = isLoaded;
    },
    
    toggleRealTime: (state, action: PayloadAction<boolean>) => {
      state.realTimeEnabled = action.payload;
    },
  },
});
```

### 3. Complex Business Logic Hooks

```typescript
// src/hooks/useOpportunities.ts
export const useOpportunities = (locationId: string, filters: OpportunityFilters) => {
  const dispatch = useAppDispatch();
  const { activeFilters, currentLocationId } = useAppSelector(selectOpportunities);
  
  // Memoize search parameters
  const searchParams = useMemo(() => ({
    locationId,
    ...filters,
  }), [locationId, filters]);
  
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = opportunitiesApi.useSearchOpportunitiesQuery(searchParams, {
    skip: !locationId || !filters.pipelineId,
  });
  
  // Update active filters when they change
  useEffect(() => {
    if (!isEqual(filters, activeFilters)) {
      dispatch(opportunitiesSlice.actions.setActiveFilters(filters));
    }
  }, [filters, activeFilters, dispatch]);
  
  // Update location when it changes
  useEffect(() => {
    if (locationId !== currentLocationId) {
      dispatch(opportunitiesSlice.actions.setCurrentLocation(locationId));
    }
  }, [locationId, currentLocationId, dispatch]);
  
  // Add opportunities to watch list
  useEffect(() => {
    if (searchResult?.opportunities) {
      const contactIds = searchResult.opportunities
        .map(opp => opp.contact_id)
        .filter(Boolean);
      
      dispatch(opportunitiesSlice.actions.addToWatchList(contactIds));
      
      // Also add to contacts watch list
      dispatch(contactsSlice.actions.addToWatchList(contactIds));
    }
  }, [searchResult, dispatch]);
  
  return {
    opportunities: searchResult?.opportunities || [],
    aggregates: searchResult?.opportunitiesStageData || {},
    isLoading,
    error,
    refetch,
  };
};

export const useOpportunityPagination = (
  locationId: string,
  filters: OpportunityFilters,
  stageId: string
) => {
  const dispatch = useAppDispatch();
  const { stageLoadingStates } = useAppSelector(selectOpportunities);
  
  const stageState = stageLoadingStates[stageId] || { isLoading: false, isLoaded: false };
  
  const loadMore = useCallback(async (startAfter: string) => {
    if (stageState.isLoading) return;
    
    dispatch(opportunitiesSlice.actions.setStageLoading({ stageId, isLoading: true }));
    
    try {
      const result = await dispatch(
        opportunitiesApi.endpoints.searchOpportunities.initiate({
          ...filters,
          locationId,
          pipelineStageId: stageId,
          startAfter,
        })
      ).unwrap();
      
      if (result.opportunities.length < 15) {
        dispatch(opportunitiesSlice.actions.setStageLoaded({ stageId, isLoaded: true }));
      }
    } finally {
      dispatch(opportunitiesSlice.actions.setStageLoading({ stageId, isLoading: false }));
    }
  }, [dispatch, filters, locationId, stageId, stageState.isLoading]);
  
  return { loadMore, isLoading: stageState.isLoading, isLoaded: stageState.isLoaded };
};
```

### 4. Real-time Updates Integration

```typescript
// src/hooks/useOpportunityRealTime.ts
export const useOpportunityRealTime = (locationId: string, pipelineId: string) => {
  const dispatch = useAppDispatch();
  const { realTimeEnabled, watchedOpportunities, activeFilters } = useAppSelector(selectOpportunities);
  
  useEffect(() => {
    if (!realTimeEnabled || !locationId || !pipelineId) return;
    
    // Set up WebSocket for real-time opportunity updates
    const ws = new WebSocket(`/ws/opportunities?locationId=${locationId}&pipelineId=${pipelineId}`);
    
    ws.onmessage = async (event) => {
      const { type, data: opportunity } = JSON.parse(event.data);
      
      if (type === 'opportunity_updated' || type === 'opportunity_added') {
        // Check if opportunity passes current filters
        const passesFilter = await validateOpportunityFilter(opportunity, activeFilters);
        
        if (passesFilter) {
          // Update the opportunity in cache
          dispatch(
            opportunitiesApi.util.updateQueryData('searchOpportunities', activeFilters, (draft) => {
              const index = draft.opportunities.findIndex(opp => opp.id === opportunity.id);
              
              if (index !== -1) {
                // Update existing opportunity
                draft.opportunities[index] = opportunity;
              } else {
                // Add new opportunity at appropriate position
                const insertIndex = findInsertPosition(draft.opportunities, opportunity, activeFilters.orderBy);
                draft.opportunities.splice(insertIndex, 0, opportunity);
              }
              
              // Update aggregates
              updateAggregatesInDraft(draft, opportunity);
            })
          );
        } else if (watchedOpportunities.has(opportunity.id)) {
          // Remove opportunity that no longer matches filters
          dispatch(
            opportunitiesApi.util.updateQueryData('searchOpportunities', activeFilters, (draft) => {
              const index = draft.opportunities.findIndex(opp => opp.id === opportunity.id);
              if (index !== -1) {
                draft.opportunities.splice(index, 1);
                updateAggregatesInDraft(draft, opportunity, 'remove');
              }
            })
          );
        }
      }
      
      if (type === 'opportunity_deleted') {
        dispatch(
          opportunitiesApi.util.updateQueryData('searchOpportunities', activeFilters, (draft) => {
            const index = draft.opportunities.findIndex(opp => opp.id === opportunity.id);
            if (index !== -1) {
              draft.opportunities.splice(index, 1);
              updateAggregatesInDraft(draft, opportunity, 'remove');
            }
          })
        );
      }
    };
    
    return () => {
      ws.close();
    };
  }, [realTimeEnabled, locationId, pipelineId, watchedOpportunities, activeFilters, dispatch]);
};
```

### 5. Migration Strategy

#### Phase 1: Basic API Setup (Week 1)
1. Create opportunitiesApi with search endpoint
2. Implement basic RTK slice
3. Create useOpportunities hook

#### Phase 2: Complex Filtering (Week 2)
1. Implement advanced search parameters
2. Add filter validation logic
3. Handle complex query serialization

#### Phase 3: Aggregates & Pagination (Week 3)
1. Add aggregate data endpoints
2. Implement pagination with merge logic
3. Create pagination hooks

#### Phase 4: Real-time Updates (Week 4)
1. Replace Firebase listeners with WebSocket
2. Implement optimistic updates
3. Add filter validation for real-time data

#### Phase 5: Performance Optimization (Week 5)
1. Implement selective cache updates
2. Add memory management
3. Optimize re-renders

### 6. Testing Strategy

```typescript
// Complex business logic testing
describe('opportunityFilters', () => {
  it('should validate opportunity against filters', async () => {
    const opportunity = createMockOpportunity();
    const filters = createMockFilters();
    const result = await validateOpportunityFilter(opportunity, filters);
    expect(result).toBe(true);
  });
});

describe('opportunityPositioning', () => {
  it('should find correct insert position for new opportunity', () => {
    const opportunities = createMockOpportunities();
    const newOpportunity = createMockOpportunity();
    const position = findInsertPosition(opportunities, newOpportunity, 'date_desc');
    expect(position).toBe(2);
  });
});
```

### 7. Migration Checklist

- [ ] Create opportunitiesApi with search and aggregate endpoints
- [ ] Implement opportunitiesSlice for complex state
- [ ] Create useOpportunities hook with filtering
- [ ] Implement pagination with merge logic
- [ ] Add real-time update handling
- [ ] Create opportunity positioning logic
- [ ] Implement aggregate calculations
- [ ] Add filter validation
- [ ] Write comprehensive tests
- [ ] Optimize performance
- [ ] Handle error states
- [ ] Update existing components

### 8. Success Criteria

- [ ] Complex search and filtering works correctly
- [ ] Real-time updates maintain data consistency
- [ ] Pagination loads additional data properly
- [ ] Aggregate calculations are accurate
- [ ] Performance is maintained with large datasets
- [ ] Memory usage is optimized
- [ ] Error handling provides clear feedback
